{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "command": "npm start",
      "name": "Run npm start",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "type": "pwa-node",
      "request": "launch",
      "name": "Launch Program",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/dist/index.js",
      "preLaunchTask": "tsc: build - tsconfig.json",
      "outFiles": [
        "${workspaceFolder}/dist/**/*.js"
      ]
    },
    {
      "type": "pwa-node",
      "request": "launch",
      "name": "Debug QuickBooks Test",
      "program": "${workspaceFolder}/node_modules/.bin/ts-node",
      "args": ["test/test_qb_auth.ts"],
      "skipFiles": [
        "<node_internals>/**"
      ],
      "env": {
        "TS_NODE_PROJECT": "tsconfig.json"
      },
      "console": "integratedTerminal"
    }
  ]
}
