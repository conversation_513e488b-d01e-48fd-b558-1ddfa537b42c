{
  "editor.rulers": [120],
  "editor.tabCompletion": "on",
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.formatOnSave": true
  },
  "[typescript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.formatOnSave": true
  },
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "files.exclude": {
    "**/.DS_Store": true,
    "**/.git": true,
    "**/.hg": true,
    "**/.svn": true,
    "**/CVS": true,
    "dist": true
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "typescript.tsdk": "./node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false,
  "typescript.preferences.quoteStyle": "single",
  "eslint.run": "onSave",
  "eslint.nodePath": "./node_modules",
  "eslint.validate": ["javascript", "typescript"],
  "workbench.colorTheme": "Default Dark+",
  "workbench.colorCustomizations": {
    "titleBar.activeBackground": "#482cfff8",
    "titleBar.inactiveBackground": "#482cfff8",
    // "titleBar.activeForeground": "#2cff3771",
    "titleBar.inactiveForeground": "#482cfff8"
  },
  "cSpell.words": [
    "datalake",
    "EJSON",
    "IPCA",
    "LAYERNEXT"
  ]
}
