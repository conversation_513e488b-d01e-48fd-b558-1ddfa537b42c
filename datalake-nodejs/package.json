{"name": "datalake", "version": "0.0.1", "description": "datalake", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.16"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t datalake .", "docker:run": "docker run -p 3000:3000 -d datalake", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build", "stop": "pm2 stop ecosystem.config.js --env development"}, "repository": {"type": "git", "url": ""}, "author": "isuru <<EMAIL>>", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@azure/storage-blob": "^12.14.0", "@google-cloud/bigquery": "^7.9.1", "@google-cloud/storage": "^6.11.0", "@loopback/authentication": "^10.1.5", "@loopback/authentication-jwt": "^0.14.0", "@loopback/authorization": "^0.14.0", "@loopback/boot": "^6.1.0", "@loopback/core": "^5.1.5", "@loopback/cron": "^0.11.0", "@loopback/repository": "^6.1.0", "@loopback/rest": "^13.1.0", "@loopback/rest-explorer": "^6.1.0", "@loopback/service-proxy": "^6.1.0", "@types/cron": "^2.0.0", "@types/lodash": "^4.14.182", "@types/mongodb": "^4.0.7", "@types/uuid": "^8.3.4", "aws-sdk": "^2.1167.0", "axios": "^0.27.2", "base-64": "^1.0.0", "bson": "^4.7.0", "cron": "^2.0.0", "crypto": "^1.0.1", "csvtojson": "^2.0.10", "dotenv": "^16.0.1", "express": "^4.19.2", "fs": "^0.0.1-security", "intuit-oauth": "^4.2.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "log4js": "^6.5.2", "loopback-connector-mongodb": "^5.6.0", "moment": "^2.29.4", "mongodb": "^4.7.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "multiparty": "^4.2.3", "mysql2": "^3.7.1", "node-quickbooks": "^2.0.46", "path": "^0.12.7", "pg": "^8.12.0", "randomcolor": "^0.6.2", "readdirp": "^3.6.0", "redis": "^4.1.0", "snowflake-sdk": "^1.15.0", "tedious": "^18.3.0", "tslib": "^2.0.0", "utf8": "^3.0.0"}, "devDependencies": {"@loopback/build": "^10.1.0", "@loopback/eslint-config": "^14.0.1", "@loopback/testlab": "^6.1.0", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.14.182", "@types/mssql": "^9.1.5", "@types/multer": "^1.4.7", "@types/multiparty": "^0.0.36", "@types/node": "^10.17.60", "@types/pg": "^8.11.6", "eslint": "^8.41.0", "source-map-support": "^0.5.19", "typescript": "^4.9.5"}}