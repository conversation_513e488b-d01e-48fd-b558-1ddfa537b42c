var trashLeafObj = db.getCollection('MetaData').aggregate([
  {
    $match: {
      objectStatus: 1,
      objectType: { $in: [1, 2, 6] }
    }
  },
  {
    $lookup: {
      from: 'MetaData',
      foreignField: '_id',
      localField: 'collectionId',
      as: 'col'
    }
  },
  {
    $unwind: "$col"
  },
  {
    $match: {
      "col.objectStatus": { $ne: 1 }
    }
  },
  {
    $project: { _id: 1 }
  },
  {
    $group: {
      _id: null,
      idList: { $push: "$_id" }
    }
  }
]).toArray()

if (trashLeafObj && trashLeafObj.length > 0) {
  var idList = trashLeafObj[0].idList

  print(idList)

  db.getCollection('MetaData').updateMany(
    {
      _id: { $in: idList }
    },
    {
      $set: { "showInTrash": true }
    }
  )

  db.getCollection('MetaData').updateMany(
    {
      objectStatus: 1,
      objectType: { $in: [4, 5, 7] }
    },
    {
      $set: { "showInTrash": true }
    }
  )
}
