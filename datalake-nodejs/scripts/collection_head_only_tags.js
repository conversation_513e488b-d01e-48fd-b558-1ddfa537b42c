var collections = db.getCollection('MetaData').aggregate([
    {$match: {objectType: {$in: [4, 5, 7]}, Tags: {$nin: [[], null]}}}
]).toArray()

for(var collection of collections){

    var tags = db.getCollection('MetaData').aggregate([
        {$match: {"collectionId" : collection._id}},
        {$unwind: "$Tags"},
        {$group: {_id: null, Tags: {$addToSet: "$Tags"}}}
    ]).toArray()
    collectio_head_tags = collection.Tags
    collection_child_tags = []
    if(tags[0]) collection_child_tags = tags[0].Tags
    head_tags = []
    for(var tag of collectio_head_tags){
        if(!collection_child_tags.includes(tag)){
            head_tags.push(tag)
        }
    }
    print(head_tags)
    db.getCollection('MetaData').updateOne({_id: collection._id}, {$set: {"collectionHeadOnlyMeta.Tags": head_tags}})
}
