var collectionObjArray = db
  .getCollection('MetaData')
  .aggregate([
    {
      $match: {objectType: {$in: [4, 5, 7]}},
    },
    {
      $group: {
        _id: null,
        collectionIds: {$addToSet: '$_id'},
      },
    },
  ])
  .toArray();

var collectionIdList = collectionObjArray[0]['collectionIds'];
// print(collectionIdList)
for (var collectionId of collectionIdList) {
  print(collectionId);
  db.getCollection('MetaData').updateMany(
    {collectionId: collectionId},
    {
      $addToSet: {vCollectionIdList: collectionId},
    },
  );
}
