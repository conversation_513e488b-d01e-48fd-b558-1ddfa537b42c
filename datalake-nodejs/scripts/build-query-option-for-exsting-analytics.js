var operationIdList = db.getCollection('MetaData').aggregate([
  {
    $match: { "analytics.0": { $exists: true }, objectType: { $in: [4, 5, 7] } }
  },
  {
    $project: { "analytics.operationId": 1, _id: 1 }
  },
  {
    $unwind: "$analytics"
  },
  {
    $project: { id: "$_id", operationId: "$analytics.operationId", _id: 0 }
  }
]).toArray();

var dotOperation = ["."];
var comparisonOperation = ["=", "!=", ">", ">=", "<", "<="]
var values = [50, 75, 90, 100]
var mlValues = ["recall", "precision", "f1Score"]

var insertionArray = []

if (operationIdList) {
  operationIdList.forEach(operationObj => {
    var queryOptionObject = {
      teamId: ObjectId("6374c3decb468b7a7a68a116"),
      collectionId: "",
      keyGroup: "",
      isRootGroup: true,
      key: "",
      operators: []
    }

    queryOptionObject.keyGroup = "analytics"
    queryOptionObject.collectionId = operationObj.id
    queryOptionObject.key = operationObj.operationId
    queryOptionObject.operators = dotOperation

    insertionArray.push(queryOptionObject)

    mlValues.forEach(mlValue => {
      queryOptionObject = {
        teamId: ObjectId("6374c3decb468b7a7a68a116"),
        collectionId: "",
        keyGroup: "",
        isRootGroup: false,
        key: "",
        operators: []
      }
      queryOptionObject.keyGroup = `analytics.${operationObj.operationId}`
      queryOptionObject.isRootGroup = false
      queryOptionObject.key = mlValue
      queryOptionObject.operators = dotOperation
      queryOptionObject.collectionId = operationObj.id
      insertionArray.push(queryOptionObject)


      values.forEach(value => {
        queryOptionObject = {
          teamId: ObjectId("6374c3decb468b7a7a68a116"),
          collectionId: "",
          keyGroup: "",
          isRootGroup: false,
          key: "",
          operators: []
        }
        queryOptionObject.keyGroup = `analytics.${operationObj.operationId}.${mlValue}`
        queryOptionObject.operators = comparisonOperation
        queryOptionObject.key = value
        queryOptionObject.collectionId = operationObj.id

        insertionArray.push(queryOptionObject)
      })
    })


  });

}

db.getCollection('QueryOption').insertMany(insertionArray)
