db.MetaData.aggregate([
  {$match: {objectKey: {$exists: true}}},
  {
    $addFields: {
      fileType: {
        $arrayElemAt: [
          {$split: ['$objectKey', '.']}, // Split the objectKey by '.'
          -1, // Get the last element from the split array, which should be the file extension
        ],
      },
    },
  },
  {
    $merge: {
      into: 'MetaData', // Merge results back into the original collection
      on: '_id', // Use the _id field to match documents between the aggregation results and the existing documents in the collection
      whenMatched: 'replace', // Replace the whole document in the collection with the aggregation result document
      whenNotMatched: 'discard', // Discard any documents from the aggregation result that don't match an existing document in the collection
    },
  },
]);
