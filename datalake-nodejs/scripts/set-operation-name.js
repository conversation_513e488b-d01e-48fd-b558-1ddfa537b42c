//run these sequentially

//run on studioDB
var operationArray = db.getCollection('AnnotationProject').aggregate([
  {$match: {"name": {$exists: true}}},
  {$project: {"operationId": "$_id", operationName: "$name"}}
]).toArray()
print(operationArray)

//run on datalakeDB
for(var operation of operationArray){
  //print(operation)
  db.getCollection('MetaDataUpdate').updateMany(
      {operationId: operation.operationId},
      {$set: {operationName: operation.operationName}}
  )
}

//run on datalakeDB
db.getCollection('MetaDataUpdate').updateMany(
  {operationName: {$exists: false}},
  [{$set: {"operationName": "$operationId"}}]
)
