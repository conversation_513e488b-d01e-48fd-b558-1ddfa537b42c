var collections = db.getCollection('MetaData').aggregate([
    {$match: {objectType: {$in: [4, 5, 7]}}}
]).toArray()

for(var collection of collections){
    var customMeta = {}
    if(collection.customMeta) customMeta = collection.customMeta
    //var collectionHeadOnlyMetaCustomMeta = {}
    //if(collection.collectionHeadOnlyMeta && collection.collectionHeadOnlyMeta.customMeta) collectionHeadOnlyMetaCustomMeta = collection.collectionHeadOnlyMeta.customMeta
    // db.getCollection('MetaData').updateOne({_id: collection._id}, {$set: {"collectionHeadOnlyMeta.Tags": head_tags}})
    //print(customMeta)
    for(var key in customMeta){
        for(var k of customMeta[key]){
            var obj = {};
            obj["customMeta."+key] = k;
            obj["collectionId"] = collection._id
            print(obj)
            print({collectionId: collection._id})
            var found = db.getCollection('MetaData').find(obj).toArray()
            print(found)
            if(found.length == 0){
                var collectionHeadOnlyMetaCustomMetaName = "collectionHeadOnlyMeta.customMeta." + key
                db.getCollection('MetaData').updateOne({_id: collection._id}, {$set: {[collectionHeadOnlyMetaCustomMetaName]: k}})
                break;
            }
        }
   }
}
