var knownFields = [
  'id',
  '_id',
  'metaObjectId',
  'objectKey',
  'collectionId',
  'teamId',
  'isPendingThumbnail',
  'parentList',
  'Tags',
  'directory',
  'frameCount',
  'frameRate',
  'resolution',
  'videoCount',
  'imageCount',
  'frameCollectionCount',
  'fileSize',
  'videoLength',
  'objectType',
  'dataCrawlId',
  'datasetGroupId',
  'name',
  'createdAt',
  'updatedAt',
  'url',
  'thumbnailUrl',
  'thumbnailKey',
  'urlExpiredAt',
  'annotationProjectList',
  'curationProjectList',
  'taskIdList',
  'vCollectionIdList',
  'datasetVersionList',
  'frameVerificationStatus',
  'verificationStatusCount',
  'isLeaf',
  'statPending',
  'annotationStatPending',
  'datasetStatPending',
  'frameAnalyticsCalcAt',
  'hierarchyLevel',
  'isMediaProcessingPending',
  'frameCollectionId',
  'labelList',
  'isFromVideo',
  'videoFrameIndex',
  'frameId',
  'mediaProcessError',
  'isError',
  'uploadInProgress',
  'uploadingUserId',
  'fileUploadId',
  'analytics',
  'isDerivedFile',
  'isOriginalUploadFile',
  'isMetaPropagationPending',
  'sourceVideoId',
  'isVerificationStatusPending',
  'isLogical',
  'customMeta',
  'isAccessible',
  'statPendingAt'
]

var count = 0
var LIMIT = 10000

while (true) {

  var metaArr = db.getCollection('MetaData').aggregate(
    [
      { $match: { customMeta: { $exists: false } } },
      { $limit: LIMIT }
    ],
    {
      allowDiskUse: true
    }
  ).toArray()

  metaArr.forEach(metaObj => {

    print("custom meta bundling for id: " + metaObj._id)
    print(++count)


    var setCustomMeta = {}
    var unsetCustomMeta = {}
    for (var key in metaObj) {

      if (!knownFields.includes(key)) {
        setCustomMeta[key] = metaObj[key]
        unsetCustomMeta[key] = ''
      }
    }

    db.getCollection("MetaData").updateOne(
      { _id: metaObj._id },
      {
        $set: {
          customMeta: setCustomMeta
        },

        $unset: unsetCustomMeta
      }
    )

  });

  if (!metaArr || (metaArr.length != LIMIT)) {
    break
  }

}
