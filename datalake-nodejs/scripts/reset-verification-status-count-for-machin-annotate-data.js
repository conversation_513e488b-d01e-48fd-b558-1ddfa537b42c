var LIMIT = 1000
var count = 0

while (true) {

  var metaUpdatesArray = db.getCollection('MetaDataUpdate').aggregate([
    {
      $match: {
        "annotationObjects.0": { $exists: true },
        operationType: 1,
        operationMode: 2
      }
    },
    { $sort: { objectKey: 1 } },
    { $skip: count * LIMIT },
    { $limit: LIMIT },
    {
      $group: {
        _id: null,
        objectKey: { $addToSet: "$objectKey" },
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  if (metaUpdatesArray[0]) {

    var objectKeyArray = metaUpdatesArray[0].objectKey;

    db.getCollection('MetaData').updateMany(
      {
        objectKey: { $in: objectKeyArray }
      },
      {
        $set: {
          "verificationStatusCount.machineAnnotated": 1,
          annotationStatPending: true
        }
      })
  }

  count += 1;
  print(count)

  if (metaUpdatesArray[0]) {
    if (metaUpdatesArray[0].count < LIMIT) break
  } else break

}
