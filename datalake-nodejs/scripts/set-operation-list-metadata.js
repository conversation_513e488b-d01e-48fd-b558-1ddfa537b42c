var count = 0
var LIMIT = 10000

while (true) {

  var metaArr = db.getCollection('MetaData').aggregate(
    [
      { $match: { objectType: 2, operationList: { $exists: false } } },
      { $limit: LIMIT },
      { $project: { _id: 1, objectKey: 1 } }
    ],
    {
      allowDiskUse: true
    }
  ).toArray()

  metaArr.forEach(metaObj => {

    print("setting operationList for id: " + metaObj._id)
    print(++count)


    var operationArr = db.getCollection("MetaDataUpdate").aggregate(
      [
        {
          $match: {
            objectKey: metaObj.objectKey,
            operationType: 1
          }
        },
        {
          $unwind: { path: '$annotationObjects' }
        },
        {
          $group: {
            _id: { label: '$annotationObjects.label.label', operationId: '$operationId' },
            count: { $sum: 1 },
            operationMode: { $first: '$operationMode' },
            operationName: { $first: '$operationName' }
          }
        },
        {
          $group: {
            _id: '$_id.operationId',
            labelList: {
              $push: {
                label: '$_id.label',
                count: '$count'
              }
            },
            operationMode: { $first: '$operationMode' },
            operationName: { $first: '$operationName' }
          }
        },
        {
          $project: {
            operationId: '$_id',
            operationMode: '$operationMode',
            operationName: '$operationName',
            labelList: '$labelList',
            _id: 0
          }
        }
      ]
    ).toArray()

    var operationList = []
    if (operationArr && operationArr.length > 0) {
      operationList = operationArr
    }

    db.getCollection("MetaData").updateOne(
      { _id: metaObj._id },
      {
        $set: {
          operationList: operationList
        }
      }
    )

  });

  if (!metaArr || (metaArr.length != LIMIT)) {
    break
  }

}

