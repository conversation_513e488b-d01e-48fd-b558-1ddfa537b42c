var lblObjArray = db.getCollection('SystemLabel').aggregate([
  {
    $match: {
      uniqueLabelText: { $exists: false }
    }
  },
  {
    $project: {
      _id: 1,
      labelText: 1
    }
  }
]).toArray();

print(lblObjArray)

if (lblObjArray) {
  lblObjArray.forEach(lblObj => {
    var uniqueName = lblObj.labelText.toLowerCase().trim().replace(/[^0-9A-Z]+/gi, "");

    var result = db.getCollection('SystemLabel').updateOne({ _id: lblObj["_id"] }, { $set: { "uniqueLabelText": uniqueName } })

    print(result)
  });
}
