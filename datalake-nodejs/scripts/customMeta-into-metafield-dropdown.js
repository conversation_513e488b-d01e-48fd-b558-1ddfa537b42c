var keyArray = db.getCollection('Test').aggregate([
  { $project: { customMeta: { $objectToArray: "$customMeta" } } },
  { $unwind: "$customMeta" },
  { $group: { _id: null, keys: { $addToSet: "$customMeta.k" } } }
]).toArray()[0].keys;

print(keyArray)

var insertObjectArray = [];

if (keyArray) {
  keyArray.forEach(key => {

    var createObject = {
      fieldName: "",
      fieldType: NumberInt(2),
      options: [],
      teamId: "xxxxx",
      uniqueName: "",
      lastModifiedAt: new Date(),
      modifiedBy: "LayerxAdmin",
    }

    createObject.fieldName = key;
    createObject.uniqueName = createObject.fieldName.toLowerCase().trim().replace(/[^0-9A-Z]+/gi, "");

    var valuesArray = db.getCollection('Test').distinct(`customMeta.${key}`);
    valuesArray.forEach(value => {
      createObject.options.push(
        {
          valueId: UUID().toString().substr(6, 36),
          value: value
        }
      )
    })
    insertObjectArray.push(createObject)
  })
}

// print(insertObjectArray)

db.getCollection('MetaField').insertMany(insertObjectArray)

