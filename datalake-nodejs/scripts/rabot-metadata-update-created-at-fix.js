db.MetaDataUpdate.updateMany(
  {},
  [
    {
      $set: {
        annotationObjects: {
          $map: {
            input: "$annotationObjects",
            as: "ao",
            in: {
              $mergeObjects: [
                "$$ao",
                {
                  createdAt: {
                    $cond: {
                      if: { $lt: ["$$ao.createdAt", "$createdAt"] },
                      then: "$createdAt",
                      else: "$$ao.createdAt"
                    }
                  }
                }
              ]
            }
          }
        }
      }
    }
  ]
)
