var collections = db.getCollection('MetaData').find({objectType: {$in: [4, 5, 7]}, isLogical: {$ne: true}}).toArray()

for(var collection of collections){
    print(collection)
    var customMeta = {}
    if(collection.customMeta) customMeta = collection.customMeta
    for(var key in customMeta){
        if(!Array.isArray(customMeta[key])){
            var customValues = customMeta[key].split(",")
            //customMeta[key] = customField
            var customFieldName = "customMeta."+key
            db.getCollection('MetaData').updateOne({_id: collection._id}, {$set: {[customFieldName]: customValues}})
        }
    }

}
