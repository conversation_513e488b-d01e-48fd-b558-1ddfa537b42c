for(var i=0; i<40; i++){

var collectionObject = db.getCollection('MetaData').findOne(
    {
        isFromVideo:true, 
        objectType:5,
        })

var collectionId = collectionObject._id

print(collectionId.toString())

db.getCollection('MetaData').remove({collectionId:collectionId})

db.getCollection('MetaData').updateOne({frameCollectionId:collectionId}, {$set:{imageCount:0, statPending:true}})

db.getCollection('MetaData').updateOne({frameCollectionId:collectionId}, {$unset:{frameCollectionId:""}})

db.getCollection('MetaData').remove({_id:collectionId})

}