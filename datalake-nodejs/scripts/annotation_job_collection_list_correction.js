var jobs = db.getCollection('Job').find({jobType: 2}).toArray()

for(var job of jobs){
    
     var tryKeys = job.jobSpecificDetails.annotationUploadTryObjectKeys;
     for(var objectKey of job.jobSpecificDetails.annotationUploadFailedObjectKeys){
         if(!tryKeys.includes(objectKey)){
             tryKeys.push(objectKey)
             }
     }
     
     var annotationUploadCollectionList = [];
     var annotationUploadCollectionListString = [];
     for(var objectKey of tryKeys){
         var metaData = db.getCollection('MetaData').find({objectKey: objectKey}).toArray()
         if(metaData && Array.isArray(metaData) && metaData.length > 0){
             if(!annotationUploadCollectionListString.includes(metaData[0].collectionId.toString())){
                 
                annotationUploadCollectionList.push(metaData[0].collectionId)
                 annotationUploadCollectionListString.push(metaData[0].collectionId.toString())
             }
         }
     }
     //print(annotationUploadCollectionList)
     
     db.getCollection('Job').updateOne({_id: job._id}, {$set: {
         "jobSpecificDetails.annotationUploadTryObjectKeys": tryKeys,
         "jobSpecificDetails.annotationUploadCollectionList": annotationUploadCollectionList.length > 0 ? annotationUploadCollectionList : job.jobSpecificDetails.annotationUploadCollectionList
     }})
     
     
}