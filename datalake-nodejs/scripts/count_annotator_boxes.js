db.getCollection('MetaDataUpdate').aggregate([{$match: {operationId:ObjectId("64819d5beb5a7147df5c737d")} }, 
{$project: {userBoxes: {$filter: { input: "$annotationObjects", as: "box", cond: {$and: [
    {$eq: ["$$box.annotatorId", ObjectId("640f009d6dd1bc1a744ca9e2")] }, {$gt: ["$$box.createdAt", new Date('2023-06-01') ]} ] }
    } } }},
{$project: {boxCount: {"$size": "$userBoxes" } } },
{$group: {_id: null, count: {$sum: "$boxCount" } } }])