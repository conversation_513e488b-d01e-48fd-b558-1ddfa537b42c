var list = db.getCollection('MetaData').aggregate([
  {
    "$match": {
      "objectType": 2
    }
  },{
    "$unwind": "$datasetVersionList"
  },
  {
    "$group": {
      "_id": "$datasetVersionList.datasetMetaId",
      "size": {
        "$sum": "$fileSize"
      }
    }
  },
  {$match: {_id: {$ne: null}}}
]).toArray()
for(var obj of list){
    db.getCollection('MetaData').updateOne({_id: obj._id}, {$set: {fileSize: obj.size}})
}