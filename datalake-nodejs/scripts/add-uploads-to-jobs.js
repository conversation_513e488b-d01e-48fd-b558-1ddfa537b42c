var uploads = db.getCollection('FileUploadProgress').aggregate([
{ $lookup: { from: 'MetaData', foreignField: '_id', localField: 'collectionId', as: 'collection' } },{$unwind: "$collection"}
]).toArray();
for(var job of uploads){

    var contentType = 1;
    if(job.collectionObjectType == 5){
        contentType = 2;
    }
    var name = 'unknown file upload'
    if(job.collection){
        name = job.collection.name
    }
    var status = 1
    var progress = 0
    if(job.uploadedFileList.length == job.allFileList.length){
         progress = 100;
         status = 2
    }else{
         progress = (job.uploadedFileList.length/job.allFileList.length) * 100
    }
    var uploadedFileList = []
    if(job.uploadedFileList){
        for(var fileName of job.uploadedFileList){
            var fileNameList = fileName.split('/')
            var fileNameListName = fileNameList[fileNameList.length -1]
            uploadedFileList.push(fileNameListName)
        }
    }

    var jobDetails = {
        "jobName" : name,
        "teamId" : job.teamId,
        "jobType" : 1,
        "createdAt" : job.startedAt,
        "updatedAt" : job.updatedAt,
        "progress" : progress,
        "status" : status,
        "jobSpecificDetails" : {
            "uploadId" : job._id,
            "allFileList" : job.allFileList,
            "uploadedFileList" : uploadedFileList,
            "startedAt" : job.startedAt,
            "contentType" : contentType,
            "finishedAt" : job.uploadFinishedAt
        }
    }
    print(jobDetails);
    db.getCollection('Job').insertOne(jobDetails);
}
