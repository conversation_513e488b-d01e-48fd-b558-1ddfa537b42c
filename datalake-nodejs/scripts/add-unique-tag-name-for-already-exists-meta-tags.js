var tagsObjArray = db.getCollection('MetaTag').aggregate([
  {
    $match: {
      uniqueTagName: { $exists: false }
    }
  },
  {
    $project: {
      _id: 1,
      tag: 1
    }
  }
]).toArray();

print(tagsObjArray)
var count = 0;

if (tagsObjArray) {
  tagsObjArray.forEach(tagObj => {
    var uniqueTagName = tagObj.tag.toLowerCase().trim().replace(/[^0-9A-Z]+/gi, "");

    var res = db.getCollection('MetaTag').updateOne({ _id: tagObj["_id"] }, { $set: { "uniqueTagName": uniqueTagName } })

    // print(res)

    if (res.modifiedCount > 0) {
      count = count + res.modifiedCount
    }
  });
}

print(count)
