var lgList = db.getCollection('LabelGroup').aggregate([
  {
    $match: { uniqueName: { $exists: false } }
  },
  {
    $project: {
      _id: 1,
      groupName: 1
    }
  }
]).toArray();

if (lgList.length > 0) {

  lgList.forEach(labelGroup => {
    var uniqueName = labelGroup.groupName.toLowerCase().trim().replace(/[^0-9A-Z]+/gi, "");

    var result = db.getCollection('LabelGroup').updateOne({ _id: labelGroup["_id"] }, { $set: { "uniqueName": uniqueName } })

    print(result)
  });
}
