var systemData = db.getCollection('SystemData').find({}).toArray()[0]
var allTags = systemData.allTags
var teamId = systemData.teamId
// print(allTags)
var tagObjList = []
for(var tag of allTags){
        //var metaData = db.getCollection('MetaData').find({Tags: tag}).sort({"objectType":1}).limit(1).toArray()
        //print(metaData)
    var tagObj = {
        tag: tag,
        teamId: teamId,
        lastModifiedAt: new ISODate(),
        modifiedBy: "LayerX Admin"
    }
    var tag = db.MetaTag.find({tag: tag}).toArray()
    if(tag.length == 0) tagObjList.push(tagObj)

}
print(tagObjList)
db.MetaTag.insertMany(tagObjList);
