var width = 1280;
var height = 720;
var project_id = "641bd6a8a429c272a184685c"
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.0.0": {$gt: width}}}}, 
    {$set:{"annotationObjects.$[elem].points.0.0": width } },
    {arrayFilters: [{"elem.points.0.0": {$gt: width} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.0.0": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.0.0": 0 } },
    {arrayFilters: [{"elem.points.0.0": {$lt: 0} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.0.1": {$gt: height}}}}, 
    {$set:{"annotationObjects.$[elem].points.0.1": height } },
    {arrayFilters: [{"elem.points.0.1": {$gt: height} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.0.1": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.0.1": 0 } },
    {arrayFilters: [{"elem.points.0.1": {$lt: 0} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.1.0": {$gt: width}}}}, 
    {$set:{"annotationObjects.$[elem].points.1.0": width } },
    {arrayFilters: [{"elem.points.1.0": {$gt: width} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.1.0": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.1.0": 0 } },
    {arrayFilters: [{"elem.points.1.0": {$lt: 0} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.1.1": {$gt: height}}}}, 
    {$set:{"annotationObjects.$[elem].points.1.1": height } },
    {arrayFilters: [{"elem.points.1.1": {$gt: height} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.1.1": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.1.1": 0 } },
    {arrayFilters: [{"elem.points.1.1": {$lt: 0} } ] }
    )

db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.2.0": {$gt: width}}}}, 
    {$set:{"annotationObjects.$[elem].points.2.0": width } },
    {arrayFilters: [{"elem.points.2.0": {$gt: width} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.2.0": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.2.0": 0 } },
    {arrayFilters: [{"elem.points.2.0": {$lt: 0} } ] }
    )
    
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.2.1": {$gt: height}}}}, 
    {$set:{"annotationObjects.$[elem].points.2.1": height } },
    {arrayFilters: [{"elem.points.2.1": {$gt: height} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.2.1": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.2.1": 0 } },
    {arrayFilters: [{"elem.points.2.1": {$lt: 0} } ] }
    )
    
    
 db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.3.0": {$gt: width}}}}, 
    {$set:{"annotationObjects.$[elem].points.3.0": width } },
    {arrayFilters: [{"elem.points.3.0": {$gt: width} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.3.0": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.3.0": 0 } },
    {arrayFilters: [{"elem.points.3.0": {$lt: 0} } ] }
    )
    
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.3.1": {$gt: height}}}}, 
    {$set:{"annotationObjects.$[elem].points.3.1": height } },
    {arrayFilters: [{"elem.points.3.1": {$gt: height} } ] }
    )
db.getCollection('MetaDataUpdate').updateMany({operationId:ObjectId(project_id), 
    "annotationObjects":{$elemMatch: {"points.3.1": {$lt: 0}}}}, 
    {$set:{"annotationObjects.$[elem].points.3.1": 0 } },
    {arrayFilters: [{"elem.points.3.1": {$lt: 0} } ] }
    )