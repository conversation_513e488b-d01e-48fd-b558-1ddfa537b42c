/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in QueryGraphDetails model
 */

/**
 * @class QueryGraphDetailsRepository
 * purpose of QueryGraphDetailsRepository is to query and create QueryGraphDetails data
 * @description repository class that use for Service interface that provides strong-typed data access operation in QueryGraphDetails model
 * <AUTHOR>
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {QueryGraphDetails, QueryGraphDetailsRelations} from '../models';

export class QueryGraphDetailsRepository extends DefaultCrudRepository<
  QueryGraphDetails,
  typeof QueryGraphDetails.prototype._id,
  QueryGraphDetailsRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(QueryGraphDetails, dataSource);
  }

  /**
   * Use for query data from method of directRemove
   * @param params {any[]} parameters for directRemove the database
   * @returns filtered data from database
   */
  public async directRemove(params?: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('QueryGraphDetails').deleteMany(params);
    return response;
  }
}
