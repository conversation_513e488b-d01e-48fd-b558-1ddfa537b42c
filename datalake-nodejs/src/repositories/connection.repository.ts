/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in Connection model
 */

/**
 * @class ConnectionRepository
 * purpose of ConnectionRepository is to query and create Connection
 * @description repository class that use for Service interface that provides strong-typed data access operation in Source model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {Connection, ConnectionRelations, DataSourceConnectionType} from '../models/connection.model';

export class ConnectionRepository extends DefaultCrudRepository<
  Connection,
  typeof Connection.prototype._id,
  ConnectionRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(Connection, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('Connection').aggregate(params).get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return;
    } else if (params.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('Connection').bulkWrite(params, {
      ordered: false,
    });
    return response;
  }

  /**
   * Find one connection by connection ID
   * @param connectionId - The ID of the connection
   * @returns A promise that resolves to the connection object
   */
  public async findOneByConnectionId(connectionId: string): Promise<Connection | null> {
    const connection = await this.findOne({
      where: {connectionId: connectionId},
    });
    return connection;
  }

  /**
   * Get all connection IDs
   * @returns A promise that resolves to an array of connection IDs
   */
  public async getAllConnectionIds(): Promise<string[]> {
    const connections = await this.find();
    const connectionIds = connections
      .map(connection => connection.connectionId)
      .filter((id): id is string => id !== undefined); // Type guard to ensure `id` is a string
    return connectionIds;
  }
  /**
   * Get all connection details (ID and name) as a JSON list
   * @returns A promise that resolves to a JSON list of connection details
   */
  public async getAllConnectionDetailsAsJson(): Promise<string> {
    const connections = await this.find();
    const connectionDetails = connections
      .map(connection => ({
        connectionId: connection.connectionId,
        name: connection.sourceName,
        connectionType: connection.connectionType,
      }))
      .filter(
        detail =>
          detail.connectionId !== undefined &&
          detail.name !== undefined &&
          detail.connectionType == DataSourceConnectionType.LAYERNEXT_DATABASE,
      );
    return JSON.stringify(connectionDetails);
  }

  /**
   * Get all connection details as a object list
   * @returns A promise that resolves to a object list of connections
   */
  public async getLayerNextConnections(): Promise<string> {
    const connections = await this.find();

    const connectionDetails = connections
      .map(connection => ({
        connectionId: connection.connectionId,
        source: connection.sourceName,
        type: connection.type,
        connectionType: connection.connectionType,
        dataFlow: connection.data_flow,
        dataSourceOverview: connection.dataSourceOverview,
      }))
      .filter(detail => detail.connectionType == DataSourceConnectionType.LAYERNEXT_DATABASE);

    return JSON.stringify(connectionDetails);
  }

  /**
   * Get data source overview for a given connectionId
   * @returns A promise that resolves to a string
   */
  public async getDataSourceOverview(connectionId: string): Promise<string> {
    const response = await this.aggregate([
      {
        $match: {
          connectionId: connectionId,
          dataSourceOverview: {$exists: true},
        },
      },
      {
        $project: {
          dataSourceOverview: 1,
        },
      },
    ]);

    let dataSourceOverview = response[0]?.dataSourceOverview || '';

    return dataSourceOverview;
  }

  /**
   * Get data source name for a given connectionId
   * @returns A promise that resolves to a string
   */
  public async getDataSourceName(connectionId: string): Promise<string> {
    const response = await this.aggregate([
      {
        $match: {
          connectionId: connectionId,
          sourceName: {$exists: true},
        },
      },
      {
        $project: {
          sourceName: 1,
        },
      },
    ]);

    let dataSourceName = response[0]?.sourceName || '';

    return dataSourceName;
  }

  public async updateByConnectionId(connectionId: string, data: Partial<Connection>): Promise<boolean> {
    const collection = (this.dataSource.connector as any).collection('Connection');
    const result = await collection.updateOne({connectionId: connectionId}, {$set: data});
    return result.modifiedCount > 0;
  }
}
