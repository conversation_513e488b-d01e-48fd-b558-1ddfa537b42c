/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in RemovedMetaDataUpdate model
 */

/**
 * @class RemovedMetaDataUpdateRepository
 * purpose of RemovedMetaDataUpdateRepository is to inset, query and update RemovedMetaDataUpdate records
 * @description repository class that use for Service interface that provides strong-typed data access operation in RemovedMetaDataUpdate model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {RemovedMetaDataUpdate, RemovedMetaDataUpdateRelations} from '../models';

export class RemovedMetaDataUpdateRepository extends DefaultCrudRepository<
  RemovedMetaDataUpdate,
  typeof RemovedMetaDataUpdate.prototype.id,
  RemovedMetaDataUpdateRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(RemovedMetaDataUpdate, dataSource);
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('RemovedMetaDataUpdate')
      .aggregate(params)
      .get();
    return response;
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async insertMany(data?: any[]) {
    if (!data) data = [];
    const response = await (this.dataSource.connector as any)
      .collection('RemovedMetaDataUpdate')
      .insertMany(data);
    return response;
  }


}
