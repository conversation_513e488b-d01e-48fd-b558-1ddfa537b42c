/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in SystemLabel model
 */

/**
 * @class SystemLabelRepository
 * purpose of SystemLabelRepository is to query and create SystemLabel data
 * @description repository class that use for Service interface that provides strong-typed data access operation in SystemLabel model
 * <AUTHOR> chathushka
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {EJSON} from 'bson';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {SystemLabel, SystemLabelRelations} from '../models';

export class SystemLabelRepository extends DefaultCrudRepository<
  SystemLabel,
  typeof SystemLabel.prototype.id,
  SystemLabelRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(SystemLabel, dataSource);
  }


  /**
   * push elements to array
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns updated count
   */
  public async updateManyPushToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('SystemLabel')
      .updateMany(params, {$push: data}, {arrayFilters: arrayFilter})
    logger.debug('Label update System Label Count:', response.modifiedCount)
    return response;
  }


  /**
   * add to set elements to array
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns updated count
   */
  public async updateManyAddToSetList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('SystemLabel')
      .updateMany(params, {$addToSet: data}, {arrayFilters: arrayFilter})
    logger.debug('Label update System Label Count:', response.modifiedCount)
    return response;
  }

  /**
   * remove element form array with array filters
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyRemoveFromList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('SystemLabel')
      .updateMany(params, {$pull: data}, {arrayFilters: arrayFilter})
    logger.debug('MongoDB tasks updateMany modified count(item removed):', response.modifiedCount)
    return response;
  }


  /**
   * remove element form array with array filters
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateMany(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('SystemLabel')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter})
    //logger.debug('MongoDB tasks updateMany modified count(item removed):', response.modifiedCount)
    return response;
  }



  /**
   * aggregate meta data
   * @param {string} query aggregate query
   * @returns data
   */
  async aggregateSystemLabel(query: string): Promise<any> {

    logger.debug(`Aggregate metadata  | MetaDataRepository.aggregateMetaData | N/A | N/A `);

    let parsedQuery = EJSON.parse(query) as any[]

    // console.log(`---------------------------------------------------------`)
    // console.log(parsedQuery)
    // console.log(JSON.stringify(parsedQuery, null, 2))
    let data: any = []

    try {

      data = await this.aggregate(parsedQuery);
      //logger.debug(`Aggregate metadata | MetaDataRepository.aggregateMetaData | N/A | data: `, data);

    } catch (e) {
      logger.error(`Aggregate metadata | MetaDataRepository.aggregateMetaData | N/A | error: ${e}`, e);
    }

    return data
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('SystemLabel')
      .aggregate(params)
      .get();
    return response;
  }




}
