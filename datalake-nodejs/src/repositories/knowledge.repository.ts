/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Repository class describes the data access layer for the Knowledge model
 */

/**
 * @class KnowledgeRepository
 * @description Repository class describes the data access layer for the Knowledge model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {Knowledge, KnowledgeRelations} from '../models';

export class KnowledgeRepository extends DefaultCrudRepository<
  Knowledge,
  typeof Knowledge.prototype._id,
  KnowledgeRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(Knowledge, dataSource);
  }
}
