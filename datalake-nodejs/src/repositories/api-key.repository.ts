/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in ApiKey model
 */

/**
 * @class ApiKeyRepository
 * purpose of ApiKeyRepository is to query and create ApiKey data
 * @description repository class that use for Service interface that provides strong-typed data access operation in ApiKey model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {ApiKey, ApiKeyRelations} from '../models';

export class ApiKeyRepository extends DefaultCrudRepository<
  ApiKey,
  typeof ApiKey.prototype._id,
  ApiKeyRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(ApiKey, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('ApiKey')
      .aggregate(params)
      .get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return
    } else if (params.length == 0) {
      return
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any)
      .collection('ApiKey')
      .bulkWrite(
        params,
        {
          ordered: false
        }
      )
    return response;
  }

}
