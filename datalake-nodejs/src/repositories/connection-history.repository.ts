/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in ConnectionHistory model
 */

/**
 * @class ConnectionHistoryRepository
 * purpose of ConnectionHistoryRepository is to query and create ConnectionHistory
 * @description repository class that use for Service interface that provides strong-typed data access operation in Source model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {ConnectionHistory, ConnectionHistoryRelations} from '../models/connection-history.model';

export class ConnectionHistoryRepository extends DefaultCrudRepository<
  ConnectionHistory,
  typeof ConnectionHistory.prototype._id,
  ConnectionHistoryRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(ConnectionHistory, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('ConnectionHistory').aggregate(params).get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return;
    } else if (params.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('ConnectionHistory').bulkWrite(params, {
      ordered: false,
    });
    return response;
  }
}
