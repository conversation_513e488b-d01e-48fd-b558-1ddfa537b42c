/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in SystemLabel model
 */

/**
 * @class LabelGroupRepository
 * purpose of LabelGroupRepository is to create Label Group records in DB
 * @description repository class that use for Service interface that provides strong-typed data access operation in LabelGroup model
 * <AUTHOR>
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {LabelGroup, LabelGroupRelations} from '../models';

export class LabelGroupRepository extends DefaultCrudRepository<
  LabelGroup,
  typeof LabelGroup.prototype.id,
  LabelGroupRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(LabelGroup, dataSource);
  }

  async createGroup(groupName: string, teamId: string, uniqueName: string, userId?: string, userName?: string) {
    try {
      let groupObj = await this.create({
        groupName: groupName,
        teamId: teamId,
        uniqueName: uniqueName,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: userId,
        modifiedById: userId,
        modifiedByName: userName
      });
      return {
        isSuccess: true,
        createdObj: groupObj,
        errorMsg: '',
      };
    } catch (err) {
      logger.error(err);
      return {
        isSuccess: false,
        createdObj: null,
        errorMsg: 'System error',
      };
    }
  }



  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('LabelGroup')
      .aggregate(params)
      .get();
    return response;
  }
}
