/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in MetaData model
 */

/**
 * @class MetaDataRepository
 * purpose of MetaDataRepository is to query and create MetaData records
 * @description repository class that use for Service interface that provides strong-typed data access operation in MetaData model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository, Where, repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {EJSON} from 'bson';
import moment from 'moment';
import {ObjectId} from 'mongodb';
// import prettyBytes from 'pretty-bytes';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {
  ContentType,
  LabelList,
  MetaData,
  MetaDataRelations,
  OBJECT_STATUS,
  OperationList,
  OperationMode,
  OperationType,
  StorageMapping,
} from '../models';
import {FLOWS, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {isArrayWithLength} from '../settings/tools';
import {StorageMappingRepository} from './storage-mapping.repository';

const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

export class MetaDataRepository extends DefaultCrudRepository<
  MetaData,
  typeof MetaData.prototype.id,
  MetaDataRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
    @repository('StorageMappingRepository')
    private storageMappingRepository: StorageMappingRepository,
  ) {
    super(MetaData, dataSource);
  }

  /**
   * MongoDB direct find and modify  in MetaData
   * @param params {object} filter Object
   * @param data {object} updating data
   * @returns response
   */
  public async updateAllMetaData(params: any, data: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any).collection('MetaData').updateMany(params, data);
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateAllMetaData | response: `, response);
    return response;
  }
  /**
   * BulkWrite to mongodb
   * @param metaDataItems {string[]}
   */
  public async bulkWrite(metaDataItems: any[]) {
    if (!metaDataItems) {
      return;
      // metaDataItems = [];
    } else if (metaDataItems.length == 0) {
      return;
    }

    const response = await (this.dataSource.connector as any).collection('MetaData').bulkWrite(metaDataItems, {
      ordered: false,
    });
    return response;
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('MetaData').aggregate(params).get();
    return response;
  }

  /**
   * Use for query data from method of distinct
   * @param field The field for which to return distinct values.
   * @param query A query that specifies the documents from which to retrieve the distinct values.
   * @returns returns the distinct values in an array
   */
  public async distinct(field: string, query?: any) {
    if (!query) query = {};
    const response: any[] = await (this.dataSource.connector as any).collection('MetaData').distinct(field, query);
    return response;
  }

  /**
   * Sub method - to format date suffixed collection name
   * @param name {string[]}
   * @returns collectionName - date suffixed
   */
  async formatCollectionNameWithDate(name: string, useOnlyDate?: boolean) {
    let now = moment();

    let suffixDate = moment(new Date()).format('YYYY-MM-DD');
    let suffixTime = now.diff(suffixDate, 'milliseconds');

    let suffix = `${suffixDate}_${suffixTime}`;
    if (useOnlyDate) {
      suffix = `${suffixDate}`;
    }

    let collectionName = `${name}_${suffix}`;
    return collectionName;
  }

  /**
   * Handle Creating new collection
   * @param name {string[]}
   * @returns collectionId
   */
  async handleCreateCollection(
    name: string,
    type: ContentType,
    customMetaData?: Partial<MetaData>,
    currentUserProfile?: UserProfileDetailed,
  ) {
    logger.debug(`Create new collection | MetaDataRepository.createCollection | name:${name} | creating`);

    let collectionName = await this.formatCollectionNameWithDate(name);
    let teamId = new ObjectId(currentUserProfile?.teamId);
    let allowedUserIdListObj: {allowedUserIdList: ObjectId[]} = {allowedUserIdList: []};
    if (
      currentUserProfile?.userType == UserType.USER_TYPE_COLLABORATOR &&
      !allowedUserIdListObj.allowedUserIdList.includes(new ObjectId(currentUserProfile.id))
    ) {
      allowedUserIdListObj.allowedUserIdList.push(new ObjectId(currentUserProfile.id));
    }

    let collectionId = await this.createCollectionMeta(
      collectionName,
      type,
      teamId,
      customMetaData,
      undefined,
      allowedUserIdListObj,
    );
    // console.log(collectionId)
    return {
      collectionId: collectionId,
      collectionName: collectionName,
    };

    // console.log(suffixTime)
    // console.log(suffixDate)
  }

  /**
   * Sub method - Create new collection metaData item
   * called from handleCreateCollection
   * @param collectionName {string[]}
   * @returns collectionId
   */
  async createCollectionMeta(
    collectionName: string,
    type: ContentType,
    teamId: ObjectId,
    customMetaData?: Partial<MetaData>,
    isNotStoragePathRequired?: boolean,
    allowedUserIdListObj?: {allowedUserIdList: ObjectId[]},
  ) {
    collectionName = collectionName.trim();
    let collectionObjectType = await this.getCollectionObjectTypeByObjectType(type);

    // if collectionName length 0 after trim - return error
    if (collectionName.length == 0) {
      logger.error(
        `Create new collection | MetaDataRepository.createCollectionMeta | ${teamId} | failed to create new collection collectionName:${collectionName} type:${type} collectionObjectType:${collectionObjectType} teamId:${teamId}, collectionName length is 0`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_COLLECTION_NAME);
    }

    //check whether simillar collection exist
    let isExist = await this.findOne({
      where: {
        name: collectionName,
        objectType: collectionObjectType,
        teamId: teamId,
      },
    });
    if (isExist) {
      logger.error(
        `Create new collection | MetaDataRepository.createCollectionMeta | ${teamId} | failed to create new collection collectionName:${collectionName} type:${type} collectionObjectType:${collectionObjectType} teamId:${teamId}, similar collection exist forcreated collection: ${isExist.id}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.SIMILAR_OBJECT_EXIST);
    }

    let metaData: Partial<MetaData> = {};
    if (customMetaData) {
      metaData = customMetaData;

      if (customMetaData.verificationStatusCount) {
        metaData.verificationStatusCount = {
          raw: 0,
          machineAnnotated: 0,
          verified: 0,
        };
      }
    }

    if (!isNotStoragePathRequired) {
      let uniqueName = await this.getUniqueNameForCollection(collectionName, teamId.toString()); // ##################
      metaData.storagePath = uniqueName;
    }

    if (
      allowedUserIdListObj &&
      allowedUserIdListObj.allowedUserIdList &&
      Array.isArray(allowedUserIdListObj.allowedUserIdList) &&
      allowedUserIdListObj.allowedUserIdList.length > 0
    ) {
      metaData.allowedUserIdList = allowedUserIdListObj.allowedUserIdList;
    }

    metaData.name = collectionName;
    metaData.nameInLowerCase =
      typeof metaData?.name == 'string' && metaData.name.length != 0 ? metaData.name.toLowerCase() : '';

    metaData.objectType = collectionObjectType;
    metaData.statPending = true;
    metaData.statPendingAt = new Date();
    metaData.isLeaf = collectionObjectType == ContentType.DATASET ? true : false;
    metaData.createdAt = new Date();
    metaData.updatedAt = new Date();
    metaData.isPendingThumbnail = true;
    metaData.teamId = teamId;
    metaData.objectStatus = OBJECT_STATUS.ACTIVE;

    logger.debug(`metadata: ${JSON.stringify(metaData, null, 2)}`);

    if ([ContentType.IMAGE_COLLECTION, ContentType.DATASET].includes(collectionObjectType)) {
      metaData.isFeatureGraphPending = true;
    }

    //For other collections, don't need to generate thumbnail
    if (type == ContentType.OTHER_COLLECTION) {
      metaData.isPendingThumbnail = false;
    }

    // console.log(metaData)
    let response: Partial<MetaData> = {};
    try {
      response = await this.create(metaData);
      if (response.id && metaData.storagePath)
        await this.storageMappingRepository.updateOneSet(
          {collectionStoragePath: metaData.storagePath},
          {collectionId: new ObjectId(response.id)},
          [],
        );
    } catch (e) {
      logger.error(`Create new collection | MetaDataRepository.createCollectionMeta | N/A | failed`);
      logger.error(e);
    }
    // console.log(response)
    logger.debug(
      `Create new collection | MetaDataRepository.createCollectionMeta | name:${response.name} | created collection`,
    );

    return response.id;
  }

  async createRecordForStorageMapping(
    rootFolderName: string,
    isDefaultBucket: boolean,
    teamId: string,
    collectionId?: string,
  ) {
    // if (!isDefaultBucket) {
    //   let collectionHeadMeta = await this.findById(collectionId);
    //   if (!collectionHeadMeta) {
    //     logger.error(
    //       `Create Record For Collection Head Meta | MetaDataService.createRecordForCollectionHeadMeta | ${teamId} | Collection head meta not found for id: ${collectionId}`,
    //     );
    //     throw new HttpErrors.NotFound(DatalakeUserMessages.COLLECTION_NOT_EXIST);
    //   }
    // }

    // if rootFolderName is already exist in collectionHeadMeta, then skip it
    let isExist = await this.storageMappingRepository.findOne({
      where: {
        collectionStoragePath: rootFolderName,
      },
    });

    let storageMappingObj: Partial<StorageMapping> = {
      collectionStoragePath: rootFolderName,
      collectionId: collectionId ? new ObjectId(collectionId) : undefined,
      isDefaultBucketCrawledCollection: isDefaultBucket,
      teamId: new ObjectId(teamId),
      createdAt: new Date(),
    };

    if (!isExist) {
      let isCreated = await this.storageMappingRepository.create(storageMappingObj);
      // if mapping succesfully created
      if (isCreated){
        return true;
      }
      else{
        return false;
      }
    } else {
      // skip it
      logger.warn(
        `Create Record For Collection Head Meta | MetaDataService.createRecordForCollectionHeadMeta | ${teamId} | Collection head meta already exist for rootFolderName: ${rootFolderName}`,
      );
      return true;
    }
  }

  async updateCollectionMeta(collectionId: string, customMetaData: Partial<MetaData>) {
    let updatableFields = ['updatedAt', 'customMeta', 'Tags', 'augmentationCollectionId'];

    // iterate through customMetaData and update relevent fields
    for (const [key, value] of Object.entries(customMetaData)) {
      // check if metadata model has the key
      if (updatableFields.includes(key)) {
        // then ok
        if (key == 'Tags') {
          await this.updateById(collectionId, {
            $addToSet: {
              Tags: {
                $each: value,
              },
            },
          });
        } else if (key == 'customMeta') {
          let customMetaObj: Record<string, any> = {};
          for (const [customKey, customValue] of Object.entries(value)) {
            customMetaObj['customMeta.' + customKey] = customValue;
          }
          await this.updateById(collectionId, {
            $set: customMetaObj,
          });
        } else {
          await this.updateById(collectionId, {
            [key]: value,
          });
        }
      } else {
        // skip updating
        continue;
      }
    }
  }

  /**
   * Sub method - Create new collection metaData item or update exisiting by name
   * called in upload metadata (frontend) flow
   * @param collectionName {string[]}
   * @returns collectionId
   */
  async createOrUpdateCollectionMetaByName(
    type: ContentType,
    teamId: ObjectId,
    collectionName?: string,
    customMetaData?: {[k: string]: any},
    isNotStoragePathRequired?: boolean,
    allowedUserId?: string,
  ) {
    logger.debug(type, teamId, collectionName, isNotStoragePathRequired, allowedUserId);
    let metaDataObject: Partial<MetaData> = {};
    if (!collectionName) {
      // if no collection name given - generate collection name
      collectionName = await this.formatCollectionNameWithDate('new_file_upload', true);
    }

    //if the collection name has / removing those. This is for the local disk storage
    collectionName = collectionName.trim();
    if (collectionName.includes('/')) {
      collectionName = collectionName.replace(/\//g, '_').replace(/^_/, '');
    }
    // check if current collection name available in relavent team
    let whereParams: Where<MetaData> = {
      objectType: type,
      name: collectionName,
    };
    if (teamId) {
      whereParams.teamId = teamId;
    }

    // console.log(whereParams)
    let collectionData = await this.findOne({where: whereParams});
    // console.log(JSON.stringify(collectionData, null, 2))

    // let collectionData1 = await this.find(whereParams)
    // console.log(JSON.stringify(collectionData1, null, 2))
    if (customMetaData) {
      metaDataObject = await this.formatMetadataObject(customMetaData);
    }

    // let allowedUserIdListObj: {allowedUserIdList: ObjectId[]} = {allowedUserIdList: []};
    // let allowedUserIdList = metaDataObject.allowedUserIdList || [];
    // if (allowedUserId && !allowedUserIdList.includes(new ObjectId(allowedUserId))) {
    //   allowedUserIdList.push(new ObjectId(allowedUserId));
    // }
    // allowedUserIdListObj.allowedUserIdList = allowedUserIdList;

    // logger.debug(
    //   `metadata upload on file upload start | MetaDataRepository.createOrUpdateCollectionMetaByName | N/A | ${JSON.stringify(
    //     allowedUserIdListObj,
    //     null,
    //     2,
    //   )}`,
    // );

    if (collectionData && collectionData.id) {
      logger.debug(
        `metadata upload on file upload start | MetaDataRepository.createOrUpdateCollectionMetaByName | collection name: ${collectionData.name} | collection with name exists`,
      );
      if (metaDataObject) {
        // update collection
        await this.updateCollectionMeta(collectionData.id, {...metaDataObject /*...allowedUserIdListObj*/});
      }

      return collectionData.id;
    } else {
      let allowedUserIdListObj: {allowedUserIdList: ObjectId[]} = {allowedUserIdList: []};
      if (allowedUserId && !allowedUserIdListObj.allowedUserIdList.includes(new ObjectId(allowedUserId))) {
        allowedUserIdListObj.allowedUserIdList.push(new ObjectId(allowedUserId));
      }
      logger.debug(
        `metadata upload on file upload start | MetaDataRepository.createOrUpdateCollectionMetaByName | N/A | ${JSON.stringify(
          allowedUserIdListObj,
          null,
          2,
        )}`,
      );
      // no collection of same type with the name for the team - create a collection
      return await this.createCollectionMeta(
        collectionName,
        type,
        teamId,
        metaDataObject,
        isNotStoragePathRequired,
        allowedUserIdListObj,
      );
    }
  }

  /**
   * Sub method - Delete collection if no child files available
   * called from subsequent crawling flow
   * @param collectionId {string[]}
   */
  async deleteCollectionIfNoChildren(collectionId: string, childrenObjectType: ContentType) {
    let childCountData = await this.count({
      objectType: childrenObjectType,
      parentList: collectionId,
    });
    let childCount = childCountData.count;

    if (childCount > 0) {
      // then ok - do nothing
    } else {
      logger.info(
        `Delete collection since no files added | MetaDataRepository.deleteCollectionIfNoChildren | collectionId:${collectionId} | deleting collection`,
      );
      await this.deleteById(collectionId);
    }

    return;
  }

  /**
   * use to retrive file information of a list
   * @param idList ObjectId[]
   * @returns data : file info
   */
  async getFileDataArray(idList: string[]) {
    if (idList && Array.isArray(idList) && idList.length > 0) {
      //  then ok
    } else {
      logger.debug(`Get file info list | MetaDataRepository.getFileDataArray | invalid input (idList) | Failed`);
      return;
    }
    logger.debug(
      `Get file info list | MetaDataRepository.getFileDataArray | file list length: ${idList.length} | retrieving`,
    );

    let objectIdList = idList.map((id: string) => new ObjectId(id));

    let params = [
      {$match: {_id: {$in: objectIdList}}},
      {
        $project: {
          id: '$_id',
          name: 1,
          fileSize: 1,
          objectType: 1,
          _id: 0,
        },
      },
    ];

    let dataList = await this.aggregate(params);

    let formattedDataList = dataList.map((data: Partial<MetaData>) => {
      let fileSizeNew = data.fileSize || 10 * 1000 * 1000;
      // let formattedSize = prettyBytes(fileSize);
      data.fileSize = fileSizeNew;
      return data;
    });

    return formattedDataList;
  }

  async getAllDistinctValuesForCustomFieldInCollection(collectionId: string, customField: string) {
    // let collectionObj = await this.findById(collectionId);

    // let _matchQuery = await this.getMatchObjectsOfCollection(collectionObj);

    let distinctValues = await this.distinct('customMeta.' + customField, {
      // vCollectionIdList: new ObjectId(collectionId),
      $or: [{vCollectionIdList: new ObjectId(collectionId)}, {_id: new ObjectId(collectionId)}],
      //replace flag with enum
      objectStatus: OBJECT_STATUS.ACTIVE,
      // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
    });

    return distinctValues;
  }

  /**
   * Method to get metData list of a task with metDataUpdates
   * @param taskId - filter metadata by taskId
   * @returns metaData list
   */
  async getTaskMetaData(taskId: string, projectId: string, modelRunIds: string[]) {
    let params = [
      {$match: {taskIdList: new ObjectId(taskId)}},
      {$sort: {sourceVideoId: 1, videoFrameIndex: 1, _id: 1}},
      {
        $lookup: {
          from: 'MetaDataUpdate',
          let: {objectKey: '$objectKey'},
          pipeline: [
            {
              $match: {
                $expr: {$eq: ['$$objectKey', '$objectKey']},
              },
            },
            // {$match: {taskId: new ObjectId(taskId)}},
            {
              $addFields: {
                operationIdString: {$toString: '$operationId'},
              },
            },
            {
              $match: {
                $or: [
                  {
                    operationIdString: projectId,
                    operationType: OperationType.ANNOTATION,
                    operationMode: OperationMode.HUMAN,
                  },
                  {
                    operationIdString: {$in: modelRunIds},
                  },
                ],
              },
            },
          ],
          as: 'metaDataUpdates',
        },
      },
    ];

    let frames = await this.aggregate(params);

    return frames;
  }

  /**
   * remove element form array with array filters
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyRemoveFromList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    const response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$pull: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyRemoveFromList | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async metaDataUpdateMany(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, data, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManySet | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManySet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManySet | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - findOne
   * @param matchField {object} filter Object
   * @returns response
   */
  public async findOneDirectDB(matchField: any) {
    if (!matchField) return;
    return await (this.dataSource.connector as any).collection('MetaData').findOne(matchField);
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyRename(params: any, data: any, arrayFilter: any) {
    if (!params || !data) {
      console.log('params or data is empty');
      return;
    }
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$rename: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyRename | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateOneSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateOne(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateOneSet | response: `, response);
    return response;
  }

  /**
   * Unset - metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyUnSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$unset: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyUnSet | response: `, response);
    return response;
  }

  /**
   * pullAll - metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyPullAll(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$pullAll: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyPullAll | response: `, response);
    return response;
  }

  /**
   * Unset - metaData using external mongodb connector - updateOne
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateOneUnSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateOne(params, {$unset: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateOneUnSet | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany addToSet
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyAddToSetToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$addToSet: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyAddToSetToList | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany push
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyPushToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaData')
      .updateMany(params, {$push: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaDataRepository.updateManyPushToList | response: `, response);
    return response;
  }

  /**
   * Update annotationStatPending and statPending flags to true
   * @param params {object} filter Object
   * @returns response
   */
  public async markStatPendingFlagsTrue(params: any) {
    if (!params) return;
    let updates = {
      isVerificationStatusPending: true, // flag to trigger recalculate verification status
      statPending: true,
      statPendingAt: new Date(),
      annotationStatPending: true,
    };

    let response = await this.updateManySet(params, updates, []);

    return response;
  }

  // /**
  //  * Use when deleting project
  //  * Update projectList in metaData
  //  * @param projectId string
  //  * @returns response
  //  */
  // public async removeProjectFromMetaData(projectId: string) {
  //   let projectOid = new ObjectId(projectId)
  //   let params = {"annotationProjectList.id": projectOid}
  //   let pullParams = {annotationProjectList: {id: projectOid}}
  //   const response = await (this.dataSource.connector as any)
  //     .collection('MetaData')
  //     .updateMany(params, {$pull: pullParams})
  //   logger.debug(`Remove project from metaData | MetaDataRepository.removeProjectFromMetaData | projectId: ${projectId} | removing`)
  //   return response;
  // }

  // /**
  //  * Use when deleting task
  //  * Update taskIdList in metaData
  //  * @param taskId string
  //  * @returns response
  //  */
  // public async removeTaskFromMetaData(taskId: string) {
  //   let taskOid = new ObjectId(taskId)
  //   let params = {taskIdList: taskOid}
  //   // let pullParams = {annotationProjectList:{id:taskOid}}
  //   const response = await (this.dataSource.connector as any)
  //     .collection('MetaData')
  //     .updateMany(params, {$pull: params})
  //   logger.debug(`Remove task from metaData | MetaDataRepository.removeTaskFromMetaData | taskId: ${taskId} | removing`)
  //   return response;
  // }

  /**
   * filter custom fields from metaObject
   * @param metaObject : MetaData or MetaData.customMeta
   * return formatted fields in array
   */
  filterCustomFieldsFromMetaData(metaObject?: Partial<MetaData>) {
    let customFieldList: {key: string; value: any}[] = [];

    if (!metaObject) {
      return customFieldList;
    }

    for (const [key, value] of Object.entries(metaObject)) {
      if (value == null || value == undefined || value == '') {
        continue;
      }

      // check if metadata model has the key
      if (!MetaData.definition.properties.hasOwnProperty(key)) {
        // then it is a custom field

        // create a object from key,value pair
        let tempObj = {
          key: key,
          value: value,
        };
        customFieldList.push(tempObj);
      }
    }
    return customFieldList;
  }

  /**
   * filter custom fields from metaObject
   * @param metaObject : MetaData or MetaData.customMeta
   * return custom metadata object
   */
  filterAndGetCustomMetaTagObject(metaObject: Partial<MetaData>, isAddAsArray?: boolean, isAppendEach?: boolean) {
    let customMetaObj: Record<string, any> = {};

    for (const [key, value] of Object.entries(metaObject)) {
      // check if metadata model has the key
      if (key == 'Tags') {
        // then it is a custom field
        let tempVal: any;
        if (isAddAsArray && key == 'Tags') {
          if (Array.isArray(value)) {
            if (value.length == 0) {
              continue;
            }
            tempVal = value;
          } else {
            tempVal = [value];
          }
        } else {
          tempVal = value;
        }

        if (isAppendEach && tempVal && Array.isArray(tempVal) && tempVal.length > 0) {
          tempVal = {$each: tempVal};
        }

        // create a object from key,value pair
        if (key == 'Tags') {
          customMetaObj[key] = tempVal;
        }
      }
    }
    return customMetaObj;
  }

  /**
   * filter custom fields from metaObject
   * @param metaObject : MetaData or MetaData.customMeta
   * return custom metadata object
   */
  filterAndGetCustomMetaObject(metaObject: Partial<MetaData>) {
    let customMetaObj: Record<string, any> = {};

    for (const [key, value] of Object.entries(metaObject)) {
      // check if metadata model has the key
      if (!MetaData.definition.properties.hasOwnProperty(key)) {
        // then it is a custom field
        let tempVal: any;

        tempVal = value;

        // create a object from key,value pair

        customMetaObj['customMeta.' + key] = tempVal;
      }
    }
    return customMetaObj;
  }

  async formatMetadataObject(metaObject: Partial<MetaData>) {
    let formattedObject: Partial<MetaData> = {};

    for (const [key, value] of Object.entries(metaObject)) {
      // check if metadata model has the key
      if (MetaData.definition.properties.hasOwnProperty(key)) {
        // then it is a custom field
        formattedObject[key] = value;
      } else {
        if (key == 'Tags') {
          if (Array.isArray(value)) {
            if (value.length == 0) {
              continue;
            }
            formattedObject[key] = value;
          } else {
            formattedObject[key] = [value];
          }
        } else {
          if (!formattedObject.customMeta) {
            formattedObject.customMeta = {};
          }
          formattedObject.customMeta[key] = value;
        }
      }
    }
    return formattedObject;
  }

  /**
   * Use to get root matching criteria for a objects of a collection
   */
  // async getMatchObjectsOfCollection(collectionObj: Partial<MetaData>, lookupField?: string) {
  //   // generate match filter for collection and virtual collection
  //   if (
  //     collectionObj.objectType == ContentType.IMAGE_COLLECTION ||
  //     collectionObj.objectType == ContentType.VIDEO_COLLECTION ||
  //     collectionObj.objectType == ContentType.OTHER_COLLECTION
  //   ) {
  //     if (lookupField) {
  //       let tempVCollectionId = `${lookupField}.vCollectionIdList`;
  //       return {[tempVCollectionId]: new ObjectId(collectionObj.id)};
  //     } else {
  //       return {vCollectionIdList: new ObjectId(collectionObj.id)};
  //     }
  //   } else if (collectionObj.objectType == ContentType.DATASET) {
  //     if (lookupField) {
  //       let tempDatasetMetaId = `${lookupField}.datasetVersionList.datasetMetaId`;
  //       let tempObjectType = `${lookupField}.objectType`;
  //       return {
  //         [tempObjectType]: ContentType.IMAGE,
  //         [tempDatasetMetaId]: new ObjectId(collectionObj.id),
  //       };
  //     } else {
  //       return {
  //         objectType: ContentType.IMAGE,
  //         'datasetVersionList.datasetMetaId': new ObjectId(collectionObj.id),
  //       };
  //     }
  //   } else {
  //     logger.error(
  //       `${FLOWS.DATALAKE_COLLECTION} | MetaDataRepository.matchObjectsOfCollection | ${collectionObj.teamId} | invalid object type for a collection, collectionId: ${collectionObj.id}`,
  //       collectionObj,
  //     );
  //     throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
  //   }
  // }

  /**
   * Use to get root matching criteria for leaf objects that associated with given project
   */
  async getMatchObjectsOfProjectFilter(projectId: string) {
    //Get frames since its leaf level
    let matchFilter = {
      annotationProjectList: {$elemMatch: {id: new ObjectId(projectId)}},
      objectType: ContentType.IMAGE,
    };
    //Get first object that matches this to identify project
    let firstMetaObj: MetaData = await (this.dataSource.connector as any).collection('MetaData').findOne(matchFilter);

    if (firstMetaObj) {
      let projName: string | null = null;
      if (firstMetaObj.annotationProjectList) {
        firstMetaObj.annotationProjectList.forEach(proj => {
          if (proj.id == projectId) {
            projName = proj.name;
          }
        });
      }
      if (projName) {
        return {
          match: matchFilter,
          projectName: projName,
        };
      } else {
        logger.warn(
          `${FLOWS.API_CLIENT_REQUEST} | MetaDataRepository.getMatchObjectsOfProjectFilter | ${projectId} | Failed to find objects matching project`,
        );
        return null;
      }
    } else {
      logger.warn(
        `${FLOWS.API_CLIENT_REQUEST} | MetaDataRepository.getMatchObjectsOfProjectFilter | ${projectId} | Failed to find objects matching project`,
      );
      return null;
    }
  }

  /**
   * aggregate meta data
   * @param {string} query aggregate query
   * @returns data
   */
  async aggregateMetaData(query: string): Promise<any> {
    logger.debug(`Aggregate metadata  | MetaDataRepository.aggregateMetaData | N/A | N/A `);

    let parsedQuery = EJSON.parse(query) as any[];

    // console.log(`---------------------------------------------------------`)
    // console.log(parsedQuery)
    // console.log(JSON.stringify(parsedQuery, null, 2))
    let data: any = [];

    try {
      data = await this.aggregate(parsedQuery);
      //logger.debug(`Aggregate metadata | MetaDataRepository.aggregateMetaData | N/A | data: `, data);
    } catch (e) {
      logger.error(`Aggregate metadata | MetaDataRepository.aggregateMetaData | N/A | error: ${e}`, e);
    }

    return data;
  }
  /**
   * Use to convert ContentType into ContentType
   * @param contentType ContentType
   * @returns ContentType
   */
  async getObjectType(isAllSelected: boolean, contentType?: ContentType, collectionId?: string, idList?: string[]) {
    // if isAll selected
    if (isAllSelected) {
      if (contentType == ContentType.ALL) {
        // if collection exist, return child object type
        if (collectionId) {
          let collectionObj = await this.findById(collectionId);
          if (
            collectionObj.objectType == ContentType.IMAGE_COLLECTION ||
            collectionObj.objectType == ContentType.DATASET
          ) {
            return ContentType.IMAGE;
          } else if (collectionObj.objectType == ContentType.VIDEO_COLLECTION) {
            return ContentType.VIDEO;
          } else if (collectionObj.objectType == ContentType.OTHER_COLLECTION) {
            return ContentType.OTHER;
          } else {
            return ContentType.UNSUPPORTED;
          }
        } else {
          return ContentType.UNSUPPORTED;
        }
      } else {
        if (contentType) {
          if (contentType == ContentType.VIDEO) {
            return ContentType.VIDEO;
          } else if (contentType == ContentType.IMAGE) {
            return ContentType.IMAGE;
          } else if (contentType == ContentType.OTHER) {
            return ContentType.OTHER;
          } else if (contentType == ContentType.DATASET) {
            return ContentType.DATASET;
          } else if (contentType == ContentType.VIDEO_COLLECTION) {
            return ContentType.VIDEO_COLLECTION;
          } else if (contentType == ContentType.IMAGE_COLLECTION) {
            return ContentType.IMAGE_COLLECTION;
          } else if (contentType == ContentType.OTHER_COLLECTION) {
            return ContentType.OTHER_COLLECTION;
          } else {
            return ContentType.UNSUPPORTED;
          }
        } else {
          return ContentType.UNSUPPORTED;
        }
      }
    } else {
      if (idList) {
        if (idList.length > 0) {
          let distinctObjectTypes: ContentType[] = await this.distinct('objectType', {
            _id: {$in: idList.map(elem => new ObjectId(elem))},
          });
          if (distinctObjectTypes.length == 1) {
            return distinctObjectTypes[0];
          } else {
            return ContentType.UNSUPPORTED;
          }
        } else {
          return ContentType.UNSUPPORTED;
        }
      } else {
        return ContentType.UNSUPPORTED;
      }
    }
  }

  /**
   * Use to get child object type of a parent object type
   */
  async getChildObjectTypeByObjectType(objectType: ContentType) {
    if (objectType == ContentType.IMAGE || objectType == ContentType.VIDEO || objectType == ContentType.OTHER) {
      return objectType;
    } else if (objectType == ContentType.IMAGE_COLLECTION || objectType == ContentType.DATASET) {
      return ContentType.IMAGE;
    } else if (objectType == ContentType.VIDEO_COLLECTION) {
      return ContentType.VIDEO;
    } else if (objectType == ContentType.OTHER_COLLECTION) {
      return ContentType.OTHER;
    } else {
      return ContentType.UNSUPPORTED;
    }
  }

  /**
   * Use to get parent object type/ collection type of a child object type
   */
  async getCollectionObjectTypeByObjectType(objectType: ContentType) {
    if (
      objectType == ContentType.IMAGE_COLLECTION ||
      objectType == ContentType.VIDEO_COLLECTION ||
      objectType == ContentType.OTHER_COLLECTION ||
      objectType == ContentType.DATASET
    ) {
      return objectType;
    } else if (objectType == ContentType.IMAGE) {
      return ContentType.IMAGE_COLLECTION;
    } else if (objectType == ContentType.VIDEO) {
      return ContentType.VIDEO_COLLECTION;
    } else if (objectType == ContentType.OTHER) {
      return ContentType.OTHER_COLLECTION;
    } else {
      return ContentType.UNSUPPORTED;
    }
  }

  /**
   * Use to get LabelList from OperationList
   * @param operationList OperationList}]
   */
  async getLabelListFromOperationList(operationList: OperationList[]) {
    let labelCountMap: {[k: string]: number} = {};

    for (let operation of operationList) {
      if (!Array.isArray(operation.labelList)) {
        continue;
      }

      for (let labelObj of operation.labelList) {
        if (labelCountMap[labelObj.label]) {
          labelCountMap[labelObj.label] = labelCountMap[labelObj.label] + labelObj.count;
        } else {
          labelCountMap[labelObj.label] = labelObj.count;
        }
      }
    }

    let labelList: LabelList[] = Object.keys(labelCountMap).map(key => {
      return {label: key, count: labelCountMap[key]};
    });

    return labelList;
  }

  /**
   * validate object for trashing
   * validate -> if object is frame or frameCollection them check whether it is used in any annotation, curation, dataset project - if used could not be trashed
   * @param objectId id related to trash object
   * @returns {isSuccess : true or false}
   */
  validateObjectToTrash(metaObject: Partial<MetaData>, collectionObj?: Partial<MetaData>) {
    //images and image collections are allowed to trash if they are not used in any annotation, curation, dataset project
    if (metaObject.objectType && [ContentType.IMAGE, ContentType.IMAGE_COLLECTION].includes(metaObject.objectType)) {
      if (
        isArrayWithLength(metaObject.annotationProjectList) ||
        isArrayWithLength(metaObject.curationProjectList) ||
        isArrayWithLength(metaObject.datasetVersionList)
      ) {
        return {
          isSuccess: false,
          reasons: {
            annotationProjectList: metaObject.annotationProjectList,
            curationProjectList: metaObject.curationProjectList,
            datasetVersionList: metaObject.datasetVersionList,
          },
        };
      } else {
        return {isSuccess: true};
      }
    } else if (metaObject.objectType === ContentType.DATASET) {
      return {
        isSuccess: false,
        reasons: {
          type: ContentType.DATASET,
        },
      };
    } else {
      return {isSuccess: true};
    }
  }

  /**
   * Use to get unique name for the collection head
   * @param collectionName given collection name
   * @param teamId team id
   * @returns unique name for the collection
   */
  async getUniqueNameForCollection(collectionName: string, teamId: string) {
    let uniqueName = '';
    let isExist = false;
    let count = 0;

    // only can contain letters, numbers, hyphens, spaces and underscores. other characters will be replaced with underscore.
    let filteredCollectionName = collectionName.replace(/%20/g, ' ').replace(/[^a-zA-Z0-9\-_ ]/g, '_');

    do {
      uniqueName = `${filteredCollectionName}${count ? `_${count}` : ''}`;
      let isExistObj = await this.storageMappingRepository.findOne({
        where: {
          collectionStoragePath: uniqueName, //################
        },
      });

      if (isExistObj) {
        isExist = true;
        count += 1;
      } else {
        try {
          await this.createRecordForStorageMapping(uniqueName, false, teamId, '');
          isExist = false;
        } catch (e) {
          logger.error(`Error while creating storage mapping record for collection: ${uniqueName} | error: ${e}`, e);
          isExist = true;
        }
      }
    } while (isExist);

    return uniqueName;
  }

  /**
   * BulkWrite to mongodb
   * @param metaDataItems {string[]}
   */
  public async bulkWriteSimilar(metaDataItems: any[]) {
    if (!metaDataItems) {
      return;
      // metaDataItems = [];
    } else if (metaDataItems.length == 0) {
      return;
    }

    const response = await (this.dataSource.connector as any).collection('SimilarImages').bulkWrite(metaDataItems, {
      ordered: false,
    });
    return response;
  }

  /**
   * Use for update the metadata updated at with its parents
   * @param metadataId {string} id of the metadata
   */
  async updateUpdatedAt(metadataId?: string, objectKey?: string) {
    if (!metadataId && !objectKey) return;

    logger.info(
      `Update updatedAt | MetaDataRepository.updateUpdatedAt | N/A | metadataId: ${metadataId}, objectKey: ${objectKey}`,
    );
    let metaObj;
    //set updatedAt
    if (metadataId) {
      metaObj = await this.findById(metadataId);
    } else {
      metaObj = await this.findOne({where: {objectKey: objectKey}});
    }

    logger.info(
      `Update updatedAt | MetaDataRepository.updateUpdatedAt | N/A | metadataId: ${metadataId}, objectKey: ${objectKey}, metaObj: ${JSON.stringify(
        metaObj,
      )}`,
    );

    if (metaObj) {
      let updateList = metaObj.vCollectionIdList ? metaObj.vCollectionIdList : [];
      let updateObjectIdList = updateList.map(obj => new ObjectId(obj));
      updateObjectIdList.push(new ObjectId(metaObj.id));
      if (updateObjectIdList && updateObjectIdList.length > 0) {
        logger.debug(
          `Update updatedAt | MetaDataService.updateUpdatedAt | N/A | edit updateAt parent list: ${updateObjectIdList} `,
        );
        this.updateManySet({_id: {$in: updateObjectIdList}}, {updatedAt: new Date()}, []);
      }
    }
  }
}

export interface MetaDataFindAndModifyRes {
  value: MetaDataFindAndModify;
  lastErrorObject: LastErrorObject;
}

interface MetaDataFindAndModify extends Omit<MetaData, 'id'> {
  _id: string;
}

export interface LastErrorObject {
  n: number;
  updatedExisting: boolean;
  upserted?: boolean;
}
