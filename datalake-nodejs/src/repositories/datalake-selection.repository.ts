/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in DatalakeSelection model
 */

/**
 * @class DatalakeSelectionRepository
 * purpose of DatalakeSelectionRepository is to query and create DatalakeSelection data
 * Use to tag a particular selection for a project creation
 * Id of the selection document will be used as the selection tag
 * @description repository class that use for Service interface that provides strong-typed data access operation in DatalakeSelection model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {DatalakeSelection, DatalakeSelectionRelations} from '../models';

export class DatalakeSelectionRepository extends DefaultCrudRepository<
  DatalakeSelection,
  typeof DatalakeSelection.prototype.id,
  DatalakeSelectionRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(DatalakeSelection, dataSource);
  }
}
