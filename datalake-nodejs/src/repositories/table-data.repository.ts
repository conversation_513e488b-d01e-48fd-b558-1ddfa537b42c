import {inject} from '@loopback/core';
import {DefaultCrudRepository, Filter} from '@loopback/repository';
import {ObjectId} from 'bson';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {TableData, TableDataRelations, TableField} from '../models';
import {FLOWS} from '../settings/constants';

export class TableDataRepository extends DefaultCrudRepository<
  TableData,
  typeof TableData.prototype._id,
  TableDataRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(TableData, dataSource);
  }

  /**
   * Finds table data by connection ID.
   * @param connectionId {string} - The connection ID.
   * @returns {Promise<TableData[]>} - A promise that resolves to an array of table data.
   */
  async findByConnectionId(connectionId: string): Promise<TableData[]> {
    return this.find({where: {connectionId}});
  }

  /**
   * Finds table data by connection ID with pagination and optional search key.
   * @param connectionId {string} - The connection ID.
   * @param pageIndex {number} - The index of the page.
   * @param pageSize {number} - The size of the page.
   * @param searchKey {string} - (Optional) The search key for filtering table names.
   * @returns {Promise<{data: TableData[]; total: number}>} - A promise that resolves to an object containing the data and total count.
   */

  async findByConnectionIdWithPagination(
    connectionId: string,
    pageIndex: number,
    pageSize: number,
    searchKey?: string,
  ): Promise<{data: TableData[]; total: number}> {
    const skip = pageIndex * pageSize;
    const limit = pageSize;

    const filter: Filter<TableData> = {
      where: {
        connectionId,
        ...(searchKey && {
          tableName: {regexp: new RegExp(searchKey, 'i')},
        }),
      },
      skip,
      limit,
    };

    const [data, total] = await Promise.all([
      this.find(filter),
      this.count({connectionId, ...(searchKey && {tableName: {regexp: new RegExp(searchKey, 'i')}})}),
    ]);

    return {data, total: total.count};
  }

  /**
   * Finds fields by table ID with pagination and optional search key.
   * @param tableId {string} - The table ID.
   * @param pageIndex {number} - The index of the page.
   * @param pageSize {number} - The size of the page.
   * @param searchKey {string} - (Optional) The search key for filtering fields.
   * @returns {Promise<{
   *   data: TableField[];
   *   total: number;
   *   isVisible: boolean;
   *   isVerified: boolean;
   *   tableCreatedAt: Date;
   *   tableName: string;
   *   description: string;
   *    overview: string;
   * }>} - A promise that resolves to an object containing the field data, total count, visibility, verification status, table creation date, table name, and description.
   */
  async findFieldsByTableId(
    tableId: string,
    pageIndex: number,
    pageSize: number,
    searchKey?: string,
  ): Promise<{
    data: TableField[];
    total: number;
    isVisible: boolean;
    isVerified: boolean;
    tableCreatedAt: Date;
    tableName: string;
    description: string;
    overview: string;
  }> {
    const filter: Filter<TableData> = {
      where: {
        _id: tableId,
      },
      fields: {
        tableName: true,
        fields: true,
        subFields: true,
        createdAt: true,
        modifiedBy: true,
        modifiedDate: true,
        fieldsCount: true,
        isVisible: true,
        isVerified: true,
        isVisibleToAI: true,
        description: true,
        table_overview: true,
      },
    };

    const tableData = await this.findOne(filter);
    if (!tableData)
      return {
        data: [],
        total: 0,
        isVisible: false,
        isVerified: false,
        tableCreatedAt: new Date(),
        tableName: '',
        description: '',
        overview: '',
      };

    let fields = tableData.fields;

    if (searchKey) {
      fields = fields.filter(field => field.name.toLowerCase().includes(searchKey.toLowerCase()));
    }

    const total = fields.length;
    const paginatedFields = fields.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize);

    const data = paginatedFields.map(field => ({
      _id: tableId,
      name: field.name,
      mappedName: field.mappedName || field.name,
      dataType: field.dataType,
      description: field.description,
      isVerified: field.isVerified,
      lastModifiedAt: field.lastModifiedAt,
      verifiedBy: field.verifiedBy,
      createdAt: field.createdAt,
      isVisible: !!field?.isVisibleToAI,
      sampleData: field?.fieldStats?.value_distribution
        ? Object.keys(field.fieldStats.value_distribution).slice(0, 25)
        : [],
    }));
    return {
      data,
      total,
      isVisible: !!tableData?.isVisibleToAI,
      isVerified: tableData.isVerified,
      tableCreatedAt: tableData.createdAt,
      tableName: tableData.tableName,
      description: tableData.description || '',
      overview: tableData.table_overview || '',
    };
  }

  /**
   * Updates the description of a specific field in a table.
   * @param id {string} - The table ID.
   * @param fieldName {string} - The name of the field.
   * @param description {string} - The new description for the field.
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   * @throws {Error} - If the field is not found.
   */

  async updateFieldDescription(id: string, fieldName: string, description: string): Promise<void> {
    const updateData = {
      [`fields.$[field].description`]: description,
      [`fields.$[field].lastModifiedAt`]: new Date(),
      modifiedDate: new Date(),
    };

    const arrayFilter = [
      {
        'field.name': fieldName,
      },
    ];

    const result = await this.updateOneSet({_id: new ObjectId(id)}, updateData, arrayFilter);

    if (!result?.modifiedCount) {
      throw new Error('Field not found');
    }
  }

  /**
   * Updates the visibility of a specific field in a table based on the table ID and field name.
   *
   * This method retrieves the table data by its ID, finds the specified field by its name,
   * and updates its visibility (`isVisible`). It also updates the `lastModifiedAt` timestamp
   * for the field and the `modifiedDate` for the table as a whole. If the field is found and
   * updated, the method returns the updated `tableData`. If the field is not found, it logs a
   * warning and returns `null`.
   *
   * @param {string} id - The unique identifier of the table data entry.
   * @param {string} fieldName - The name of the field to update the visibility for.
   * @param {boolean} isVisible - The new visibility status of the field.
   * @returns {Promise<TableData | null>} - A promise that resolves to the updated table data, or null if the field was not found.
   *
   * @throws {Error} - If the update operation fails, it logs the error but doesn't explicitly throw in this method.
   */
  async updateFieldVisibility(
    id: string,
    fieldName: string,
    isVisible: boolean,
  ): Promise<{isSuccess: boolean; message: string; data?: TableData}> {
    let tableData: TableData;
    try {
      tableData = await this.findById(id);
      if (!tableData) {
        return {isSuccess: false, message: `TableData with id "${id}" not found`};
      }
    } catch (error) {
      logger.error(`Error retrieving table data with id "${id}": ${error.message}`);
      return {isSuccess: false, message: `Failed to retrieve table data with id "${id}"`};
    }

    const field = tableData.fields.find(f => f.name === fieldName);
    if (!field) {
      return {isSuccess: false, message: `Field "${fieldName}" not found in table with id "${id}"`};
    }
    try {
      field.isVisible = isVisible;
      field.lastModifiedAt = new Date();
      tableData.modifiedDate = new Date();
      await this.updateById(id, tableData);
      logger.info(
        `Update isVisible in DatalakeDB tableData tableInfo fields Successfully| TableDataRepository.updateFieldVisibility | N/A | Trying to execute db query`,
      );
      return {isSuccess: true, message: `Field visibility updated successfully`, data: tableData};
    } catch (error) {
      logger.error(
        'Error updating field visibility for field "${fieldName}" in table "${id}": ${error.message} || TableDataRepository.updateFieldVisibility | N/A | Trying to execute db query',
      );
      return {isSuccess: false, message: 'Failed to update field visibility for field "${fieldName}"'};
    }
  }

  /**
   * Updates the description of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param description {string} - The new description for the field.
   * @param currentUserName {string} - name of the modifier
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   * @throws {Error} - If the field is not found.
   */

  async updateTableDescription(tableId: string, description: string, currentUserName: string): Promise<void> {
    const updateData = {
      description: description,
      modifiedDate: new Date(),
      modifiedBy: currentUserName,
    };

    const result = await this.updateOneSet({_id: new ObjectId(tableId)}, updateData, []);

    if (!result?.modifiedCount) {
      throw new Error('Table not found');
    }
  }

  /**
   * Updates the overview of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param overview {string} - The new overview for the field.
   * @param currentUserName {string} - The name of the modifier.
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   * @throws {Error} - If the table is not found.
   */
  async updateTableOverview(tableId: string, overview: string, currentUserName: string): Promise<void> {
    const updateData = {
      table_overview: overview,
      modifiedDate: new Date(),
      modifiedBy: currentUserName,
    };

    const result = await this.updateOneSet({_id: new ObjectId(tableId)}, updateData, []);

    if (!result?.modifiedCount) {
      throw new Error('Table not found or no modification was made');
    }
  }

  async getNavigationData(connectionId: string, currentTableId: string): Promise<any> {
    const tables = await this.find({where: {connectionId}});
    const total = tables.length;

    if (total === 0) {
      throw new Error('No tables found for the given connectionId');
    }

    const currentIndex = tables.findIndex(table => table._id!.toString() === currentTableId);
    if (currentIndex === -1) {
      throw new Error('Current table not found');
    }

    const previousId = currentIndex > 0 ? tables[currentIndex - 1]._id : null;
    const nextId = currentIndex < total - 1 ? tables[currentIndex + 1]._id : null;

    return {
      nextId: nextId ? nextId.toString() : null,
      previousId: previousId ? previousId.toString() : null,
      currentTable: currentIndex + 1,
      total,
    };
  }

  /**
   * Update visibility for a given connectionId and tableName
   * @param connectionId {string} connection ID
   * @param tableName {string} table name
   * @param isVisible {boolean} visibility status
   * @returns {Promise<{isSuccess: boolean}>} update status
   */
  public async updateVisibility(
    connectionId: string,
    tableName: string,
    isVisible: boolean,
    currentUserName: string,
  ): Promise<{isSuccess: boolean}> {
    const collection = (this.dataSource.connector as any).collection('TableData');

    const result = await collection.updateOne(
      {
        connectionId: connectionId,
        tableName: tableName,
      },
      {
        $set: {
          isVisible: isVisible,
          modifiedDate: new Date(),
          modifiedBy: currentUserName,
        },
      },
    );

    if (result.modifiedCount === 0) {
      return {isSuccess: false};
    }

    // Fetch the updated document
    const updatedDocument = await collection.findOne(
      {
        connectionId: connectionId,
        tableName: tableName,
      },
      {
        projection: {
          isVisible: 1,
          modifiedDate: 1,
          modifiedBy: 1,
        },
      },
    );
    // console.log('Updated Document:', updatedDocument);

    return {isSuccess: true};
  }

  /**
   * Update isVerified for both tableInfo and a specific field if provided
   * @param connectionId {string} connection ID
   * @param tableName {string} table name
   * @param isVerified {boolean} verification status
   * @param fieldName {string} (optional) field name
   * @returns {Promise<{isSuccess: boolean}>} update status
   */
  public async updateVerification(
    connectionId: string,
    tableName: string,
    isVerified: boolean,
    fieldName?: string,
  ): Promise<{isSuccess: boolean}> {
    const collection = (this.dataSource.connector as any).collection('TableData');

    let updateQuery: any = {};
    let arrayFilters: any[] = [];

    // if user verified the table, then all fields are verified automatically.
    // if user unverified any field, then related table verifiation status changed to unverified status
    if (fieldName) {
      if (isVerified) {
        updateQuery = {
          $set: {'fields.$[elem].isVerified': isVerified},
        };
        arrayFilters = [{'elem.name': fieldName}];
      } else {
        updateQuery = {
          $set: {'fields.$[elem].isVerified': isVerified, 'tableInfo.isVerified': isVerified},
        };
        arrayFilters = [{'elem.name': fieldName}];
      }
    } else {
      if (isVerified) {
        updateQuery = {
          $set: {isVerified: isVerified, 'fields.$[].isVerified': isVerified},
        };
      } else {
        updateQuery = {
          $set: {isVerified: isVerified},
        };
      }
    }

    const filterCriteria = {
      connectionId: connectionId,
      tableName: tableName,
    };
    const result = await collection.updateOne(filterCriteria, updateQuery, {arrayFilters: arrayFilters});
    if (result.modifiedCount === 0) {
      return {isSuccess: false};
    }

    return {isSuccess: true};
  }

  /**
   * Retrieves the table schema information as a JSON object for a given connection ID and tableInfo Visibility=true.
   *
   * This method finds all table data entries associated with the provided connection ID and
   * based on visbility extracts the schema details, including table names, descriptions, and field details
   * (names, data types, and descriptions). If no table data is found for the given connection ID,
   * it returns a message indicating that no data was found.
   * If visbleTableSchema is empty it returns a message No visible table data found for the given connectionId.
   *
   * @param {string} connectionId - The connection ID for which to retrieve table schema information.
   * @returns {Promise<any>} - A promise that resolves to a JSON object containing the table schema information
   *                           or a message indicating that no table data was found or No visible table data found for the given connectionId.
   */
  async getTableSchemaAsJson(connectionId: string): Promise<any> {
    // Find all table data entries by connection ID
    const tableDataEntries = await this.findByConnectionId(connectionId);

    if (tableDataEntries.length === 0) {
      return {message: 'No table data found for the given connectionId'};
    }
    /**
     * Filter the tableDataEntries based on the visibility status.
     * Only include entries where tableInfo.isVisible is true.
     */
    const visibleTableSchemas = tableDataEntries
      .filter(entry => entry.isVisible)
      .map(entry => ({
        tableName: entry.tableName,
        mappedName: entry.mappedName || entry.tableName,
        tableDescription: entry.description,
        relations: entry?.relationships || [],
        fields: entry.fields
          .filter(field => field.isVisible)
          .map(field => ({
            name: field.name,
            mappedName: field.mappedName || field.name,
            dataType: field.dataType,
            description: field.description,
            fieldStats: field?.fieldStats || {},
          })),
      }));

    if (visibleTableSchemas.length === 0) {
      return {message: 'No visible table data found for the given connectionId'};
    }

    // Return the schema as a JSON object
    return visibleTableSchemas;
  }

  /**
   * Retrieves the latest modified date among all fields in the tables for a given connection ID.
   * @param connectionId {string} - The connection ID.
   * @returns {Promise<Date>} - A promise that resolves to the latest modified date.
   */
  async getLatestModifiedFieldDate(connectionId: string): Promise<Date> {
    const tableDataEntries = await this.findByConnectionId(connectionId);
    if (tableDataEntries.length === 0) {
      return new Date(0);
    }

    let latestModifiedDate: Date = new Date(0); // Initialize to epoch

    tableDataEntries.forEach(entry => {
      entry.fields.forEach(field => {
        if (field.lastModifiedAt && field.lastModifiedAt > latestModifiedDate) {
          latestModifiedDate = field.lastModifiedAt;
        }
      });
      if (entry.modifiedDate > latestModifiedDate) {
        latestModifiedDate = entry.modifiedDate;
      }
    });

    return latestModifiedDate;
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('TableData').aggregate(params).get();
    return response;
  }

  /**
   * Retrieves the count of table data entries for a given list of connection IDs.
   * @param connectionIds {string[]} - The list of connection IDs.
   * @returns {Promise<number>} - A promise that resolves to the count of table data entries.
   */
  public async getCountByConnectionIds(connectionIds: string[]): Promise<number> {
    if (!connectionIds || connectionIds.length === 0) {
      return 0;
    }
    const response = await (this.dataSource.connector as any)
      .collection('TableData')
      .aggregate([{$match: {connectionId: {$in: connectionIds}}}, {$count: 'count'}])
      .get();
    if (!response || response.length === 0) {
      return 0;
    }
    return response[0].count;
  }

  public async updateOneSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('TableData')
      .updateOne(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.updateOneSet | response: `, response);
    return response;
  }

  public async updateOne(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('TableData')
      .updateOne(params, data, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.updateOne | response: `, response);
    return response;
  }

  public async updateManySet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('TableData')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.updateManySet | response: `, response);
    return response;
  }

  public async updateManyPull(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('TableData')
      .updateMany(params, {$pull: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.updateManyPull | response: `, response);
    return response;
  }

  public async deleteOne(params: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('TableData').deleteOne(params);
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.deleteOne | response: `, response);
    return response;
  }

  public async deleteMany(params: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('TableData').deleteMany(params);
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | TableDataRepository.deleteMany | response: `, response);
    return response;
  }
}
