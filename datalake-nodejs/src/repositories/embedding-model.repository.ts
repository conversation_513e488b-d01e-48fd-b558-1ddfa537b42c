/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in EmbeddingModel model
 */

/**
 * @class EmbeddingModelRepository
 * purpose of EmbeddingModelRepository is to query and create EmbeddingModel data
 * @description repository class that use for Service interface that provides strong-typed data access operation in EmbeddingModel model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {EmbeddingModel, EmbeddingModelRelations} from '../models/embedding-model.model';

export class EmbeddingModelRepository extends DefaultCrudRepository<
  EmbeddingModel,
  typeof EmbeddingModel.prototype._id,
  EmbeddingModelRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(EmbeddingModel, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('EmbeddingModel')
      .aggregate(params)
      .get();
    return response;
  }

}
