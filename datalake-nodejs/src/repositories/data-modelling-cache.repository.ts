/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in DataModellingCache model
 */

/**
 * @class DataModellingCacheRepository
 * purpose of DataModellingCacheRepository is to query and create DataModellingCache data
 * @description repository class that use for Service interface that provides strong-typed data access operation in DataModellingCache model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {DataModellingCache, DataModellingCacheRelations} from '../models';
import {FLOWS} from '../settings/constants';

export class DataModellingCacheRepository extends DefaultCrudRepository<
  DataModellingCache,
  typeof DataModellingCache.prototype._id,
  DataModellingCacheRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(DataModellingCache, dataSource);
  }

  /**
   * delete many data modelling cache
   * @param params
   * @returns
   */
  public async deleteMany(params: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('DataModellingCache').deleteMany(params);
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | DataModellingCacheRepository.deleteMany | response: `, response);
    return response;
  }
}
