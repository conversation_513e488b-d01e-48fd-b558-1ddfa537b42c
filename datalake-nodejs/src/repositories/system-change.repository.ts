/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in SystemChange model
 */

/**
 * @class SystemChangeRepository
 * purpose of SystemChangeRepository is to query and create SystemChange data
 * @description repository class that use for Service interface that provides strong-typed data access operation in SystemChange model
 * <AUTHOR>
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {SystemChange, SystemChangeRelations} from '../models';

export class SystemChangeRepository extends DefaultCrudRepository<
  SystemChange,
  typeof SystemChange.prototype.id,
  SystemChangeRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(SystemChange, dataSource);
  }


}
