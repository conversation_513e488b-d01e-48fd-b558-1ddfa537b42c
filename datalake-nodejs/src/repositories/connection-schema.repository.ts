import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {ConnectionSchema, ConnectionSchemaRelations} from '../models';
import {FLOWS} from '../settings/constants';

export class ConnectionSchemaRepository extends DefaultCrudRepository<
  ConnectionSchema,
  typeof ConnectionSchema.prototype._id,
  ConnectionSchemaRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(ConnectionSchema, dataSource);
  }

  public async updateMany(params: any, data: any, arrayFilter: any) {
    let response = await (this.dataSource.connector as any)
      .collection('ConnectionSchema')
      .updateMany(params, data, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | ConnectionSchemaRepository.updateMany | response: `, response);
    return response;
  }
}
