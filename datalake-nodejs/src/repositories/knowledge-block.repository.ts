import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {KnowledgeBlock, KnowledgeBlockRelations} from '../models';

export class KnowledgeBlockRepository extends DefaultCrudRepository<
  KnowledgeBlock,
  typeof KnowledgeBlock.prototype._id,
  KnowledgeBlockRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(KnowledgeBlock, dataSource);
  }
}
