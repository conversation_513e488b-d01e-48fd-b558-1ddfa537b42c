/**
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in EvalSetRun model
 */
/**
 * @class EvalSetRunRepository
 * purpose of EvalSetRunRepository is to query and create EvalSetRun data
 * Use to store data related to an evaluation run set
 * @description repository class that use for Service interface that provides strong-typed data access operation in EvalSetRun model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {EvalSetRun, EvalSetRunRelations} from '../models';

export class EvalSetRunRepository extends DefaultCrudRepository<
  EvalSetRun,
  typeof EvalSetRun.prototype._id,
  EvalSetRunRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(EvalSetRun, dataSource);
  }
}
