/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in StorageMapping model
 */

/**
 * @class StorageMappingRepository
 * purpose of StorageMappingRepository is to query and create StorageMapping records
 * @description repository class that use for Service interface that provides strong-typed data access operation in StorageMapping model
 * <AUTHOR> chamath, chathushka, channa, manelka
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {StorageMapping, StorageMappingRelations} from '../models';
import {FLOWS} from '../settings/constants';

export class StorageMappingRepository extends DefaultCrudRepository<
  StorageMapping,
  typeof StorageMapping.prototype.id,
  StorageMappingRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(StorageMapping, dataSource);
  }

  /**
   * Update storageMapping using external mongodb connector - updateOne
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateOneSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('StorageMapping')
      .updateOne(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | StorageMappingRepository.updateOneSet | response: `, response);
    return response;
  }
}
