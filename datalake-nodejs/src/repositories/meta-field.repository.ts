/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in MetaField model
 */

/**
 * @class MetaFieldRepository
 * purpose of MetaFieldRepository is to query and create MetaField records
 * @description repository class that use for Service interface that provides strong-typed data access operation in MetaField model
 * <AUTHOR> chamath, chathushka, channa, manelka
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {MetaField, MetaFieldRelations} from '../models';

export class MetaFieldRepository extends DefaultCrudRepository<
  MetaField,
  typeof MetaField.prototype._id,
  MetaFieldRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(MetaField, dataSource);
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('MetaField').aggregate(params).get();
    return response;
  }

  /**
   * Delete metaField using external mongodb connector - deleteOne
   * @param params {object} filter Object
   * @returns response
   */
  public async deleteOne(params: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('MetaField').deleteOne(params);
    return response;
  }
}
