import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {KnowledgeTree, KnowledgeTreeRelations} from '../models';

export class KnowledgeTreeRepository extends DefaultCrudRepository<
  KnowledgeTree,
  typeof KnowledgeTree.prototype._id,
  KnowledgeTreeRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(KnowledgeTree, dataSource);
  }
}
