/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in MetadataHistory model
 */

/**
 * @class MetadataHistoryRepository
 * purpose of MetadataHistoryRepository is to query and create MetadataHistory records
 * @description repository class that use for Service interface that provides strong-typed data access operation in MetadataHistory model
 * <AUTHOR> chamath, chathushka, channa, manelka
 */
import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {MetadataHistory, MetadataHistoryRelations} from '../models';

export class MetadataHistoryRepository extends DefaultCrudRepository<
  MetadataHistory,
  typeof MetadataHistory.prototype.id,
  MetadataHistoryRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(MetadataHistory, dataSource);
  }
}
