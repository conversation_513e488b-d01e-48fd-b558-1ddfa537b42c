/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in DataCrawl model
 */

/**
 * @class DataCrawlRepository
 * purpose of DataCrawlRepository is to query and create DataCrawl data
 * @description repository class that use for Service interface that provides strong-typed data access operation in DataCrawl model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {DataCrawl, DataCrawlRelations} from '../models';

export class DataCrawlRepository extends DefaultCrudRepository<
  DataCrawl,
  typeof DataCrawl.prototype.id,
  DataCrawlRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(DataCrawl, dataSource);
  }
}
