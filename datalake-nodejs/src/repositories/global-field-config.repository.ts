/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in GlobalFieldConfig model
 */

/**
 * @class GlobalFieldConfigRepository
 * purpose of GlobalFieldConfigRepository repository is to query and create GlobalFieldConfig data
 * @description repository class that use for Service interface that provides strong-typed data access operation in GlobalFieldConfig model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {GlobalFieldConfig, GlobalFieldConfigRelations} from '../models';

export class GlobalFieldConfigRepository extends DefaultCrudRepository<
  GlobalFieldConfig,
  typeof GlobalFieldConfig.prototype.id,
  GlobalFieldConfigRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(GlobalFieldConfig, dataSource);
  }
}
