/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in InputMetaDataFeed model
 */

/**
 * @class InputMetaDataFeedRepository
 * purpose of InputMetaDataFeedRepository is to query and create InputMetaDataFeed data
 * @description repository class that use for Service interface that provides strong-typed data access operation in InputMetaDataFeed model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository, repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {ContentType, InputMetaDataFeed, InputMetaDataFeedRelations, MetaData, MetaDataInputFeedObject} from '../models';
import {UserType} from '../settings/constants';
import {ApiKeyRepository} from './api-key.repository';
import {MetaDataRepository} from './meta-data.repository';
dotenv.config();

const storageType = process.env.STORAGE_TYPE;
const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

const ClassName = 'InputMetaDataFeedRepository';

export class InputMetaDataFeedRepository extends DefaultCrudRepository<
  InputMetaDataFeed,
  typeof InputMetaDataFeed.prototype.id,
  InputMetaDataFeedRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
    @repository('MetaDataRepository') private metaDataRepository: MetaDataRepository,
    @repository('ApiKeyRepository') private apiKeyRepository: ApiKeyRepository,
  ) {
    super(InputMetaDataFeed, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('InputMetaDataFeed').aggregate(params).get();
    return response;
  }

  /**
   * Get current running operation count in mongodb
   */
  public async getDBCurrentOpCount() {
    const response = await (this.dataSource.connector as any).collection('$cmd.sys.inprog').currentOp(true).inprog
      .length;
    return response;
  }
  //-------------------------------

  /**
   * BulkWrite to mongodb
   * @param metaDataItems {string[]}
   */
  public async bulkWrite(metaDataItems: any[]) {
    if (!metaDataItems) {
      return;
      // metaDataItems = [];
    } else if (metaDataItems.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('InputMetaDataFeed').bulkWrite(metaDataItems, {
      ordered: false,
    });
    return response;
  }

  /**
   *Add metaData input to a queue
   * @param metaDataListObject
   */
  async onInputMetaData(metaDataListObject: MetaDataInputFeedObject, currentUserProfile?: UserProfileDetailed) {
    // console.log(JSON.stringify(metaDataListObject, null, 2))
    let apiKeyTeamList = await this.apiKeyRepository.find({fields: {key: true, teamId: true}});
    let apiKeyHashList = Object.fromEntries(apiKeyTeamList.map(e => [e['key'], e.teamId]));

    let teamId = metaDataListObject.apiKey ? new ObjectId(apiKeyHashList[metaDataListObject.apiKey]) : null;

    if (!teamId) {
      logger.error(`${ClassName} | onInputMetaData | `, 'Team doesnt exists on api key');
      throw new HttpErrors.Unauthorized(`Team doesnt exists on api key`);
    }

    let updatesArray = [];

    if (metaDataListObject.metaDataList) {
      if (metaDataListObject.collectionName) {
        if (!metaDataListObject.collectionType) {
          // cannot create collection without type
          return;
        }
        let collectionMetaData: Partial<MetaData> = {};
        let newCollectionObj = await this.metaDataRepository.handleCreateCollection(
          metaDataListObject.collectionName,
          metaDataListObject.collectionType,
          collectionMetaData,
          currentUserProfile,
        );

        let newCollectionId = newCollectionObj.collectionId;
        metaDataListObject.collectionId = newCollectionId;
        //metaDataListObject.parentList = [new ObjectId(newCollectionId)]
      } else if (metaDataListObject.collectionId) {
        metaDataListObject.collectionId = new ObjectId(metaDataListObject.collectionId) as any;
        //metaDataListObject.parentList = [new ObjectId(metaDataListObject.collectionId)]
      }

      for (let metaDataItem of metaDataListObject.metaDataList) {
        metaDataItem.isActive = true;
        metaDataItem.apiKey = metaDataListObject.apiKey;
        metaDataItem.metaDataObject.teamId = teamId;

        metaDataItem.metaDataObject.allowedUserIdList =
          metaDataItem.metaDataObject.allowedUserIdList &&
          Array.isArray(metaDataItem.metaDataObject.allowedUserIdList) &&
          metaDataItem.metaDataObject.allowedUserIdList.length > 0
            ? metaDataItem.metaDataObject.allowedUserIdList
            : [];
        let allowedUserStringIdList = metaDataItem.metaDataObject.allowedUserIdList.map(id => id.toString());
        if (
          currentUserProfile &&
          currentUserProfile.id &&
          currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR &&
          !allowedUserStringIdList.includes(currentUserProfile.id.toString())
        ) {
          metaDataItem.metaDataObject.allowedUserIdList.push(new ObjectId(currentUserProfile.id));
        }

        if (metaDataListObject.collectionId) {
          metaDataItem.metaDataObject.collectionId = new ObjectId(metaDataListObject.collectionId);
          //NOTE: removed below code as it is reset video length in meta data object to zero if meta received to existing video. (correct value which is set from media processing will be removed and set to null)
          // metaDataItem.metaDataObject.videoLength =
          //   (metaDataItem.metaDataObject.objectType == ContentType.VIDEO) ?
          //     metaDataItem.metaDataObject.videoLength : 0
          if (metaDataItem.metaDataObject.objectType != ContentType.VIDEO) {
            metaDataItem.metaDataObject.videoLength = 0;
          }

          if (metaDataItem.parentList) {
            metaDataItem.parentList.push(metaDataListObject.collectionId);
          } else {
            metaDataItem.parentList = [metaDataListObject.collectionId];
          }
        }

        metaDataItem.metaDataObject.bucketName = defaultBucketName;

        let insertItem = {insertOne: metaDataItem};
        await updatesArray.push(insertItem);
      }
    } else {
      logger.debug(`no metaDataList`);
    }
    await this.bulkWrite(updatesArray);

    return {
      collectionId: metaDataListObject.collectionId,
    };
  }
}
