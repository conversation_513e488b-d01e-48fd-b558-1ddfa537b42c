/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in FileUploadProgress model
 */

/**
 * @class FileUploadProgressRepository
 * purpose of FileUploadProgressRepository is to keep upload progress and related custome metadata
 * @description repository class that use for Service interface that provides strong-typed data access operation in FileUploadProgress model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {FileUploadProgress, FileUploadProgressRelations, FileUploadProgressStatus} from '../models/file-upload-progress.model';
import {ContentType, MetaData} from '../models/meta-data.model';

export class FileUploadProgressRepository extends DefaultCrudRepository<
  FileUploadProgress,
  typeof FileUploadProgress.prototype.id,
  FileUploadProgressRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(FileUploadProgress, dataSource);
  }

  /**
  * Create a file upload progress record on bulkMetaDataUpload start
  * @param collectionId ObjectId - relevaent collection files are being uploaded to
  * @returns data : file info
  */
  async createRecordOnFIleUploadStart(
    collectionId: string,
    objectKeyList: string[],
    metaDataUpdates?: Partial<MetaData>,
    collectionType?: ContentType,
    teamId?: string,
    overrideMetaData?: boolean,
    imageCollectionId?: string,
    videoCollectionId?: string,
  ) {
    let objectType = ContentType.UNSUPPORTED
    if (collectionType == ContentType.IMAGE_COLLECTION) {
      objectType = ContentType.IMAGE
    } else if (collectionType == ContentType.VIDEO_COLLECTION) {
      objectType = ContentType.VIDEO
    }
    else if (collectionType == ContentType.OTHER_COLLECTION) {
      objectType = ContentType.OTHER
    }

    let isOverrideMetaData: boolean = overrideMetaData ? overrideMetaData : false;

    let metaDataUpdateObj = {
      ...metaDataUpdates,
      objectType: objectType
    }
    let data: Partial<FileUploadProgress> = {
      collectionId: collectionId,
      startedAt: new Date(),
      updatedAt: new Date(),
      metaDataUpdates: metaDataUpdateObj,
      isOverrideMetaData: isOverrideMetaData,
      allFileList: objectKeyList,
      status: FileUploadProgressStatus.UPLOADING,
      progress: 0,
      collectionObjectType: collectionType,
      teamId: teamId,
      imageCollectionId: imageCollectionId,
      videoCollectionId: videoCollectionId
    }

    let createdRecord = await this.create(data)

    let recordId = createdRecord.id

    return recordId
  }

  /**
   * addToSet elements to array
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns updated count
   */
  public async updateOneAddToSetToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('FileUploadProgress')
      .updateOne(params, {$addToSet: data, $set: {"updatedAt": new Date()}}, {arrayFilters: arrayFilter})
    logger.debug('Added Count:', response.modifiedCount)
    return response;
  }

}
