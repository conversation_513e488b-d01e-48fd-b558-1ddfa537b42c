/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in MetaTag model
 */

/**
 * @class MetaTagRepository
 * purpose of MetaTagRepository is to query and create MetaTag records
 * @description repository class that use for Service interface that provides strong-typed data access operation in MetaTag model
 * <AUTHOR> chamath, chathushka, channa, manelka
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {MetaTag, MetaTagRelations} from '../models/meta-tag.model';
import {FLOWS} from '../settings/constants';

export class MetaTagRepository extends DefaultCrudRepository<MetaTag, typeof MetaTag.prototype._id, MetaTagRelations> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(MetaTag, dataSource);
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('MetaTag').aggregate(params).get();
    return response;
  }

  /**
   * Delete metaField using external mongodb connector - deleteOne
   * @param params {object} filter Object
   * @returns response
   */
  public async deleteOne(params: any) {
    if (!params) return;
    const response = await (this.dataSource.connector as any).collection('MetaTag').deleteOne(params);
    return response;
  }

  /**
   * remove element form array with array filters
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyRemoveFromList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    const response = await (this.dataSource.connector as any)
      .collection('MetaTag')
      .updateMany(params, {$pull: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaTagRepository.updateManyRemoveFromList | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManySet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaTag')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaTagRepository.updateManySet | response: `, response);
    return response;
  }

  /**
   * Unset - metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyUnSet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaTag')
      .updateMany(params, {$unset: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaTagRepository.updateManyUnSet | response: `, response);
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany push
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManyPushToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('MetaTag')
      .updateMany(params, {$push: data}, {arrayFilters: arrayFilter});
    if (response.connection) delete response.connection;
    if (response.message) delete response.message;
    logger.debug(`${FLOWS.DIRECT_DB_OPERATION} | MetaTagRepository.updateManyPushToList | response: `, response);
    return response;
  }

  /**
   * Use for query data from method of distinct
   * @param field The field for which to return distinct values.
   * @param query A query that specifies the documents from which to retrieve the distinct values.
   * @returns returns the distinct values in an array
   */
  public async distinct(field: string, query?: any) {
    if (!query) query = {};
    const response: any[] = await (this.dataSource.connector as any).collection('MetaTag').distinct(field, query);
    return response;
  }
}
