/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in SimilarImage model
 */

/**
 * @class SimilarImageRepository
 * purpose of SimilarImageRepository is to query and create SimilarImage data
 * @description repository class that use for Service interface that provides strong-typed data access operation in SimilarImage model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {SimilarImage, SimilarImageRelations} from '../models/similar-image.model';

export class SimilarImageRepository extends DefaultCrudRepository<
  SimilarImage,
  typeof SimilarImage.prototype._id,
  SimilarImageRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(SimilarImage, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[], args?: any) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('SimilarImage')
      .aggregate(params, args)
      .get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return
    } else if (params.length == 0) {
      return
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any)
      .collection('SimilarImage')
      .bulkWrite(
        params,
        {
          ordered: false
        }
      )
    return response;
  }


  /**
   * Use for query data from method of directRemove
   * @param params {any[]} parameters for directRemove the database
   * @returns filtered data from database
   */
  public async directRemove(params?: any) {
    if (!params) return
    const response = await (this.dataSource.connector as any)
      .collection('SimilarImage')
      .deleteMany(params)
    return response;
  }

}
