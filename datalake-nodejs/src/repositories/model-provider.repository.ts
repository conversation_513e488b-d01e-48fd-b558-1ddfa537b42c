/**
 * Copyright (c) 2025 LayerNext, Inc.
 * 
 * all rights reserved.
 * 
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 * 
 * Handle the request related to the model provider
 */

/**
 * @description This repository is used to handle the request related to the model provider
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {ModelProvider, ModelProviderRelations} from '../models';

export class ModelProviderRepository extends DefaultCrudRepository<
  ModelProvider,
  typeof ModelProvider.prototype._id,
  ModelProviderRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(ModelProvider, dataSource);
  }
}
