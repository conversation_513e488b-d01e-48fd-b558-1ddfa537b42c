/**
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in EvalHist model
 */
/**
 * @class EvalHistRepository
 * purpose of EvalHistRepository is to query and create EvalHist data
 * Use to store data related to an evaluation run (question, truth and the answere received from the LLM agent and its assessment)
 * @description repository class that use for Service interface that provides strong-typed data access operation in EvalHist model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {EvalHist, EvalHistRelations} from '../models';

export class EvalHistRepository extends DefaultCrudRepository<
  EvalHist,
  typeof EvalHist.prototype._id,
  EvalHistRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(EvalHist, dataSource);
  }

  public async insertMany(data: any, options?: any) {
    if (!data) return;
    let response = await (this.dataSource.connector as any).collection('EvalHist').insertMany(data, options);
    return response;
  }
}
