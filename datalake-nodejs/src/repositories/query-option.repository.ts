/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in QueryOption model
 */

/**
 * @class QueryOptionRepository
 * purpose of QueryOptionRepository is to query and create QueryOption data
 * @description repository class that use for Service interface that provides strong-typed data access operation in QueryOption model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository, repository} from '@loopback/repository';
import {ObjectId} from 'bson';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {
  ContentType,
  IgnoredSearchType,
  OBJECT_STATUS,
  QueryGroupOperators,
  QueryOperators,
  QueryOption,
  QueryOptionRelations,
  SearchQueryRootGroup,
  SearchQuerySubGroup,
} from '../models';
import {FLOWS} from '../settings/constants';
import {isArrayWithLength, isLiteralObject, isValidObjectId} from '../settings/tools';
import {MetaDataRepository} from './meta-data.repository';

export class QueryOptionRepository extends DefaultCrudRepository<
  QueryOption,
  typeof QueryOption.prototype.id,
  QueryOptionRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
    @repository('MetaDataRepository') private metaDataRepository: MetaDataRepository,
  ) {
    super(QueryOption, dataSource);
  }

  /**
   * Use to retrive search query option from db for given team, collectionId
   * @param teamId Use to filter queries
   * @param collectionId Use to filter queries
   * @param collectionObjectType // use to filter search option according to collection type (EX: no need of frameRate suggetion for image collections)
   * @returns
   */
  async getDynamicSearchQueryOptions(teamId: string, collectionId?: string, collectionObjectType?: ContentType) {
    let _andArr: any[] = [{teamId: new ObjectId(teamId)}];

    // filter quer options foa a collection
    if (collectionId && isValidObjectId(collectionId)) {
      _andArr.push({
        $or: [{collectionId: new ObjectId(collectionId)}, {collectionId: {$exists: false}}, {collectionId: null}],
      });

      //filter search option according to collection type (EX: no need of frameRate suggetion for image collections)
      if (collectionObjectType == ContentType.IMAGE_COLLECTION || collectionObjectType == ContentType.DATASET) {
        _andArr.push({
          ignoredSearchType: {
            $nin: [IgnoredSearchType.ANY_COLLECTION_SEARCH, IgnoredSearchType.IMAGE_COLLECTION_SEARCH],
          },
        });
      } else if (collectionObjectType == ContentType.VIDEO_COLLECTION) {
        _andArr.push({
          ignoredSearchType: {
            $nin: [IgnoredSearchType.ANY_COLLECTION_SEARCH, IgnoredSearchType.VIDEO_COLLECTION_SEARCH],
          },
        });
      }
    }

    let pipeline = [
      {
        $match: {
          $and: _andArr,
        },
      },
      {
        $group: {
          _id: {key: '$keyGroup'},
          values: {$addToSet: '$key'},
          isRoot: {$first: '$isRootGroup'},
          operators: {$first: '$operators'},
        },
      },
      {
        $project: {
          key: '$_id.key',
          values: 1,
          isRoot: 1,
          _id: 0,
          operators: 1,
        },
      },
    ];
    let result: {
      isRoot: boolean;
      key: string;
      operators: string[];
      values: string[];
    }[] = await this.aggregate(pipeline);
    return result;
  }

  /**
   * Use to insert query option to db
   * @param keyGroup Group of search query Ex: metadata, annotation or metadata.location, annotation.label
   * @param key Values of keyGroup Ex: location, label, project or San Fransinsco, Car
   * @param isRootGroup true if keyGroup is root group
   * @param teamId id of team
   * @param collectionId id of collection
   */
  async updateQueryOption(
    keyGroup: string,
    key: any,
    isRootGroup: boolean,
    teamId?: string,
    collectionId?: string,
    addedQueryKeysWithKeyGroup?: {[k: string]: any[]},
  ) {
    if (!teamId || !collectionId) {
      logger.warn(
        `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | SearchQueryBuilderService.updateQueryOption | ${teamId} | trying to build serach query option for teamId or collectionId undefined request. keyGroup: ${keyGroup}, key: ${key}, isRootGroup: ${isRootGroup}, collectionId: ${collectionId}, teamId: ${teamId}`,
      );
    }

    if (isLiteralObject(key)) {
      for (const [_key, _value] of Object.entries(key)) {
        //console.log(`==== keyGroup: ${keyGroup}, key: ${_key} ====`)
        const uniqueKey: string = teamId + '_' + collectionId + '_' + keyGroup;
        if (addedQueryKeysWithKeyGroup) {
          //use to temparally keep added queryOptions to reduce DB calls for current loop
          if (addedQueryKeysWithKeyGroup[uniqueKey]) {
            if (addedQueryKeysWithKeyGroup[uniqueKey].includes(_key)) {
              await this.updateQueryOption(
                keyGroup + '.' + _key,
                _value,
                false,
                teamId,
                collectionId,
                addedQueryKeysWithKeyGroup,
              );
              return;
            }
          }
        }
        let isQueryOptionAvailable = await this.findOne({
          where: {
            teamId: teamId,
            collectionId: collectionId,
            keyGroup: keyGroup,
            key: _key,
            isRootGroup: isRootGroup,
          },
        });

        //if not exist, then insert to db
        if (!isQueryOptionAvailable) {
          await this.create({
            teamId: teamId,
            collectionId: collectionId,
            keyGroup: keyGroup,
            key: _key,
            isRootGroup: isRootGroup,
            operators: [QueryGroupOperators.dot],
          });

          if (addedQueryKeysWithKeyGroup) {
            //use to temparally keep added queryOptions to reduce DB calls for current loop
            if (addedQueryKeysWithKeyGroup[uniqueKey]) {
              addedQueryKeysWithKeyGroup[uniqueKey].push(_key);
            } else {
              addedQueryKeysWithKeyGroup[uniqueKey] = [_key];
            }
          }
          logger.info(
            `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | SearchQueryBuilderService.updateQueryOption | ${teamId} | adding serach query option keyGroup: ${keyGroup}, key: ${_key}, isRootGroup: ${isRootGroup}, collectionId: ${collectionId}, teamId: ${teamId}`,
          );
        }
        //if exist, then add it to temparary object map
        else {
          if (addedQueryKeysWithKeyGroup) {
            //use to temparally keep added queryOptions to reduce DB calls for current loop
            if (addedQueryKeysWithKeyGroup[uniqueKey]) {
              addedQueryKeysWithKeyGroup[uniqueKey].push(_key);
            } else {
              addedQueryKeysWithKeyGroup[uniqueKey] = [_key];
            }
          }
        }

        await this.updateQueryOption(
          keyGroup + '.' + _key,
          _value,
          false,
          teamId,
          collectionId,
          addedQueryKeysWithKeyGroup,
        );
      }
    } else if (Array.isArray(key)) {
      for (let elem of key) {
        await this.updateQueryOption(keyGroup, elem, false, teamId, collectionId, addedQueryKeysWithKeyGroup);
      }
    } else {
      //console.log(`==== keyGroup: ${keyGroup}, key: ${key} ====`)
      const uniqueKey: string = teamId + '_' + collectionId + '_' + keyGroup;
      if (addedQueryKeysWithKeyGroup) {
        //use to temparally keep added queryOptions to reduce DB calls for current loop
        if (addedQueryKeysWithKeyGroup[uniqueKey]) {
          if (addedQueryKeysWithKeyGroup[uniqueKey].includes(key)) {
            return;
          }
        }
      }

      let isQueryOptionAvailable = await this.findOne({
        where: {
          teamId: teamId,
          collectionId: collectionId,
          keyGroup: keyGroup,
          key: key,
          isRootGroup: isRootGroup,
        },
      });

      //if not exist, then insert to db
      if (!isQueryOptionAvailable) {
        await this.create({
          teamId: teamId,
          collectionId: collectionId,
          keyGroup: keyGroup,
          key: key,
          operators: this.getPossibleOperatorList(key, keyGroup),
          isRootGroup: isRootGroup,
        });

        if (addedQueryKeysWithKeyGroup) {
          //use to temparally keep added queryOptions to reduce DB calls for current loop
          if (addedQueryKeysWithKeyGroup[uniqueKey]) {
            addedQueryKeysWithKeyGroup[uniqueKey].push(key);
          } else {
            addedQueryKeysWithKeyGroup[uniqueKey] = [key];
          }
        }

        logger.info(
          `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | SearchQueryBuilderService.updateQueryOption | ${teamId} | adding serach query option keyGroup: ${keyGroup}, key: ${key}, isRootGroup: ${isRootGroup}, collectionId: ${collectionId}, teamId: ${teamId}`,
        );
      }
      //if exist, then add it to temparary object map
      else {
        if (addedQueryKeysWithKeyGroup) {
          //use to temparally keep added queryOptions to reduce DB calls for current loop
          if (addedQueryKeysWithKeyGroup[uniqueKey]) {
            addedQueryKeysWithKeyGroup[uniqueKey].push(key);
          } else {
            addedQueryKeysWithKeyGroup[uniqueKey] = [key];
          }
        }
      }
    }
  }

  /**
   * Use to get possible operator list for a given metadata value
   * @param key {any} value of metadata
   * @param keyGroup {MetaGroup} group of metadata value
   * @returns [list of operators]
   */
  getPossibleOperatorList(key: any, keyGroup: SearchQueryRootGroup | string) {
    let rootKeyGroup = keyGroup.split('.')[0];
    if (rootKeyGroup == SearchQueryRootGroup.METADATA) {
      if (typeof key == 'string') {
        return [QueryOperators.eq, QueryOperators.neq];
      } else if (typeof key == 'number') {
        return [
          QueryOperators.eq,
          QueryOperators.neq,
          QueryOperators.gt,
          QueryOperators.gte,
          QueryOperators.lt,
          QueryOperators.gte,
        ];
      } else {
        return [];
      }
    } else if (rootKeyGroup == SearchQueryRootGroup.ANNOTATION) {
      return [QueryOperators.eq, QueryOperators.neq];
    } else {
      if (typeof key == 'string') {
        return [QueryOperators.eq, QueryOperators.neq];
      } else if (typeof key == 'number') {
        return [
          QueryOperators.eq,
          QueryOperators.neq,
          QueryOperators.gt,
          QueryOperators.gte,
          QueryOperators.lt,
          QueryOperators.gte,
        ];
      } else {
        return [];
      }
    }
  }

  /**
   * Use to deleted serach query options
   * Ex: delete query options when system label delete, annotation project delete
   */
  async deleteQueryOption(keyGroup: string, key?: string, teamId?: string, collectionId?: string) {
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | QueryOptionRepository.deleteQueryOption | ${teamId} | deleting serach query option keyGroup: ${keyGroup}, key: ${key}, teamId: ${teamId}, collectionId: ${collectionId}`,
    );

    if (!collectionId && !teamId) {
      // return if both collectionId & teamId not exist
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | QueryOptionRepository.deleteQueryOption | ${teamId} | failed to delete serach query option keyGroup: ${keyGroup}, key: ${key}, teamId: ${teamId}, collectionId: ${collectionId}`,
      );
      return;
    }

    let filterOptions: {
      keyGroup: string;
      teamId?: string;
      key?: string;
      collectionId?: string;
    } = {
      keyGroup: keyGroup,
    };

    if (teamId) filterOptions.teamId = teamId;
    if (collectionId) filterOptions.collectionId = collectionId;
    if (key) filterOptions.key = key;

    let deletedCount = await this.deleteAll(filterOptions);

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | QueryOptionRepository.deleteQueryOption | ${teamId} | ${deletedCount.count} records deleted for serach query option keyGroup: ${keyGroup}, key: ${key}, teamId: ${teamId}`,
    );
  }

  async updateQueryKeyValue(keyGroup: string, newKey: string, oldKey: string, teamId: string) {
    return await this.updateManySet(
      {
        keyGroup: keyGroup,
        key: oldKey,
        teamId: new ObjectId(teamId),
      },
      {
        key: newKey,
      },
      [],
    );
  }

  // async deleteQueryKeyValue(keyGroup: string, key: string, teamId: string) {
  //   return await this.deleteAll({keyGroup: keyGroup, key: key, teamId: teamId})
  // }

  /**
   * Use to rebuild search query option of a collection for a given metadata field
   * @param rootGroup SearchQueryRootGroup Ex. metadata, annotation
   * @param metaField meta field Ex. CameraId, projectName
   * @param teamId teamId
   * @param collectionId collectionId
   * @param distinctValues array of disctint values in the collection for the field. in case of deleteting field suggetion, then call the function with empty array
   */
  async rebuildCollectionQueryOptionForGivenMetadataField(
    rootGroup: SearchQueryRootGroup,
    metaField: string,
    teamId: string,
    collectionId: string,
    distinctValues: any[],
  ) {
    // if (!distinctValues) {
    //   if (isCustomField) {
    //     distinctValues = await this.metaDataRepository.distinct('customMeta.' + metaField, {collectionId: new ObjectId(collectionId)})
    //   } else {
    //     distinctValues = await this.metaDataRepository.distinct(metaField, {collectionId: new ObjectId(collectionId)})
    //   }
    // }

    logger.info(`${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | QueryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField | ${teamId} |
     rootGroup: ${rootGroup},
     metaField: ${metaField},
     teamId: ${teamId},
     collectionId: ${collectionId},
     distinctValues: ${distinctValues}
    `);

    let subGroup = rootGroup + QueryGroupOperators.dot + metaField;

    //if the collection doesn't contain any value for the field. then delete field from query option
    if (!Array.isArray(distinctValues) || distinctValues.length == 0) {
      await this.deleteQueryOption(rootGroup, metaField, teamId, collectionId);
      await this.deleteQueryOption(subGroup, undefined, teamId, collectionId); // make sure all values removed
      return;
    }

    //delete existing options
    await this.deleteQueryOption(subGroup, undefined, teamId, collectionId);

    //rebuild options
    await this.updateQueryOption(rootGroup, {[metaField]: distinctValues}, true, teamId, collectionId);
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('QueryOption').aggregate(params).get();
    return response;
  }

  /**
   * Update queryOption using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManySet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('QueryOption')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter});
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param data {any[]}
   */
  public async bulkWrite(data: any[]) {
    if (!data) {
      return;
      // metaDataItems = [];
    } else if (data.length == 0) {
      return;
    }

    const response = await (this.dataSource.connector as any).collection('QueryOption').bulkWrite(data, {
      ordered: false,
    });
    return response;
  }

  /**
   * Use to rebuild tag query option for collections
   * @param collectionId {string} id of the collection head
   * @returns
   */
  async rebuildCollectionQueryOptionForTags(collectionId: string) {
    try {
      let collectionObj = await this.metaDataRepository.findById(collectionId);

      let teamId = collectionObj?.teamId;

      if (!teamId) {
        logger.error(
          `QueryOptionRepository | MetaDataService.rebuildCollectionQueryOptionForTags | N/A | cannot  find team id`,
        );
        return;
      }

      // let _matchQuery = await this.metaDataRepository.getMatchObjectsOfCollection(collectionObj);

      // child files tag list not related to trashed object
      let regenerateTagList = await this.metaDataRepository.distinct('Tags', {
        // vCollectionIdList: new ObjectId(collectionObj.id),
        $or: [{vCollectionIdList: new ObjectId(collectionObj.id)}, {_id: new ObjectId(collectionObj.id)}],
        //replace flag with enum
        objectStatus: OBJECT_STATUS.ACTIVE,
        // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
      });

      //add collection head only tags to regenerate list
      // if (collectionObj.collectionHeadOnlyMeta && collectionObj.collectionHeadOnlyMeta.Tags) {
      //   for (let tag of collectionObj.collectionHeadOnlyMeta.Tags) {
      //     if (!regenerateTagList.includes(tag)) {
      //       regenerateTagList.push(tag);
      //     }
      //   }
      // }

      if (Array.isArray(regenerateTagList) && regenerateTagList.length > 0) {
        logger.debug(
          `QueryOptionRepository | MetaDataService.rebuildCollectionQueryOptionForTags | N/A | rebuild  Query Option For Tags of collectionId: ${collectionId}, uniqueTAgs length: ${regenerateTagList.length}`,
        );
        this.rebuildCollectionQueryOptionForGivenMetadataField(
          SearchQueryRootGroup.METADATA,
          'Tags',
          String(teamId),
          collectionId,
          regenerateTagList,
        );
      }
    } catch (error) {
      logger.error(
        `QueryOptionRepository | MetaDataService.rebuildCollectionQueryOptionForTags | N/A | failed to rebuild  Query Option For Tags of collectionId: ${collectionId}`,
      );
      logger.error(
        `QueryOptionRepository | MetaDataService.rebuildCollectionQueryOptionForTags | N/A | error: `,
        error,
      );
    }
  }

  /**
   * Use to rebuild dataset tag query option when dataset file changed
   * @param datasetGroupId {string} id of the dataset group
   * @returns
   */
  async rebuildDatasetQueryOptionForTags(datasetGroupId: string) {
    let datasetMetaObj = await this.metaDataRepository.findOne({where: {datasetGroupId: datasetGroupId}});
    let datasetMetaId = datasetMetaObj?.id;
    let teamId = datasetMetaObj?.teamId;

    if (!datasetMetaId || !teamId) {
      logger.error(
        `QueryOptionRepository | MetaDataService.rebuildDatasetQueryOptionForTags | N/A | cannot  find team id or dataset meta id`,
      );
      return;
    }

    await this.rebuildCollectionQueryOptionForTags(datasetMetaId);
  }

  /**
   * Add embedding query option for embedding models
   * @param matchQuery {[k: string]: any} match query
   * @param parentId {string} parent id
   * @param teamId {string} team id
   * @returns {Promise<string[]>} list of unique embedding model name
   */
  async addEmbeddingQueryOption(matchQuery: {[k: string]: any}, parentId: string, teamId?: string): Promise<void> {
    let param: Record<string, any>[] = [
      {$match: matchQuery},
      {$unwind: '$embeddingModels'},
      {
        $group: {
          _id: null,
          uniqueModel: {$addToSet: '$embeddingModels.modelName'},
        },
      },
    ];

    let aggregateRes: {_id: null; uniqueModel: string[]}[] | undefined = await this.metaDataRepository.aggregate(param);

    if (!Array.isArray(aggregateRes) || aggregateRes.length === 0) return;
    let embeddingModelList: string[] = aggregateRes[0].uniqueModel;

    if (Array.isArray(embeddingModelList) && embeddingModelList.length > 0) {
      for (const embeddingModel of embeddingModelList) {
        let tempObj: {[k: string]: any} = {};
        tempObj[SearchQuerySubGroup.EMBEDDING_MODEL] = embeddingModel;
        await this.updateQueryOption(SearchQueryRootGroup.ANALYTICS, tempObj, true, teamId, parentId);
      }
    }
  }

  /**
   * Use to rebuild query option related to given collectionId
   * @param collectionId {string} id of the collection head
   * @param teamId id of team
   */
  async regenerateQueryOptionForCollection(collectionId: string, teamId: string) {
    // get all custom meta data
    let customMetaObject = await this.metaDataRepository.aggregate([
      {
        $match: {
          $or: [{vCollectionIdList: new ObjectId(collectionId)}, {_id: new ObjectId(collectionId)}],
          teamId: new ObjectId(teamId),
          objectStatus: OBJECT_STATUS.ACTIVE,
        },
      },
      {
        $group: {
          _id: null,
          customMeta: {
            $mergeObjects: '$customMeta',
          },
        },
      },
      {
        $project: {
          _id: 0,
          customMeta: 1,
        },
      },
    ]);

    let customMeta: {[k: string]: any} = {};

    if (isArrayWithLength(customMetaObject)) {
      customMeta = customMetaObject[0].customMeta;
    }
    customMeta.Tags = ''; // add Tags field to customMeta

    // iterate customMeta and update customMeta keys with values array
    for (const key in customMeta) {
      let distinctKey = '';
      if (key === 'Tags') {
        distinctKey = 'Tags';
      } else {
        distinctKey = `customMeta.${key}`;
      }

      let valueArray = await this.metaDataRepository.distinct(distinctKey, {
        $or: [{vCollectionIdList: new ObjectId(collectionId)}, {_id: new ObjectId(collectionId)}],
        teamId: new ObjectId(teamId),
        objectStatus: OBJECT_STATUS.ACTIVE,
      });

      // if valueArray is empty, then delete key from customMeta
      if (valueArray && valueArray.length > 0) {
        customMeta[key] = valueArray;
      } else {
        delete customMeta[key];
      }
    }
    // delete all QueryOption realated to collection id
    let deletedCount = await this.deleteAll({
      collectionId: collectionId,
      teamId: teamId,
    });

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | QueryOptionRepository.regenerateQueryOption | ${teamId} | ${deletedCount.count} records deleted for serach query option collectionId: ${collectionId}, teamId: ${teamId}`,
    );

    if (Object.keys(customMeta).length > 0) {
      // rebulid QueryOption according to collection head
      await this.updateQueryOption(SearchQueryRootGroup.METADATA, customMeta, true, teamId, collectionId);
    }
  }
}
