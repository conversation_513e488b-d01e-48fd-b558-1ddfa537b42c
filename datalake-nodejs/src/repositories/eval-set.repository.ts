/**
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in EvalSet model
 */
/**
 * @class EvalSetRepository
 * purpose of EvalSetRepository is to query and create EvalSet data
 * Use to store data related to a evaluation set (its a grouped eval snaps)
 * @description repository class that use for Service interface that provides strong-typed data access operation in EvalSet model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {EvalSet, EvalSetRelations} from '../models';

export class EvalSetRepository extends DefaultCrudRepository<EvalSet, typeof EvalSet.prototype._id, EvalSetRelations> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(EvalSet, dataSource);
  }

  public async findOneAndUpdate(params: any, data: any, options: any) {
    if (!params || !data) return;
    const response = await (this.dataSource.connector as any)
      .collection('EvalSet')
      .findOneAndUpdate(params, data, options);
    return response;
  }
}
