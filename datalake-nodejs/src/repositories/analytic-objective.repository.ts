import {inject} from "@loopback/core";
import {DefaultCrudRepository} from "@loopback/repository";
import {MongoDbDataSource} from "../datasources";
import {AnalyticObjective, AnalyticObjectiveRelations} from "../models/analytic-objective.model";

export class AnalyticObjectiveRepository extends DefaultCrudRepository<
    AnalyticObjective, // Replace with your model type if needed
    typeof AnalyticObjective.prototype._id,
    AnalyticObjectiveRelations
> {
    constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(AnalyticObjective, dataSource);
    }
  
    /**
   * Aggregate method for MongoDB
   * @param params MongoDB aggregation pipeline
   */
    public async aggregate(params: any[]) {
        if (!params) params = [];
        return await (this.dataSource.connector as any)
          .collection('BusinessObjective')
          .aggregate(params)
          .toArray();
    }





}