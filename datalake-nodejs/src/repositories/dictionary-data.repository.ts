import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {DictionaryData, DictionaryDataRelations} from '../models';

export class DictionaryDataRepository extends DefaultCrudRepository<
  DictionaryData,
  typeof DictionaryData.prototype.id,
  DictionaryDataRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(DictionaryData, dataSource);
  }
}
