/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in Destination model
 */

/**
 * @class DestinationRepository
 * purpose of DestinationRepository is to query and create Destination data
 * @description repository class that use for Service interface that provides strong-typed data access operation in Destination model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {Destination, DestinationRelations} from '../models/destination.model';

export class DestinationRepository extends DefaultCrudRepository<
  Destination,
  typeof Destination.prototype._id,
  DestinationRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(Destination, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('Destination').aggregate(params).get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return;
    } else if (params.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('Destination').bulkWrite(params, {
      ordered: false,
    });
    return response;
  }
}
