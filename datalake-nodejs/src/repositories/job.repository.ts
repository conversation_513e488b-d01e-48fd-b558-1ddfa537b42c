/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in Job model
 */

/**
 * @class JobRepository
 * purpose of JobRepository is to query and create Job data
 * @description repository class that use for Service interface that provides strong-typed data access operation in Job model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {Job, JobRelations} from '../models/job.model';

export class JobRepository extends DefaultCrudRepository<Job, typeof Job.prototype._id, JobRelations> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(Job, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('Job').aggregate(params).get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return;
    } else if (params.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('Job').bulkWrite(params, {
      ordered: false,
    });
    return response;
  }

  /**
   * Update metaData using external mongodb connector - updateMany
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns response
   */
  public async updateManySet(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    let response = await (this.dataSource.connector as any)
      .collection('Job')
      .updateMany(params, {$set: data}, {arrayFilters: arrayFilter});
    return response;
  }

  /**
   * Update metaData using external mongodb connector - findOne
   * @param matchField {object} filter Object
   * @returns response
   */
  public async findOneDirectDB(matchField: any) {
    if (!matchField) return;
    return await (this.dataSource.connector as any).collection('Job').findOne(matchField);
  }

  /**
   * Update metaData using external mongodb connector - insert
   * @param data {object} updating data
   * @returns response
   */
  public async insert(data: any) {
    if (!data) return;
    let response = await (this.dataSource.connector as any).collection('Job').insert(data);
    return response;
  }

  /**
   * addToSet elements to array
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns updated count
   */
  public async updateOneAddToSetToList(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return;
    const response = await (this.dataSource.connector as any)
      .collection('Job')
      .updateOne(params, {$addToSet: data, $set: {updatedAt: new Date()}}, {arrayFilters: arrayFilter});
    logger.debug('Added Count:', response.modifiedCount);
    return response;
  }
}
