/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle business logics related to the data dictionary business rules
 */

/**
 * @class BusinessRuleRepository
 * @description This repository use for Handle business logics related to the data dictionary business rules
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {BusinessRule, BusinessRuleRelations} from '../models/business-rule-model';

export class BusinessRuleRepository extends DefaultCrudRepository<
  BusinessRule,
  typeof BusinessRule.prototype._id,
  BusinessRuleRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(BusinessRule, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];

    return await (this.dataSource.connector as any).collection('BusinessRule').aggregate(params).get();
  }

  /**
   * Retrieves the latest modifiedDate from the BusinessRule collection.
   * This function queries the BusinessRule collection for the document containing the modified_date field
   * and extracts the modifiedDate. It returns the modifiedDate as a JavaScript Date object.
   * If the modifiedDate is missing or the BusinessRule field is not present,
   * the function returns the Unix epoch (Date(0)) as a fallback.
   * If any Error Occurs it should Notify with WARN. Can be checked that in Server Logs
   *
   *
   * @returns {Promise<Date>} The latest modifiedDate from BusinessRule Collection, or Date(0) if not found.
   */
  public async getBusinessRulesLatestModifiedDate(): Promise<Date> {
    try {
      const response = await (this.dataSource.connector as any).collection('BusinessRule').findOne(
        {},
        {
          projection: {
            modified_date: 1,
            _id: 0,
          },
          sort: {
            modified_date: -1,
          },
        },
      );

      if (response?.modified_date) {
        return new Date(response.modified_date);
      } else {
        logger.warn('ModifiedDate is missing in BusinessRule.');
        return new Date(0);
      }
    } catch (error) {
      logger.error('Error fetching BusinessRule modifiedDate:', error);
      return new Date(0);
    }
  }

  /**
   * Check if a business rule is enabled
   * @param id Business rule id
   * @returns {Promise<boolean>} True if the business rule is enabled, false otherwise
   */
  public async isBusinessRuleEnabled(id: string) {
    const businessRule = await this.findById(id);
    return businessRule.is_enabled;
  }
}
