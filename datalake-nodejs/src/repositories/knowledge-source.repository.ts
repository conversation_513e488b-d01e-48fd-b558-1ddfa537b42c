/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Repository class describes the data access layer for the KnowledgeSource model
 */

/**
 * @class KnowledgeSourceRepository
 * @description Repository class describes the data access layer for the KnowledgeSource model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {KnowledgeSource, KnowledgeSourceRelations} from '../models';

export class KnowledgeSourceRepository extends DefaultCrudRepository<
  KnowledgeSource,
  typeof KnowledgeSource.prototype._id,
  KnowledgeSourceRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(KnowledgeSource, dataSource);
  }
}
