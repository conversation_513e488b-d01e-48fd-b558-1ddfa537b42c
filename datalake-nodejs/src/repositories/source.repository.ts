/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in Source model
 */

/**
 * @class SourceRepository
 * purpose of SourceRepository is to query and create Source data
 * @description repository class that use for Service interface that provides strong-typed data access operation in Source model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDbDataSource} from '../datasources';
import {Source, SourceRelations} from '../models/source.model';

export class SourceRepository extends DefaultCrudRepository<Source, typeof Source.prototype._id, SourceRelations> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(Source, dataSource);
  }

  /**
   * Aggregate to mongodb
   * @param params {string[]}
   */
  public async aggregate(params: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any).collection('Source').aggregate(params).get();
    return response;
  }

  /**
   * BulkWrite to mongodb
   * @param params {string[]}
   */
  public async bulkWrite(params: any[]) {
    if (!params) {
      return;
    } else if (params.length == 0) {
      return;
    }
    // console.log(metaDataItems)
    const response = await (this.dataSource.connector as any).collection('Source').bulkWrite(params, {
      ordered: false,
    });
    return response;
  }
}
