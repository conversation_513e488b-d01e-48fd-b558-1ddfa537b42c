/**
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in EvalSnap model
 */
/**
 * @class EvalSnapRepository
 * purpose of EvalSnapRepository is to query and create EvalSnap data
 * Use to store data related to a evaluation truth data (questions and its ground truth)
 * @description repository class that use for Service interface that provides strong-typed data access operation in EvalSnap model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {EvalSnap, EvalSnapRelations} from '../models';
import {FLOWS} from '../settings/constants';

export class EvalSnapRepository extends DefaultCrudRepository<
  EvalSnap,
  typeof EvalSnap.prototype._id,
  EvalSnapRelations
> {
  constructor(@inject('datasources.mongoDB') dataSource: MongoDbDataSource) {
    super(EvalSnap, dataSource);
  }

  /**
   * BulkWrite to mongodb
   * @param data {any[]} - array of EvalSnap objects to be bulk written
   * @returns {Promise<any>} - result of the bulk write operation
   * @description This method is used to bulk write EvalSnap objects to the mongodb database.
   *              It will return the result of the bulk write operation.
   *              If the data is null or empty, it will return without performing any operation.
   */
  public async bulkWrite(data: any[]) {
    if (!data) {
      logger.debug(`${FLOWS.DATA_DICT_EVAL} | EvalSnapRepository.bulkWrite | data is null: `, data);
      return;
    } else if (data.length == 0) {
      logger.debug(`${FLOWS.DATA_DICT_EVAL} | EvalSnapRepository.bulkWrite | data is empty: `, data);
      return;
    }

    const response = await (this.dataSource.connector as any).collection('EvalSnap').bulkWrite(data, {
      ordered: false,
    });
    logger.debug(`${FLOWS.DATA_DICT_EVAL} | EvalSnapRepository.bulkWrite | response: `, JSON.stringify(response));
    return;
  }

  public async aggregate(params: any[]) {
    if (!params) return [];
    const response = await (this.dataSource.connector as any).collection('EvalSnap').aggregate(params).get();
    return response;
  }

  public async updateMany(params: any, data: any, options?: any) {
    if (!data) return;
    let response = await (this.dataSource.connector as any).collection('EvalSnap').updateMany(params, data, options);
    return response;
  }

  public async updateOne(params: any, data: any, options?: any) {
    if (!data) return;
    let response = await (this.dataSource.connector as any).collection('EvalSnap').updateOne(params, data, options);
    return response;
  }
}
