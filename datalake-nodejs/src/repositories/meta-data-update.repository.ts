/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * repository class that use for Service interface that provides strong-typed data access operation in MetaDataUpdate model
 */

/**
 * @class MetaDataUpdateRepository
 * purpose of MetaDataUpdateRepository is to inset, query and update MetaDataUpdate records
 * @description repository class that use for Service interface that provides strong-typed data access operation in MetaDataUpdate model
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {EJSON} from 'bson';
import {logger} from '../config';
import {MongoDbDataSource} from '../datasources';
import {AnnotationObject, MetaDataUpdate, MetaDataUpdateRelations, MetaUpdatesLabelGroup, MetaUpdatesToKeyMap, MetaUpdatesToKeyMaps, OperationMode, OperationType} from '../models';

export class MetaDataUpdateRepository extends DefaultCrudRepository<
  MetaDataUpdate,
  typeof MetaDataUpdate.prototype.id,
  MetaDataUpdateRelations
> {
  constructor(
    @inject('datasources.mongoDB') dataSource: MongoDbDataSource,
  ) {
    super(MetaDataUpdate, dataSource);
  }

  /**
   * Use for query data from method of aggregate
   * @param params {any[]} parameters for aggregate the database
   * @returns filtered data from database
   */
  public async aggregate(params?: any[]) {
    if (!params) params = [];
    const response = await (this.dataSource.connector as any)
      .collection('MetaDataUpdate')
      .aggregate(params)
      .get();
    return response;
  }


  /**
   * Use for query data from method of directRemove
   * @param params {any[]} parameters for directRemove the database
   * @returns filtered data from database
   */
  public async directRemove(params?: any) {
    if (!params) return
    const response = await (this.dataSource.connector as any)
      .collection('MetaDataUpdate')
      .deleteMany(params)
    return response;
  }

  /**
   * Use to get human annotated data for given metaData Key list
   * @param objectKeys {string[]} array of unique metadata objectkeys
   * @returns object which has key as metaData objectKey and value as labelwise grouped annotation updates
   */
  async getGroundTruthDataToKeyMap(objectKeys: string[], metaUpdatesToKeyMaps: MetaUpdatesToKeyMaps) {

    // let groundTruthData = await this.getGroundTruthData(objectKeys)
    let groundTruthData = await this.getAnnotationData(objectKeys, OperationMode.HUMAN)

    let groundTruthToKeyMap: MetaUpdatesToKeyMap = {}

    groundTruthData.forEach(_data => {groundTruthToKeyMap[_data._id] = _data.annotations})

    metaUpdatesToKeyMaps.groundTruthToKeyMap = groundTruthToKeyMap
  }

  /**
   * Use to get human annotated data for given metaData Key list
   * @param objectKeys {string[]} array of unique metadata objectkeys
   * @returns labelwise grouped annotation updates
   */
  async getGroundTruthData(objectKeys: string[]) {

    let params = [
      {
        $match: {objectKey: {$in: objectKeys}, operationType: OperationType.ANNOTATION, operationMode: OperationMode.HUMAN}
      },
      {
        $unwind: {path: '$annotationObjects'}
      },
      {$addFields: {"annotationObjects.operationId": "$operationId"}},
      {
        $group: {
          _id: {label: '$annotationObjects.label.label', objectKey: '$objectKey'},
          count: {$sum: 1},
          annotationObjects: {$push: '$annotationObjects'}
        }
      },
      {
        $project: {
          label: '$_id.label',
          objectKey: '$_id.objectKey',
          count: 1,
          annotationObjects: 1,
          _id: 0
        }
      },
      {
        $group: {
          _id: '$objectKey',
          labelCount: {$sum: '$count'},
          labelList: {$push: {label: '$label', count: '$count', annotationObjects: '$annotationObjects'}}
        }
      },
      {
        $project: {
          _id: 1,
          groundTruth: [
            {
              labelCount: '$labelCount',
              labelList: '$labelList'
            }
          ]
        }
      }
    ]

    let groudTruthData: {
      _id: string,
      groundTruth: MetaUpdatesLabelGroup[]
    }[] = await this.aggregate(params)

    return groudTruthData
  }

  /**
   * Use to get auto annotated data for given metaData Key list
   * @param objectKeys {string[]} array of unique metadata objectkeys
   * @returns object which has key as metaData objectKey and value as labelwise & operationIdwise grouped annotation updates
   */
  async getModelsRunDataToKeyMap(objectKeys: string[], metaUpdatesToKeyMaps: MetaUpdatesToKeyMaps) {

    // let modelRunsData = await this.getModelsRunData(objectKeys)
    let modelRunsData = await this.getAnnotationData(objectKeys, OperationMode.AUTO)

    let modelRunsToKeyMap: MetaUpdatesToKeyMap = {}

    modelRunsData.forEach(_data => {modelRunsToKeyMap[_data._id] = _data.annotations})

    metaUpdatesToKeyMaps.modelRunsToKeyMap = modelRunsToKeyMap
  }

  /**
   * Use to get auto annotated data for given metaData Key list
   * @param objectKeys {string[]} array of unique metadata objectkeys
   * @returns labelwise & operationIdwise grouped annotation updates
   */
  async getModelsRunData(objectKeys: string[]) {

    let params = [
      {
        $match: {objectKey: {$in: objectKeys}, operationType: 1, operationMode: 2}
      },
      {
        $unwind: {path: '$annotationObjects'}
      },
      {$addFields: {"annotationObjects.operationId": "$operationId"}},
      {
        $group: {
          _id: {label: '$annotationObjects.label.label', objectKey: '$objectKey', operationId: '$operationId'},
          count: {$sum: 1},
          annotationObjects: {$push: '$annotationObjects'}
        }
      },
      {
        $project: {
          label: '$_id.label',
          objectKey: '$_id.objectKey',
          operationId: '$_id.operationId',
          count: 1,
          annotationObjects: 1,
          _id: 0
        }
      },
      {
        $group: {
          _id: {objectKey: '$objectKey', operationId: '$operationId'},
          labelCount: {$sum: '$count'},
          labelList: {$push: {label: '$label', count: '$count', annotationObjects: '$annotationObjects'}}
        }
      },
      {
        $project: {
          objectKey: '$_id.objectKey',
          operationId: '$_id.operationId',
          labelCount: 1,
          labelList: 1,
          _id: 0
        }
      },
      {
        $group: {
          _id: '$objectKey',
          modelRuns: {$push: {labelCount: '$labelCount', labelList: '$labelList', operationId: '$operationId'}}
        }
      }
    ]

    let modelRunsData: {
      _id: string,
      annotationObjects: AnnotationObject[],
      modelRuns: MetaUpdatesLabelGroup[]
    }[] = await this.aggregate(params)

    return modelRunsData
  }

  /**
   * Use to get annotation data for given metaData Key list and operationMode
   * @param objectKeys {string[]} array of unique metadata objectkeys
   * @param operationMode {OperationMode} operationMode
   * @returns labelwise grouped annotation updates
   */
  async getAnnotationData(objectKeys: string[], operationMode: OperationMode) {

    let params = [

      {
        $match: {objectKey: {$in: objectKeys}, operationType: OperationType.ANNOTATION, operationMode: operationMode}
      },
      {
        $unwind: {path: '$annotationObjects'}
      },
      {$addFields: {"annotationObjects.operationId": "$operationId"}},
      {
        $group: {
          _id: {label: '$annotationObjects.label.label', objectKey: '$objectKey', operationId: '$operationId', operationName: "$operationName"},
          count: {$sum: 1},
          annotationObjects: {$push: '$annotationObjects'}
        }
      },
      {
        $project: {
          label: '$_id.label',
          objectKey: '$_id.objectKey',
          operationId: '$_id.operationId',
          operationName: '$_id.operationName',
          count: 1,
          annotationObjects: 1,
          _id: 0
        }
      },
      {
        $group: {
          _id: {objectKey: '$objectKey', operationId: '$operationId', operationName: "$operationName"},
          labelCount: {$sum: '$count'},
          labelList: {$push: {label: '$label', count: '$count', annotationObjects: '$annotationObjects'}}
        }
      },
      {
        $project: {
          objectKey: '$_id.objectKey',
          operationId: '$_id.operationId',
          operationName: '$_id.operationName',
          labelCount: 1,
          labelList: 1,
          _id: 0
        }
      },
      {
        $group: {
          _id: '$objectKey',
          annotations: {$push: {labelCount: '$labelCount', labelList: '$labelList', operationId: '$operationId', operationName: '$operationName'}}
        }
      }
    ]

    let projectData: {
      _id: string,
      annotations: MetaUpdatesLabelGroup[]
    }[] = await this.aggregate(params)

    return projectData

  }



  /**
   * remove label attribute of boxes
   * @param params {object} filter Object
   * @param data {object} updating data
   * @param arrayFilter {objects}  An array of filter documents that determine which array elements to modify for an update operation on an array field
   * @returns  label updated frame count
   */
  public async unsetLabelAttibuteInFrame(params: any, data: any, arrayFilter: any) {
    if (!params || !data) return
    const response = await (this.dataSource.connector as any)
      .collection('MetaDataUpdate')
      .updateMany(params, {$unset: data}, {arrayFilters: arrayFilter})
    logger.debug('Label update frame Count:', response.modifiedCount)
    return response;
  }



  /**
   * remove element form array
   * @param params {object} filter Object
   * @param data {object} updating data
   * @returns response
   */
  public async updateManyRemoveFromList(params: any, data: any, arrayFilter?: any) {
    if (!params || !data) return

    if (!Array.isArray(arrayFilter)) {
      arrayFilter = []
    }

    const response = await (this.dataSource.connector as any)
      .collection('MetaDataUpdate')
      .updateMany(params, {$pull: data}, {arrayFilters: arrayFilter})
    logger.debug('MongoDB frame updateMany modified count(versions removed):', response.modifiedCount)
    return response;
  }

  /**
   * Get response by aggregating metaDataUploads
   * Use to calculate annotation stats from annotation studio
   * Parse aggregate query to re create date and ObjectId objects
   * @param query {aggregate query}
   * @returns response from aggregation
   */
  async aggregateMetaDataUpdates(query: string) {
    logger.debug(`Aggregate metadata update | MetaDataUpdateRepository.aggregateMetaDataUpdates | N/A | N/A `)
    //  logger.debug(JSON.stringify(query, null, 2))
    // function customParser(key: string, value: any) {
    //   // console.log("value:", value)
    //   // console.log("type: ", typeof(value))
    //   console.log("-------------------")
    //   if (typeof (value) == 'string') {
    //     console.log(`---str - ${value}`)

    //     let dateRegex = /^#new_date_\((.+?)\)\/#\.$/
    //     let dateRegexCount = value.search(dateRegex)
    //     let objectIdRegex = /^#object_id_\((.+?)\)\/#\.$/
    //     let objectIdRegexCount = value.search(objectIdRegex)

    //     console.log(`---dates - ${dateRegexCount}`)
    //     console.log(`---objectIds - ${objectIdRegexCount}`)

    //     if (dateRegexCount >= 0) {
    //       let matches = value.match(dateRegex)
    //       // console.log(matches)
    //       return new Date((matches![1]))
    //     }
    //     if (objectIdRegexCount >= 0) {
    //       let matches = value.match(objectIdRegex)
    //       // console.log(matches)
    //       return new ObjectId(matches![1])
    //     }
    //   }
    //   console.log(`---els`)
    //   return value
    // }
    let parsedQuery = EJSON.parse(query) as any[]
    // console.log(`---------------------------------------------------------`)
    // console.log(parsedQuery)
    // console.log(JSON.stringify(parsedQuery, null, 2))
    let data: any = []

    try {
      data = await this.aggregate(parsedQuery)
      logger.debug(`Aggregate metadata update | MetaDataUpdateRepository.aggregateMetaDataUpdates | N/A | data: `, data)
    } catch (e) {
      logger.error(`Aggregate metadata update | MetaDataUpdateRepository.aggregateMetaDataUpdates | N/A | error: ${e}`, e)
    }

    return data
  }
}
