/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * StatsCalculation related functions
 */

/**
 * @class StatsCalculationService
 * StatsCalculation related functions embedding insert, search, manage
 * @description
 * <AUTHOR>
 */

import {BindingKey, BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'bson';
import dotenv from 'dotenv';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  DatalakeSelection,
  DatalakeSelectionRequest,
  DatalakeSelectionRequestForAnalytics,
} from '../models/datalake-selection.model';
import {Analytics, AnalyticsUpdateAgg, LabelAnalytics} from '../models/meta-data-update.model';
import {
  ContentType,
  ExplorerFilterV2,
  GetEmbeddingAggregationForSelectionRes,
  LabelAnalyticsHeadCountChartFormat,
  MetaData,
  MetaFieldAndArraySummaryFormat,
  MetaFieldAndTagAnalyticsLabelwiseKeysFormat,
  OBJECT_STATUS,
} from '../models/meta-data.model';
import {InputFieldTypes} from '../models/meta-field.model';
import {
  AnalyticsObjectTypeFormat,
  MetaFieldSummaryObjectTypeFormat,
  ObjectTypeWiseCounts,
} from '../models/system-data.model';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {MetaFieldRepository} from '../repositories/meta-field.repository';
import {SystemDataRepository} from '../repositories/system-data.repository';
import {UserType} from '../settings/constants';
import {getLabelWiseCount} from '../settings/tools';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from './system-label.service';
dotenv.config();

@injectable({scope: BindingScope.TRANSIENT})
export class StatsCalculationService {
  constructor(
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(SYSTEM_LABEL_SERVICE)
    private systemLabelService: SystemLabelService,
    @repository(MetaFieldRepository)
    private metaFieldRepository: MetaFieldRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
  ) {}

  /**
   * Use for get detail source
   * @param filter {DatalakeSelectionRequest}
   * @returns {
        sourceType: Use to decide how label stats/ embedding graph should be calculated or taken pre calculated values
        id: '',
        contentType: contentType,
        approachMethod: Use to decide how metadata stats should be calculated or taken pre calculated values
   }
   */
  async getDetailsSource(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let contentType = filter.contentType || ContentType.ALL;

    function isFilterDataEmpty(filterData: ExplorerFilterV2 | undefined) {
      if (!filterData) return true;
      if (
        (Array.isArray(filterData?.annotationTypes) && filterData.annotationTypes.length > 0) ||
        (Array.isArray(filterData?.labels) && filterData.labels.length > 0) ||
        (Array.isArray(filterData?.tags) && filterData.tags.length > 0) ||
        (filterData.date && JSON.stringify(filterData.date) != '{}') ||
        (filterData.metadata && JSON.stringify(filterData.metadata) != '{}')
      )
        return false;
      return true;
    }

    if (
      !filter.query &&
      isFilterDataEmpty(filter.filterData) &&
      !filter.referenceImage &&
      !filter.embeddingSelection?.graphId
    ) {
      if (!filter.collectionId) {
        if (filter.isAllSelected && filter.objectIdList?.length == 0) {
          if (currentUserProfile && currentUserProfile.userType) {
            if (currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
              return {
                sourceType: DetailSource.DYNAMIC_QUERY,
                id: '',
                contentType: contentType,
                approachMethod: DetailSource.DYNAMIC_QUERY,
              };
            }
          }

          return {
            sourceType: DetailSource.SYSTEM_DATA,
            id: '',
            contentType: contentType,
            approachMethod: DetailSource.SYSTEM_DATA,
          };
        }
        if (!filter.isAllSelected && filter.objectIdList?.length == 1) {
          return {
            sourceType: DetailSource.METADATA,
            id: filter.objectIdList[0],
            contentType: contentType,
            approachMethod: DetailSource.METADATA, // collection or object
          };
        }
        return {
          sourceType: DetailSource.DYNAMIC_QUERY,
          id: '',
          contentType: contentType,
          approachMethod: DetailSource.DYNAMIC_QUERY,
        };
      } else if (filter.collectionId) {
        if (filter.isAllSelected && filter.objectIdList?.length == 0) {
          return {
            sourceType: DetailSource.METADATA,
            id: filter.collectionId,
            contentType: contentType,
            approachMethod: DetailSource.METADATA, // collection
          };
        }
        if (!filter.isAllSelected && filter.objectIdList?.length == 1) {
          return {
            sourceType: DetailSource.METADATA,
            id: filter.objectIdList[0],
            contentType: contentType,
            approachMethod: DetailSource.DYNAMIC_QUERY, // object
          };
        }
        return {
          sourceType: DetailSource.DYNAMIC_QUERY,
          id: '',
          contentType: contentType,
          approachMethod: DetailSource.DYNAMIC_QUERY,
        };
      } else {
        return {
          sourceType: DetailSource.DYNAMIC_QUERY,
          id: '',
          contentType: contentType,
          approachMethod: DetailSource.DYNAMIC_QUERY,
        };
      }
    } else {
      return {
        sourceType: DetailSource.DYNAMIC_QUERY,
        id: '',
        contentType: contentType,
        approachMethod: DetailSource.DYNAMIC_QUERY,
      };
    }
  }

  /**
   * Use to get frame level stats
   * @param filter {DatalakeSelectionRequest}
   * @param teamId {string} id of the team
   * @returns stats
   */
  async getFrameLevelStats(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let teamId = currentUserProfile.teamId;
    let detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
    } = await this.getDetailsSource(filter, currentUserProfile);

    let frameLevelStats: {
      frameCounts: any;
      labelCounts: any;
    } = {frameCounts: {}, labelCounts: []};
    if (detailsSource.sourceType == DetailSource.DYNAMIC_QUERY) {
      let datalakeSelection: Partial<DatalakeSelection> = {
        teamId: teamId,
        selectionRequest: filter,
        objectType: filter.contentType,
      };

      let aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
        currentUserProfile,
        undefined,
        datalakeSelection,
      );
      let lookupSection: any[] = [];
      // if (filter?.embeddingSelection?.selection && filter?.embeddingSelection?.graphId) {
      //   lookupSection = await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(
      //     filter.embeddingSelection,
      //   );
      // }

      frameLevelStats = await this.calcFrameLevelStats(
        aggregateQuery?.matchQuery,
        filter.contentType || ContentType.ALL,
        lookupSection,
      );
    } else if (detailsSource.sourceType == DetailSource.METADATA) {
      let metaObject = await this.metaDataRepository.findById(detailsSource.id);
      frameLevelStats = {
        frameCounts: {
          rawSum: metaObject.verificationStatusCount?.raw,
          machineAnnotatedSum: metaObject.verificationStatusCount?.machineAnnotated,
          verifiedSum: metaObject.verificationStatusCount?.verified,
          frameCountSum: metaObject.frameCount,
          itemCountSum: 1,
          fileSize: metaObject.fileSize,
          videoCount: metaObject.videoCount,
          imageCount: metaObject.imageCount,
          otherCount: metaObject.otherCount,
          videoLength: metaObject.videoLength,
        },
        labelCounts: metaObject.labelList ? metaObject.labelList : [],
      };
    } else {
      let systemData = await this.systemDataRepository.findOne({});
      let labelList: {label: string; count: number}[] = [];
      let typeKey: keyof ObjectTypeWiseCounts;
      switch (filter.contentType) {
        case ContentType.IMAGE:
          typeKey = 'images';
          break;
        case ContentType.VIDEO:
          typeKey = 'videos';
          break;
        case ContentType.OTHER:
          typeKey = 'other';
          break;

        case ContentType.IMAGE_COLLECTION:
          typeKey = 'imageCollections';
          break;
        case ContentType.VIDEO_COLLECTION:
          typeKey = 'videoCollections';
          break;
        case ContentType.OTHER_COLLECTION:
          typeKey = 'otherCollections';
          break;
        case ContentType.DATASET:
          typeKey = 'datasets';
          break;
        default:
          typeKey = 'images';
          break;
      }

      if (systemData?.objectTypeWiseCounts) {
        let keys = Object.keys(systemData?.objectTypeWiseCounts?.[typeKey].labelList);
        for (let key of keys) {
          labelList.push({
            label: key,
            count: systemData?.objectTypeWiseCounts?.[typeKey].labelList[key],
          });
        }
      }

      frameLevelStats = {
        frameCounts: {
          rawSum: systemData?.objectTypeWiseCounts?.[typeKey].raw,
          machineAnnotatedSum: systemData?.objectTypeWiseCounts?.[typeKey].machineAnnotated,
          verifiedSum: systemData?.objectTypeWiseCounts?.[typeKey].verified,
          frameCountSum: systemData?.objectTypeWiseCounts?.[typeKey].frames,
          itemCountSum: systemData?.objectTypeWiseCounts?.[typeKey].count,
          fileSize: systemData?.objectTypeWiseCounts?.[typeKey].size,
          videoCount: systemData?.objectTypeWiseCounts?.[typeKey].imageCount,
          imageCount: systemData?.objectTypeWiseCounts?.[typeKey].count,
          otherCount: systemData?.objectTypeWiseCounts?.[typeKey].otherCount,
          videoLength: systemData?.objectTypeWiseCounts?.[typeKey].length,
        },
        labelCounts: labelList,
      };
    }

    let labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId); //NOTE: add teamId filter

    //assign values to response object
    let totalLabelCount = 0;
    let totalAnnotationCount = 0;
    let tempLabelList = [];
    let maxDataValueLabel = 0;
    for (let label of frameLevelStats.labelCounts) {
      if (!labelToLabelTextMap[label.label]) continue;
      if (maxDataValueLabel < label.count) maxDataValueLabel = label.count;
      totalLabelCount += 1;
      totalAnnotationCount = totalAnnotationCount + label.count;
      let labelObj = {
        label: labelToLabelTextMap[label.label],
        value: label.count,
        _id: label.label,
      };
      tempLabelList.push(labelObj);
    }

    let maxDataValueFrame = 0;
    if (frameLevelStats.frameCounts.machineAnnotatedSum < frameLevelStats.frameCounts.rawSum) {
      if (frameLevelStats.frameCounts.rawSum < frameLevelStats.frameCounts.verifiedSum) {
        maxDataValueFrame = frameLevelStats.frameCounts.verifiedSum;
      } else {
        maxDataValueFrame = frameLevelStats.frameCounts.rawSum;
      }
    } else {
      if (frameLevelStats.frameCounts.machineAnnotatedSum < frameLevelStats.frameCounts.verifiedSum) {
        maxDataValueFrame = frameLevelStats.frameCounts.verifiedSum;
      } else {
        maxDataValueFrame = frameLevelStats.frameCounts.machineAnnotatedSum;
      }
    }

    return {
      frameStats: {
        rawSum: frameLevelStats.frameCounts.rawSum,
        machineAnnotatedSum: frameLevelStats.frameCounts.machineAnnotatedSum,
        verifiedSum: frameLevelStats.frameCounts.verifiedSum,
        frameCountSum: frameLevelStats.frameCounts.frameCountSum,
        maxDataValue: maxDataValueFrame,
      },
      labelStats: {
        labelCount: totalLabelCount,
        maxDataValue: maxDataValueLabel,
        annotationCount: totalAnnotationCount,
        labelList: tempLabelList,
      },
    };
  }

  /**
   * Use to aggregate the frame level stats
   * @param aggregateQuery {any} aggregate match query
   * @param objectType {ContentType} objectType
   * @returns stats
   */
  async calcFrameLevelStats(aggregateQuery: any, objectType: ContentType, lookupSection?: any) {
    let promiseList: Promise<any>[] = [];
    let isItemCountSumNeedUpdated = false;
    let params: any = [];
    let paramsForLabel: any = [];
    let idList = [];

    if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(objectType)) {
      params = [{$match: aggregateQuery}];
      paramsForLabel = [{$match: aggregateQuery}];
      if (Array.isArray(lookupSection) && lookupSection.length > 0) {
        params.push(...lookupSection);
      }
      params.push({
        $project: {
          verificationStatusCount: 1,
          frameCount: 1,
          fileSize: 1,
          videoCount: 1,
          otherCount: 1,
          objectType: 1,
          imageCount: {$cond: [{$eq: ['$objectType', 2]}, '$frameCount', '$imageCount']},
        },
      });
    } else {
      let paramsGetIdList: any = [{$match: aggregateQuery}];
      paramsGetIdList.push({
        $group: {
          _id: null,
          idList: {$push: '$_id'},
        },
      });

      let tempIdList = await this.metaDataRepository.aggregate(paramsGetIdList);
      if (Array.isArray(tempIdList) && tempIdList.length > 0) {
        idList = tempIdList[0].idList ? tempIdList[0].idList : [];
      }

      params = [{$match: {vCollectionIdList: {$in: idList}}}];
      paramsForLabel = [{$match: {vCollectionIdList: {$in: idList}}}];
      if (Array.isArray(lookupSection) && lookupSection.length > 0) {
        params.push(...lookupSection);
      }
      isItemCountSumNeedUpdated = true;
    }
    // if (Array.isArray(lookupSection) && lookupSection.length > 0) {
    //   params.push(...lookupSection);
    // }
    params.push({
      $group: {
        _id: null,
        rawSum: {$sum: '$verificationStatusCount.raw'},
        machineAnnotatedSum: {$sum: '$verificationStatusCount.machineAnnotated'},
        verifiedSum: {$sum: '$verificationStatusCount.verified'},
        frameCountSum: {$sum: '$frameCount'},
        itemCountSum: {$sum: 1},
        fileSize: {$sum: '$fileSize'},
        videoCount: {$sum: '$videoCount'},
        imageCount: {$sum: '$imageCount'},
        otherCount: {$sum: '$otherCount'},
        videoLength: {$sum: '$videoLength'},
      },
    });

    paramsForLabel.push({$unwind: '$labelList'});
    paramsForLabel.push({$group: {_id: '$labelList.label', count: {$sum: '$labelList.count'}}});
    paramsForLabel.push({$project: {label: '$_id', count: '$count', _id: 0}});
    paramsForLabel.push({$sort: {count: -1}});

    promiseList.push(this.metaDataRepository.aggregate(params));

    promiseList.push(this.metaDataRepository.aggregate(paramsForLabel));

    let resultArr = await Promise.all(promiseList);

    let frameCounts = resultArr[0][0]
      ? resultArr[0][0]
      : {
          rawSum: 0,
          machineAnnotatedSum: 0,
          verifiedSum: 0,
          frameCountSum: 0,
          itemCountSum: 0,
          fileSize: 0,
          videoCount: 0,
          imageCount: 0,
          otherCount: 0,
          videoLength: 0,
        };

    if (isItemCountSumNeedUpdated) frameCounts.itemCountSum = idList.length;

    return {
      frameCounts: frameCounts,
      labelCounts: resultArr[1],
    };
  }

  /**
   * Calculate analytics for a given operation
   * @param matchQuery  {any} match query
   * @returns analytics
   */
  async analyticsCalculation(
    matchQuery: {[k: string]: any},
    lookup?: GetEmbeddingAggregationForSelectionRes[],
  ): Promise<Analytics[]> {
    let param: Record<string, any>[] = [{$match: matchQuery}];

    if (Array.isArray(lookup) && lookup.length > 0) {
      param.push(...lookup);
    }

    param = [
      ...param,
      {$unwind: '$analytics'},
      {
        $unwind: '$analytics.labelWiseAnalytics',
      },
      {
        $group: {
          _id: {
            operationId: '$analytics.operationId',
            label: '$analytics.labelWiseAnalytics.label',
          },
          truePositive: {$sum: '$analytics.labelWiseAnalytics.truePositive'},
          falsePositive: {$sum: '$analytics.labelWiseAnalytics.falsePositive'},
          falseNegative: {$sum: '$analytics.labelWiseAnalytics.falseNegative'},
        },
      },
      {
        $group: {
          _id: '$_id.operationId',
          truePositive: {$sum: '$truePositive'},
          falsePositive: {$sum: '$falsePositive'},
          falseNegative: {$sum: '$falseNegative'},
          labelData: {
            $push: {
              label: '$_id.label',
              truePositive: '$truePositive',
              falsePositive: '$falsePositive',
              falseNegative: '$falseNegative',
            },
          },
        },
      },
      {$project: {operationId: '$_id', truePositive: 1, falsePositive: 1, falseNegative: 1, labelData: 1, _id: 0}},
    ];

    const analyticsUpdateList: AnalyticsUpdateAgg[] = await this.metaDataRepository.aggregate(param);
    let analytics: Analytics[] = [];

    if (!Array.isArray(analyticsUpdateList)) return analytics;

    for (let analyticsObj of analyticsUpdateList) {
      try {
        let precision =
          analyticsObj.truePositive + analyticsObj.falsePositive > 0
            ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falsePositive)
            : 'NaN';
        let recall =
          analyticsObj.truePositive + analyticsObj.falseNegative > 0
            ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falseNegative)
            : 'NaN';
        let f1Score =
          2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive > 0
            ? (100 * 2 * analyticsObj.truePositive) /
              (2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive)
            : 'NaN';

        let labelWiseAnalytics: LabelAnalytics[] = getLabelWiseCount(analyticsObj.labelData);
        let tempAnalyticsObj: Analytics = {
          truePositive: analyticsObj.truePositive,
          falseNegative: analyticsObj.falseNegative,
          falsePositive: analyticsObj.falsePositive,
          operationId: analyticsObj.operationId,
          precision: precision,
          recall: recall,
          f1Score: f1Score,
          labelWiseAnalytics: labelWiseAnalytics,
        };
        analytics.push(tempAnalyticsObj);
      } catch (err) {
        logger.warn(
          `analytics calculation | StatsCalculationService.analyticsCalculation | N/A | operation id: ${analyticsObj.operationId}`,
        );
        logger.warn(err);
        continue;
      }
    }
    return analytics;
  }

  /**
   * Use to calculate the analytics for system
   * @returns analytics
   */
  async calculateSystemAnalytics(): Promise<AnalyticsObjectTypeFormat> {
    logger.debug(`System analytics calculation | StatsCalculationService.calculateSystemAnalytics | N/A | initiated`);
    let formatData: AnalyticsObjectTypeFormat = {
      images: await this.analyticsCalculation({objectType: ContentType.IMAGE}),
      datasets: await this.analyticsCalculation({objectType: ContentType.DATASET}),
      videos: await this.analyticsCalculation({objectType: ContentType.VIDEO}),
    };
    return formatData;
  }

  //- meta fields and stats pre calculation ---------------------------------------------------------------------
  /**
   * calculate meta fields and tags summary (stats, and distinct values) for collections and system
   * callable from cron job
   */
  async propagateMetaFieldsAndTagsStats() {
    logger.debug(
      `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | N/A | initiated`,
    );
    let collectionAggregateParams = [
      {
        $match: {
          objectType: {
            $in: [
              ContentType.IMAGE_COLLECTION,
              ContentType.VIDEO_COLLECTION,
              ContentType.DATASET,
              ContentType.OTHER_COLLECTION,
            ],
          },
        },
      },
      {$match: {isMetaFieldsPropagationRequired: {$ne: false}}},
      {$project: {_id: 1}},
    ];

    let collectionsList = await this.metaDataRepository.aggregate(collectionAggregateParams);

    if (collectionsList && collectionsList.length > 0) {
      logger.info(
        `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | N/A | ${collectionsList.length} collections found - starting`,
      );
      for (let collectionId of collectionsList) {
        // populate meta fields and tags to collection head
        try {
          await this.handleMetaFieldsAndTagsSummaryPropagation(collectionId._id, true);
        } catch (e) {
          logger.warn(
            `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | collectionId: ${collectionId.id} | error propagating summary `,
          );
          logger.warn(e);
          continue;
        }
      }
      // populate meta fields and tags to system
      try {
        await this.handlePropagateMetaFieldsAndTagsStatsToSystem();
      } catch (e) {
        logger.warn(
          `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | system | error propagating summary to system `,
        );
        logger.warn(e);
      }
      // // populate meta fields analytics to system
      // try {
      //   await this.handlePropagateMetaFieldsAndTagsAnalyticsLabelwiseToSystem();
      // } catch (e) {
      //   logger.warn(
      //     `Metadata field analytics calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | system | error propagating analytics to system `,
      //   );
      //   logger.warn(e);
      // }
      logger.info(
        `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | N/A | propagation completed`,
      );
    } else {
      logger.debug(
        `Metadata field summary calculation | StatsCalculationService.propagateMetaFieldsAndTagsStats | N/A | no summary calculation required collections`,
      );
    }
  }

  /**
   * calculate meta fields and tags summary (stats, and distinct values) for collections
   * @param collectionId {string} id of the collection
   * @param cronTrigger {boolean} check whether method is called by  cron trigger
   * @returns void
   */
  async handleMetaFieldsAndTagsSummaryPropagation(collectionId: string, cronTrigger: boolean) {
    logger.debug(
      `Metadata field summary calculation | StatsCalculationService.handleMetaFieldsAndTagsSummaryPropagation | collectionId: ${collectionId} | Calculate for collection initiated`,
    );
    let metaFieldsArray = await this.propagateMetaFieldsAndStatsToCollectionHead(collectionId);
    let tagDataArray = await this.propagateMetaTagsStatsToCollectionHead(collectionId);

    let metaFieldSummaryObj: MetaFieldAndArraySummaryFormat[] = [...metaFieldsArray, ...tagDataArray];

    // sort meta fields
    metaFieldSummaryObj.sort(this.sortMetaFieldSummaryObjByName);

    // let metaFieldAnalyticsObject: MetaFieldAndTagAnalyticsLabelwiseFormat[] =
    //   await this.handleMetaAnalyticsLabelwisePrecalculationInCollectionHead(collectionId);

    let updateData: Partial<MetaData> = {metaDataFieldSummary: metaFieldSummaryObj};
    if (cronTrigger) updateData.isMetaFieldsPropagationRequired = false;
    // update collection head
    await this.metaDataRepository.updateById(collectionId, updateData);

    return;
  }

  /**
   * Part of aggregate pipeline to find distinct metadata fields with counts
   * @param dropDownFieldNameList
   * @returns
   */
  getMetaFieldSummaryMatchParams(dropDownFieldNameList: string[]) {
    const metaFieldMaxLimit = 10;
    let fieldParams = [
      {
        $project: {
          keyValueArray: {$objectToArray: '$customMeta'},
        },
      },
      {
        $unwind: '$keyValueArray',
      },
      {
        $group: {
          _id: {
            key: '$keyValueArray.k',
            value: '$keyValueArray.v',
          },
          count: {$sum: 1},
        },
      },
      {
        $group: {
          _id: '$_id.key',
          values: {
            $push: {
              value: '$_id.value',
              count: '$count',
            },
          },
          count: {$sum: '$count'},
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              {$gt: [{$size: '$values'}, 0]},
              {
                $or: [
                  {$lte: [{$size: '$values'}, metaFieldMaxLimit]}, // either less than max limit
                  {$in: ['$values', dropDownFieldNameList]}, // or a dropdown
                ],
              },
            ],
          },
        },
      },
    ];
    return fieldParams;
  }

  // /**
  //  * Get custom fields which are dropdowns from system metadata
  //  * @param collectionId
  //  * @returns
  //  */
  // async getParentDropDownListOfCollection(collectionId: string) {
  //   let dropdownParams = [
  //     {$match: {_id: new ObjectId(collectionId)}},
  //     {$unwind: '$metaFieldList'},
  //     {
  //       $lookup: {
  //         from: 'MetaField',
  //         let: {metaFieldId: '$metaFieldList.metaFieldId'},
  //         pipeline: [
  //           {
  //             $match: {
  //               $expr: {
  //                 $and: [{$eq: ['$_id', '$$metaFieldId']}, {$eq: ['$fieldType', InputFieldTypes.DROP_DOWN]}],
  //               },
  //             },
  //           },
  //         ],
  //         as: 'metaFieldData',
  //       },
  //     },
  //     {$unwind: '$metaFieldData'},
  //     {$project: {metaFieldData: 1}},
  //   ];
  //   let dropdownFields = await this.metaDataRepository.aggregate(dropdownParams);
  //   let dropDownFieldNameList = dropdownFields.map((field: any) => field.metaFieldData.fieldName);

  //   return dropDownFieldNameList;
  // }

  /**
   * calculate meta fields summary (stats, and distinct values) for collections
   * @param collectionId
   * @returns
   */
  async propagateMetaFieldsAndStatsToCollectionHead(collectionId: string) {
    // get dropdown fields
    // let dropDownFieldNameList = await this.getParentDropDownListOfCollection(collectionId);
    let dropDownFieldNameList = await this.getParentDropDownListOfSystem();

    // get fields match
    let fieldParamsMatch = this.getMetaFieldSummaryMatchParams(dropDownFieldNameList);
    // get fields
    let fieldParams = [
      {
        $match: {
          objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]},
          vCollectionIdList: new ObjectId(collectionId),
        },
      },
      ...fieldParamsMatch,
    ];

    let metaFieldsArray = await this.metaDataRepository.aggregate(fieldParams);

    return metaFieldsArray;
  }

  /**
   * Part of aggregate pipeline to find distinct meta tags with counts
   * @returns
   */
  getMetaTagSummaryMatchParams() {
    let tagDataParams = [
      {$unwind: '$Tags'},
      {
        $group: {
          _id: '$Tags',
          count: {$sum: 1},
        },
      },
      {
        $project: {
          _id: 'Tags',
          values: [
            {
              value: '$_id',
              count: '$count',
            },
          ],
          count: '$count',
        },
      },
    ];
    return tagDataParams;
  }

  /**
   * calculate meta tags summary (stats, and distinct values) for collections
   * @param collectionId
   * @returns
   */
  async propagateMetaTagsStatsToCollectionHead(collectionId: string) {
    let tagMatchParams = this.getMetaTagSummaryMatchParams();
    let tagDataParams = [
      {
        $match: {
          objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]},
          vCollectionIdList: new ObjectId(collectionId),
        },
      },
      ...tagMatchParams,
    ];

    let tagDataArray = await this.metaDataRepository.aggregate(tagDataParams);
    return tagDataArray;
  }

  /**
   * Calculate meta fields and tags summary for a given collection.(if calculation is ongoing or already calculated by cron, then skip the operation)
   * @param {string} collectionId {string} collection id
   * @returns {Promise<{skipOperation: boolean}>} {Promise<{skipOperation: boolean}>} whether operation is skipped or not
   */
  async calculateMetaFieldsAndTagsSummary(collectionId: string): Promise<{skipOperation: boolean}> {
    let collectionDetails: Pick<MetaData, 'metDataUpdateInfo' | 'isMetaFieldsPropagationRequired'> =
      await this.metaDataRepository.findById(collectionId, {
        fields: {metDataUpdateInfo: true, isMetaFieldsPropagationRequired: true},
      });

    let ongoingCal: boolean = collectionDetails.metDataUpdateInfo?.isOngoing ?? false;

    //if calculation is already calculated by cron or  calculation is ongoing, then skip the operation
    if (!collectionDetails.isMetaFieldsPropagationRequired || ongoingCal) return {skipOperation: true};

    //Loop will break, if meta data update request is not made during calculation
    while (true) {
      try {
        let updateDataInitial: Record<string, any> = {
          'metDataUpdateInfo.calculationStartAt': new Date(),
          'metDataUpdateInfo.isOngoing': true,
        };

        await this.metaDataRepository.updateManySet({_id: new ObjectId(collectionId)}, updateDataInitial, []);
        //calculate meta fields and tags summary and update in collection
        await this.handleMetaFieldsAndTagsSummaryPropagation(collectionId, false);
      } catch (err) {
        logger.error(
          `Calculate meta fields and tags summary | StatsCalculationService.calculateMetaFieldsAndTagsSummary | collection Id: ${collectionId} | Error in calculate meta fields and tag summary in meta update`,
          err,
        );
        break;
      }

      let collectionDetails = (await this.metaDataRepository.findById(collectionId, {
        fields: {metDataUpdateInfo: true},
      })) as Required<Pick<MetaData, 'metDataUpdateInfo'>>;

      let metDataUpdateInfo = collectionDetails.metDataUpdateInfo;
      //Check if meta data update request is made during calculation(if it's true, re calculate meta fields and tags summary)
      if (metDataUpdateInfo.calculationStartAt.getTime() >= metDataUpdateInfo.metaUpdateAt.getTime()) {
        break;
      }
    }

    //set isMetaFieldsPropagationRequired true, if update request is not made after calculation initiated
    let updateData: Record<string, any> = {
      updatedAt: new Date(),
      'metDataUpdateInfo.isOngoing': false,
      isMetaFieldsPropagationRequired: {
        $cond: {
          if: {$gte: ['$metDataUpdateInfo.metaUpdateAt', '$metDataUpdateInfo.calculationStartAt']},
          then: true,
          else: false,
        },
      },
    };

    await this.metaDataRepository.metaDataUpdateMany({_id: new ObjectId(collectionId)}, [{$set: updateData}], []);
    return {skipOperation: false};
  }

  /**
   * calculate meta fields and tags summary (stats, and distinct values) for system
   * Handler method
   * Calculate separately for main types
   */
  async handlePropagateMetaFieldsAndTagsStatsToSystem() {
    logger.debug(
      `Metadata field summary calculation | StatsCalculationService.handlePropagateMetaFieldsAndTagsStatsToSystem | system | Calculate for system initiated`,
    );
    // calculate meta fields and tags for images
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.IMAGE, 'images');

    // calculate meta fields and tags for videos
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.VIDEO, 'videos');

    // calculate meta fields and tags for image collections
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.IMAGE_COLLECTION, 'imageCollections');

    // calculate meta fields and tags for video collections
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.VIDEO_COLLECTION, 'videoCollections');

    // calculate meta fields and tags for datasets
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.DATASET, 'datasets');
    let datasetFrameQuery = {
      objectStatus: OBJECT_STATUS.ACTIVE,
      objectType: ContentType.DATASET,
    };
    await this.calculateMetaFieldsAndTagsStatsToSystemByQuery(
      datasetFrameQuery,
      ContentType.DATASET,
      'datasetsFrameData',
      true,
    );

    // calculate meta fields and tags for other
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.OTHER, 'other');

    // calculate meta fields and tags for other collections
    await this.calculateMetaFieldsAndTagsStatsToSystemContentType(ContentType.OTHER_COLLECTION, 'otherCollections');
  }

  // /**
  //  * calculate meta fields and tags analytics labelwise for system
  //  * Handler method
  //  * Calculate separately for main types
  //  */
  // async handlePropagateMetaFieldsAndTagsAnalyticsLabelwiseToSystem() {
  //   logger.debug(
  //     `Metadata field analytics labelwise calculation | StatsCalculationService.handlePropagateMetaFieldsAndTagsAnalyticsLabelwiseToSystem | system | Calculate for system initiated`,
  //   );
  //   // calculate meta fields and tags for images
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.IMAGE, 'images');

  //   // calculate meta fields and tags for videos
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.VIDEO, 'videos');

  //   // calculate meta fields and tags for image collections
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.IMAGE_COLLECTION, 'imageCollections');

  //   // calculate meta fields and tags for video collections
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.VIDEO_COLLECTION, 'videoCollections');

  //   // calculate meta fields and tags for datasets
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.IMAGE, 'datasets');
  //   let datasetFrameQuery = {
  //     objectStatus: OBJECT_STATUS.ACTIVE,
  //     objectType: ContentType.DATASET,
  //   };
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.DATASET, 'datasetsFrameData');

  //   // calculate meta fields and tags for other
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.OTHER, 'other');

  //   // calculate meta fields and tags for other collections
  //   await this.handleMetaDataAnalyticsLabelwiseSavingToSystem(ContentType.OTHER_COLLECTION, 'otherCollections');
  // }

  /**
   * calculate meta fields and tags summary (stats, and distinct values) for system
   * And save to system data
   * @param metaContentType
   * @returns
   */
  async calculateMetaFieldsAndTagsStatsToSystemContentType(
    metaContentType: ContentType,
    saveToType: keyof MetaFieldSummaryObjectTypeFormat,
  ) {
    let metaFieldsArray = await this.calculateMetaFieldStatsToSystem(metaContentType);
    let tagDataArray = await this.calculateMetaTagStatsToSystem(metaContentType);

    let metaFieldSummaryArray: MetaFieldAndArraySummaryFormat[] = [...metaFieldsArray, ...tagDataArray];

    // sort meta fields
    metaFieldSummaryArray.sort(this.sortMetaFieldSummaryObjByName);

    await this.saveMetaPrecalculationInSystemData(metaFieldSummaryArray, metaContentType, saveToType);

    return;
  }

  /**
   * calculate meta fields and tags summary (stats, and distinct values) for given query
   * applicable to system
   * @param dbQuery
   * @param contentType
   * @param saveToType
   * @param isGetChildren
   * @returns
   */
  async calculateMetaFieldsAndTagsStatsToSystemByQuery(
    dbQuery: any,
    contentType: ContentType,
    saveToType: keyof MetaFieldSummaryObjectTypeFormat,
    isGetChildren?: boolean,
  ) {
    let metaFieldsArray = await this.getMetaFieldStatsForQuery(dbQuery, contentType, undefined, isGetChildren);
    let tagDataArray = await this.getMetaTagStatsForQuery(dbQuery, contentType, isGetChildren);

    let metaFieldSummaryArray: MetaFieldAndArraySummaryFormat[] = [...metaFieldsArray, ...tagDataArray];

    // sort meta fields
    metaFieldSummaryArray.sort(this.sortMetaFieldSummaryObjByName);

    await this.saveMetaPrecalculationInSystemData(metaFieldSummaryArray, contentType, saveToType);

    return;
  }

  /**
   * Save precalculated metadata stats for system
   * @param metaFieldSummaryArray
   * @param metaContentType
   * @param saveToType
   */
  async saveMetaPrecalculationInSystemData(
    metaFieldSummaryArray: MetaFieldAndArraySummaryFormat[],
    metaContentType: ContentType,
    saveToType: keyof MetaFieldSummaryObjectTypeFormat,
  ) {
    // sort meta fields
    metaFieldSummaryArray.sort(this.sortMetaFieldSummaryObjByName);

    // save meta summary in system data
    let metaFieldSummaryKey = `metaDataFieldSummary.${saveToType ? saveToType : metaContentType}`;
    await this.systemDataRepository.updateOneSetData({}, {[metaFieldSummaryKey]: metaFieldSummaryArray}, []);
  }

  /**
   * Get custom fields which are dropdowns from system metadata
   * used in filtering metadata for stats
   * @returns
   */
  async getParentDropDownListOfSystem() {
    let dropdownParams = [{$match: {fieldType: InputFieldTypes.DROP_DOWN}}, {$project: {fieldName: 1}}];
    let dropdownFields = await this.metaFieldRepository.aggregate(dropdownParams);
    let dropDownFieldNameList = dropdownFields.map((field: any) => field.fieldName);
    return dropDownFieldNameList;
  }

  /**
   * calculate meta fields summary(stats, and distinct values) of a content type for system
   * @param metaContentType
   * @returns
   */
  async calculateMetaFieldStatsToSystem(metaContentType: ContentType) {
    // find all the dropdown fields
    let dropDownFieldNameList = await this.getParentDropDownListOfSystem();

    // get fields match
    let fieldParamsMatch = this.getMetaFieldSummaryMatchParams(dropDownFieldNameList);
    // get fields
    let fieldParams = [
      {
        $match: {
          objectType: metaContentType,
          objectStatus: OBJECT_STATUS.ACTIVE,
        },
      },
      ...fieldParamsMatch,
    ];
    let metaFieldsArray = await this.metaDataRepository.aggregate(fieldParams);
    return metaFieldsArray;
  }

  /**
   * calculate meta tags summary(stats, and distinct values) of a content type for system
   * @param metaContentType
   * @returns
   */
  async calculateMetaTagStatsToSystem(metaContentType: ContentType) {
    let tagMatchParams = this.getMetaTagSummaryMatchParams();
    let tagDataParams = [
      {
        $match: {
          objectType: metaContentType,
          objectStatus: OBJECT_STATUS.ACTIVE,
        },
      },
      ...tagMatchParams,
    ];

    let tagDataArray = await this.metaDataRepository.aggregate(tagDataParams);
    return tagDataArray;
  }

  // ----------------------------------------------------------------------
  // meta fields and stats retrieval ---

  /**
   * Format metadata field stats fro frontend
   * @param selectionFilter
   * @param teamId
   * @returns
   */
  async getMetaFieldStatsForFrontendChart(
    selectionFilter: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
  ) {
    const minTagCutoffPercentage = 0.02;
    const minTagAllowedCount = 100;
    let teamId = currentUserProfile.teamId;
    if (!teamId) {
      logger.error(
        `Metadata field summary calculation | StatsCalculationService.getMetaFieldStatsForFrontendChart | teamId not found`,
      );
      throw new HttpErrors.NotAcceptable('teamId not found');
    }

    let fieldStats: MetaFieldAndArraySummaryFormat[] =
      (await this.getMetaFieldsStats(selectionFilter, currentUserProfile)) ?? [];

    // calculate max values
    let maxCountVal = 0;
    for (let field of fieldStats) {
      if (field.count > maxCountVal) {
        maxCountVal = field.count;
      }
    }

    // filter out Tags with low count
    // let tagCountCutoff = Math.min(minTagAllowedCount, maxTagCountVal * minTagCutoffPercentage);
    let tagCountCutoff = maxCountVal * minTagCutoffPercentage;
    fieldStats = fieldStats.filter((field: MetaFieldAndArraySummaryFormat) => {
      if (field.count < tagCountCutoff) {
        return false;
      }
      return true;
    });

    // calculate count values
    let maxDataValue = 0;
    let metaDataCount = 0;
    let metaFieldCount = 0;
    for (let field of fieldStats) {
      if (field.count > maxDataValue) {
        maxDataValue = field.count;
      }
      metaDataCount += field.count;
      metaFieldCount += 1;
    }

    // format for frontend
    let chartData = fieldStats.map((field: MetaFieldAndArraySummaryFormat) => {
      let stackData: {label: string; value: number}[] = [];
      if (field.values && field.values.length > 0) {
        stackData = field.values.map((value: {value: string; count: number}) => {
          let stackDataObj = {
            label: value.value,
            value: value.count,
          };
          return stackDataObj;
        });
      }
      let mainLabelName = field._id;
      if (field._id == 'Tags') {
        mainLabelName = field.values[0].value;
        let stackValueName = `${field.values[0].value} (Tags)`;
        stackData[0].label = stackValueName;
      }
      let chartDataObj = {
        label: mainLabelName,
        value: field.count,
        stackData: stackData,
      };
      return chartDataObj;
    });

    return {
      chartTitle: 'metadataDistribution',
      maxDataValue: maxDataValue,
      metaFieldCount: metaFieldCount,
      metaDataCount: metaDataCount,
      chartData: chartData,
    };
  }

  /**
   * Handle getting metadata field stats
   * get from precalculated or calculate dynamically
   * @param selectionFilter
   * @param teamId
   * @returns
   */
  async getMetaFieldsStats(selectionFilter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let teamId = currentUserProfile.teamId;
    let detailsSource: {
      sourceType: DetailSource;
      approachMethod: DetailSource;
      id: string;
      contentType: ContentType;
    } = await this.getDetailsSource(selectionFilter, currentUserProfile);

    switch (detailsSource.approachMethod) {
      case DetailSource.SYSTEM_DATA:
        // then get system precalculated
        return await this.getMetaDataFieldStatsPreCalculated(undefined, selectionFilter.contentType);
        break;

      case DetailSource.METADATA:
        // can be a collection or a file
        let metDataObj = await this.metaDataRepository.findById(detailsSource.id);
        if (
          [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
            metDataObj.objectType,
          )
        ) {
          // then get the collection precalculated
          return await this.getMetaDataFieldStatsPreCalculated(detailsSource.id);
        } else {
          // then process dynamically
          let dbQuery = {
            _id: new ObjectId(detailsSource.id),
            objectType: metDataObj.objectType,
          };
          let dbQueryObject = {
            matchQuery: dbQuery,
            objectType: metDataObj.objectType,
          };
          return await this.getGetMetaFieldsStatsForQuery(dbQueryObject, selectionFilter.collectionId, true);
        }
        break;
      case DetailSource.DYNAMIC_QUERY:
        // then process dynamically
        // let dbQueryObject = await
        let datalakeSelection: Partial<DatalakeSelection> = {
          teamId: teamId,
          selectionRequest: selectionFilter,
          objectType: selectionFilter.contentType,
        };

        let dbQueryObject = await this.searchQueryBuilderService.getMatchQueryForSelection(
          currentUserProfile,
          undefined,
          datalakeSelection,
        );
        let lookupSection: any[] = [];
        // if (selectionFilter?.embeddingSelection?.selection && selectionFilter?.embeddingSelection?.graphId) {
        //   lookupSection = await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(
        //     selectionFilter.embeddingSelection,
        //   );
        // }
        if (!dbQueryObject) {
          throw new HttpErrors.UnprocessableEntity('Invalid selection query');
        }
        return await this.getGetMetaFieldsStatsForQuery(
          dbQueryObject,
          selectionFilter.collectionId,
          true,
          lookupSection,
        );
        break;
      default:
        throw new HttpErrors.UnprocessableEntity('Invalid selection query');
    }
  }

  /**
   * get metadata field stats from precalculated data
   * @param collectionId
   * @param contentType
   * @returns
   */
  async getMetaDataFieldStatsPreCalculated(collectionId?: string, contentType?: ContentType) {
    if (collectionId) {
      let collectionData = await this.metaDataRepository.findById(collectionId, {fields: {metaDataFieldSummary: true}});
      if (collectionData && collectionData.metaDataFieldSummary) {
        return collectionData.metaDataFieldSummary;
      } else {
        return [];
      }
    } else if (contentType) {
      const contentTypeNameMap = {
        [ContentType.IMAGE]: 'images',
        [ContentType.VIDEO]: 'videos',
        [ContentType.IMAGE_COLLECTION]: 'images',
        [ContentType.VIDEO_COLLECTION]: 'videos',
        [ContentType.DATASET]: 'datasetsFrameData',
        [ContentType.OTHER]: 'other',
        [ContentType.OTHER_COLLECTION]: 'other',
      };
      let contentTypeKey = contentTypeNameMap[
        contentType as keyof typeof contentTypeNameMap
      ] as keyof MetaFieldSummaryObjectTypeFormat;
      // get from the system
      let systemData = await this.systemDataRepository.findOne({}, {fields: {metaDataFieldSummary: true}});
      if (systemData && systemData.metaDataFieldSummary && systemData.metaDataFieldSummary[contentTypeKey]) {
        return systemData.metaDataFieldSummary[contentTypeKey];
      }
    } else {
      return [];
    }
  }

  /**
   * Calculate metadata field stats for a query
   * @param dbQueryObject
   * @param parentCollection
   * @param isGetChildren
   * @param lookupSection
   * @returns
   */
  async getGetMetaFieldsStatsForQuery(
    dbQueryObject: {matchQuery: any; objectType: ContentType},
    parentCollection?: string,
    isGetChildren?: boolean,
    lookupSection?: any[],
  ) {
    logger.debug(
      `Metadata field stats get | StatsCalculationService.getGetMetaFieldsStatsForQuery | N/A | Meta stats for query initiated`,
    );
    let metaFieldsPromise = this.getMetaFieldStatsForQuery(
      dbQueryObject.matchQuery,
      dbQueryObject.objectType,
      parentCollection,
      isGetChildren,
      lookupSection,
    );
    let tagDataPromise = this.getMetaTagStatsForQuery(
      dbQueryObject.matchQuery,
      dbQueryObject.objectType,
      isGetChildren,
      lookupSection,
    );

    let [metaFieldsArray, tagDataArray] = await Promise.all([metaFieldsPromise, tagDataPromise]);

    let metaFieldSummaryObj: MetaFieldAndArraySummaryFormat[] = [...metaFieldsArray, ...tagDataArray];

    // sort meta fields
    metaFieldSummaryObj.sort(this.sortMetaFieldSummaryObjByName);

    return metaFieldSummaryObj;
  }

  /**
   * Generate match query to match children of a given query
   * @param parentQuery
   * @param contentType
   * @returns
   */
  async generateChildQueryForParentQuery(parentQuery: any, contentType: ContentType) {
    let childType = ContentType.IMAGE; // for image collection and dataset

    if (contentType == ContentType.VIDEO_COLLECTION) childType = ContentType.VIDEO;
    else if (contentType == ContentType.OTHER_COLLECTION) childType = ContentType.OTHER;

    let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
      {
        $match: parentQuery,
      },
      {
        $project: {_id: 1},
      },
    ]);

    let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));

    let matchQuery = {
      vCollectionIdList: {$in: collectionObjectIdList},
      objectStatus: OBJECT_STATUS.ACTIVE,
      objectType: childType,
    };

    return matchQuery;
  }

  /**
   * Calculate metada field stats for a query
   * @param dbQuery
   * @param contentType
   * @param parentCollection
   * @param isGetChildren
   * @param lookupSection
   * @returns
   */
  async getMetaFieldStatsForQuery(
    dbQuery: any,
    contentType: ContentType,
    parentCollection?: string,
    isGetChildren?: boolean,
    lookupSection?: any[],
  ) {
    let parentDropDownList: string[] = [];
    // if (parentCollection) {
    //   parentDropDownList = await this.getParentDropDownListOfCollection(parentCollection);
    // } else {
    //   parentDropDownList = await this.getParentDropDownListOfSystem();
    // }
    parentDropDownList = await this.getParentDropDownListOfSystem();

    // get fields match
    let fieldParamsMatch = this.getMetaFieldSummaryMatchParams(parentDropDownList);

    // generate match query
    let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

    if (
      isGetChildren &&
      [
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.OTHER_COLLECTION,
        ContentType.DATASET,
      ].includes(contentType)
    ) {
      let childMatchQuery = await this.generateChildQueryForParentQuery(dbQuery, contentType);
      matchQueryParams = [{$match: childMatchQuery}];
    }

    // get fields
    let fieldParams: any[] = [];
    if (Array.isArray(matchQueryParams) && matchQueryParams.length > 0) fieldParams.push(...matchQueryParams);
    if (Array.isArray(lookupSection) && lookupSection.length > 0) fieldParams.push(...lookupSection);
    if (Array.isArray(fieldParamsMatch) && fieldParamsMatch.length > 0) fieldParams.push(...fieldParamsMatch);

    let metaFieldsArray = await this.metaDataRepository.aggregate(fieldParams);
    return metaFieldsArray;
  }

  /**
   * Calculate meta tags stats for a query
   * @param dbQuery
   * @param contentType
   * @param isGetChildren
   * @param lookupSection
   * @returns
   */
  async getMetaTagStatsForQuery(
    dbQuery: any,
    contentType: ContentType,
    isGetChildren?: boolean,
    lookupSection?: any[],
  ) {
    let tagMatchParams = this.getMetaTagSummaryMatchParams();

    let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

    if (
      isGetChildren &&
      [
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.OTHER_COLLECTION,
        ContentType.DATASET,
      ].includes(contentType)
    ) {
      let childMatchQuery = await this.generateChildQueryForParentQuery(dbQuery, contentType);
      matchQueryParams = [{$match: childMatchQuery}];
    }

    let tagDataParams: any[] = [];
    if (Array.isArray(matchQueryParams) && matchQueryParams.length > 0) tagDataParams.push(...matchQueryParams);
    if (Array.isArray(lookupSection) && lookupSection.length > 0) tagDataParams.push(...lookupSection);
    if (Array.isArray(tagMatchParams) && tagMatchParams.length > 0) tagDataParams.push(...tagMatchParams);
    let tagDataArray = await this.metaDataRepository.aggregate(tagDataParams);
    return tagDataArray;
  }

  /**
   * Sort method to sort meta fields by frame count
   * @param a
   * @param b
   * @returns
   */
  private sortMetaFieldSummaryObjByName(a: MetaFieldAndArraySummaryFormat, b: MetaFieldAndArraySummaryFormat) {
    let sortKeyA = a._id;
    let sortKeyB = b._id;
    if (a._id == 'Tags') {
      sortKeyA = a.values[0].value;
    }
    if (b._id == 'Tags') {
      sortKeyB = b.values[0].value;
    }
    // if (sortKeyA < sortKeyB) {
    //   return -1;
    // } else if (sortKeyA > sortKeyB) {
    //   return 1;
    // } else {
    //   return 0;
    // }
    return sortKeyA.localeCompare(sortKeyB);
  }

  // async filterMetaFieldStatsForQuery(dbQueryObject: {matchQuery: any, objectType: ContentType}, parentCollection?: string) {
  //   let parentMetadataSummaryList: MetaFieldAndArraySummaryFormat[] = []
  //   // get the applicable Tag and metadata fields values in the parent
  //   if (parentCollection) {
  //     let parentMetadataSummary = await this.metaDataRepository.findById(parentCollection, {fields: {metaDataFieldSummary: true}});
  //     if (parentMetadataSummary && parentMetadataSummary.length > 0) {
  //       parentMetadataSummaryList = parentMetadataSummary.metaDataFieldSummary || []
  //     }
  //   }
  //   else {
  //     let systemData = await this.systemDataRepository.findOne({}, {fields: {metaDataFieldSummary: true}});
  //     if (systemData && systemData.metaDataFieldSummary) {
  //       let parentMetadataSummary = systemData.metaDataFieldSummary[dbQueryObject.objectType]
  //       if (parentMetadataSummary && parentMetadataSummary.length > 0) {
  //         parentMetadataSummaryList = parentMetadataSummary
  //       }
  //     }
  //   }

  //   // get applicable tags and metadata fields

  // }

  // async getMetaFieldsForQuery(dbQueryObject: {matchQuery: any, objectType: ContentType}, fieldNameList:string[]) {
  //   let fieldParams:any = [
  //     {$match: dbQueryObject.matchQuery},
  //   ]

  //   let facetParams:any = {}
  //   for(let fieldName of fieldNameList) {
  //     //
  //     let fieldNameString = `customMeta.${fieldName}`
  //     facetParams[fieldName] = [
  //       {$match:{[fieldNameString]: {$exists: true}}},
  //     ]
  //   }
  // }

  // async getMetaTagsOfQuery(dbQueryObject: {matchQuery: any; objectType: ContentType}) {
  //   let distinctTags = await this.metaDataRepository.distinct('Tags', dbQueryObject.matchQuery);
  //   return distinctTags;
  // }

  // async getMetaFieldsOfQuery(dbQueryObject: {matchQuery: any; objectType: ContentType}) {
  //   function generateFacetParams(fieldName: string) {
  //     let fieldNameString = `customMeta.${fieldName}`;
  //     let faceParam = [{$match: {[fieldNameString]: {$exists: true}}}, {limit: 1}, {$project: {_id: 1}}];
  //   }
  // }

  // -------------------------------------------------------------------------------------------------
  // analytics - labelWise stat -----------------------

  // Fields processing

  // /**
  //  * Part of aggregate pipeline to find distinct metadata grouped labelwise with counts
  //  * @param dropDownFieldNameList
  //  * @returns
  //  */
  // getMetaFieldAnalyticsLabelwiseMatchParams(dropDownFieldNameList: string[]) {
  //   const metaFieldMaxLimit = 10;
  //   let fieldParams = [
  //     {
  //       $project: {
  //         labelList: 1,
  //         keyValueArray: {
  //           $objectToArray: '$customMeta',
  //         },
  //       },
  //     },
  //     {
  //       $unwind: '$labelList',
  //     },
  //     {
  //       $unwind: '$keyValueArray',
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           key: '$keyValueArray.k',
  //           value: '$keyValueArray.v',
  //           label: '$labelList.label',
  //         },
  //         frameCount: {
  //           $sum: 1,
  //         },
  //         labelCount: {
  //           $sum: '$labelList.count',
  //         },
  //       },
  //     },
  //     {
  //       $group: {
  //         _id: {
  //           key: '$_id.key',
  //           label: '$_id.label',
  //         },
  //         values: {
  //           $push: {
  //             value: '$_id.value',
  //             valueFrameCount: '$frameCount',
  //             valueLabelCount: '$labelCount',
  //           },
  //         },
  //         keyFrameCount: {
  //           $sum: '$frameCount',
  //         },
  //         keyLabelCount: {
  //           $sum: '$labelCount',
  //         },
  //         distinctValues: {
  //           $sum: 1,
  //         },
  //       },
  //     },
  //     {
  //       $match: {
  //         $expr: {
  //           $and: [
  //             {$gt: [{$size: '$values'}, 0]},
  //             {
  //               $or: [
  //                 {$lte: [{$size: '$values'}, metaFieldMaxLimit]}, // either less than max limit
  //                 {$in: ['$_id.key', dropDownFieldNameList]}, // or a dropdown
  //               ],
  //             },
  //           ],
  //         },
  //       },
  //     },
  //     {
  //       $group: {
  //         _id: '$_id.label',
  //         keys: {
  //           $push: {
  //             key: '$_id.key',
  //             values: '$values',
  //             keyFrameCount: '$keyFrameCount',
  //             keyLabelCount: '$keyLabelCount',
  //             distinctValues: '$distinctValues',
  //           },
  //         },
  //         labelFrameCount: {
  //           $sum: '$keyFrameCount',
  //         },
  //         labelLabelCount: {
  //           $sum: '$keyLabelCount',
  //         },
  //         distinctKeys: {
  //           $sum: 1,
  //         },
  //       },
  //     },
  //   ];
  //   return fieldParams;
  // }

  // /**
  //  *
  //  * @param dbQuery
  //  * @param contentType
  //  * @param isGetChildren
  //  * @returns
  //  */
  // async getMetaFieldAnalyticsLabelwiseForQuery(dbQuery: any, contentType: ContentType, isGetChildren?: boolean) {
  //   let parentDropDownList: string[] = [];
  //   parentDropDownList = await this.getParentDropDownListOfSystem();

  //   // get fields match
  //   let fieldParamsMatch = this.getMetaFieldAnalyticsLabelwiseMatchParams(parentDropDownList);

  //   // generate match query
  //   let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

  //   if (
  //     isGetChildren &&
  //     [
  //       ContentType.IMAGE_COLLECTION,
  //       ContentType.VIDEO_COLLECTION,
  //       ContentType.OTHER_COLLECTION,
  //       ContentType.DATASET,
  //     ].includes(contentType)
  //   ) {
  //     let childMatchQuery = await this.generateChildQueryForParentQuery(dbQuery, contentType);
  //     matchQueryParams = [{$match: childMatchQuery}];
  //   }

  //   // get fields
  //   let fieldParams = [...matchQueryParams, ...fieldParamsMatch];
  //   let metaFieldsArray = await this.metaDataRepository.aggregate(fieldParams);
  //   return metaFieldsArray;
  // }

  // Tags processing

  // /**
  //  * Part of aggregate pipeline to find distinct meta tags labelwise with counts
  //  * @returns
  //  */
  // getMetaTagAnalyticsLabelwiseMatchParams() {
  //   let tagDataParams = [
  //     {$unwind: '$Tags'},
  //     {$unwind: '$labelList'},
  //     {
  //       $group: {
  //         _id: {
  //           label: '$labelList.label',
  //           tag: '$Tags',
  //         },
  //         frameCount: {
  //           $sum: 1,
  //         },
  //         labelCount: {
  //           $sum: '$labelList.count',
  //         },
  //       },
  //     },
  //     {
  //       $group: {
  //         _id: '$_id.label',
  //         keys: {
  //           $push: {
  //             key: 'Tag',
  //             values: [
  //               {
  //                 value: '$_id.tag',
  //                 valueFrameCount: '$frameCount',
  //                 valueLabelCount: '$labelCount',
  //               },
  //             ],
  //             keyFrameCount: '$frameCount',
  //             keyLabelCount: '$labelCount',
  //           },
  //         },
  //         labelFrameCount: {$sum: '$frameCount'},
  //         labelLabelCount: {$sum: '$labelCount'},
  //       },
  //     },
  //   ];
  //   return tagDataParams;
  // }

  // async getMetaTagAnalyticsLabelwiseQuery(dbQuery: any, contentType: ContentType, isGetChildren?: boolean) {
  //   let tagMatchParams = this.getMetaTagAnalyticsLabelwiseMatchParams();

  //   let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

  //   if (
  //     isGetChildren &&
  //     [
  //       ContentType.IMAGE_COLLECTION,
  //       ContentType.VIDEO_COLLECTION,
  //       ContentType.OTHER_COLLECTION,
  //       ContentType.DATASET,
  //     ].includes(contentType)
  //   ) {
  //     let childMatchQuery = await this.generateChildQueryForParentQuery(dbQuery, contentType);
  //     matchQueryParams = [{$match: childMatchQuery}];
  //   }

  //   let tagDataParams = [...matchQueryParams, ...tagMatchParams];

  //   let tagDataArray = await this.metaDataRepository.aggregate(tagDataParams);
  //   return tagDataArray;
  // }

  // /**
  //  * Part of aggregate pipeline to find distinct meta tags labelwise with counts
  //  * @returns
  //  */
  // private getLabelCountsAnalyticsLabelwiseMatchParams() {
  //   let tagDataParams = [
  //     {
  //       $project: {labelList: 1},
  //     },
  //     {$unwind: '$labelList'},
  //     {
  //       $group: {
  //         _id: '$labelList.label',
  //         labelCount: {$sum: '$labelList.count'},
  //         frameCount: {$sum: 1},
  //       },
  //     },
  //   ];
  //   return tagDataParams;
  // }

  // /**
  //  * get total box count and frame count of labels for a query
  //  * @param dbQuery
  //  * @param contentType
  //  * @param isGetChildren
  //  * @returns
  //  */
  // async getLabelWiseAnalyticsLabelCounts(dbQuery: any, contentType: ContentType, isGetChildren?: boolean) {
  //   let labelHeadMatchParams = this.getLabelCountsAnalyticsLabelwiseMatchParams();

  //   let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

  //   if (
  //     isGetChildren &&
  //     [
  //       ContentType.IMAGE_COLLECTION,
  //       ContentType.VIDEO_COLLECTION,
  //       ContentType.OTHER_COLLECTION,
  //       ContentType.DATASET,
  //     ].includes(contentType)
  //   ) {
  //     let childMatchQuery = await this.generateChildQueryForParentQuery(dbQuery, contentType);
  //     matchQueryParams = [{$match: childMatchQuery}];
  //   }

  //   let labelDataParams = [...matchQueryParams, ...labelHeadMatchParams];

  //   logger.debug(`--------------------------------------------`)
  //   logger.debug(`labelDataParams: \n${JSON.stringify(labelDataParams)}`)
  //   logger.debug(`--------------------------------------------`)

  //   let labelDataArray = await this.metaDataRepository.aggregate(labelDataParams);
  //   return labelDataArray;
  // }

  /**
   * Handle getting and formatting metadata analytics labelwise - label headers
   * @param selectionFilter
   * @param teamId
   * @returns
   */
  async getLabelWiseAnalyticsLabelHeadersForFrontend(
    selectionFilter: DatalakeSelectionRequestForAnalytics,
    currentUserProfile: UserProfileDetailed,
  ) {
    let chartLabelHeaderData: LabelAnalyticsHeadCountChartFormat[] = [];
    let labelHeaderDataObject = await this.getFrameLevelStats(selectionFilter, currentUserProfile);
    let labelListDataArray = labelHeaderDataObject.labelStats.labelList;

    let totalLabelCount = 0;

    for (let label of labelListDataArray) {
      totalLabelCount += label.value;
      let labelData: LabelAnalyticsHeadCountChartFormat = {
        _id: label._id,
        labelCount: label.value,
        labelText: label.label,
        percentage: 0,
      };
      chartLabelHeaderData.push(labelData);
    }

    // calculate percentages
    if (totalLabelCount > 0) {
      for (let label of chartLabelHeaderData) {
        label.percentage = (label.labelCount * 100) / totalLabelCount;
      }
      // filter by percentage
      const percentageCutoff = 2;
      chartLabelHeaderData = chartLabelHeaderData.filter(label => label.percentage > percentageCutoff);
    }

    // sort label header data
    chartLabelHeaderData.sort((a, b) => b.labelCount - a.labelCount);

    // apply filter
    if (
      selectionFilter.chartFilterData &&
      selectionFilter.chartFilterData.labels &&
      selectionFilter.chartFilterData.labels.length > 0
    ) {
      chartLabelHeaderData = this.filterMetadataLabelwiseAnalyticsChartData(
        chartLabelHeaderData,
        selectionFilter.chartFilterData,
      );
    }

    return {
      chartHeaders: chartLabelHeaderData,
      // maxValue: labelHeaderDataObject.labelStats.maxDataValue,
      maxValue: totalLabelCount,
    };
  }

  /**
   * Filter analytics chart labels
   * @param inputArray
   * @param filter
   * @returns
   */
  private filterMetadataLabelwiseAnalyticsChartData(
    inputArray: LabelAnalyticsHeadCountChartFormat[],
    filter: ExplorerFilterV2,
  ) {
    let filteredArray = inputArray.slice();
    let labelArray: string[] = [];
    if (filter && filter.labels && filter.labels.length > 0) {
      labelArray = filter.labels;
      filteredArray = filteredArray.filter((labelObj: LabelAnalyticsHeadCountChartFormat) => {
        return labelArray.includes(labelObj.labelText);
      });
    }
    return filteredArray;
  }

  // private sortMetaFieldAnalyticsArrayByLabelCount(inputArray: MetaFieldAndTagAnalyticsLabelwiseFormat[]) {
  //   let sortedArray = inputArray.slice();
  //   if (sortedArray && sortedArray.length > 0) {
  //     // sort label level
  //     sortedArray.sort((a, b) => b.labelLabelCount - a.labelLabelCount);

  //     // sort key level
  //     for (let labelObj of sortedArray) {
  //       if (labelObj.keys && labelObj.keys.length > 0) {
  //         labelObj.keys.sort((a, b) => b.keyLabelCount - a.keyLabelCount);

  //         // sort value level
  //         for (let keyObj of labelObj.keys) {
  //           if (keyObj.values && keyObj.values.length > 0) {
  //             keyObj.values.sort((a, b) => b.valueLabelCount - a.valueLabelCount);
  //           }
  //         }
  //       }
  //     }
  //   }

  //   return sortedArray;
  // }

  // private mergeAndSortMetadataAnalyticsLabelwiseObjects(
  //   inputArray: MetaFieldAndTagAnalyticsLabelwiseFormat[],
  //   countArray: LabelWiseAnalyticsLabelCountFormat[],
  //   labelToLabelTextMap: Record<string, string>,
  // ) {
  //   let mergedObjectsMap = new Map();

  //   // merge fields and tags
  //   for (let obj of inputArray) {
  //     // Add label text to the object
  //     obj.labelText = labelToLabelTextMap[obj._id];

  //     let existingObj = mergedObjectsMap.get(obj._id);

  //     if (existingObj) {
  //       // Merge the keys arrays
  //       existingObj.keys.push(...obj.keys);

  //       // Update label counts and frame counts
  //       // existingObj.labelFrameCount += obj.labelFrameCount;
  //       // existingObj.labelLabelCount += obj.labelLabelCount;
  //     } else {
  //       // If object doesn't exist, add it to the map
  //       mergedObjectsMap.set(obj._id, {...obj});
  //     }
  //   }

  //   // Add counts from the countArray
  //   for (let countObj of countArray) {
  //     let existingObj = mergedObjectsMap.get(countObj._id);

  //     if (existingObj) {
  //       existingObj.labelLabelCount = countObj.labelCount;
  //       existingObj.labelFrameCount = countObj.frameCount;
  //     }
  //   }

  //   // Convert the map values back to an array
  //   let mergedObjectsArray = Array.from(mergedObjectsMap.values());

  //   // Sort the objects by _id
  //   const sortedArray = this.sortMetaFieldAnalyticsArrayByLabelCount(mergedObjectsArray);

  //   return sortedArray;
  //   // return mergedObjectsArray;
  // }

  // async getMetaFieldsAnalyticsForQuery(
  //   dbQueryObject: {matchQuery: any; objectType: ContentType},
  //   teamId?: string,
  //   isGetChildren?: boolean,
  // ) {
  //   logger.debug(
  //     `Metadata field analytics get | StatsCalculationService.getMetaFieldsAnalyticsForQuery | N/A | Meta field analytics for query initiated`,
  //   );
  //   let metaFieldsPromise = this.getMetaFieldAnalyticsLabelwiseForQuery(
  //     dbQueryObject.matchQuery,
  //     dbQueryObject.objectType,
  //     isGetChildren,
  //   );
  //   let tagDataPromise = this.getMetaTagAnalyticsLabelwiseQuery(
  //     dbQueryObject.matchQuery,
  //     dbQueryObject.objectType,
  //     isGetChildren,
  //   );
  //   let labelCountPromise = this.getLabelWiseAnalyticsLabelCounts(
  //     dbQueryObject.matchQuery,
  //     dbQueryObject.objectType,
  //     isGetChildren,
  //   );

  //   let [metaFieldsArray, tagDataArray, labelCountArray] = await Promise.all([
  //     metaFieldsPromise,
  //     tagDataPromise,
  //     labelCountPromise,
  //   ]);

  //   let metaFieldSummaryObj: MetaFieldAndTagAnalyticsLabelwiseFormat[] = [...metaFieldsArray, ...tagDataArray];

  //   let labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId);

  //   let mergedObjectsArray = this.mergeAndSortMetadataAnalyticsLabelwiseObjects(
  //     metaFieldSummaryObj,
  //     labelCountArray,
  //     labelToLabelTextMap,
  //   );

  //   return mergedObjectsArray;
  // }

  // -- caller methods

  // async getMetaFieldAnalyticsLabelwiseForFrontendChart(selectionFilter: DatalakeSelectionRequest, teamId: string) {
  //   const minTagCutoffPercentage = 0.01;
  //   const minTagAllowedCount = 100;

  //   let rawData: MetaFieldAndTagAnalyticsLabelwiseFormat[] = await this.getMetaFieldsAnalyticsLabelwise(
  //     selectionFilter,
  //     teamId,
  //   );

  //   logger.debug(
  //     `Metadata field analytics get | StatsCalculationService.getMetaFieldAnalyticsLabelwiseForFrontendChart | N/A | Meta field analytics for query data fetched`,
  //   );
  //   // return rawData;

  //   let mainLabels: string[] = [];
  //   let boldLabels: string[] = [];

  //   let labelClassStylePercentages: number[] = [];
  //   let labelClassStylePercentageLabels: string[] = [];
  //   let labelClassStyleData: number[] = [];

  //   let dynamicStylePercentages: number[][] = [];
  //   let dynamicStylePercentageLabels: string[][] = [];
  //   let dynamicStyleData: number[][] = [];

  //   let tagStylePercentages: number[] = [];
  //   let tagStylePercentageLabels: string[] = [];
  //   let tagStyleData: number[] = [];

  //   let labelStyleColor: string[] = ['bg-pattern', 'no-hover', 'rgba(153, 146, 251, 1)', 'rgba(101, 91, 224, 1)'];
  //   let dynamicStyleColors: string[][] = [];
  //   let tagStyleColor: string[] = ['bg-static', 'hover-static', 'rgba(212, 209, 253, 1)', 'rgba(226, 224, 253, 1)'];

  //   let totalLabelCount = 0;
  //   let numberOfDynamicStyles = 0;
  //   for (let _label of rawData) {
  //     //max value of key length will be the numberOfDynamicStyles
  //     for (let _key of _label.keys) {
  //       if (_key.values.length > numberOfDynamicStyles) {
  //         numberOfDynamicStyles = _key.values.length;
  //       }
  //     }

  //     totalLabelCount += _label.labelLabelCount;
  //   }

  //   //initialize dynamic styles arrays
  //   for (let i = 0; i < numberOfDynamicStyles; i++) {
  //     dynamicStylePercentages.push([]);
  //     dynamicStylePercentageLabels.push([]);
  //     dynamicStyleData.push([]);

  //     //push dynamic style colors
  //     let dynamicStyleColor: string[] = [];
  //     dynamicStyleColor.push('bg-static');
  //     dynamicStyleColor.push('hover-pattern');
  //     let opacity = 1 - ((1 - 0.6) / Math.max(1, numberOfDynamicStyles - 1)) * i;
  //     dynamicStyleColor.push(`rgba(212, 209, 253, ${parseFloat(opacity.toFixed(2))})`);
  //     dynamicStyleColor.push('rgba(45, 40, 99, 1)');
  //     dynamicStyleColors.push(dynamicStyleColor);
  //   }

  //   //iterate through the data and populate the arrays
  //   for (let _label of rawData) {
  //     mainLabels.push(_label.labelText || ''); //push label class to main labels
  //     boldLabels.push(_label.labelText || ''); //push label class to bold labels

  //     labelClassStyleData.push(_label.labelLabelCount); //push label count to label class style data and zero to other style data
  //     labelClassStylePercentages.push(parseFloat(((_label.labelLabelCount / totalLabelCount) * 100).toFixed(2))); //push label class percentage to label class style percentages
  //     labelClassStylePercentageLabels.push(_label.labelText || ''); //push label class percentage label to label class style percentages labels
  //     for (let i = 0; i < numberOfDynamicStyles; i++) {
  //       dynamicStyleData[i].push(0);
  //       dynamicStylePercentages[i].push(0);
  //       dynamicStylePercentageLabels[i].push('');
  //     }
  //     tagStyleData.push(0);
  //     tagStylePercentages.push(0);
  //     tagStylePercentageLabels.push('');

  //     for (let _key of _label.keys) {
  //       mainLabels.push(_key.key); //push mata field to main labels
  //       // boldLabels.push(''); //push empty string to bold labels for each meta field
  //       labelClassStyleData.push(0); //push zero to label class style data for each meta field
  //       labelClassStylePercentages.push(0); //push zero to label class style percentages for each meta field
  //       labelClassStylePercentageLabels.push(''); //push empty string to label class style percentages labels for each meta field

  //       if (_key.key === 'Tag') {
  //         for (let i = 0; i < numberOfDynamicStyles; i++) {
  //           dynamicStyleData[i].push(0); //push zero to style data for each meta field
  //           dynamicStylePercentages[i].push(0); //push zero to style percentages for each meta field
  //           dynamicStylePercentageLabels[i].push(''); //push empty string to dynamic style percentages labels
  //         }
  //         tagStyleData.push(_key.values[0].valueLabelCount); //push meta value count to tag style data
  //         tagStylePercentages.push(
  //           parseFloat(((_key.values[0].valueLabelCount / _key.keyLabelCount) * 100).toFixed(2)),
  //         ); //push meta value percentage to tag style percentages
  //         tagStylePercentageLabels.push(_key.values[0].value); //push meta value to tag style percentages labels
  //       } else {
  //         for (const [idx, _value] of _key.values.entries()) {
  //           dynamicStyleData[idx].push(_value.valueLabelCount); //push meta value count to dynamic style data
  //           dynamicStylePercentages[idx].push(
  //             parseFloat(((_value.valueLabelCount / _key.keyLabelCount) * 100).toFixed(2)),
  //           ); //push meta value percentage to dynamic style percentages
  //           dynamicStylePercentageLabels[idx].push(_value.value); //push meta value to dynamic style percentages labels
  //         }
  //         tagStyleData.push(0); //push empty string to tag style data for each meta field
  //         tagStylePercentages.push(0); //push empty string to tag style percentages for each meta field
  //         tagStylePercentageLabels.push(''); //push empty string to tag style percentages labels for each meta field
  //       }
  //     }

  //     mainLabels.push(''); //push empty string to main labels for each label
  //     // boldLabels.push(''); //push empty string to bold labels for each label
  //     labelClassStyleData.push(0); //push zero to style data for each label
  //     labelClassStylePercentages.push(0); //push zero to style percentages for each label
  //     labelClassStylePercentageLabels.push(''); //push empty string to label class style percentages labels for each label
  //     for (let i = 0; i < numberOfDynamicStyles; i++) {
  //       dynamicStyleData[i].push(0);
  //       dynamicStylePercentages[i].push(0);
  //       dynamicStylePercentageLabels[i].push('');
  //     }
  //     tagStyleData.push(0);
  //     tagStylePercentages.push(0);
  //     tagStylePercentageLabels.push('');
  //   }

  //   return {
  //     mainLabels: mainLabels,
  //     boldLabels: boldLabels,
  //     percentages: [labelClassStylePercentages, ...dynamicStylePercentages, tagStylePercentages],
  //     percentageLabels: [labelClassStylePercentageLabels, ...dynamicStylePercentageLabels, tagStylePercentageLabels],
  //     data: [labelClassStyleData, ...dynamicStyleData, tagStyleData],
  //     colors: [labelStyleColor, ...dynamicStyleColors, tagStyleColor],
  //   };
  // }

  // async getMetaFieldsAnalyticsLabelwise(selectionFilter: DatalakeSelectionRequest, teamId: string) {

  //   // --------------------------------------------------
  //   let datalakeSelection: Partial<DatalakeSelection> = {
  //     teamId: teamId,
  //     selectionRequest: selectionFilter,
  //     objectType: selectionFilter.contentType,
  //   };

  //   let dbQueryObject = await this.searchQueryBuilderService.getMatchQueryForSelection(undefined, datalakeSelection);

  //   if (!dbQueryObject) {
  //     throw new HttpErrors.UnprocessableEntity('Invalid selection query');
  //   }
  //   return await this.getMetaFieldsAnalyticsForQuery(dbQueryObject, teamId, true);
  // }

  // -------------------------- metadata analytics labelwise precalculation --------

  // async handleMetaAnalyticsLabelwisePrecalculationInCollectionHead(collectionId: string) {
  //   let collectionData = await this.metaDataRepository.findById(collectionId);
  //   let contentType = this.determineChildrenContentTypeByParentTypeForAnalytics(collectionData.objectType);
  //   let dbQuery = {vCollectionIdList: new ObjectId(collectionId), objectStatus: OBJECT_STATUS.ACTIVE};
  //   let dbQueryObject = {
  //     matchQuery: dbQuery,
  //     objectType: contentType,
  //   };
  //   let metaFieldSummaryArray: MetaFieldAndTagAnalyticsLabelwiseFormat[] = await this.getMetaFieldsAnalyticsForQuery(
  //     dbQueryObject,
  //   );

  //   return metaFieldSummaryArray;
  // }

  // async handleMetaDataAnalyticsLabelwiseSavingToSystem(
  //   contentType: ContentType,
  //   saveToType: keyof MetaFieldSummaryObjectTypeFormat,
  // ) {
  //   let dbQuery = {objectType: contentType, objectStatus: OBJECT_STATUS.ACTIVE};
  //   let dbQueryObject = {
  //     matchQuery: dbQuery,
  //     objectType: contentType,
  //   };
  //   let metaFieldSummaryObj: MetaFieldAndTagAnalyticsLabelwiseFormat[] = await this.getMetaFieldsAnalyticsForQuery(
  //     dbQueryObject,
  //   );

  //   await this.saveMetaAnalyticsLabelwisePrecalculationInSystemData(metaFieldSummaryObj, saveToType);
  // }

  // async saveMetaAnalyticsLabelwisePrecalculationInSystemData(
  //   metaFieldSummaryArray: MetaFieldAndTagAnalyticsLabelwiseFormat[],
  //   saveToType: keyof MetaFieldSummaryObjectTypeFormat,
  // ) {
  //   // save meta summary in system data
  //   let metaFieldSummaryKey = `metaDataFieldAnalyticsLabelwise.${saveToType}`;
  //   await this.systemDataRepository.updateOneSetData({}, {[metaFieldSummaryKey]: metaFieldSummaryArray}, []);
  // }

  //------------------------------------------------------------------------

  /**
   * Get metadata analytics for selected label
   * @param selectionFilter
   * @param teamId
   * @returns
   */
  async getOneLabelAnalyticsMetadataForFrontend(
    selectionFilter: DatalakeSelectionRequestForAnalytics,
    currentUserProfile: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile.teamId;
    let datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: selectionFilter,
      objectType: selectionFilter.contentType,
    };

    // let timePoint1 = new Date().getTime();

    let dbQueryObject = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );

    if (!dbQueryObject) {
      throw new HttpErrors.UnprocessableEntity('Invalid selection query');
    }

    let labelMatchParams: any[] = [];

    if (!selectionFilter.labelId) {
      throw new HttpErrors.UnprocessableEntity('Invalid label id');
    }

    let labelAnalyticsMetadata = await this.getOneLabelMetaFieldsAnalyticsForQuery(
      selectionFilter.labelId,
      dbQueryObject,
    );

    let formattedLabelAnalyticsMetadata: OneLabelMetadataAnalyticsFrontendFormat[] = [];

    // calculate percentages and format data
    let maxKeyLabelCount = 0;
    let totalKeyLabelCount = 0;
    for (let key of labelAnalyticsMetadata) {
      if (key.keyLabelCount > maxKeyLabelCount) {
        maxKeyLabelCount = key.keyLabelCount;
      }
      totalKeyLabelCount += key.keyLabelCount;
      // format value level
      let stackData: OneLabelMetadataAnalyticsFrontendValueStackFormat[] = [];
      let maxValLabelCount = 0;
      let totalValLabelCount = 0;
      for (let value of key.values) {
        if (value.valueLabelCount > maxValLabelCount) {
          maxValLabelCount = value.valueLabelCount;
        }
        totalValLabelCount += value.valueLabelCount;
        let labelString = value.value;
        if (key.key == 'Tag') {
          labelString = `${value.value} (Tag)`;
        }
        let stackValueItem: OneLabelMetadataAnalyticsFrontendValueStackFormat = {
          label: labelString,
          value: value.valueLabelCount,
          percentage: 0,
        };
        stackData.push(stackValueItem);
      }
      // // calculate percentages
      // if (maxValLabelCount > 0) {
      //   for (let stackItem of stackData) {
      //     stackItem.percentage = (stackItem.value * 100) / maxValLabelCount;
      //   }
      // }
      let formattedKey: OneLabelMetadataAnalyticsFrontendFormat = {
        labelName: key._id,
        stackData: stackData,
      };
      formattedLabelAnalyticsMetadata.push(formattedKey);
    }

    let formattedPercentageFilteredLabelAnalyticsMetadata: OneLabelMetadataAnalyticsFrontendFormat[] = [];
    const percentageCutoff = 2;
    // calculate percentages
    let totalLabelBoxCount = await this.getOneLabelBoxCountForQuery(
      selectionFilter.labelId,
      dbQueryObject.matchQuery,
      dbQueryObject.objectType,
    );
    if (totalLabelBoxCount && totalLabelBoxCount > 0) {
      for (let key of formattedLabelAnalyticsMetadata) {
        let totalPerc = 0;
        for (let stackItem of key.stackData) {
          stackItem.percentage = (stackItem.value * 100) / totalLabelBoxCount;
          totalPerc += stackItem.percentage;
        }
        // logger.debug(`totalPerc: ${totalPerc}`)
        if (totalPerc > percentageCutoff) {
          formattedPercentageFilteredLabelAnalyticsMetadata.push(key);
        }
      }
    }

    // filter metadata array
    if (
      selectionFilter.chartFilterData &&
      (selectionFilter.chartFilterData.metadata || selectionFilter.chartFilterData.tags)
    ) {
      formattedPercentageFilteredLabelAnalyticsMetadata = this.filterMetadataInLabelAnalyticsChartData(
        formattedPercentageFilteredLabelAnalyticsMetadata,
        selectionFilter.chartFilterData,
      );
    }

    return {
      maxValue: maxKeyLabelCount,
      chartData: formattedPercentageFilteredLabelAnalyticsMetadata,
    };
  }

  /**
   * Filter metadata in analytics chart by metadata
   * @param inputArray
   * @param filter
   * @returns
   */
  private filterMetadataInLabelAnalyticsChartData(
    inputArray: OneLabelMetadataAnalyticsFrontendFormat[],
    filter: ExplorerFilterV2,
  ) {
    let filteredArray = inputArray.slice();
    let metaDataObj: Record<string, string[]> = {};
    if (
      filter &&
      ((filter.metadata && Object.keys(filter.metadata).length >= 0) || (filter.tags && filter.tags.length >= 0))
    ) {
      metaDataObj = filter.metadata || {};
      filteredArray = [];
      for (let item of inputArray) {
        if (metaDataObj[item.labelName]) {
          let valueArray = metaDataObj[item.labelName] || [];
          if (valueArray.length > 0) {
            item.stackData = item.stackData.filter((valueItem: OneLabelMetadataAnalyticsFrontendValueStackFormat) =>
              valueArray.includes(valueItem.label),
            );
          }
          filteredArray.push(item);
        } else if (filter.tags && filter.tags.length > 0) {
          // let tagName = item.stackData[0].label;
          if (filter.tags.includes(item.labelName)) {
            filteredArray.push(item);
          }
        }
      }
    }
    return filteredArray;
  }

  /**
   * Get metadata analytics for a given query for a given label
   * @param labelId
   * @param dbQueryObject
   * @returns
   */
  async getOneLabelMetaFieldsAnalyticsForQuery(
    labelId: string,
    dbQueryObject: {matchQuery: any; objectType: ContentType},
  ) {
    logger.debug(
      `Metadata field analytics get | StatsCalculationService.getOneLabelMetaFieldsAnalyticsForQuery | N/A | Meta field analytics for query initiated`,
    );
    let metaFieldsPromise = this.getOneLabelMetaFieldAnalyticsForQuery(
      labelId,
      dbQueryObject.matchQuery,
      dbQueryObject.objectType,
    );
    let tagDataPromise = this.getOneLabelMetaTagAnalyticsQuery(
      labelId,
      dbQueryObject.matchQuery,
      dbQueryObject.objectType,
    );

    let [metaFieldsArray, tagDataArray]: [
      MetaFieldAndTagAnalyticsLabelwiseKeysFormat[],
      MetaFieldAndTagAnalyticsLabelwiseKeysFormat[],
    ] = await Promise.all([metaFieldsPromise, tagDataPromise]);

    // sort metaFieldsArray

    // sort inside values
    let _tempSortedMetaFieldsArray: MetaFieldAndTagAnalyticsLabelwiseKeysFormat[] = [];
    for (let metaField of metaFieldsArray) {
      let sortedValues = metaField.values.sort((a, b) => a.value.localeCompare(b.value));
      metaField.values = sortedValues;
      _tempSortedMetaFieldsArray.push(metaField);
    }
    // sort main array
    let sortedMetaFieldsArray = metaFieldsArray.sort((a, b) => b.keyLabelCount - a.keyLabelCount);

    // sort tagDataArray
    let sortedTagDataArray = tagDataArray.sort((a, b) => b.keyLabelCount - a.keyLabelCount);

    let metaFieldSummaryArr: MetaFieldAndTagAnalyticsLabelwiseKeysFormat[] = [
      ...sortedMetaFieldsArray,
      ...sortedTagDataArray,
    ];

    return metaFieldSummaryArr;
  }

  /**
   * Part of aggregate pipeline to find distinct metadata for given label with counts
   * @param dropDownFieldNameList
   * @returns
   */
  getOneLabelMetaFieldAnalyticsMatchParams(dropDownFieldNameList: string[], labelId: string) {
    const metaFieldMaxLimit = 10;
    let fieldParams = [
      {$match: {'labelList.label': labelId}},
      {
        $project: {
          labelList: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$labelList',
                  as: 'item',
                  cond: {$eq: ['$$item.label', labelId]},
                },
              },
              0,
            ],
          },
          keyValueArray: {
            $objectToArray: '$customMeta',
          },
        },
      },
      {
        $unwind: '$keyValueArray',
      },
      {
        $group: {
          _id: {
            key: '$keyValueArray.k',
            value: '$keyValueArray.v',
          },
          frameCount: {
            $sum: 1,
          },
          labelCount: {
            $sum: '$labelList.count',
          },
        },
      },
      {
        $group: {
          _id: '$_id.key',
          values: {
            $push: {
              value: '$_id.value',
              valueFrameCount: '$frameCount',
              valueLabelCount: '$labelCount',
            },
          },
          keyFrameCount: {
            $sum: '$frameCount',
          },
          keyLabelCount: {
            $sum: '$labelCount',
          },
          distinctValues: {
            $sum: 1,
          },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              {$gt: [{$size: '$values'}, 0]},
              {
                $or: [
                  {$lte: [{$size: '$values'}, metaFieldMaxLimit]}, // either less than max limit
                  {$in: ['$_id.key', dropDownFieldNameList]}, // or a dropdown
                ],
              },
            ],
          },
        },
      },
    ];
    return fieldParams;
  }

  /**
   * Get metadata field analytics for a given query for a given label
   * @param labelId
   * @param dbQuery
   * @param contentType
   * @returns
   */
  async getOneLabelMetaFieldAnalyticsForQuery(labelId: string, dbQuery: any, contentType: ContentType) {
    let parentDropDownList: string[] = [];
    parentDropDownList = await this.getParentDropDownListOfSystem();

    // get fields match
    let fieldParamsMatch = this.getOneLabelMetaFieldAnalyticsMatchParams(parentDropDownList, labelId);

    // generate match query
    let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

    // get fields
    let fieldParams = [...matchQueryParams, ...fieldParamsMatch];
    let metaFieldsArray = await this.metaDataRepository.aggregate(fieldParams);
    return metaFieldsArray;
  }

  /**
   * Part of aggregate pipeline to find distinct meta tags for given label with counts
   * @returns
   */
  getOneLabelMetaTagAnalyticsMatchParams(labelId: string) {
    let tagDataParams = [
      {$match: {'labelList.label': labelId}},
      {
        $project: {
          Tags: 1,
          labelList: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$labelList',
                  as: 'item',
                  cond: {$eq: ['$$item.label', labelId]},
                },
              },
              0,
            ],
          },
        },
      },
      {$unwind: '$Tags'},
      {
        $group: {
          _id: '$Tags',
          value: {$first: '$Tags'},
          frameCount: {
            $sum: 1,
          },
          labelCount: {
            $sum: '$labelList.count',
          },
        },
      },
      {
        $project: {
          _id: '$value',
          key: 'Tag',
          values: [
            {
              value: '$value',
              valueLabelCount: '$labelCount',
            },
          ],
          keyFrameCount: '$frameCount',
          keyLabelCount: '$labelCount',
        },
      },
    ];
    return tagDataParams;
  }

  /**
   * Get metadata Tag analytics for a given query for a given label
   * @param labelId
   * @param dbQuery
   * @param contentType
   * @returns
   */
  async getOneLabelMetaTagAnalyticsQuery(labelId: string, dbQuery: any, contentType: ContentType) {
    let tagMatchParams = this.getOneLabelMetaTagAnalyticsMatchParams(labelId);

    let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

    let tagDataParams = [...matchQueryParams, ...tagMatchParams];

    // logger.debug(`--------------------------------------------`)
    // console.log(JSON.stringify(tagDataParams, null, 2))

    let tagDataArray = await this.metaDataRepository.aggregate(tagDataParams);

    // logger.debug(`--------------------------------------------`)
    // console.log(JSON.stringify(tagDataArray, null, 2))

    return tagDataArray;
  }

  /**
   * Get metadata label box count for given query
   * @param labelId
   * @param dbQuery
   * @param contentType
   * @returns
   */
  async getOneLabelBoxCountForQuery(labelId: string, dbQuery: any, contentType: ContentType) {
    let matchQueryParams = [{$match: {objectType: contentType}}, {$match: dbQuery}];

    let boxCountMatchParams = [
      {$match: {'labelList.label': labelId}},
      {
        $project: {
          labelList: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$labelList',
                  as: 'item',
                  cond: {$eq: ['$$item.label', labelId]},
                },
              },
              0,
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          totalCount: {$sum: '$labelList.count'},
        },
      },
    ];

    let boxCountDataParams = [...matchQueryParams, ...boxCountMatchParams];

    let countDataArray = await this.metaDataRepository.aggregate(boxCountDataParams);
    let count = 0;
    if (countDataArray && countDataArray[0]) {
      let countObject = countDataArray[0];
      if (countObject && countObject.totalCount && countObject.totalCount > 0) {
        count = countObject.totalCount;
      }
    }

    return count;
  }

  // -----------------------------------------------------------------------

  // determineChildrenContentTypeByParentTypeForAnalytics(parentType: ContentType) {
  //   let childType = ContentType.IMAGE; // for image collection and dataset

  //   if (parentType == ContentType.VIDEO_COLLECTION) childType = ContentType.VIDEO;
  //   else if (parentType == ContentType.OTHER_COLLECTION) childType = ContentType.OTHER;

  //   return childType;
  // }

  /**
   * Method to find all collections where a given query is affected
   * (itself in case of collections and parent collections in case of files)
   * @param dbMatchQuery
   * @returns
   */
  async findApplicableCollectionsForQuery(dbMatchQuery: any) {
    let matchParams = {$match: dbMatchQuery};
    let collectionSet: Set<string> = new Set();

    // get where itself are collections
    let selfCollectionParams = [
      {
        $match: {
          objectType: {
            $in: [
              ContentType.IMAGE_COLLECTION,
              ContentType.VIDEO_COLLECTION,
              ContentType.OTHER_COLLECTION,
              ContentType.DATASET,
            ],
          },
        },
      },
      matchParams,
      {$project: {_id: 1}},
    ];
    let selfCollectionDataList = await this.metaDataRepository.aggregate(selfCollectionParams);
    for (let collection of selfCollectionDataList) {
      collectionSet.add(collection._id.toString());
    }

    // get parent collections
    let parentCollectionParams = [
      {$match: {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}}},
      matchParams,
      {$unwind: '$vCollectionIdList'},
      {$group: {_id: '$vCollectionIdList'}},
    ];
    let parentCollectionDataList = await this.metaDataRepository.aggregate(parentCollectionParams);
    for (let collection of parentCollectionDataList) {
      collectionSet.add(collection._id.toString());
    }

    // convert set to array
    let collectionArray = Array.from(collectionSet);
    return collectionArray;
  }
}
export const STATS_CALCULATION_SERVICE = BindingKey.create<StatsCalculationService>('service.statsCalculation');

export enum DetailSource {
  SYSTEM_DATA = 1,
  METADATA = 2,
  DYNAMIC_QUERY = 3,
}

export interface LabelWiseAnalyticsLabelCountFormat {
  _id: string;
  labelCount: number;
  frameCount: number;
}

export interface OneLabelMetadataAnalyticsFrontendFormat {
  labelName: string;
  stackData: OneLabelMetadataAnalyticsFrontendValueStackFormat[];
}

export interface OneLabelMetadataAnalyticsFrontendValueStackFormat {
  label: string;
  value: number;
  percentage: number;
}
