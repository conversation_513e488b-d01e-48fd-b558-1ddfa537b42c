/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the search query buliding and datalake exploring related logics
 */

/**
 * @class SearchQueryBuilderService
 * purpose of this service is search query buliding and datalake exploring
 * @description search query buliding and datalake exploring
 * <AUTHOR> chathushka, lahiru
 */

import {Metadata} from '@google-cloud/storage/build/src/nodejs-common';
import {BindingKey, BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {default as Axios} from 'axios';
import {EJSON} from 'bson';
import dotenv from 'dotenv';
import moment from 'moment';
import {ObjectId} from 'mongodb';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AnnotationObject,
  AnnotationObjectInfo,
  AnnotationProjectInfo,
  BulkUpdateOneQueryOptionFormat,
  ContentType,
  DatalakeSelection,
  DatalakeSelectionRequest,
  EmbeddingSelectionObject,
  ExploreSortBy,
  Explore_API_TYPE,
  ExplorerCollectionViewResponse,
  ExplorerDefaultViewResponse,
  ExplorerFilterV2,
  ExtendedAnnotationObject,
  FrameVerificationStatus,
  FrontendFeatureGraphDataFormat,
  FrontendRequestCoordinateDataFormat,
  GetEmbeddingAggregationForSelectionRes,
  GraphDataAvailability,
  GraphStatus,
  InputMetaDataFeedGroup,
  ItemObject,
  MetaData,
  MetaUpdateProjectInfo,
  MetaUpdateProjectType,
  MetaUpdatesLabelGroup,
  MetaUpdatesToKeyMaps,
  MetaWithMetaUpdates,
  MetaWithMetaUpdatesFilter,
  MongodbQueryOperators,
  OBJECT_STATUS,
  OperationMode,
  OptionListType,
  QueryGraphDetails,
  QueryOption,
  SELECTION_TYPE,
  SampleGraphDataFormat,
  SearchQueryRootGroup,
  SortObject,
  SortOrder,
  SystemData,
  VerificationStatusFrontendConstants,
  VerificationStatusFrontendConstantsInDetailView,
} from '../models';
import {JobStatus, JobType} from '../models/job.model';
import {
  DatalakeSelectionRepository,
  MetaDataRepository,
  MetaDataUpdateRepository,
  QueryGraphDetailsRepository,
  QueryOptionRepository,
  SystemDataRepository,
  SystemLabelRepository,
} from '../repositories';
import {JobRepository} from '../repositories/job.repository';
import {QueryGraphDataRepository} from '../repositories/query-graph-data.repository';
import {SimilarImageRepository} from '../repositories/similar-image.repository';
import {EMBEDDING_COLLECTION, FLOWS, SIMILARITY_SCORE_THRESHOLD, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DEFAULT_ITEM_SORT_IN_COLLECTION, TeamDefaultQueryOption} from '../settings/global-field-config';
import {
  byteTransform,
  getFormattedItemCount,
  getFormattedVideoLength,
  getLocalTime,
  isValidObjectId,
} from '../settings/tools';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from './system-label.service';
dotenv.config();

const OPERATOR_ARRAY = ['||', '&&'];
const AND_OERATOR = '&&';
const OR_OERATOR = '||';
const PYTHON_HOST = process.env.PYTHON_BASE_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class SearchQueryBuilderService {
  constructor(
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository(SimilarImageRepository)
    private similarImageRepository: SimilarImageRepository,
    @repository(MetaDataUpdateRepository)
    private metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @inject(SYSTEM_LABEL_SERVICE)
    private systemLabelService: SystemLabelService,
    @repository(SystemLabelRepository)
    private systemLabelRepository: SystemLabelRepository,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @repository(QueryGraphDataRepository)
    private queryGraphDataRepository: QueryGraphDataRepository,
    @repository(QueryGraphDetailsRepository)
    private queryGraphDetailsRepository: QueryGraphDetailsRepository,
    @repository(JobRepository)
    private jobRepository: JobRepository,
  ) {}

  /**
   * Use to update search query options upon MetaDataInputFeed Dequeue
   * @param inputMetaDataFeedGroupList InputMetaDataFeedGroup[]
   */
  async updateQueryOptionOnMetaDataInputFeedDequeue(inputMetaDataFeedGroupList: InputMetaDataFeedGroup[]) {
    const addedQueryKeysWithKeyGroup: {[k: string]: any[]} = {}; //use to temparally keep added queryOptions to reduce DB calls for current loop

    for (const inputMetaDataFeedGroup of inputMetaDataFeedGroupList) {
      for (const metaDataObj of inputMetaDataFeedGroup.metaDataArray) {
        // find teamId & collectionId
        let teamId: string | undefined = undefined;
        let collectionId: string | undefined = undefined;
        if (metaDataObj.metaDataObject.teamId) teamId = metaDataObj.metaDataObject.teamId as unknown as string;
        if (metaDataObj.metaDataObject.collectionId)
          collectionId = metaDataObj.metaDataObject.collectionId as unknown as string; //if update object has collectionId, then assig it
        if (!collectionId) {
          //if update object has not collectionId, then try to find collection id from metaDataObjectForUpdate
          if (inputMetaDataFeedGroup.metaDataObjectForUpdate) {
            if (inputMetaDataFeedGroup.metaDataObjectForUpdate.length > 0) {
              if (inputMetaDataFeedGroup.metaDataObjectForUpdate[0].collectionId) {
                collectionId = inputMetaDataFeedGroup.metaDataObjectForUpdate[0].collectionId as unknown as string;
              }
            }
          }
        }

        for (const [key, value] of Object.entries(metaDataObj.metaDataObject)) {
          // update query option for custom metadata (custome matadata wil be available in inputMetaDataFeedGroup.metaDataArray)
          if (!MetaData.definition.properties.hasOwnProperty(key) || key == 'Tags') {
            // create a object from key,value pair
            const tempObj: {[k: string]: any} = {};
            tempObj[key] = value;
            // call query update function
            await this.queryOptionRepository.updateQueryOption(
              SearchQueryRootGroup.METADATA,
              tempObj,
              true,
              teamId,
              collectionId,
              addedQueryKeysWithKeyGroup,
            );
          }
        }
      }
    }
  }

  /**
   * Use to insert Default Search query option for datalake teams
   */
  async insertDefaultQueryOptions() {
    await new Promise(resolve => setTimeout(resolve, 5000));

    const datalakeTeams: SystemData[] = await this.systemDataRepository.aggregate([
      {
        $match: {
          isDefaultQueryOptionsInserted: {$ne: true},
        },
      },
    ]);

    for (const datalakeTeam of datalakeTeams) {
      const dataArr: BulkUpdateOneQueryOptionFormat[] = [];

      for (const elem of TeamDefaultQueryOption) {
        if (elem.values.length == 0) {
          const _document: Partial<QueryOption> = {
            keyGroup: elem.key,
            isRootGroup: elem.isRootGroup,
            operators: elem.operators,
            teamId: datalakeTeam.teamId,
            ignoredSearchType: elem.ignoredSearchType,
          };

          dataArr.push({
            updateOne: {
              filter: _document,
              update: {
                $set: _document,
              },
              upsert: true,
            },
          });
        } else {
          for (const val of elem.values) {
            const _document: Partial<QueryOption> = {
              keyGroup: elem.key,
              isRootGroup: elem.isRootGroup,
              key: val,
              operators: elem.operators,
              teamId: datalakeTeam.teamId,
              ignoredSearchType: elem.ignoredSearchType,
            };

            dataArr.push({
              updateOne: {
                filter: _document,
                update: {
                  $set: _document,
                },
                upsert: true,
              },
            });
          }
        }
      }
      //console.log(dataArr)
      await this.queryOptionRepository.bulkWrite(dataArr);
      await this.systemDataRepository.updateById(datalakeTeam._id, {
        isDefaultQueryOptionsInserted: true,
      });
    }
  }

  /**
   * Use to retrive search query option for a given team
   * @param teamId id of the team - Use to filter query options
   * @param collectionId id of the collection - Use to filter query options
   * @returns
   */
  async getSearchQueryOptions(teamId: string, collectionId?: string) {
    let collectionObjectType: ContentType | undefined;
    if (collectionId) {
      const collectionObj = await this.metaDataRepository.findById(collectionId);
      collectionObjectType = collectionObj.objectType;
    }

    const returnValues = await Promise.all([
      this.queryOptionRepository.getDynamicSearchQueryOptions(teamId, collectionId, collectionObjectType),
      this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId),
    ]);
    const searchQueryOptions = returnValues[0];
    const labelToLabelTextMap = returnValues[1];

    for (const option of searchQueryOptions) {
      if (option.key == 'annotation.label') {
        const labelTextList: string[] = [];
        for (const label of option.values) {
          if (labelToLabelTextMap[label]) {
            labelTextList.push(labelToLabelTextMap[label]);
          } else {
            logger.error(
              `${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | SearchQueryBuilderService.getSearchQueryOptions | ${teamId} | couldn't find label mapping for label: ${label} of teamId: ${teamId}`,
            );
          }
        }
        option.values = labelTextList;
        break;
      }
    }

    return searchQueryOptions;
  }

  /**
   * Get list sorting object for given sort by and sort direction
   * @param defaultSorting {boolean} whether to sort in default order
   * @param sortBy {ExploreSortBy} sort by
   * @param sortDirection {SortOrder} sort direction
   * @returns {Record<string, any>} sort object
   * @memberof SearchQueryBuilderService
   */
  async getListSortingObj(
    defaultSorting: boolean,
    sortBy?: ExploreSortBy,
    sortDirection?: SortOrder,
  ): Promise<Record<string, any>> {
    let sortObj: Record<string, any> = {};
    let sortValue: number | undefined = undefined;

    if (sortDirection) sortValue = sortDirection == SortOrder.ASC ? 1 : -1;

    if (sortBy == ExploreSortBy.DATE_MODIFIED) {
      sortObj = {$sort: {updatedAt: sortValue ?? -1, _id: sortValue ?? -1}};
    } else if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
    } else if (sortBy == ExploreSortBy.DATE_CREATED) {
      sortObj = {$sort: {createdAt: sortValue ?? -1, _id: sortValue ?? -1}};
    } else if (sortBy == ExploreSortBy.NAME) {
      sortObj = {$sort: {nameInLowerCase: sortValue ?? 1, _id: sortValue ?? 1}};
    } else if (sortBy == ExploreSortBy.SIZE) {
      sortObj = {$sort: {fileSize: sortValue ?? 1, _id: sortValue ?? 1}};
    } else if (sortBy == ExploreSortBy.VIDEO_INDEX) {
      sortObj = {$sort: {sourceVideoId: sortValue ?? 1, videoFrameIndex: sortValue ?? 1, _id: sortValue ?? 1}};
    } else if (defaultSorting) {
      sortObj = {$sort: {updatedAt: -1, _id: -1}};
    }

    return sortObj;
  }

  /**
   * Use to get object list for datalake explorer tab initial view
   * @param contentType {ContentType} type of which objects should include in response
   * @param pageIndex {number} page number [start from 0]
   * @param pageSize {number} page size [default 4]
   * @param collectionId {string} id of collection if response should include objects belongs to only particular collection
   * @returns ExplorerDefaultViewResponse
   */
  async getObjectListToExplorer(
    filter: ExplorerFilterV2,
    contentType: ContentType,
    pageIndex: number,
    pageSize: number,
    currentUserProfile: UserProfileDetailed,
    query?: any,
    referenceImage?: string,
    modelName?: string,
    score?: number,
    sortBy?: ExploreSortBy,
    exportApiType?: Explore_API_TYPE,
    project?: any,
    sortDirection?: SortOrder,
    embeddingSelection?: EmbeddingSelectionObject,
  ) {
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | contentType=${contentType} pageIndex=${pageIndex} pageSize=${pageSize}`,
    );
    const start_time = new Date().getTime();
    let sortValue: number | undefined = undefined;
    const teamId = currentUserProfile.teamId;

    if (sortDirection) {
      sortValue = sortDirection == SortOrder.ASC ? 1 : -1;
    }

    let projectObj: any = {
      $project: {
        _id: 1,
        name: 1,
        objectKey: 1,
        frameCount: 1,
        otherCount: 1,
        thumbnailUrl: 1,
        objectType: 1,
        annotationProjectList: 1,
        curationProjectList: 1,
        verificationStatusCount: 1,
        uploadInProgress: 1,
        uploadingUserId: 1,
        datasetVersionList: 1,
        datasetGroupId: 1,
        objectStatus: 1,
        customSortOrder: 1,
      },
    };

    if (project) {
      projectObj = project;
    }

    // initialize variables
    // let orderQuery: string[] = ['updatedAt DESC'];
    const explorerDefaultViewResponse: ExplorerDefaultViewResponse = {
      // videos: {
      count: 0,
      itemList: [],
      // },
      // videoCollections: {
      //   count: 0,
      //   itemList: [],
      // },
      // images: {
      //   count: 0,
      //   itemList: [],
      // },
      // imageCollections: {
      //   count: 0,
      //   itemList: [],
      // },
      // datasets: {
      //   count: 0,
      //   itemList: [],
      // },
      // others: {
      //   count: 0,
      //   itemList: [],
      // },
      // otherCollections: {
      //   count: 0,
      //   itemList: [],
      // },
    };

    //genarate contentType array
    let contentTypes: ContentType[] = [];
    if (contentType == ContentType.ALL) {
      contentTypes = [ContentType.VIDEO_COLLECTION, ContentType.IMAGE_COLLECTION, ContentType.DATASET];
    } else {
      contentTypes = [contentType];
    }

    //filter match query create
    let filterMatchQuery: any = {};

    if (referenceImage && exportApiType == Explore_API_TYPE.LIST) {
      filterMatchQuery = await this.getMatchQueryForExplorerFilter(
        filter,
        embeddingSelection,
        'metaData',
        currentUserProfile,
      );
    } else {
      filterMatchQuery = await this.getMatchQueryForExplorerFilter(
        filter,
        embeddingSelection,
        undefined,
        currentUserProfile,
      );
    }
    let promiseArray: Promise<void>[] = [];
    const aggregateQuery: any[] = [];
    const skipCount = pageIndex * pageSize;
    if (exportApiType == Explore_API_TYPE.LIST) {
      let _content: Partial<MetaData>[] = [];
      //generate find query async functions for each content type
      promiseArray = contentTypes.map(async _contentType => {
        // if similarity change occur list call go through similarImage collection first
        // then lookup the metadata collection and apply query and filters
        if (referenceImage && contentType == ContentType.IMAGE) {
          const _andArr: any[] = [];

          _andArr.push({'metaData.objectType': _contentType});
          if (Object.keys(query).length != 0) _andArr.push(query);

          //filter by team Id
          if (teamId) {
            _andArr.push({'metaData.teamId': new ObjectId(teamId)});
          }

          // if (_contentType == ContentType.IMAGE || _contentType == ContentType.VIDEO) {
          // _andArr.push({'metaData.isError': {$ne: true}});
          // _andArr.push({'metaData.isAccessible': true});
          // _andArr.push({'metaData.isMediaProcessingPending': {$ne: true}});
          // }

          aggregateQuery.push({
            $match: {referenceObjectKey: referenceImage, modelName: modelName, scoreThreshold: score},
          });
          aggregateQuery.push({
            $group: {
              _id: '$objectKey',
              objectKey: {$first: '$objectKey'},
              score: {$first: '$score'},
            },
          });
          //This will already sorted according to similarity score
          if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
            aggregateQuery.push({$sort: {score: -1}});
          }
          aggregateQuery.push({
            $lookup: {
              from: 'MetaData',
              foreignField: 'objectKey',
              localField: 'objectKey',
              as: 'metaData',
            },
          });
          aggregateQuery.push({
            $match: {
              'metaData.objectStatus': OBJECT_STATUS.ACTIVE,
              $and: _andArr,
              ...filterMatchQuery,
            },
          });

          // if (embeddingSelection?.graphId && embeddingSelection?.selection) {
          //   let embeddingLookupQuery: GetEmbeddingAggregationForSelectionRes[] =
          //     await this.getEmbeddingAggregationForSelection(embeddingSelection, 'metaData');

          //   if (Array.isArray(embeddingLookupQuery) && embeddingLookupQuery.length > 0) {
          //     aggregateQuery.push(...embeddingLookupQuery);
          //   }
          // }

          if (sortBy == ExploreSortBy.DATE_MODIFIED) {
            aggregateQuery.push({$sort: {'metaData.updatedAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
          }
          //This will already sorted according to similarity score
          else if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
          } else if (sortBy == ExploreSortBy.DATE_CREATED) {
            aggregateQuery.push({$sort: {'metaData.createdAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
          } else if (sortBy == ExploreSortBy.NAME) {
            aggregateQuery.push({$sort: {'metaData.nameInLowerCase': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
          } else if (sortBy == ExploreSortBy.SIZE) {
            aggregateQuery.push({$sort: {'metaData.fileSize': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
          } else if (sortBy == ExploreSortBy.VIDEO_INDEX) {
            aggregateQuery.push({
              $sort: {
                'metaData.sourceVideoId': sortValue ?? 1,
                'metaData.videoFrameIndex': sortValue ?? 1,
                'metaData._id': sortValue ?? 1,
              },
            });
          }
          // else {
          //   aggregateQuery.push({$sort: {'metaData.updatedAt': -1, 'metaData._id': -1}});
          // }
          aggregateQuery.push({$skip: skipCount});
          aggregateQuery.push({$limit: pageSize});
          aggregateQuery.push({$unwind: '$metaData'});
          projectObj = {
            $project: {
              _id: '$metaData._id',
              name: '$metaData.name',
              objectKey: '$metaData.objectKey',
              frameCount: '$metaData.frameCount',
              otherCount: '$metaData.otherCount',
              thumbnailUrl: '$metaData.thumbnailUrl',
              objectType: '$metaData.objectType',
              annotationProjectList: '$metaData.annotationProjectList',
              curationProjectList: '$metaData.curationProjectList',
              verificationStatusCount: '$metaData.verificationStatusCount',
              uploadInProgress: '$metaData.uploadInProgress',
              uploadingUserId: '$metaData.uploadingUserId',
              datasetVersionList: '$metaData.datasetVersionList',
              datasetGroupId: '$metaData.datasetGroupId',
              objectStatus: '$metaData.objectStatus',
              customSortOrder: '$metaData.customSortOrder',
            },
          };
          aggregateQuery.push(projectObj);
          //logger.debug(JSON.stringify(aggregateQuery, null, 2));

          const startTime = new Date().getTime();

          logger.debug(JSON.stringify(aggregateQuery, null, 2));
          _content = await this.similarImageRepository.aggregate(aggregateQuery, {allowDiskUse: true});
          const endTime = new Date().getTime();
          const responseTime = endTime - startTime;
          logger.info(
            `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | object list aggregation responseTime=${responseTime}`,
          );
        } else {
          const _andArr: any[] = [];
          // if (referenceImage) _andArr.push({objectKey: {$in: similarObjectKeyList}})
          _andArr.push({objectType: _contentType});
          if (Object.keys(query).length != 0) _andArr.push(query);

          //filter by team Id
          if (teamId) {
            _andArr.push({teamId: new ObjectId(teamId)});
          }

          // if (_contentType == ContentType.IMAGE || _contentType == ContentType.VIDEO) {
          // _andArr.push({isError: {$ne: true}});
          // _andArr.push({isAccessible: true});
          // _andArr.push({isMediaProcessingPending: {$ne: true}});
          // }

          aggregateQuery.push({
            $match: {
              objectStatus: OBJECT_STATUS.ACTIVE,
              $and: _andArr,
              ...filterMatchQuery,
            },
          });

          // if (embeddingSelection?.graphId && embeddingSelection?.selection) {
          //   let embeddingLookupQuery: GetEmbeddingAggregationForSelectionRes[] =
          //     await this.getEmbeddingAggregationForSelection(embeddingSelection);

          //   if (Array.isArray(embeddingLookupQuery) && embeddingLookupQuery.length > 0) {
          //     aggregateQuery.push(...embeddingLookupQuery);
          //   }
          // }

          // logger.debug(JSON.stringify(aggregateQuery, null, 2))

          if (sortBy == ExploreSortBy.DATE_MODIFIED) {
            aggregateQuery.push({$sort: {updatedAt: sortValue ?? -1, _id: sortValue ?? -1}});
          } else if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
          } else if (sortBy == ExploreSortBy.DATE_CREATED) {
            aggregateQuery.push({$sort: {createdAt: sortValue ?? -1, _id: sortValue ?? -1}});
          } else if (sortBy == ExploreSortBy.NAME) {
            aggregateQuery.push({$sort: {nameInLowerCase: sortValue ?? 1, _id: sortValue ?? 1}});
          } else if (sortBy == ExploreSortBy.SIZE) {
            aggregateQuery.push({$sort: {fileSize: sortValue ?? 1, _id: sortValue ?? 1}});
          } else if (sortBy == ExploreSortBy.VIDEO_INDEX) {
            aggregateQuery.push({
              $sort: {sourceVideoId: sortValue ?? 1, videoFrameIndex: sortValue ?? 1, _id: sortValue ?? 1},
            });
          } else if (sortBy == ExploreSortBy.TEXT_SCORE) {
            if (filter.keywords && Array.isArray(filter.keywords) && filter.keywords.length > 0) {
              aggregateQuery.push({$addFields: {score: {$meta: 'textScore'}}});
              aggregateQuery.push({$sort: {score: -1}});
            } else {
              aggregateQuery.push({$sort: {_id: 1}});
            }
          } else {
            aggregateQuery.push({$sort: {updatedAt: -1, _id: 1}});
          }
          aggregateQuery.push({$skip: skipCount});
          aggregateQuery.push({$limit: pageSize});
          aggregateQuery.push(projectObj);
          // logger.debug(JSON.stringify(aggregateQuery, null, 2));

          const startTime = new Date().getTime();

          logger.debug(JSON.stringify(aggregateQuery, null, 2));
          _content = await this.metaDataRepository.aggregate(aggregateQuery);
          const endTime = new Date().getTime();
          const responseTime = endTime - startTime;
          logger.info(
            `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | object list aggregation responseTime=${responseTime}`,
          );
        }

        //logger.debug(JSON.stringify(aggregateQuery, null, 2))
        const formattedContent = _content.map((metaObj, idx) => {
          const formattedMetaObj: ItemObject = this.getFormattedMetaObject(
            metaObj,
            skipCount,
            idx,
            currentUserProfile,
            undefined,
          );
          return formattedMetaObj;
        });

        switch (_contentType) {
          case ContentType.VIDEO:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.VIDEO_COLLECTION:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.IMAGE:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.IMAGE_COLLECTION:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.DATASET:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.OTHER:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          case ContentType.OTHER_COLLECTION:
            explorerDefaultViewResponse.itemList = formattedContent;
            break;
          default:
            break;
        }
      });

      if (!filter.annotationTypes) filter.annotationTypes = [];

      // push async function to get counts
      // Add counts only for initial explorer view
      if (
        Object.keys(query).length == 0 &&
        ((filter && contentType == ContentType.ALL && !filter.date && filter.annotationTypes.length == 0) || !filter)
      ) {
        promiseArray.push(this.getTotalObjectCounts(contentType, explorerDefaultViewResponse, query));
      }
    } else if (exportApiType == Explore_API_TYPE.COUNT) {
      if (!filter.annotationTypes) filter.annotationTypes = [];

      // push async function to get counts
      if (
        Object.keys(query).length == 0 &&
        !referenceImage &&
        ((filter && contentType == ContentType.ALL && !filter.date && filter.annotationTypes.length == 0) || !filter)
      ) {
        promiseArray.push(this.getTotalObjectCounts(contentType, explorerDefaultViewResponse, query));
      } else {
        const countMatchQuery: any[] = [];

        const matchObject: any = {
          objectType: {$in: contentTypes},
          // isError: {$ne: true},
          // isMediaProcessingPending: {$ne: true},
          // $or: [{isAccessible: true}, {objectType: {$nin: [ContentType.IMAGE, ContentType.VIDEO]}}],
        };
        if (teamId) {
          matchObject.teamId = new ObjectId(teamId);
        }
        const aggregateQuery: any[] = [];
        aggregateQuery.push({
          $match: {
            //replace flag with enum
            objectStatus: OBJECT_STATUS.ACTIVE,
            // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
            ...matchObject,
            ...filterMatchQuery,
          },
        });
        if (Object.keys(query).length != 0)
          aggregateQuery.push({
            $match: query,
          });

        // if similarity search occur count query check similarArray in MetaData collection
        if (referenceImage)
          aggregateQuery.push({
            $match: {
              similarArray: {
                $elemMatch: {
                  referenceObjectKey: referenceImage,
                  modelName: modelName,
                  scoreThreshold: score,
                },
              },
            },
          });

        // if (embeddingSelection?.graphId && embeddingSelection?.selection) {
        //   let embeddingLookupQuery: GetEmbeddingAggregationForSelectionRes[] =
        //     await this.getEmbeddingAggregationForSelection(embeddingSelection);

        //   if (Array.isArray(embeddingLookupQuery) && embeddingLookupQuery.length > 0) {
        //     aggregateQuery.push(...embeddingLookupQuery);
        //   }
        // }
        aggregateQuery.push({$group: {_id: '$objectType', count: {$sum: 1}}});

        //logger.debug(JSON.stringify(aggregateQuery, null, 2));
        promiseArray.push(
          this.getTotalObjectCountsQuery(aggregateQuery, explorerDefaultViewResponse, contentType, query),
        );
      }
    }

    // execute async functions and wait untill all finished
    await Promise.all(promiseArray);

    const end_time = new Date().getTime();
    const response_time = end_time - start_time;
    if (referenceImage) {
      // logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | ${JSON.stringify(explorerDefaultViewResponse, null, 2)}`)
      logger.debug(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | time - ${response_time}`,
      );
    }

    return explorerDefaultViewResponse;
  }

  /**
   * Use to update count fields of explorerDefaultViewResponse
   * @param contentType {ContentType} type of which objects should include in response
   * @param explorerDefaultViewResponse response object of this.getObjectListToExplorer function
   * @returns null
   */
  async getTotalObjectCountsQuery(
    aggregateQuery: any[],
    explorerDefaultViewResponse: ExplorerDefaultViewResponse,
    contentType: ContentType,
    query?: any,
  ) {
    logger.debug('aggregate: ', JSON.stringify(aggregateQuery, null, 2));
    //NOTE: add teamId filter
    const startTime = new Date().getTime();
    const totalCountsList: {_id: number; count: number}[] = await this.metaDataRepository.aggregate(aggregateQuery);
    const endTime = new Date().getTime();
    const responseTime = endTime - startTime;
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getObjectListToExplorer | N/A | object count aggregation responseTime=${responseTime}`,
    );
    if (totalCountsList && totalCountsList.length > 0) {
      for (const obj of totalCountsList) {
        switch (obj._id) {
          case ContentType.ALL:
            if (Object.keys(query).length == 0) {
              explorerDefaultViewResponse.count = obj.count;
              explorerDefaultViewResponse.count = obj.count;
              explorerDefaultViewResponse.count = obj.count;
              explorerDefaultViewResponse.count = obj.count;
            } else {
              explorerDefaultViewResponse.count = obj.count;
              explorerDefaultViewResponse.count = obj.count;
              explorerDefaultViewResponse.count = obj.count;
            }

            break;
          case ContentType.VIDEO:
            if (Object.keys(query).length != 0 || contentType == ContentType.VIDEO) {
              explorerDefaultViewResponse.count = obj.count;
            }

            break;
          case ContentType.VIDEO_COLLECTION:
            if (Object.keys(query).length == 0 || contentType == ContentType.VIDEO_COLLECTION) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          case ContentType.IMAGE:
            if (Object.keys(query).length != 0 || contentType == ContentType.IMAGE) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          case ContentType.IMAGE_COLLECTION:
            if (Object.keys(query).length == 0 || contentType == ContentType.IMAGE_COLLECTION) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          case ContentType.DATASET:
            if (Object.keys(query).length == 0 || contentType == ContentType.DATASET) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          case ContentType.OTHER:
            if (Object.keys(query).length != 0 || contentType == ContentType.OTHER) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          case ContentType.OTHER_COLLECTION:
            if (Object.keys(query).length != 0 || contentType == ContentType.OTHER_COLLECTION) {
              explorerDefaultViewResponse.count = obj.count;
            }
            break;
          default:
            break;
        }
      }
    }
  }

  /**
   * Use to update count fields of explorerDefaultViewResponse
   * @param contentType {ContentType} type of which objects should include in response
   * @param explorerDefaultViewResponse response object of this.getObjectListToExplorer function
   * @returns null
   */
  async getTotalObjectCounts(
    contentType: ContentType,
    explorerDefaultViewResponse: ExplorerDefaultViewResponse,
    query?: any,
  ) {
    //NOTE: add teamId filter
    const totalCounts = await this.systemDataRepository.findOne();
    switch (contentType) {
      case ContentType.ALL:
        if (Object.keys(query).length == 0) {
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.videoCollections.count || 0;
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.imageCollections.count || 0;
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.datasets.count || 0;
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.otherCollections.count || 0;
        } else {
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.videos.count || 0;
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.images.count || 0;
          explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.other.count || 0;
        }
        break;
      case ContentType.VIDEO:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.videos.count || 0;
        break;
      case ContentType.VIDEO_COLLECTION:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.videoCollections.count || 0;
        break;
      case ContentType.IMAGE:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.images.count || 0;
        break;
      case ContentType.IMAGE_COLLECTION:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.imageCollections.count || 0;
        break;
      case ContentType.DATASET:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.datasets.count || 0;
        break;
      case ContentType.OTHER:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.other.count || 0;
        break;
      case ContentType.OTHER_COLLECTION:
        explorerDefaultViewResponse.count = totalCounts?.objectTypeWiseCounts?.otherCollections.count || 0;
        break;
      default:
        break;
    }
  }

  /**
   * Use to get object list for datalake explorer collection view
   * @param collectionId {string} id of collection which is going to view
   * @param pageIndex {number} page number [strart from 0]
   * @param pageSize {number} page size [default 16]
   * @returns ExplorerCollectionViewResponse
   */
  async getObjectListOfCollection(
    collectionId: string,
    pageIndex: number,
    pageSize: number,
    filter: ExplorerFilterV2,
    contentType: ContentType,
    currentUserProfile: UserProfileDetailed,
    queryFilter?: any,
    referenceImage?: string,
    modelName?: string,
    score?: number,
    sortBy?: ExploreSortBy,
    exportApiType?: Explore_API_TYPE,
    project?: any,
    sortDirection?: SortOrder,
    embeddingSelection?: EmbeddingSelectionObject,
  ) {
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | SearchQueryBuilderService.getObjectListOfCollection | N/A | collectionId=${collectionId} pageIndex=${pageIndex} pageSize=${pageSize} collectionContentType=${contentType}`,
    );
    const teamId = currentUserProfile.teamId;
    let sortValue: number | undefined = undefined;

    if (sortDirection) {
      sortValue = sortDirection == SortOrder.ASC ? 1 : -1;
    }
    let projectObj: any = {
      $project: {
        _id: 1,
        name: 1,
        objectKey: 1,
        createdAt: 1,
        updatedAt: 1,
        thumbnailUrl: 1,
        objectType: 1,
        annotationProjectList: 1,
        curationProjectList: 1,
        verificationStatusCount: 1,
        frameId: 1,
        videoFrameIndex: 1,
        datasetVersionList: 1,
        objectStatus: 1,
      },
    };

    if (project) {
      projectObj = project;
    }

    const explorerCollectionViewResponse: ExplorerCollectionViewResponse = {
      count: 0,
      itemList: [],
    };

    const collectionObj = await this.metaDataRepository.findById(collectionId);

    const _pipeline: any[] = [];
    let countQuery: any[] = [];
    const skipCount = pageIndex * pageSize;

    // if similarity change occur list call go through similarImage collection first
    // then lookup the metadata collection and apply query and filters
    if (referenceImage && contentType == ContentType.IMAGE && exportApiType == Explore_API_TYPE.LIST) {
      // generate match filter for collection and virtual collection
      if (
        collectionObj.objectType == ContentType.IMAGE_COLLECTION ||
        collectionObj.objectType == ContentType.VIDEO_COLLECTION ||
        collectionObj.objectType == ContentType.DATASET ||
        collectionObj.objectType == ContentType.OTHER_COLLECTION
      ) {
        _pipeline.push({$match: {referenceObjectKey: referenceImage, modelName: modelName, scoreThreshold: score}});
        _pipeline.push({
          $group: {
            _id: '$objectKey',
            objectKey: {$first: '$objectKey'},
            score: {$first: '$score'},
          },
        });
        if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
          _pipeline.push({$sort: {score: -1}});
        }
        _pipeline.push({
          $lookup: {
            from: 'MetaData',
            foreignField: 'objectKey',
            localField: 'objectKey',
            as: 'metaData',
          },
        });

        // let _matchQuery = await this.metaDataRepository.getMatchObjectsOfCollection(collectionObj, 'metaData');
        const _matchQuery = {'metaData.vCollectionIdList': new ObjectId(collectionObj.id)};

        _pipeline.push({
          $match: _matchQuery,
        });
      } else {
        logger.error(
          `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | SearchQueryBuilderService.getObjectListOfCollection | N/A | invalid id for collection view, collectionId: ${collectionId}`,
        );
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_VIEW_NOT_APPLICABLE);
      }

      //filter match query create
      const filterMatchQuery: any = await this.getMatchQueryForExplorerFilter(
        filter,
        embeddingSelection,
        'metaData',
        currentUserProfile,
      );

      //avoid sending url not generated metaObjects to frontend
      _pipeline.push({
        $match: {
          'metaData.teamId': new ObjectId(teamId),
          'metaData.objectStatus': OBJECT_STATUS.ACTIVE,
          ...filterMatchQuery,
          ...queryFilter,
        },
      });

      // if (embeddingSelection?.graphId && embeddingSelection?.selection) {
      //   let embeddingLookupQuery: GetEmbeddingAggregationForSelectionRes[] =
      //     await this.getEmbeddingAggregationForSelection(embeddingSelection, 'metaData');

      //   if (Array.isArray(embeddingLookupQuery) && embeddingLookupQuery.length > 0) {
      //     _pipeline.push(...embeddingLookupQuery);
      //   }
      // }

      if (sortBy == ExploreSortBy.DATE_MODIFIED) {
        _pipeline.push({$sort: {'metaData.updatedAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
      } else if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
      } else if (sortBy == ExploreSortBy.DATE_CREATED) {
        _pipeline.push({$sort: {'metaData.createdAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
      } else if (sortBy == ExploreSortBy.NAME) {
        _pipeline.push({$sort: {'metaData.nameInLowerCase': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
      } else if (sortBy == ExploreSortBy.SIZE) {
        _pipeline.push({$sort: {'metaData.fileSize': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
      } else if (sortBy == ExploreSortBy.VIDEO_INDEX) {
        _pipeline.push({
          $sort: {
            'metaData.sourceVideoId': sortValue ?? 1,
            'metaData.videoFrameIndex': sortValue ?? 1,
            'metaData._id': sortValue ?? 1,
          },
        });
      }
      // else {
      //   _pipeline.push({
      //     $sort: {
      //       'metaData.sourceVideoId': 1,
      //       'metaData.videoFrameIndex': 1,
      //       'metaData._id': 1,
      //     },
      //   });
      // }
      _pipeline.push({$skip: skipCount});
      _pipeline.push({$limit: pageSize});
      _pipeline.push({$unwind: '$metaData'});
      projectObj = {
        $project: {
          _id: '$metaData._id',
          name: '$metaData.name',
          objectKey: '$metaData.objectKey',
          createdAt: '$metaData.createdAt',
          updatedAt: '$metaData.updatedAt',
          thumbnailUrl: '$metaData.thumbnailUrl',
          objectType: '$metaData.objectType',
          annotationProjectList: '$metaData.annotationProjectList',
          curationProjectList: '$metaData.curationProjectList',
          verificationStatusCount: '$metaData.verificationStatusCount',
          frameId: '$metaData.frameId',
          videoFrameIndex: '$metaData.videoFrameIndex',
          datasetVersionList: '$metaData.datasetVersionList',
          objectStatus: '$metaData.objectStatus',
        },
      };
      _pipeline.push(projectObj);

      // logger.debug(JSON.stringify(_pipeline, null, 2));
    } else {
      // generate match filter for collection and virtual collection
      if (
        collectionObj.objectType == ContentType.IMAGE_COLLECTION ||
        collectionObj.objectType == ContentType.VIDEO_COLLECTION ||
        collectionObj.objectType == ContentType.DATASET ||
        collectionObj.objectType == ContentType.OTHER_COLLECTION
      ) {
        // let _matchQuery = await this.metaDataRepository.getMatchObjectsOfCollection(collectionObj);
        const _matchQuery = {vCollectionIdList: new ObjectId(collectionObj.id)};

        _pipeline.push({
          $match: _matchQuery,
        });
      } else {
        logger.error(
          `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | SearchQueryBuilderService.getObjectListOfCollection | N/A | invalid id for collection view, collectionId: ${collectionId}`,
        );
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_VIEW_NOT_APPLICABLE);
      }

      //filter match query create
      const filterMatchQuery: any = await this.getMatchQueryForExplorerFilter(
        filter,
        embeddingSelection,
        undefined,
        currentUserProfile,
      );

      //avoid sending url not generated metaObjects to frontend
      _pipeline.push({
        $match: {
          teamId: new ObjectId(teamId),
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
          // isAccessible: true,
          // isError: {$ne: true},
          // isMediaProcessingPending: {$ne: true},
          ...filterMatchQuery,
          ...queryFilter,
        },
      });

      // if (embeddingSelection?.graphId && embeddingSelection?.selection) {
      //   let embeddingLookupQuery: GetEmbeddingAggregationForSelectionRes[] =
      //     await this.getEmbeddingAggregationForSelection(embeddingSelection);

      //   if (Array.isArray(embeddingLookupQuery) && embeddingLookupQuery.length > 0) {
      //     _pipeline.push(...embeddingLookupQuery);
      //   }
      // }

      countQuery = [..._pipeline];

      // sort and paginate according to frontend requirement
      // if (collectionObj && collectionObj.isFromVideo) {
      //   _pipeline.push({$sort: {videoFrameIndex: 1}})
      // } else {
      //   _pipeline.push({$sort: {_id: 1}})
      // }
      if (sortBy == ExploreSortBy.DATE_MODIFIED) {
        _pipeline.push({$sort: {updatedAt: sortValue ?? -1, _id: sortValue ?? -1}});
      } else if (sortBy == ExploreSortBy.SIMILARITY_SCORE) {
        _pipeline.push({$sort: {customSortOrder: sortValue ?? 1, _id: -1}});
      } else if (sortBy == ExploreSortBy.DATE_CREATED) {
        _pipeline.push({$sort: {createdAt: sortValue ?? -1, _id: sortValue ?? -1}});
      } else if (sortBy == ExploreSortBy.NAME) {
        _pipeline.push({$sort: {nameInLowerCase: sortValue ?? 1, _id: sortValue ?? 1}});
      } else if (sortBy == ExploreSortBy.SIZE) {
        _pipeline.push({$sort: {fileSize: sortValue ?? 1, _id: sortValue ?? 1}});
      } else if (sortBy == ExploreSortBy.VIDEO_INDEX) {
        _pipeline.push({$sort: {sourceVideoId: sortValue ?? 1, videoFrameIndex: sortValue ?? 1, _id: sortValue ?? 1}});
      } else {
        _pipeline.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
      }

      //_pipeline.push({$sort: {updatedAt: -1, _id: -1}})
      _pipeline.push({$skip: skipCount});
      _pipeline.push({$limit: pageSize});
      _pipeline.push(projectObj);
      //logger.debug("getItemListOfCollection: ", JSON.stringify(_pipeline, null, 2))
      //execute async function paralally to get item count & item list
      // await Promise.all([
      //   this.getItemCountOfCollection(collectionId, explorerCollectionViewResponse, teamId, countQuery),
      //   this.getItemListOfCollection(_pipeline, skipCount, explorerCollectionViewResponse)
      // ])
      // logger.debug(JSON.stringify(_pipeline, null, 2));
    }

    if (exportApiType == Explore_API_TYPE.COUNT) {
      if (
        Object.keys(queryFilter).length == 0 &&
        ((filter && !filter.date && filter.annotationTypes && filter.annotationTypes.length == 0) || !filter) &&
        !referenceImage
      ) {
        if (
          collectionObj.objectType == ContentType.IMAGE_COLLECTION ||
          collectionObj.objectType == ContentType.DATASET
        ) {
          explorerCollectionViewResponse.count = collectionObj.frameCount ? collectionObj.frameCount : 0;
        } else if (collectionObj.objectType == ContentType.VIDEO_COLLECTION) {
          explorerCollectionViewResponse.count = collectionObj.videoCount ? collectionObj.videoCount : 0;
        } else if (collectionObj.objectType == ContentType.OTHER_COLLECTION) {
          explorerCollectionViewResponse.count = collectionObj.otherCount ? collectionObj.otherCount : 0;
        }
      } else {
        // if similarity search occur count query check similarArray in MetaData collection
        if (referenceImage)
          countQuery.push({
            $match: {
              similarArray: {
                $elemMatch: {
                  referenceObjectKey: referenceImage,
                  modelName: modelName,
                  scoreThreshold: score,
                },
              },
            },
          });
        //logger.debug(JSON.stringify(countQuery, null, 2));
        await this.getItemCountOfCollection(collectionId, explorerCollectionViewResponse, teamId, countQuery);
      }
    } else if (exportApiType == Explore_API_TYPE.LIST) {
      //logger.debug(JSON.stringify(_pipeline, null, 2));
      await this.getItemListOfCollection(
        _pipeline,
        skipCount,
        explorerCollectionViewResponse,
        collectionObj,
        currentUserProfile,
        referenceImage,
      );
    }

    return explorerCollectionViewResponse;
  }

  /**
   * Use to update item count field of explorerCollectionViewResponse
   * @param collectionId {string} id of collection going to be viewed
   * @param explorerCollectionViewResponse response object of this.getObjectListOfCollection function
   * @returns none
   */
  async getItemCountOfCollection(
    collectionId: string,
    explorerCollectionViewResponse: ExplorerCollectionViewResponse,
    teamId?: string,
    queryFilter?: any,
  ) {
    const query = [...queryFilter];

    query.push({$group: {_id: '$objectType', count: {$sum: 1}}});
    // logger.debug(JSON.stringify(query, null, 2));

    const startTime = new Date().getTime();
    const countList: {_id: number; count: number}[] = await this.metaDataRepository.aggregate(query);
    const endTime = new Date().getTime();
    const responseTime = endTime - startTime;
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getItemCountOfCollection | N/A | collection object count aggregation responseTime=${responseTime}`,
    );

    const collectionObj = await this.metaDataRepository.findOne({
      where: {id: collectionId},
      fields: {objectType: true},
    });
    let type = null;
    if (collectionObj?.objectType == ContentType.IMAGE_COLLECTION || collectionObj?.objectType == ContentType.DATASET) {
      type = ContentType.IMAGE;
    } else if (collectionObj?.objectType == ContentType.VIDEO_COLLECTION) {
      type = ContentType.VIDEO;
    } else if (collectionObj?.objectType == ContentType.OTHER_COLLECTION) {
      type = ContentType.OTHER;
    }

    if (countList && countList.length > 0 && type) {
      let count = 0;
      for (const obj of countList) {
        if (obj._id == type) {
          count = obj.count;
        }
      }
      explorerCollectionViewResponse.count = count;
    }
  }

  /**
   * Use to update item list field of explorerCollectionViewResponse
   * @param pipeline {array} aggregate query
   * @param explorerCollectionViewResponse response object of this.getObjectListOfCollection function
   * @returns none
   */
  async getItemListOfCollection(
    pipeline: any[],
    skipCount: number,
    explorerCollectionViewResponse: ExplorerCollectionViewResponse,
    collectionObj: Partial<MetaData>,
    currentUserProfile: UserProfileDetailed,
    referenceImage?: string,
  ) {
    const startTime = new Date().getTime();
    let childList: MetaData[] = [];
    logger.debug('pipeline: ', JSON.stringify(pipeline, null, 2));
    if (referenceImage) {
      childList = await this.similarImageRepository.aggregate(pipeline);
    } else {
      childList = await this.metaDataRepository.aggregate(pipeline);
    }
    const endTime = new Date().getTime();
    const responseTime = endTime - startTime;
    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | SearchQueryBuilderService.getItemListOfCollection | N/A | collection object list aggregation responseTime=${responseTime}`,
    );

    const formattedChildList = childList.map((_childObj, idx) => {
      const formattedChildObj = this.getFormattedMetaObject(
        _childObj,
        skipCount,
        idx,
        currentUserProfile,
        collectionObj,
      );
      return formattedChildObj;
    });
    explorerCollectionViewResponse.itemList = formattedChildList;
  }

  /**
   * Use to format the metadata object according to frontend requirement
   * @param metaObj MetaData object
   * @returns formatted metadata object
   */
  getFormattedMetaObject(
    metaObj: Partial<MetaData>,
    skipCount: number,
    itemIdx: number,
    currentUserProfile: UserProfileDetailed,
    collectionObj?: Partial<MetaData> | undefined,
  ) {
    const verificationStatusList: VerificationStatusFrontendConstants[] = [];
    if (metaObj.verificationStatusCount) {
      if (metaObj.verificationStatusCount.raw > 0) {
        verificationStatusList.push(VerificationStatusFrontendConstants.RAW);
      }
      if (metaObj.verificationStatusCount.machineAnnotated > 0) {
        verificationStatusList.push(VerificationStatusFrontendConstants.MACHINE_ANNOTATED);
      }
      if (metaObj.verificationStatusCount.verified > 0) {
        verificationStatusList.push(VerificationStatusFrontendConstants.VERIFIED);
      }
    }

    // format dataset list
    const datasetList: {id: string; name: string}[] = [];
    const datasetIdList: string[] = [];
    if (metaObj.datasetVersionList && metaObj.datasetVersionList.length > 0) {
      for (const version of metaObj.datasetVersionList) {
        if (version.datasetGroupId) {
          const listObj = {
            id: version.datasetGroupId.toString(),
            name: version.datasetGroupName,
          };
          if (!datasetIdList.includes(version.datasetGroupId.toString())) {
            datasetList.push(listObj);
            datasetIdList.push(version.datasetGroupId.toString());
          }
        }
      }
    }

    // configure option list
    const optionList: OptionListType[] = [];

    if (metaObj.objectStatus == OBJECT_STATUS.TRASHED) {
      optionList.push(OptionListType.RESTORE);
      if (currentUserProfile && 
          currentUserProfile.userType &&
          [UserType.USER_TYPE_TEAM_ADMIN, UserType.USER_TYPE_SUPER_ADMIN].includes(currentUserProfile.userType)
        ) {
        optionList.push(OptionListType.DELETE);
      }
    } else {
      optionList.push(OptionListType.ADD_META);
      optionList.push(OptionListType.ADD_TAGS);

      if (
        metaObj.objectType == ContentType.IMAGE_COLLECTION ||
        metaObj.objectType == ContentType.VIDEO_COLLECTION ||
        metaObj.objectType == ContentType.OTHER_COLLECTION ||
        metaObj.objectType == ContentType.DATASET
      ) {
        if (
          currentUserProfile?.userType &&
          [UserType.USER_TYPE_TEAM_ADMIN, UserType.USER_TYPE_COLLABORATOR, UserType.USER_TYPE_AUDITOR, UserType.USER_TYPE_SUPER_ADMIN].includes(
            currentUserProfile.userType,
          )
        ) {
          optionList.push(OptionListType.SHARE);
        }

        optionList.push(OptionListType.COPY_LINK);

        if (metaObj.objectType != ContentType.DATASET) {
          optionList.push(OptionListType.UPLOAD_DATA);
        }
      } else if (metaObj.objectType == ContentType.IMAGE) {
        optionList.push(OptionListType.SIMILAR_SEARCH);
      }

      if (this.checkIfObjectRemoveEnabled(metaObj.objectType, collectionObj)) {
        optionList.push(OptionListType.REMOVE);
      }

      const {isSuccess} = this.metaDataRepository.validateObjectToTrash(metaObj, collectionObj);
      if (isSuccess) {
        optionList.push(OptionListType.TRASH);
      }
    }

    const formattedMetaObj: ItemObject = {
      id: metaObj._id,
      name: metaObj.name,
      //File count is only applicable for other collections and frame count is only applicable for video/image/dataset collections
      fileCount: metaObj.objectType == ContentType.OTHER_COLLECTION ? metaObj.otherCount : undefined,
      frameCount:
        metaObj.objectType == ContentType.OTHER || metaObj.objectType == ContentType.OTHER_COLLECTION
          ? undefined
          : metaObj.frameCount,
      frameId: metaObj.objectType != ContentType.VIDEO ? skipCount + itemIdx + 1 : undefined,
      videoFrameIndex: metaObj.videoFrameIndex,
      thumbnailUrl: metaObj.thumbnailUrl,
      url: metaObj.url,
      objectKey: metaObj.objectKey,
      objectType: metaObj.objectType,
      createdAt: metaObj.createdAt,
      updatedAt: metaObj.updatedAt,
      annotationProjectList: metaObj.annotationProjectList,
      curationProjectList: metaObj.curationProjectList,
      verificationStatusList: verificationStatusList,
      uploadInProgress: metaObj.uploadInProgress,
      uploadingUserId: metaObj.uploadingUserId,
      datasetProjectList: datasetList,
      optionList: optionList,
      score: metaObj.score,
      fileTitle: metaObj.fileTitle,
      namedEntities: metaObj.namedEntities,
      fileType: metaObj.fileType,
    };

    return formattedMetaObj;
  }

  /**
   * Check if the meta object remove option is available
   * @param objectType {ContentType} object type of the meta data
   * @param collectionObj {Partial<MetaData>} collection data
   * @returns whether object remove is enabled
   *
   */
  checkIfObjectRemoveEnabled(objectType?: ContentType, collectionObj?: Partial<MetaData>): boolean {
    const enabledContent: ContentType[] = [
      ContentType.IMAGE_COLLECTION,
      ContentType.VIDEO_COLLECTION,
      ContentType.OTHER_COLLECTION,
    ];

    if (
      objectType &&
      ((collectionObj?.objectType && enabledContent.includes(collectionObj.objectType)) ||
        enabledContent.includes(objectType))
    )
      return true;
    else return false;
  }

  /**
   * Use to get initial info of metaData object for datalake explorer detail view
   * @param metaDataId mongodb id of meta data object
   * @returns object with details of meta object (response is vary according to meta object type)
   */
  async getDetailViewInfo(
    metaDataId: string,
    currentUserProfile: UserProfileDetailed,
    contentType: ContentType,
    timeZoneOffset?: number,
    navigatedCollectionId?: string,
    query?: {[k: string]: any},
    explorerFilter?: ExplorerFilterV2,
    sortByField?: ExploreSortBy,
    sortOrder?: SortOrder,
    referenceImage?: string,
    embeddingSelection?: EmbeddingSelectionObject,
  ) {
    logger.info(
      `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getDetailViewInfo | N/A | get detail view initial info for metaDataId: ${metaDataId}`,
    );
    if (!currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getDetailViewInfo | N/A | invalid teamId for user, userId: ${currentUserProfile.id}`,
      );
      throw new HttpErrors.NotAcceptable(`Invalid teamId for user, userId: ${currentUserProfile}`);
    }
    const teamId = currentUserProfile.teamId.toString();

    const metaObj = await this.metaDataRepository.findById(metaDataId);

    // if viewing meta object is a video
    if (metaObj.objectType == ContentType.VIDEO) {
      return {
        name: metaObj.name,
        metaDataId: metaObj.id,
        frameCount: metaObj.frameCount,
        contentType: metaObj.objectType,
        videoUrl: metaObj.url,
        frameRate: metaObj.frameRate,
        width: metaObj.resolution ? metaObj.resolution.width : undefined,
        height: metaObj.resolution ? metaObj.resolution.height : undefined,
        hasCollection: metaObj.frameCollectionId ? true : false, // whether video has a frame collection
        collectionId: metaObj.frameCollectionId, // frame collection id of the video
        metaData: this.getMetaDataArray(metaObj, timeZoneOffset),
        tags: metaObj.Tags,
      };
    }
    // if viewing meta object is a image
    else if (metaObj.objectType == ContentType.IMAGE) {
      let _frameCount = 0;

      // get frame count for images (user has clicked an image after navigated into a image collection view or a dataset collection view)
      if (navigatedCollectionId && isValidObjectId(navigatedCollectionId)) {
        const response = await this.getObjectListOfCollection(
          navigatedCollectionId,
          0, // default value, but this will not use for count calculation
          4, // default value, but this will not use for count calculation
          explorerFilter || {},
          contentType,
          currentUserProfile,
          query,
          referenceImage,
          EMBEDDING_COLLECTION,
          SIMILARITY_SCORE_THRESHOLD,
          sortByField,
          Explore_API_TYPE.COUNT,
          undefined,
          sortOrder,
          embeddingSelection,
        );
        _frameCount = response.count;
      }
      // get frame count for images (user has clicked and image after executing a search query in a explorer tab view)
      else {
        const response = await this.getObjectListToExplorer(
          explorerFilter || {},
          contentType,
          0, // default value, but this will not use for count calculation
          4, // default value, but this will not use for count calculation
          currentUserProfile,
          query,
          referenceImage,
          EMBEDDING_COLLECTION,
          SIMILARITY_SCORE_THRESHOLD,
          sortByField,
          Explore_API_TYPE.COUNT,
          undefined,
          sortOrder,
          embeddingSelection,
        );
        _frameCount = response.count || 0;
      }

      return {
        //collectionName: collectionObj.name, // frame colelction id which image belongs to
        frameCount: _frameCount, // frame count of collection which image belongs to
        hasCollection: true, // whether image belongs to a frame collection
        contentType: metaObj.objectType,
        collectionId: '', // frame colelction id which image belongs to
        metaDataId: metaObj.id, // id of clicked image
        clickedImageData: {
          url: metaObj.url,
          name: metaObj.name,
          width: metaObj.resolution ? metaObj.resolution.width : undefined,
          height: metaObj.resolution ? metaObj.resolution.height : undefined,
        },
      };
    }
    // if viewing meta object is other file
    else if (metaObj.objectType == ContentType.OTHER) {
      return {
        name: metaObj.name,
        metaDataId: metaObj.id,
        fileCount: metaObj.otherCount || 0,
        contentType: metaObj.objectType,
        metaData: this.getMetaDataArray(metaObj, timeZoneOffset),
        tags: metaObj.Tags,
        fileUrl: metaObj.url,
      };
    } else if (
      metaObj.objectType == ContentType.IMAGE_COLLECTION ||
      metaObj.objectType == ContentType.VIDEO_COLLECTION ||
      metaObj.objectType == ContentType.DATASET ||
      metaObj.objectType == ContentType.OTHER_COLLECTION
    ) {
      const frameArr: {key: string; value: string}[] = [];

      if (metaObj.verificationStatusCount) {
        if (metaObj.verificationStatusCount.raw) {
          frameArr.push({
            key: VerificationStatusFrontendConstantsInDetailView.RAW,
            value: getFormattedItemCount(metaObj.verificationStatusCount.raw),
          });
        }
        if (metaObj.verificationStatusCount.machineAnnotated) {
          frameArr.push({
            key: VerificationStatusFrontendConstantsInDetailView.MACHINE_ANNOTATED,
            value: getFormattedItemCount(metaObj.verificationStatusCount.machineAnnotated),
          });
        }
        if (metaObj.verificationStatusCount.verified) {
          frameArr.push({
            key: VerificationStatusFrontendConstantsInDetailView.VERIFIED,
            value: getFormattedItemCount(metaObj.verificationStatusCount.verified),
          });
        }
      }

      const labelsArr: {key: string; value: string}[] = [];

      if (metaObj.labelList) {
        const labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId); //NOTE: add teamId filter
        for (const label of metaObj.labelList) {
          labelsArr.push({
            key: labelToLabelTextMap[label.label] ? labelToLabelTextMap[label.label] : label.label,
            value: getFormattedItemCount(label.count),
          });
        }
      }

      return {
        name: metaObj.name,
        metaDataId: metaObj.id,
        frameCount: metaObj.frameCount,
        fileCount: (metaObj.videoCount || 0) + (metaObj.imageCount || 0) + (metaObj.otherCount || 0),
        contentType: metaObj.objectType,
        metaData: this.getMetaDataArray(metaObj, timeZoneOffset),
        tags: metaObj.Tags,
        frames: frameArr,
        labels: labelsArr,
      };
    } else {
      logger.error(
        `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getDetailViewInfo | N/A | Detail view not applicable for metaDataId:${metaDataId}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.META_DATA_DETAIL_VIEW_NOT_APPLICABLE);
    }
  }

  /**
   * Use to get Key,Value pairs of MetaData object as a MetaData array according to frontend requirements
   * @param metaObj MetaData object as it is in db
   * @returns Key,Value pairs of MetaData object as a MetaData array according to frontend requirements
   */
  getMetaDataArray(metaObj: Partial<MetaData>, timeZoneOffset?: number) {
    const metaDataArr: any[] = [];

    // push to array known and prioritized metadata first
    if (metaObj.name) {
      if (
        metaObj.objectType == ContentType.VIDEO ||
        metaObj.objectType == ContentType.IMAGE ||
        metaObj.objectType == ContentType.OTHER
      ) {
        metaDataArr.push({
          key: 'File Name',
          value: metaObj.name,
        });
      } else if (
        metaObj.objectType == ContentType.IMAGE_COLLECTION ||
        metaObj.objectType == ContentType.VIDEO_COLLECTION ||
        metaObj.objectType == ContentType.OTHER_COLLECTION
      ) {
        metaDataArr.push({
          key: 'Collection Name',
          value: metaObj.name,
        });
      }
    }

    if (metaObj.id) {
      if (
        metaObj.objectType == ContentType.IMAGE_COLLECTION ||
        metaObj.objectType == ContentType.VIDEO_COLLECTION ||
        metaObj.objectType == ContentType.OTHER_COLLECTION
      ) {
        metaDataArr.push({
          key: 'collectionId',
          value: metaObj.id,
        });
      }
    }

    if (metaObj.fileSize) {
      metaDataArr.push({
        key: 'Size',
        value: byteTransform(metaObj.fileSize, false),
      });
    }

    if (metaObj.frameCount) {
      if (
        metaObj.objectType == ContentType.VIDEO ||
        metaObj.objectType == ContentType.VIDEO_COLLECTION ||
        metaObj.objectType == ContentType.IMAGE_COLLECTION
      ) {
        metaDataArr.push({
          key: 'Frames',
          value: getFormattedItemCount(metaObj.frameCount),
        });
      }
    }

    if (metaObj.createdAt) {
      const uploadDate: Date = timeZoneOffset ? getLocalTime(timeZoneOffset, metaObj.createdAt) : metaObj.createdAt;

      metaDataArr.push({
        key: 'Uploaded Date',
        value: moment(uploadDate).format('MMM D, YYYY h:mm A'),
      });
    }

    if (metaObj.updatedAt) {
      const lastModified: Date = timeZoneOffset ? getLocalTime(timeZoneOffset, metaObj.updatedAt) : metaObj.updatedAt;

      metaDataArr.push({
        key: 'Last Modified',
        value: moment(lastModified).format('MMM D, YYYY h:mm A'),
      });
    }

    if (metaObj.url) {
      if (metaObj.objectType == ContentType.IMAGE) {
        metaDataArr.push({
          key: 'Image URL',
          value: metaObj.url,
        });
      } else if (metaObj.objectType == ContentType.VIDEO) {
        metaDataArr.push({
          key: 'Video URL',
          value: metaObj.url,
        });
      }
    }

    if (metaObj.objectKey) {
      metaDataArr.push({
        key: 'Unique Name',
        value: metaObj.objectKey,
      });
    }

    if (metaObj.bucketName) {
      metaDataArr.push({
        key: 'Storage Name',
        value: metaObj.bucketName,
      });
    }

    if (metaObj.storagePath) {
      metaDataArr.push({
        key: 'Storage Path',
        value: metaObj.storagePath,
      });
    }

    if (metaObj.labelList) {
      let labelCount = 0;
      for (const label of metaObj.labelList) {
        labelCount = labelCount + label.count;
      }
      metaDataArr.push({
        key: 'Number of Labels',
        value: getFormattedItemCount(labelCount),
      });
    }

    if (metaObj.resolution) {
      metaDataArr.push({
        key: 'Resolution',
        value: metaObj.resolution.width + 'x' + metaObj.resolution.height,
      });
    }

    if (metaObj.videoLength) {
      metaDataArr.push({
        key: 'Video Length',
        value: getFormattedVideoLength(metaObj.videoLength),
      });
    }

    if (metaObj.frameRate) {
      metaDataArr.push({
        key: 'Frame Rate',
        value: metaObj.frameRate.toFixed(2),
      });
    }

    if (metaObj.videoFrameIndex) {
      metaDataArr.push({
        key: 'Video Frame',
        value: metaObj.videoFrameIndex,
      });
    }

    // push to array unknown/custom metadata
    if (metaObj.customMeta) {
      for (const [key, value] of Object.entries(metaObj.customMeta)) {
        metaDataArr.push({
          key: key,
          value: value,
        });
      }
    }

    return metaDataArr;
  }

  /**
   * Use to get annotaion data in a frame range of a frame collection by user by clicking metaObject (opening player view)
   * @param filterOptions {MetaWithMetaUpdatesFilter} frameCollectionId, frameWindow start point and end point
   * @returns  MetaWithMetaUpdates[]
   */
  async getMetaWithMetaUpdates(
    filterOptions: MetaWithMetaUpdatesFilter,
    currentUserProfile: UserProfileDetailed,
    sortObject: SortObject,
    timeZoneOffset?: number,
  ) {
    const matchArr: any[] = [];
    if (!currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getMetaWithMetaUpdates | N/A | invalid teamId for user, userId: ${currentUserProfile.id}`,
      );
      throw new HttpErrors.NotAcceptable(`Invalid teamId for user, userId: ${currentUserProfile}`);
    }
    const teamId = currentUserProfile.teamId.toString();

    const params: any[] = [
      {
        $match: {
          $and: matchArr,
        },
      },
    ];
    const aggregateQuery: any[] = [];

    let skipCount: number = 0;

    // user going to view a video with its frames data in player view
    if (filterOptions.contentType == ContentType.VIDEO) {
      if (filterOptions.collectionId) {
        const collectionObj = await this.metaDataRepository.findById(filterOptions.collectionId);

        matchArr.push({collectionId: new ObjectId(filterOptions.collectionId)});
        //replace flag with enum
        matchArr.push({objectStatus: OBJECT_STATUS.ACTIVE});

        // matchArr.push({isAccessible: true});
        // matchArr.push({isError: {$ne: true}});
        // matchArr.push({objectStatus: {$ne: OBJECT_STATUS.TRASHED}});
        matchArr.push({
          videoFrameIndex: {
            $gte: filterOptions.frameWindow.start,
            $lte: filterOptions.frameWindow.end,
          },
        });
        params.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
        // if (!sortObject?.sortByField) params.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
        // else {
        //   let sortObj = await this.getListSortingObj(false, sortObject?.sortByField, sortObject?.sortOrder);
        //   if (JSON.stringify(sortObj) != '{}') params.push(sortObj);
        // }
      } else {
        logger.error(
          `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getMetaWithMetaUpdates | N/A | No frameCollectionId to Fetch MetaData with MetaUpdated:${filterOptions}`,
        );
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.META_DATA_DETAIL_VIEW_FETCH_METAUPDATES_NOT_APPLICABLE);
      }
    }
    // user going to view a image collection (actual image collection or virtual image collection) in player view
    else if (
      filterOptions.contentType == ContentType.IMAGE ||
      filterOptions.contentType == ContentType.IMAGE_COLLECTION ||
      filterOptions.contentType == ContentType.DATASET
    ) {
      //if filter options contain referenceImage its default sort order should be
      //in similarity score, so for reduce time in sorting it is aggregated from
      //similarData collection, if there are different sort fields we have to implement it
      //middle of different sort block
      if (filterOptions.referenceImage) {
        aggregateQuery.push({
          $match: {
            referenceObjectKey: filterOptions.referenceImage,
            modelName: filterOptions.modelName,
            scoreThreshold: filterOptions.scoreThreshold,
          },
        });
        aggregateQuery.push({
          $group: {
            _id: '$objectKey',
            objectKey: {$first: '$objectKey'},
            score: {$first: '$score'},
          },
        });
        //This will already sorted according to similarity score
        if (sortObject?.sortByField == ExploreSortBy.SIMILARITY_SCORE) {
          aggregateQuery.push({$sort: {score: -1}});
        }
        aggregateQuery.push({
          $lookup: {
            from: 'MetaData',
            foreignField: 'objectKey',
            localField: 'objectKey',
            as: 'metaData',
          },
        });
        if (filterOptions.navigatedCollectionId && isValidObjectId(filterOptions.navigatedCollectionId)) {
          const _matchQuery = {'metaData.vCollectionIdList': new ObjectId(filterOptions.navigatedCollectionId)};
          matchArr.push(_matchQuery);
        } else {
          matchArr.push({'metaData.objectType': ContentType.IMAGE});
        }
        //replace flag with enum
        matchArr.push({'metaData.objectStatus': OBJECT_STATUS.ACTIVE});

        const rawQuery: string = await this.combineFilterAndQuery(filterOptions?.filterData, filterOptions?.query);

        if (rawQuery) {
          const formattedQuery = await this.generateQuery(rawQuery, teamId, 'metaData');
          if (Object.keys(formattedQuery).length != 0) matchArr.push(formattedQuery);
        }

        if (filterOptions.filterData || filterOptions.embeddingSelection) {
          const filterMatchQuery: any = await this.getMatchQueryForExplorerFilter(
            filterOptions.filterData,
            filterOptions.embeddingSelection,
            'metaData',
            currentUserProfile,
          );
          matchArr.push(filterMatchQuery);
        }

        //paginating
        const skip = filterOptions.frameWindow.start - 1;
        skipCount = skip;
        const limit = filterOptions.frameWindow.end - filterOptions.frameWindow.start + 1;
        //different sort block
        let sortValue = 1;
        if (sortObject.sortOrder) {
          sortValue = sortObject.sortOrder == SortOrder.ASC ? 1 : -1;
        }
        if (sortObject.sortByField == ExploreSortBy.DATE_MODIFIED) {
          aggregateQuery.push({$sort: {'metaData.updatedAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
        }
        //This will already sorted according to similarity score
        else if (sortObject.sortByField == ExploreSortBy.SIMILARITY_SCORE) {
        } else if (sortObject.sortByField == ExploreSortBy.DATE_CREATED) {
          aggregateQuery.push({$sort: {'metaData.createdAt': sortValue ?? -1, 'metaData._id': sortValue ?? -1}});
        } else if (sortObject.sortByField == ExploreSortBy.NAME) {
          aggregateQuery.push({$sort: {'metaData.nameInLowerCase': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
        } else if (sortObject.sortByField == ExploreSortBy.SIZE) {
          aggregateQuery.push({$sort: {'metaData.fileSize': sortValue ?? 1, 'metaData._id': sortValue ?? 1}});
        } else if (sortObject.sortByField == ExploreSortBy.VIDEO_INDEX) {
          aggregateQuery.push({
            $sort: {
              'metaData.sourceVideoId': sortValue ?? 1,
              'metaData.videoFrameIndex': sortValue ?? 1,
              'metaData._id': sortValue ?? 1,
            },
          });
        }
        //different sort block
        params.push({$skip: skip});
        params.push({$limit: limit});
        aggregateQuery.push(...params);
        aggregateQuery.push({$unwind: '$metaData'});
      } else {
        if (filterOptions.navigatedCollectionId && isValidObjectId(filterOptions.navigatedCollectionId)) {
          const _matchQuery = {vCollectionIdList: new ObjectId(filterOptions.navigatedCollectionId)};
          matchArr.push(_matchQuery);

          if (!sortObject.sortByField) params.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
          else {
            const sortObj = await this.getListSortingObj(false, sortObject.sortByField, sortObject.sortOrder);
            if (JSON.stringify(sortObj) != '{}') params.push(sortObj);
          }
        } else {
          matchArr.push({objectType: ContentType.IMAGE});

          if (!sortObject.sortByField) params.push({$sort: {updatedAt: -1, _id: -1}});
          else {
            const sortObj = await this.getListSortingObj(false, sortObject.sortByField, sortObject.sortOrder);
            if (JSON.stringify(sortObj) != '{}') params.push(sortObj);
          }
        }
        //replace flag with enum
        matchArr.push({objectStatus: OBJECT_STATUS.ACTIVE});

        const rawQuery: string = await this.combineFilterAndQuery(filterOptions?.filterData, filterOptions?.query);

        if (rawQuery) {
          const formattedQuery = await this.generateQuery(rawQuery, teamId);
          if (Object.keys(formattedQuery).length != 0) matchArr.push(formattedQuery);
        }

        if (filterOptions.filterData || filterOptions.embeddingSelection) {
          const filterMatchQuery: any = await this.getMatchQueryForExplorerFilter(
            filterOptions.filterData,
            filterOptions.embeddingSelection,
            undefined,
            currentUserProfile,
          );
          matchArr.push(filterMatchQuery);
        }

        //paginating
        const skip = filterOptions.frameWindow.start - 1;
        skipCount = skip;
        const limit = filterOptions.frameWindow.end - filterOptions.frameWindow.start + 1;
        params.push({$skip: skip});
        params.push({$limit: limit});
      }
    }
    let metaData: MetaData[] = [];
    if (filterOptions.referenceImage) {
      aggregateQuery.push({
        $project: {
          _id: '$metaData._id',
          objectKey: '$metaData.objectKey',
          objectType: '$metaData.objectType',
          url: '$metaData.url',
          name: '$metaData.name',
          resolution: '$metaData.resolution',
          videoFrameIndex: '$metaData.videoFrameIndex',
          Tags: '$metaData.Tags',
          fileSize: '$metaData.fileSize',
          frameCount: '$metaData.frameCount',
          createdAt: '$metaData.createdAt',
          updatedAt: '$metaData.updatedAt',
          bucketName: '$metaData.bucketName',
          storagePath: '$metaData.storagePath',
          labelList: '$metaData.labelList',
          videoLength: '$metaData.videoLength',
          frameRate: '$metaData.frameRate',
          customMeta: '$metaData.customMeta',
        },
      });

      // logger.debug(JSON.stringify(aggregateQuery, null, 2));
      metaData = await this.similarImageRepository.aggregate(aggregateQuery);
    } else {
      params.push({
        $project: {
          _id: 1,
          objectKey: 1,
          objectType: 1,
          url: 1,
          name: 1,
          resolution: 1,
          videoFrameIndex: 1,
          Tags: 1,
          fileSize: 1,
          frameCount: 1,
          createdAt: 1,
          updatedAt: 1,
          bucketName: 1,
          storagePath: 1,
          labelList: 1,
          videoLength: 1,
          frameRate: 1,
          customMeta: 1,
        },
      });

      //logger.debug(JSON.stringify(params, null, 2))

      // get list of MetaData records
      metaData = await this.metaDataRepository.aggregate(params);
    }

    const metaDataKeys: string[] = [];
    metaData.forEach(metaData => {
      if (metaData.objectKey) metaDataKeys.push(metaData.objectKey);
    });

    const metaUpdatesToKeyMaps: MetaUpdatesToKeyMaps = {};

    const promiseArray: Promise<void>[] = [
      this.metaDataUpdateRepository.getGroundTruthDataToKeyMap(metaDataKeys, metaUpdatesToKeyMaps),
      this.metaDataUpdateRepository.getModelsRunDataToKeyMap(metaDataKeys, metaUpdatesToKeyMaps),
    ];
    await Promise.all(promiseArray);

    const labelToLabelTextMap = await this.systemLabelService.getLabelValRefToTextMapOfTeam(teamId);

    const metaListWithMetaUpdates = metaData.map((_meta, idx) => {
      // define a frame index for the each frame
      let frameId: number | undefined;
      if (filterOptions.contentType == ContentType.VIDEO) {
        frameId = _meta.videoFrameIndex;
      } else {
        frameId = skipCount + idx + 1;
      }

      const metaWithMetaUpdates: MetaWithMetaUpdates = {
        id: _meta._id,
        frameId: frameId,
        objectKey: _meta.objectKey,
        url: _meta.url,
        name: _meta.name,
        width: _meta.resolution ? _meta.resolution.width : undefined,
        height: _meta.resolution ? _meta.resolution.height : undefined,
        metaData:
          filterOptions.contentType == ContentType.VIDEO ? undefined : this.getMetaDataArray(_meta, timeZoneOffset),
        tags: _meta.Tags ? _meta.Tags : [],
      };

      const allAnnotationObjects: ExtendedAnnotationObject[] = [];

      if (_meta.objectKey) {
        // assign ground truth data
        if (metaUpdatesToKeyMaps.groundTruthToKeyMap) {
          if (metaUpdatesToKeyMaps.groundTruthToKeyMap[_meta.objectKey]) {
            // ground truth array has only one element (this made as a array with one element in order to make data type similar with model runs)
            const groundTruthArr = metaUpdatesToKeyMaps.groundTruthToKeyMap[_meta.objectKey];
            const formattedGroundTruthArr: MetaUpdatesLabelGroup[] = this.getFormattedMetaUpdatesLabelGroup(
              groundTruthArr,
              allAnnotationObjects,
              labelToLabelTextMap,
              OperationMode.HUMAN,
            );
            metaWithMetaUpdates.groundTruth = formattedGroundTruthArr;
          } else {
            metaWithMetaUpdates.groundTruth = [];
          }
        }

        // assign model runs data
        if (metaUpdatesToKeyMaps.modelRunsToKeyMap) {
          if (metaUpdatesToKeyMaps.modelRunsToKeyMap[_meta.objectKey]) {
            const modelRunsArr = metaUpdatesToKeyMaps.modelRunsToKeyMap[_meta.objectKey];
            const formattedModelRunsArr: MetaUpdatesLabelGroup[] = this.getFormattedMetaUpdatesLabelGroup(
              modelRunsArr,
              allAnnotationObjects,
              labelToLabelTextMap,
              OperationMode.AUTO,
            );
            metaWithMetaUpdates.modelRuns = formattedModelRunsArr;
          } else {
            metaWithMetaUpdates.modelRuns = [];
          }
        }
      }

      metaWithMetaUpdates.annotationObjects = allAnnotationObjects;

      return metaWithMetaUpdates;
    });

    return metaListWithMetaUpdates;
  }

  /**
   * Use to format labewise & operationwise grouped annotation data according to frontend requirenment
   * @param metaUpdatesLabelGroup mongodb aggregate result of labewise & operationwise grouped annotation data
   * @param allAnnotationObjects array to push all annotation objects
   * @param labelToLabelTextMap SystemLabel reference to test mapped object
   * @returns MetaUpdatesLabelGroup[] formatted data array
   */
  getFormattedMetaUpdatesLabelGroup(
    metaUpdatesLabelGroup: MetaUpdatesLabelGroup[],
    allAnnotationObjects: ExtendedAnnotationObject[],
    labelToLabelTextMap: {[k: string]: string},
    operationMode: OperationMode,
  ) {
    const formattedMetaUpdatesLabelGroup: MetaUpdatesLabelGroup[] = [];
    for (const _labelGroup of metaUpdatesLabelGroup) {
      const labelGroup: MetaUpdatesLabelGroup = {
        labelCount: _labelGroup.labelCount,
        labelList: [],
        operationId: _labelGroup.operationId,
        operationName: _labelGroup.operationName,
      };

      for (const label of _labelGroup.labelList) {
        const operationModeAddedAnnotationObjects = label.annotationObjects.map(_obj => ({
          ..._obj,
          operationMode: operationMode,
        }));
        allAnnotationObjects.push(...operationModeAddedAnnotationObjects);

        const fieldsReducedAnnotationObjects: Partial<AnnotationObject>[] = [];
        for (const annoObj of label.annotationObjects) {
          if (annoObj.label) {
            if (annoObj.label.label) {
              fieldsReducedAnnotationObjects.push({
                id: annoObj.id,
                label: {
                  label: annoObj.label.label,
                  labelText: labelToLabelTextMap[annoObj.label.label]
                    ? labelToLabelTextMap[annoObj.label.label]
                    : annoObj.label.label,
                },
                objectInfo: this.getAnnotationObjectInfo(annoObj, labelToLabelTextMap),
              });
            }
          }
        }
        labelGroup.labelList.push({
          label: label.label,
          labelText: labelToLabelTextMap[label.label] ? labelToLabelTextMap[label.label] : label.label,
          count: label.count,
          annotationObjects: fieldsReducedAnnotationObjects,
        });
      }

      formattedMetaUpdatesLabelGroup.push(labelGroup);
    }

    return formattedMetaUpdatesLabelGroup;
  }

  /**
   * Use to get Project List of a given metaData object
   * @param metaDataId mongodbId of metaData object
   * @returns MetaUpdateProjectInfo[]
   */
  async getProjectListOfMetaObject(metaDataId: string) {
    let projectInfoList: MetaUpdateProjectInfo[] = [];

    const metaObj = await this.metaDataRepository.findById(metaDataId);

    //Fetch annotation projects details
    const annotationProjectList = metaObj.annotationProjectList || [];
    const annotationProjectIdList = annotationProjectList.map(proj => proj.id.toString());
    const annotationProjectInfoList: AnnotationProjectInfo[] = await this.getAnnotationProjectInfoList(
      metaDataId,
      annotationProjectIdList,
    );

    // get dataset project list
    const datasetProjectInfoList: MetaUpdateProjectInfo[] = await this.getDatasetProjectInfoList(metaDataId);

    annotationProjectInfoList.forEach(_project => {
      projectInfoList.push({
        projectId: _project.id,
        projectName: _project.name,
        projectType: MetaUpdateProjectType.ANNOTATION,
        count: _project.statsSummary.totalTaskCount, //old:taskCount
        status: _project.projectStatus,
        updatedAt: _project.updatedAt,
      });
    });

    projectInfoList = [...projectInfoList, ...datasetProjectInfoList];

    return projectInfoList;
  }

  async getDatasetProjectInfoList(metadataId: string) {
    const metaData = await this.metaDataRepository.findById(metadataId);
    if (!metaData) {
      logger.warn(`get project tab | SearchQueryBuilderService.getDatasetProjectInfoList | N/A | invalid selection`);
      return [];
    }

    const datasetInfoList: MetaUpdateProjectInfo[] = [];

    if (metaData.objectType == ContentType.DATASET) {
      // then only get itself
      const datasetInfo: MetaUpdateProjectInfo = await this.getProjectTabInfoFromDataset(metaData);
      datasetInfoList.push(datasetInfo);
    } else {
      const datasetGroupIdSet = new Set<string>();

      if (metaData.datasetVersionList && metaData.datasetVersionList.length > 0) {
        for (const datasetVersion of metaData.datasetVersionList) {
          if (datasetVersion.datasetMetaId) {
            datasetGroupIdSet.add(datasetVersion.datasetMetaId.toString());
          }
        }
      }

      if (datasetGroupIdSet.size > 0) {
        const datasetGroupIdArr = [...datasetGroupIdSet];
        // let datasetProjectTabInfoPromiseList = datasetGroupIdArr.map(async (datasetId) => {
        //   let datasetMeta = await this.metaDataRepository.findById(datasetId);
        //   let projectTabInfoPromise = await this.getProjectTabInfoFromDataset(datasetMeta)
        //   return projectTabInfoPromise
        // })
        // let projectTabInfoList: MetaUpdateProjectInfo[] = await Promise.all(datasetProjectTabInfoPromiseList)
        // datasetInfoList = [
        //   ...datasetInfoList,
        //   ...projectTabInfoList
        // ]

        for (const datasetId of datasetGroupIdArr) {
          try {
            const datasetMeta = await this.metaDataRepository.findById(datasetId);
            if (datasetMeta) {
              const projectTabInfo = await this.getProjectTabInfoFromDataset(datasetMeta);
              datasetInfoList.push(projectTabInfo);
            }
          } catch {
            logger.error(
              `get project tab | SearchQueryBuilderService.getDatasetProjectInfoList | metaId:${datasetId} | invalid metadata of dataset`,
            );
          }
        }
      }
    }

    return datasetInfoList;
  }

  async getProjectTabInfoFromDataset(metaData: MetaData) {
    const projectTabInfo: MetaUpdateProjectInfo = {
      projectId: metaData.datasetGroupId!,
      projectName: metaData.name,
      projectType: MetaUpdateProjectType.DATASET,
      count: metaData.frameCount,
      updatedAt: metaData.createdAt,
    };
    return projectTabInfo;
  }

  /**
   * Use to get project info list for given projectIds from Annotation Studio Backend
   * @param metaDataId mongodbId of metaData object
   * @param annotationProjectIdList id list of annotation projects
   * @returns AnnotationProjectInfo[]
   */
  async getAnnotationProjectInfoList(metaDataId: string, annotationProjectIdList: string[]) {
    let annotationProjectInfoList: AnnotationProjectInfo[] = [];

    try {
      const url = `${process.env.ANNO_INTERNAL_SERVER}/internal/project/info/list`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          projectIdList: annotationProjectIdList,
        },
      });
      annotationProjectInfoList = response.data;
    } catch (err) {
      logger.error(
        `${FLOWS.METADATA_DETAIL_VIEW} | SearchQueryBuilderService.getAnnotationProjectInfoList | N/A | Failed to fetch Annotation project info for metaDataId: ${metaDataId}, error: ${err}`,
      );
    }

    return annotationProjectInfoList;
  }

  /**
   * Combine raw query and filter data to generate query string
   * @param {ExplorerFilterV2} filterData filter data
   * @param {string} rawQuery raw query string
   * @returns {string} query string
   */
  async combineFilterAndQuery(filterData?: ExplorerFilterV2, rawQuery?: string): Promise<string> {
    rawQuery = rawQuery ?? '';
    const filterQuery: string = await this.generateQueryString(
      filterData?.tags,
      filterData?.metadata,
      filterData?.labels,
    );

    if (filterQuery) rawQuery = rawQuery ? `(${rawQuery})` + ' && ' + filterQuery : filterQuery;

    return rawQuery;
  }

  /**
   * Generate the query string for list filter by tags,labels and meta data
   * @param {string[]} tags  array of tags
   * @param  {object} metadata metadata object
   * @param {string[]} labels  array of labels
   * @returns query string
   */
  async generateQueryString(
    tags?: string[],
    metadata?: {
      [fieldName: string]: string[];
    },
    labels?: string[],
  ): Promise<any> {
    const orCondition: string = ' || ';
    const andCondition: string = ' && ';

    const andConditionArray: string[] = [];

    await this.getLabelQueryString(andConditionArray, orCondition, labels);

    let tagQuery: string = await this.getTagQueryString(orCondition, tags);

    //Get metadata string query
    if (metadata && JSON.stringify(metadata) != '{}') {
      for (const [key, value] of Object.entries(metadata)) {
        /**
         * get query string for metadata
         */
        const query: string = await this.getQueryStringForMetaData(value, key, orCondition);

        if (query) {
          if (tagQuery.length != 0) tagQuery += orCondition;
          tagQuery += query;
        }
      }
    }

    if (tagQuery) andConditionArray.push(`(${tagQuery})`);

    return this.getFinalQueryString(andConditionArray, andCondition);
  }

  /**
   * Get final query string
   * @param andConditionArray {string[]} array of and conditions
   * @param andCondition {string} and condition string
   * @returns query string
   */
  async getFinalQueryString(andConditionArray: string[], andCondition: string): Promise<string> {
    let query: string = '';

    //Combine all and conditions(labels,tags,metadata)
    if (andConditionArray.length > 1) {
      query = andConditionArray.join(andCondition);
      query = `(${query})`;
    } else if (andConditionArray.length == 1) query = andConditionArray[0];

    return query;
  }

  /**
   * Use to get query string for tags
   * @param orCondition {string} or condition string
   * @param tags {string[]} array of tags
   * @returns void
   */
  async getTagQueryString(orCondition: string, tags?: string[]): Promise<string> {
    let tagQuery: string = '';

    //Get tag string query
    if (Array.isArray(tags) && tags.length != 0) {
      for (const iteration in tags) {
        if (Number(iteration) != 0) tagQuery += orCondition;
        tagQuery += `metadata.Tags=${tags[Number(iteration)]}`;
      }
    }

    return tagQuery;
  }

  /**
   * Use to get query string for labels
   * @param andConditionArray {string[]} array of and conditions
   * @param orCondition {string} or condition string
   * @param labels {string[]} array of labels
   * @returns void
   */
  async getLabelQueryString(andConditionArray: string[], orCondition: string, labels?: string[]): Promise<void> {
    let labelQuery: string = '';
    //Get label string query
    if (Array.isArray(labels) && labels.length != 0) {
      for (const iteration in labels) {
        if (Number(iteration) != 0) labelQuery += orCondition;
        labelQuery += `annotation.label=${labels[Number(iteration)]}`;
      }

      if (labelQuery) andConditionArray.push(`(${labelQuery})`);
    }
  }

  /**
   * Use to get query string for metadata
   * @param valueArray {string[]} array of values
   * @param key {string} metadata key
   * @param orCondition {string} or condition string
   * @returns query string
   */
  async getQueryStringForMetaData(valueArray: string[], key: string, orCondition: string): Promise<string> {
    if (!Array.isArray(valueArray) || valueArray.length == 0) return '';

    let query: string = '';
    for (const iteration in valueArray) {
      if (Number(iteration) != 0) query += orCondition;
      query += `metadata.${key}=${valueArray[Number(iteration)]}`;
    }

    return query;
  }

  /**
   * Generate the mongo query for aggregate
   * @param query {string} frontend typed query string
   * @param teamId {string} team of the query belongs
   * @returns Mongodb modified aggregate able query
   */
  async generateQuery(query?: string, teamId?: string, lookupField?: string) {
    logger.debug('Received query string: ', query);

    if (!query || query.length == 0) {
      return {};
    }

    //get query string
    let rawQueryString = query;
    //modify the query string to form json
    rawQueryString = rawQueryString.trim();

    // rawQueryString = rawQueryString[0] != "(" ? `(${rawQueryString}` : rawQueryString;
    // rawQueryString = rawQueryString[rawQueryString.length - 1] != ")" ? `${rawQueryString})` : rawQueryString;

    //Remove multiple spaces and single space in both sides of arithmetic opertor
    const queryString = await this.queryArithmeticModify(rawQueryString);

    //Convert modified query string to multple arrays with operations
    const queryArray = await this.queryToArrayConvert(queryString);
    logger.debug('query array: ', queryArray);
    logger.debug('query array: ', JSON.stringify(queryArray));

    //Concat expression properties [ A, B, &&, C] => [A B, &&, C]
    const _temp = await this.reProcess(queryArray, []);
    logger.debug('Concat expression properties arrays: ', JSON.stringify(_temp));

    //if query have label filter get the system labelText with label reference to pass for replace labelText
    //in the query expression values
    let labelList: any[] = [];
    if (query.includes('annotation.label'))
      labelList = await this.systemLabelRepository.find({
        where: {teamId: teamId, isDeleted: {neq: true}},
        fields: {label: true, labelText: true},
      });
    //Hash the system labels
    const hashLabelObjectsList = Object.fromEntries(labelList.map(e => [e['labelText'], e]));

    //logger.debug(_temp)

    //Convert modified query expression arrays to mongodb aggregate query
    const mongoQuery = await this.convertToMongoQuery(_temp, [], hashLabelObjectsList, lookupField);

    logger.debug('mongodb aggregate query', JSON.stringify(mongoQuery));

    //Retun Mongo query
    return mongoQuery;
  }

  /**
   * Concat expression properties [ A, B, &&, C] => [A B, &&, C]
   * @param list query array [[[A, &&, B], [A, ||, C], &&, D]
   * @param tempList new concated string expression included array for recursion
   * @returns concated string expression included array
   */
  async reProcess(list: any, tempList: any) {
    const operater = OPERATOR_ARRAY;
    const temp: any[] = [];
    const positions = [];
    for (const i in list) {
      if (typeof list[i] == 'object') {
        const _list = await this.reProcess(list[i], temp);
        temp.push(_list);
      } else {
        temp.push(list[i]);
      }

      if (
        Number(i) < list.length - 1 &&
        !operater.includes(list[Number(i)]) &&
        !operater.includes(list[Number(i) + 1])
      ) {
        positions.push(Number(i));
      }
    }
    let lastPosition = -2;
    let initialPosition = -2;
    const unwantedPositions: number[] = [];
    for (const position of positions) {
      if (lastPosition == position - 1) {
        temp[initialPosition] = `${temp[initialPosition]} ${temp[position + 1]}`;
        lastPosition = position;
        initialPosition = initialPosition;
      } else {
        temp[position] = `${temp[position]} ${temp[position + 1]}`;
        lastPosition = position;
        initialPosition = position;
      }
      unwantedPositions.push(position + 1);
    }
    for (const position of unwantedPositions.reverse()) {
      temp.splice(position, 1);
    }
    return temp;
  }

  /**
   * Convert modified query expression arrays to mongodb aggregate query
   * @param list modified array for create mongo query
   * @param tempList use for recursion storeing and create new mongidb array
   * @param hashLabelObjectsList hashed system label object
   * @returns modified mongodb query which able to excute on mongo match
   */
  async convertToMongoQuery(list: any, tempList: any, hashLabelObjectsList: any, lookupField?: string) {
    const andOperator = AND_OERATOR;
    const orOperator = OR_OERATOR;
    let operator = '$and';
    const temp: any[] = [];
    const expressions: any[] = [];
    const returnObj: {[key: string]: any} = {};

    for (const i in list) {
      //logger.debug(list[i])
      if (typeof list[i] == 'object') {
        const _list = await this.convertToMongoQuery(list[i], temp, hashLabelObjectsList, lookupField);
        temp.push(_list);
        expressions.push(_list);
      } else if (andOperator == list[i]) {
        operator = '$and';
      } else if (orOperator == list[i]) {
        operator = '$or';
      } else {
        temp.push(list[i]);

        //Convert frontend user typed expression to mongodb excutable form
        const expression = await this.expressionCreate(list[i], hashLabelObjectsList, lookupField);
        const searchQueryRoot = list[i].split('.')[0] as SearchQueryRootGroup;
        // if query include analytics $elmMatch query have to be push to expressions array
        if (searchQueryRoot == SearchQueryRootGroup.ANALYTICS) {
          await this.getMongoDbMatchForAnalytics(list[i], expressions, expression, lookupField);
        } else expressions.push(expression);
      }
    }
    //logger.debug(operator, expressions)
    returnObj[operator] = expressions;

    return returnObj;
  }

  /**
   * Generates a MongoDB match expression for analytics based on the provided parameters.
   * @param expressionString initial user typed expression from frontend
   * @param expressions - An array of expressions to append the generated match expression to.
   * @param expression - The expression object to match against.
   * @param lookupField - The optional lookup field to prepend to the key in the match expression.
   * @returns void
   */
  async getMongoDbMatchForAnalytics(
    expressionString: string,
    expressions: any,
    expression: {[key: string]: any},
    lookupField?: string,
  ): Promise<void> {
    const expressionStringArray: string[] = expressionString.includes('!=')
      ? expressionString.split('!=')
      : expressionString.split('=');
    const stringArr: string[] = expressionStringArray?.[0]?.split('.');
    const lastKey: string = stringArr[stringArr.length - 1];
    const key: string = lastKey == 'embeddingModel' ? 'embeddingModels' : 'analytics';
    if (key == 'analytics') {
      expressions.push({
        [lookupField ? `${lookupField}.${key}` : `${key}`]: {
          $elemMatch: expression,
        },
      });
    } else {
      expressions.push(expression);
    }
  }

  /**
   * Remove multiple spaces and single space in both sides of arithmetic opertor
   * @param queryString {string} modified query string
   * @returns arithemtic operation space removed string
   */
  async queryArithmeticModify(queryString: string) {
    const _queryString = queryString
      .replace(/ +(?= )/g, '')
      .replace(/\= /g, '=')
      .replace(/\= /g, '=')
      .replace(/ \=/g, '=')
      .replace(/\!= /g, '!=')
      .replace(/ \!=/g, '!=')
      .replace(/\> /g, '>')
      .replace(/ \>/g, '>')
      .replace(/\< /g, '<')
      .replace(/ \</g, '<')
      .replace(/\>= /g, '>=')
      .replace(/ \>=/g, '>=')
      .replace(/\<= /g, '<=')
      .replace(/ \<=/g, '<=')
      .replace(/\~ /g, '~');
    return _queryString;
  }

  /**
   * Convert frontend user typed expression to mongodb excutable form
   * @param expressionString initial user typed expression from fronend
   * @param hashLabelObjectsList hashed system label object
   * @returns expression which mongodb excutable
   */
  async expressionCreate(expressionString: string, hashLabelObjectsList: any, lookupField?: string) {
    // initialize return object
    let propertyValue: {[key: string]: any} = {};

    if (expressionString.includes('!=')) {
      const expressionStringArray = expressionString.split('!=');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.neq,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('<=')) {
      const expressionStringArray = expressionString.split('<=');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.lte,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('>=')) {
      const expressionStringArray = expressionString.split('>=');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.gte,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('>')) {
      const expressionStringArray = expressionString.split('>');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.gt,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('<')) {
      const expressionStringArray = expressionString.split('<');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.lt,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('=')) {
      const expressionStringArray = expressionString.split('=');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.eq,
        hashLabelObjectsList,
        lookupField,
      );
    } else if (expressionString.includes('~')) {
      const expressionStringArray = expressionString.split('~');
      const property = expressionStringArray[0];
      const value = expressionStringArray[1];

      //Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
      propertyValue = await this.getRealProperty(
        property,
        value,
        MongodbQueryOperators.reg,
        hashLabelObjectsList,
        lookupField,
      );
    } else {
      propertyValue = await this.getRealProperty(
        'metadata.name',
        expressionString,
        MongodbQueryOperators.reg,
        hashLabelObjectsList,
        lookupField,
      );
    }

    return propertyValue;
  }

  /**
   * Replace the fronend typed property names and values as mogodb executable form (mongodb relevent fields and values)
   * property eg: 'label' => 'labelList.label', project => 'annotationProjectList.name'
   * field example eg: Ball =>
   * @param property property name of the expression
   * @param value value of the property
   * @param hashLabelObjectsList hashed system labels
   * @returns modified expression
   */
  async getRealProperty(
    property: string,
    value: any,
    operator: string,
    hashLabelObjectsList: any,
    lookupField?: string,
  ) {
    const propertyList = property.split('.');
    let modifiedProperty = propertyList[0];
    let modifiedValue = value;
    let modifiedNumberValue = value;
    let propertyValue: {[key: string]: any} = {};

    if (Number(modifiedValue) || Number(modifiedValue) === 0) {
      modifiedNumberValue = Number(modifiedValue);
    }
    switch (propertyList[0]) {
      case SearchQueryRootGroup.ANNOTATION:
        //logger.debug(propertyList[0])
        switch (propertyList[1]) {
          case 'label':
            modifiedProperty = lookupField ? `${lookupField}.labelList.label` : 'labelList.label';
            modifiedValue = hashLabelObjectsList[modifiedValue]
              ? hashLabelObjectsList[modifiedValue].label
              : modifiedValue;
            break;
          case 'project':
            modifiedProperty = lookupField ? `${lookupField}.annotationProjectList.name` : 'annotationProjectList.name';
            break;
          default:
            break;
        }
        propertyValue = {
          [modifiedProperty]: {[operator]: modifiedValue},
        };
        break;
      case SearchQueryRootGroup.METADATA: //QueryItemTypes
        //logger.debug(propertyList[0])
        modifiedProperty = lookupField ? `${lookupField}.${propertyList[1]}` : propertyList[1];

        switch (propertyList[1]) {
          case 'name':
            if (operator === MongodbQueryOperators.reg) {
              modifiedValue = new RegExp(modifiedValue, 'i');
            } else modifiedValue = String(value);
            break;
          case 'Tags':
            modifiedValue = String(value);
            break;
          case 'frameCount':
            modifiedValue = modifiedNumberValue;
            break;
          case 'frameRate':
            modifiedValue = modifiedNumberValue;
            break;
          case 'fileSize':
            modifiedValue = modifiedNumberValue;
            break;
          case 'videoLength':
            modifiedValue = modifiedNumberValue;
            break;
          case 'resolution':
            modifiedValue = modifiedNumberValue;
            if (propertyList.length > 2) {
              modifiedProperty = lookupField
                ? `${lookupField}.${propertyList[1]}.${propertyList[2]}`
                : `${propertyList[1]}.${propertyList[2]}`;
            }
          default:
            if (!MetaData.definition.properties.hasOwnProperty(propertyList[1])) {
              modifiedProperty = lookupField
                ? `${lookupField}.customMeta.${propertyList[1]}`
                : `customMeta.${propertyList[1]}`;
            }
            break;
        }
        propertyValue = {
          [modifiedProperty]: {[operator]: modifiedValue},
        };
        break;
      case SearchQueryRootGroup.PREDICTION:
        //logger.debug(propertyList[0])
        modifiedProperty = lookupField ? `${lookupField}.${propertyList[1]}` : propertyList[1];
        break;
      case SearchQueryRootGroup.DATASET:
        switch (propertyList[1]) {
          case 'project':
            modifiedProperty = lookupField
              ? `${lookupField}.datasetVersionList.datasetGroupName`
              : 'datasetVersionList.datasetGroupName';
            break;
          default:
            modifiedProperty = lookupField ? `${lookupField}.${property}` : property;
            break;
        }
        propertyValue = {
          [modifiedProperty]: {[operator]: modifiedValue},
        };
        break;
      case SearchQueryRootGroup.ANALYTICS:
        if (propertyList[propertyList.length - 1] == 'embeddingModel') {
          modifiedProperty = lookupField ? `${lookupField}.embeddingModels.modelName` : 'embeddingModels.modelName';
          propertyValue = {
            [modifiedProperty]: {[operator]: modifiedValue},
          };
        } else {
          const analyticsProperty: string[] = [
            propertyList[0],
            propertyList.slice(1, -1).join('.'),
            propertyList[propertyList.length - 1],
          ];
          switch (analyticsProperty[2]) {
            case 'recall':
              modifiedValue = modifiedNumberValue;
              break;
            case 'precision':
              modifiedValue = modifiedNumberValue;
              break;
            case 'f1Score':
              modifiedValue = modifiedNumberValue;
              break;
          }
          propertyValue = {
            [analyticsProperty[2]]: {[operator]: modifiedValue},
            operationId: analyticsProperty[1],
          };
        }
        break;
      default:
        break;
    }

    return propertyValue;
  }

  /**
   * Convert modified query string to multple arrays with operations
   * @param str {string} modified query string
   * @returns query array [[[A, &&, B], [A, ||, C], &&, D]
   */
  async queryToArrayConvert(str: string) {
    let i = 0;
    const trailingWhiteSpace = str.endsWith(' ');
    let lastValidChar = '';
    let openedBracket = false;
    let openedBracketCount = 0;
    function main(): any {
      const arr = [];
      let startIndex = i;
      function addWord() {
        if (i - 1 > startIndex) {
          arr.push(str.slice(startIndex, i - 1));
        }
      }
      while (i < str.length) {
        if (str[i] !== ' ' && str[i] !== '(' && str[i] !== ')') {
          lastValidChar = str[i];
        }

        switch (str[i++]) {
          case ' ':
            if (
              lastValidChar === '&' ||
              lastValidChar === '|' ||
              str[i + 1] == '|' ||
              str[i + 1] == '&' ||
              str[i + 1] == '(' ||
              str[i + 1] == ')'
            ) {
              addWord();
              startIndex = i;
              continue;
            } else {
              continue;
            }
          case '(':
            if (lastValidChar === '&' || lastValidChar === '|' || lastValidChar === '') {
              openedBracket = true;
              arr.push(main());
              startIndex = i;
              continue;
            }
            if (openedBracket) {
              openedBracketCount = openedBracketCount + 1;
            }
            break;
          case ')':
            if (openedBracket) {
              if (openedBracketCount > 0) {
                openedBracketCount = openedBracketCount - 1;
              } else {
                addWord();
                return arr;
              }
            }
        }
      }
      if (!trailingWhiteSpace) {
        i = i + 1;
        addWord();
      }
      return arr;
    }
    return main();
  }

  /**
   * Use to convert ExplorerFilter to mongodb syntax
   * @param filter ExplorerFilter
   * @returns object for mongodb match filter
   */
  async getMatchQueryForExplorerFilter(
    filter?: ExplorerFilterV2,
    embeddingSelection?: EmbeddingSelectionObject,
    lookupField?: string,
    currentUserProfile?: UserProfileDetailed,
  ) {
    //filter match query create
    const filterMatchQuery: any = {};
    //objectType match
    //if (filter.contentType) filterMatchQuery["objectType"] = filter.contentType;

    if (currentUserProfile && currentUserProfile.userType === UserType.USER_TYPE_COLLABORATOR) {
      if (lookupField) {
        filterMatchQuery[`${lookupField}.allowedUserIdList`] = new ObjectId(currentUserProfile.id);
      } else {
        filterMatchQuery['allowedUserIdList'] = new ObjectId(currentUserProfile.id);
      }
    }

    //created date match
    if (filter?.date && (filter.date.toDate || filter.date.fromDate)) {
      if (filter.date.toDate) filter.date.toDate = await this.formatToDate(filter.date.toDate);

      if (filter.date.fromDate && !filter.date.toDate) {
        if (lookupField) {
          filterMatchQuery[`${lookupField}.createdAt`] = {$gte: new Date(filter.date.fromDate)};
        } else {
          filterMatchQuery['createdAt'] = {$gte: new Date(filter.date.fromDate)};
        }
      } else if (filter.date.toDate && !filter.date.fromDate) {
        if (lookupField) {
          filterMatchQuery[`${lookupField}.createdAt`] = {$lte: new Date(filter.date.toDate)};
        } else {
          filterMatchQuery['createdAt'] = {$lte: new Date(filter.date.toDate)};
        }
      } else {
        if (lookupField) {
          filterMatchQuery[`${lookupField}.createdAt`] = {
            $gte: new Date(filter.date.fromDate),
            $lte: new Date(filter.date.toDate),
          };
        } else {
          filterMatchQuery['createdAt'] = {
            $gte: new Date(filter.date.fromDate),
            $lte: new Date(filter.date.toDate),
          };
        }
      }
    }
    //filterBy match
    if (filter?.annotationTypes) {
      let filterByArray = [];
      if (filter.annotationTypes.includes(FrameVerificationStatus.RAW)) {
        if (lookupField) {
          const temp = `${lookupField}.verificationStatusCount.raw`;
          filterByArray.push({[temp]: {$gt: 0}});
        } else {
          filterByArray.push({'verificationStatusCount.raw': {$gt: 0}});
        }
      }
      if (filter.annotationTypes.includes(FrameVerificationStatus.MACHINE_ANNOTATED)) {
        if (lookupField) {
          const temp = `${lookupField}.verificationStatusCount.machineAnnotated`;
          filterByArray.push({
            [temp]: {$gt: 0},
          });
        } else {
          filterByArray.push({
            'verificationStatusCount.machineAnnotated': {$gt: 0},
          });
        }
      }
      if (filter.annotationTypes.includes(FrameVerificationStatus.VERIFIED)) {
        if (lookupField) {
          const temp = `${lookupField}.verificationStatusCount.verified`;
          filterByArray.push({[temp]: {$gt: 0}});
        } else {
          filterByArray.push({'verificationStatusCount.verified': {$gt: 0}});
        }
      }
      if (filterByArray.length == 0) {
        filterByArray = [{}];
      }
      filterMatchQuery['$or'] = filterByArray;
    }

    //embedding graph filter
    if (
      embeddingSelection?.graphId &&
      embeddingSelection?.selectionType &&
      Array.isArray(embeddingSelection?.x) &&
      Array.isArray(embeddingSelection?.y) &&
      embeddingSelection?.x.length > 0 &&
      embeddingSelection?.y.length > 0
    ) {
      const coordinates: number[][] = await this.getPolygonCoordinatesInEmbeddingSelection(embeddingSelection);

      filterMatchQuery[lookupField ? `${lookupField}.queryGraphData` : 'queryGraphData'] = {
        $elemMatch: {
          $and: [
            {['graphId']: embeddingSelection.graphId},
            {
              ['coordinates']: {
                $geoWithin: {
                  $polygon: coordinates,
                },
              },
            },
          ],
        },
      };
    }

    //full text search for keywords
    if (filter?.keywords && Array.isArray(filter.keywords) && filter.keywords.length > 0) {
      //in case of document search, we must filterout duplicate files
      filterMatchQuery['isDuplicate'] = false;

      //keywords as string array
      const keywordsString = filter.keywords.join(' ');

      filterMatchQuery['$text'] = {$search: keywordsString};
    }

    /*
    Removed category filtering
    //collection filter for categories
    if (filter?.categories && Array.isArray(filter.categories) && filter.categories.length > 0) {
      let vCols = await this.getCollectionIdListForCategories(filter.categories);
      if (Array.isArray(vCols) && vCols.length > 0) {
        filterMatchQuery['vCollectionIdList'] = {$in: vCols[0].collectionIdList};
      } else {
        filterMatchQuery['vCollectionIdList'] = {$in: []};
      }
    }
    */
    return filterMatchQuery;
  }

  /**
   * use to get virtual collection id list for given categories
   * @param categories names of virtual collections
   * @returns
   */
  async getCollectionIdListForCategories(categories: string[]) {
    const collectionIdList = await this.metaDataRepository.aggregate([
      {
        $match: {
          name: {$in: categories},
          objectType: ContentType.OTHER_COLLECTION,
        },
      },
      {
        $group: {
          _id: null,
          collectionIdList: {
            $push: '$_id',
          },
        },
      },
    ]);

    return collectionIdList;
  }

  /**
   * Use to build annotation object info object
   * This will be display in datalake explorer player view after expanding an annotation object
   */
  getAnnotationObjectInfo(annoObj: Partial<AnnotationObject>, labelToLabelTextMap: {[k: string]: string}) {
    //objectInfo: annoObj.objectInfo ? annoObj.objectInfo : {id: annoObj.id, confidence: annoObj.confidence, metaData: annoObj.metaData ? annoObj.metaData : {}}
    const annoObjectInfo: AnnotationObjectInfo = {};

    annoObjectInfo.id = annoObj.id;
    annoObjectInfo.confidence = annoObj.confidence;
    annoObjectInfo.operationId = annoObj.operationId;
    annoObjectInfo.attributes = {};
    annoObjectInfo.metaData = {};

    if (annoObj.label) {
      if (annoObj.label.attributeValues) {
        for (const [key, valueArray] of Object.entries(annoObj.label.attributeValues)) {
          if (labelToLabelTextMap[key]) {
            for (const valueObj of valueArray) {
              if (labelToLabelTextMap[valueObj.value]) {
                valueObj.value = labelToLabelTextMap[valueObj.value];
              }
            }
            annoObjectInfo.attributes[labelToLabelTextMap[key]] = valueArray;
            // annoObjectInfo.metaData[labelToLabelTextMap[key]] = labelToLabelTextMap[value]
          }
        }
      }
      if (annoObj.metadata) {
        annoObjectInfo.metaData = annoObj.metadata;
      }
    }
    return annoObjectInfo;
  }

  /**
   * Get embedding aggregation for selection
   * @param {EmbeddingSelectionObject} embeddingSelection embedding selection object
   * @returns aggregation array
   */
  async getEmbeddingAggregationForSelection(
    embeddingSelection: EmbeddingSelectionObject,
    lookupField?: string,
  ): Promise<GetEmbeddingAggregationForSelectionRes[]> {
    /**
     * Get the polygon coordinates of the embedding selection
     */
    const coordinates: number[][] = await this.getPolygonCoordinatesInEmbeddingSelection(embeddingSelection);

    const lookup: Record<string, any> = {
      from: 'QueryGraphData',
      let: {objectKey: lookupField ? `$${lookupField}.objectKey` : '$objectKey'},
      pipeline: [
        {
          $match: {
            $and: [
              {graphId: embeddingSelection.graphId},
              {$expr: {$eq: ['$objectKey', '$$objectKey']}},
              {
                coordinates: {
                  $geoWithin: {
                    $polygon: coordinates,
                  },
                },
              },
            ],
          },
        },
      ],
      as: 'embeddingData',
    };

    const param: GetEmbeddingAggregationForSelectionRes[] = [{$lookup: lookup}, {$unwind: '$embeddingData'}];
    return param;
  }

  /**
   * Use to get matching criteria to use with $match mongo aggregate
   */
  async getMatchQueryForSelection(
    currentUserProfile?: UserProfileDetailed,
    selectionId?: string,
    selection?: Partial<DatalakeSelection>,
  ) {
    let selectionObj: Partial<DatalakeSelection> = {};
    if (selectionId) {
      selectionObj = await this.datalakeSelectionRepository.findById(selectionId);
    } else if (selection) {
      selectionObj = selection;
    }

    let matchQuery: any = {};
    let objectType = selectionObj.objectType;

    //if select all request, then use query and filterData
    if (selectionObj.selectionRequest && selectionObj.selectionRequest.isAllSelected) {
      if (!selectionObj.selectionRequest.filterData) {
        return;
      }

      const rawQuery: string = await this.combineFilterAndQuery(
        selectionObj?.selectionRequest?.filterData,
        selectionObj?.selectionRequest?.query,
      );

      // build matching criteria
      let query: object = {};
      if (rawQuery) {
        query = await this.generateQuery(rawQuery, selectionObj.teamId);
      }
      const _andArr: any[] = [
        {
          objectType: selectionObj.objectType,
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
        },
      ];
      if (Object.keys(query).length != 0) _andArr.push(query);

      if (selectionObj.teamId) {
        _andArr.push({teamId: new ObjectId(selectionObj.teamId)});
      }
      if (selectionObj.selectionRequest.collectionId) {
        // let collectionObj = await this.metaDataRepository.findById(selectionObj.selectionRequest.collectionId);
        _andArr.push({
          vCollectionIdList: new ObjectId(selectionObj.selectionRequest.collectionId),
        });
      }

      //if reference image is exists search it on similar array of metadata
      if (selectionObj.selectionRequest.referenceImage) {
        _andArr.push({
          $or: [
            {
              similarArray: {
                $elemMatch: {
                  referenceObjectKey: selectionObj.selectionRequest.referenceImage,
                  modelName: selectionObj.selectionRequest.modelName,
                  scoreThreshold: selectionObj.selectionRequest.scoreThreshold,
                },
              },
            },
            {objectKey: selectionObj.selectionRequest.referenceImage},
          ],
        });
      }

      const filterMatchQuery: any = await this.getMatchQueryForExplorerFilter(
        selectionObj.selectionRequest.filterData,
        selectionObj.selectionRequest?.embeddingSelection,
        undefined,
        currentUserProfile,
      );

      matchQuery = {
        $and: _andArr,
        ...filterMatchQuery,
      };

      if (
        selectionObj.selectionRequest?.objectIdList &&
        Array.isArray(selectionObj.selectionRequest.objectIdList) &&
        selectionObj.selectionRequest.objectIdList.length > 0
      ) {
        const removedIdList = selectionObj.selectionRequest.objectIdList.map(elem => new ObjectId(elem));
        matchQuery._id = {$nin: removedIdList};
      }
    }
    // if not select all request, then use objectIdList
    else {
      const _objectIdList = selectionObj.selectionRequest
        ? selectionObj.selectionRequest.objectIdList?.map(elem => new ObjectId(elem))
        : [];
      const distinctObjectTypes = await this.metaDataRepository.distinct('objectType', {
        _id: {
          $in: _objectIdList,
        },
      });
      if (distinctObjectTypes.length == 1) {
        objectType = distinctObjectTypes[0];
        matchQuery = {
          _id: {
            $in: _objectIdList,
          },
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
        };
      } else {
        logger.error(
          `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.getMatchQueryForSelection | N/A | Couldn't perform tagging the selection, distinctObjectTypes = ${distinctObjectTypes}, selectionObj=`,
          selectionObj,
        );
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OBJECT_SELECTION_FAILED);
      }

      if (currentUserProfile && currentUserProfile.userType === UserType.USER_TYPE_COLLABORATOR) {
        matchQuery['allowedUserIdList'] = new ObjectId(currentUserProfile.id);
      }
    }

    return {
      matchQuery: matchQuery,
      objectType: selectionObj.objectType || ContentType.IMAGE,
    };
  }

  /**
   * Format to date
   * @param {Date} toDate filter to date
   * @returns updated to date
   */
  async formatToDate(toDate: Date) {
    //add one day to the toDate to include toDate
    const formattedToDate = new Date(toDate).setDate(new Date(toDate).getDate() + 1);
    return new Date(formattedToDate);
  }

  /**
   * Return a query to match frames
   * Create match query to get images belong to a project, dataset version
   * @param {string} selectionId datalake side id for a particular selection to add frames to project, dataset version
   * @param {string[]} removedCollectionIdList removed image collection id list
   * @param {string[]} addedCollectionIdList added image collection id list
   * @returns match filter
   */
  async createMatchQuery(
    currentUserProfile: UserProfileDetailed,
    selectionId?: string,
    removedCollectionIdList?: string[],
    addedCollectionIdList?: string[],
    datasetVersionId?: string,
    projectId?: string,
  ) {
    let selectQuery: any = {};

    if (selectionId && isValidObjectId(selectionId)) {
      const selectionMatchQuery = await this.getMatchQueryForSelection(currentUserProfile, selectionId);

      switch (selectionMatchQuery?.objectType) {
        case ContentType.IMAGE:
          selectQuery = selectionMatchQuery.matchQuery;
          break;

        case ContentType.DATASET:
        case ContentType.IMAGE_COLLECTION:
          const _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
            {$match: selectionMatchQuery.matchQuery},
            {$project: {_id: 1}},
          ]);

          const collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));
          selectQuery = {
            vCollectionIdList: {$in: collectionObjectIdList},
            objectStatus: OBJECT_STATUS.ACTIVE,
          };
          break;

        // case ContentType.DATASET:
        //   let _datasetCollectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
        //     {$match: selectionMatchQuery.matchQuery},
        //     {$project: {_id: 1}},
        //   ]);

        //   let datasetCollectionObjectIdList = _datasetCollectionIds.map(elem => new ObjectId(elem._id));

        //   selectQuery = {
        //     'datasetVersionList.datasetMetaId': {
        //       $in: datasetCollectionObjectIdList,
        //     },
        //     objectType: ContentType.IMAGE,
        //   };
        //   break;
      }
    }

    const andCondition: any[] = [];
    const orCondition: any[] = [];
    let matchFilter: any = {};
    const orInAndCondition: any[] = [];

    if (JSON.stringify(selectQuery) != '{}') orInAndCondition.push(selectQuery);

    if (datasetVersionId && isValidObjectId(datasetVersionId)) {
      orInAndCondition.push({
        'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
        objectType: ContentType.IMAGE,
      });
    }

    if (projectId && isValidObjectId(projectId)) {
      orInAndCondition.push({
        'annotationProjectList.id': new ObjectId(projectId),
        objectType: ContentType.IMAGE,
      });
    }

    if (orInAndCondition.length > 0) andCondition.push({$or: orInAndCondition});

    if (removedCollectionIdList && removedCollectionIdList.length > 0) {
      const _removedCollectionIdList = removedCollectionIdList.map(elem => new ObjectId(elem));
      const removedQuery = {collectionId: {$nin: _removedCollectionIdList}};
      andCondition.push(removedQuery);
    }

    if (andCondition.length > 0) orCondition.push({$and: andCondition});

    if (addedCollectionIdList && addedCollectionIdList.length > 0) {
      const _addedCollectionIdList = addedCollectionIdList.map(elem => new ObjectId(elem));
      const addedQuery = {
        collectionId: {$in: _addedCollectionIdList},
        //replace flag with enum
        objectStatus: OBJECT_STATUS.ACTIVE,
        // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
      };

      orCondition.push(addedQuery);
    }

    if (orCondition.length > 0) matchFilter = {$or: orCondition};

    matchFilter = JSON.stringify(matchFilter) == '{}' ? null : matchFilter;

    //console.log(JSON.stringify(matchFilter, null, 2))

    return matchFilter;
  }

  /**
   * use to insert graph coordinates with graph id
   * @param {DatalakeSelectionRequest} filter filter data
   * @param teamId team id
   * @returns graph id
   */
  async insertGraphObjectKeys(
    filter: DatalakeSelectionRequest,
    currentUserProfile?: UserProfileDetailed,
    teamId?: string,
    isCronJobDeletable: boolean = true,
  ) {
    const graphId = `graph-${uuidV4()}`;

    if (
      filter.contentType == ContentType.VIDEO_COLLECTION ||
      filter.contentType == ContentType.VIDEO ||
      filter.contentType == ContentType.OTHER ||
      filter.contentType == ContentType.OTHER_COLLECTION
    ) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.insertGraphObjectKeys | N/A | cannot generate embedding for ${filter.contentType}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OBJECT_SELECTION_FAILED);
    }

    // initialize insert graph details
    await this.queryGraphDetailsRepository.create({
      graphId: graphId,
      createdAt: new Date(),
      isPending: true,
      progress: 0,
      isCronJobDeletable: isCronJobDeletable,
      isEmbeddingAvailable: false,
      status: GraphStatus.PCA_GENERATION,
    });

    return this.generateGraphPCA(graphId, filter, isCronJobDeletable, currentUserProfile, teamId);
  }

  /**
   * use to generate pca after embedding generation
   * @param filter {DatalakeSelectionRequest} filter data
   * @param graphId {string} graph id
   * @param currentUserProfile {UserProfileDetailed}
   * @returns None
   */
  async generatePCAAfterEmbeddingGeneration(
    graphId: string,
    filter: DatalakeSelectionRequest,
    currentUserProfile?: UserProfileDetailed,
  ) {
    const graphDetails = await this.queryGraphDetailsRepository.findOne({where: {graphId: graphId}});

    // if embedding still generating then check job progress
    if (graphDetails?.status == GraphStatus.EMBEDDING_GENERATION) {
      const jobId = graphDetails.jobId;
      const jobDetails = await this.jobRepository.findById(jobId);

      // if job completed then generate pca
      if (jobDetails.status == JobStatus.completed) {
        await this.generateGraphPCA(
          graphId,
          filter,
          graphDetails.isCronJobDeletable,
          currentUserProfile,
          currentUserProfile?.teamId,
        );

        // update graph status to pca generation
        await this.queryGraphDetailsRepository.updateById(graphDetails._id, {
          status: GraphStatus.PCA_GENERATION,
        });
      }
    }
  }

  /**
   * Use to initiate the generation of graph pca
   * @param graphId {string} id of the graph
   * @param filter {DatalakeSelectionRequest} filter data
   * @param isCronJobDeletable {boolean} is cron job deletable
   * @param currentUserProfile {UserProfileDetailed}
   * @param teamId {string}
   * @returns {graphId: string}
   */
  async generateGraphPCA(
    graphId: string,
    filter: DatalakeSelectionRequest,
    isCronJobDeletable: boolean,
    currentUserProfile?: UserProfileDetailed,
    teamId?: string,
  ) {
    teamId = currentUserProfile?.teamId || teamId;

    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };

    const aggregateQuery = await this.getMatchQueryForSelection(currentUserProfile, undefined, datalakeSelection);

    try {
      const url = `${PYTHON_HOST}/internal/graph/insert/ipca`;

      Axios({
        url,
        method: 'POST',
        data: {
          aggregateQuery: EJSON.stringify(aggregateQuery?.matchQuery),
          graphId: graphId,
          objectType: aggregateQuery?.objectType,
          isCronJobDeletable: isCronJobDeletable,
        },
      });

      return {
        graphId: graphId,
      };
    } catch (err) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.insertGraphObjectKeys | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }

  /**
   * Use to get graph coordinates
   * @param graphId graph id
   * @param teamId team id
   * @param coordinates optional frontend coordinates
   * @returns returns graph coordinates
   */
  async getGraphSampleCoordinates(graphId: string, teamId: string, coordinates?: FrontendRequestCoordinateDataFormat) {
    let rangeCoordinates: number[][] = [];

    const graphDetails = await this.queryGraphDetailsRepository.findOne({where: {graphId: graphId}});

    if (!graphDetails) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.getGraphSampleCoordinates | N/A | Graph details not found for graph id ${graphId}`,
      );
      throw new HttpErrors.NotAcceptable(`Graph details not found for graph id ${graphId}`);
    }

    // check if graph is ready
    // if graph is not ready, then return empty coordinates
    if (graphDetails.progress != 100 || graphDetails.status == GraphStatus.EMBEDDING_GENERATION) {
      logger.warn(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.getGraphSampleCoordinates | N/A | Graph is not ready for graph id ${graphId}`,
      );

      return this.getFormattedGraphCoordinates([], graphId, graphDetails);
    }
    if (coordinates && Object.keys(coordinates).length > 0) {
      // if coordinates is not null, undefined, empty or empty object, then convert frontend coordinates to polygon coordinates
      rangeCoordinates = await this.getRangeCoordinates(coordinates);
    }

    try {
      const url = `${PYTHON_HOST}/internal/graph/sample/coordinates`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          graphId: graphId,
          teamId: teamId,
          coordinates: rangeCoordinates ? rangeCoordinates : null,
        },
      });

      const responseData = response.data;

      return await this.getFormattedGraphCoordinates(responseData, graphId, graphDetails);
    } catch (err) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.getGraphSampleCoordinates | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }

  /**
   * Get graph coordinates for embedding selection
   * @param embeddingSelection embedding selection object
   * @returns graph coordinates
   */
  async getPolygonCoordinatesInEmbeddingSelection(embeddingSelection?: EmbeddingSelectionObject): Promise<number[][]> {
    if (!embeddingSelection?.selectionType) return [];
    const xValues: number[] = embeddingSelection.x;
    const yValues: number[] = embeddingSelection.y;
    if (xValues.length != yValues.length) throw new HttpErrors.NotAcceptable(`x and y values are not equal`);

    const graphType: SELECTION_TYPE = embeddingSelection.selectionType;

    if (graphType == SELECTION_TYPE.LASSO && xValues.length < 3)
      throw new HttpErrors.NotAcceptable(`At least 3 coordinates must be provided`);

    let coordinates: number[][];

    switch (graphType) {
      case SELECTION_TYPE.BOX:
        coordinates = [
          [xValues[0], yValues[0]],
          [xValues[0], yValues[1]],
          [xValues[1], yValues[1]],
          [xValues[1], yValues[0]],
        ];
        break;
      case SELECTION_TYPE.LASSO:
        coordinates = xValues.map((x, index) => [x, yValues[index]]);
        break;
    }

    return coordinates;
  }

  /**
   * use to get polygon coordinates from frontend coordinates
   * @param coordinates frontend coordinates
   * @returns polygon coordinates
   */
  async getRangeCoordinates(coordinates: FrontendRequestCoordinateDataFormat): Promise<number[][]> {
    const rangeCoordinates: number[][] = [];

    const x0 = coordinates.x0;
    const x1 = coordinates.x1;
    const y0 = coordinates.y0;
    const y1 = coordinates.y1;

    rangeCoordinates.push([x0, y0]);
    rangeCoordinates.push([x0, y1]);
    rangeCoordinates.push([x1, y1]);
    rangeCoordinates.push([x1, y0]);

    return rangeCoordinates;
  }

  /**
   * Use to format python flask return format to frontend format
   * @param graphCoordinates python flask return format
   * @param graphId graph id
   * @returns frontend format
   */
  async getFormattedGraphCoordinates(
    graphCoordinates: SampleGraphDataFormat[],
    graphId: string,
    graphDetails: Partial<QueryGraphDetails>,
  ) {
    const returnObj: FrontendFeatureGraphDataFormat = {
      graphProgress: 0,
      graphInfo: {
        embeddingGenerationMethod: '',
        dimensionReductionMethod: '',
        createdDate: '',
        graphId: graphId,
        isEmbeddingAvailable: false,
      },
      data: [
        {
          x: [],
          y: [],
        },
      ],
    };

    const embeddingGenerationMethod = EMBEDDING_COLLECTION || '';
    const dimensionReductionMethod = 'IPCA';
    const createdDate = graphDetails?.createdAt || '';

    returnObj.graphInfo.embeddingGenerationMethod = embeddingGenerationMethod;
    returnObj.graphInfo.dimensionReductionMethod = dimensionReductionMethod;
    returnObj.graphInfo.createdDate = createdDate.toString();
    returnObj.graphInfo.isEmbeddingAvailable = graphDetails.isEmbeddingAvailable || false;

    // get graph progress
    // if jobId is not null, then get job progress + graph progress and calculate average
    // if jobId is null, then get only graph progress
    let progress = 0;
    if (graphDetails.jobId) {
      const jobDetails = await this.jobRepository.findById(graphDetails.jobId);
      progress = ((jobDetails?.progress || 0) + (graphDetails.progress || 0)) / 2;
    } else {
      progress = graphDetails.progress || 0;
    }

    returnObj.graphProgress = progress || 0;

    if (!graphCoordinates) {
      return returnObj;
    }

    const xArray: number[] = [];
    const yArray: number[] = [];

    for (const graphCoordinate of graphCoordinates) {
      xArray.push(graphCoordinate.coordinates[0]);
      yArray.push(graphCoordinate.coordinates[1]);
    }

    returnObj.data[0].x = xArray;
    returnObj.data[0].y = yArray;

    return returnObj;
  }

  /**
   * use to insert graph coordinates for pending collections (isFeatureGraphPending = true)
   * @returns
   */
  async insertGraphCoordinatesForPendingCollections() {
    let isDatasetPending = false;
    const pendingCollectionList = await this.metaDataRepository.find({
      where: {
        objectType: {inq: [ContentType.IMAGE_COLLECTION, ContentType.DATASET]},
        objectStatus: OBJECT_STATUS.ACTIVE,
        isFeatureGraphPending: true,
      },
      fields: {
        id: true,
        objectType: true,
        teamId: true,
        graphId: true,
      },
    });

    if (pendingCollectionList.length == 0) {
      return;
    }

    for (const collection of pendingCollectionList) {
      if (collection.objectType == ContentType.DATASET) {
        isDatasetPending = true;
      }

      try {
        await this.insertGraphCoordinatesForOneCollection(collection);
      } catch (err) {
        logger.warn(
          `${
            FLOWS.DATALAKE_SELECTION
          } | SearchQueryBuilderService.insertGraphCoordinatesForPendingCollections | N/A | failed to insert graph coordinates for collection, collection = ${JSON.stringify(
            collection,
          )}`,
        );
        continue;
      }
    }
    // use to recalculate system graph coordinates
    await this.insertSystemGraphCoordinates(isDatasetPending);
  }

  /**
   * Use to insert graph coordinates for one collection
   * @param collectionObject collection object
   * @returns graph id
   */
  async insertGraphCoordinatesForOneCollection(collectionObject: MetaData, currentUserProfile?: UserProfileDetailed) {
    let graphId = '';
    if (!collectionObject.id || !collectionObject.objectType || !collectionObject.teamId) {
      logger.warn(
        `${
          FLOWS.DATALAKE_SELECTION
        } | SearchQueryBuilderService.insertGraphCoordinatesForOneCollection | N/A | invalid collection, collection = ${JSON.stringify(
          collectionObject,
        )}`,
      );
      throw new HttpErrors.NotAcceptable(`Invalid collection`);
    }

    const returnObject = await this.validateGraphDataGenerationOngoing(collectionObject);

    if (!returnObject.isValid) return;

    await this.metaDataRepository.updateById(collectionObject.id, {
      graphDataGenerateInfo: {
        isGraphDataGenerationOngoing: true,
        graphDataCalculationStartedAt: new Date(),
      },
    });

    const filter: DatalakeSelectionRequest = {
      isAllSelected: false,
      objectIdList: [collectionObject.id.toString()],
      contentType: collectionObject.objectType,
      filterData: {},
      objectStatus: OBJECT_STATUS.ACTIVE,
    };

    try {
      const returnObj = await this.insertGraphObjectKeys(
        filter,
        currentUserProfile,
        collectionObject.teamId.toString(),
        false,
      );

      graphId = returnObj.graphId;

      await this.metaDataRepository.updateById(collectionObject.id, {
        isFeatureGraphPending: false,
        graphId: graphId,
      });

      // if collection has already graphId, then delete previous feature graph details from queryGraphDataRepository
      if (collectionObject.graphId && collectionObject.graphId != graphId) {
        await this.queryGraphDataRepository.deleteAll({graphId: collectionObject.graphId});
        await this.queryGraphDetailsRepository.deleteAll({graphId: collectionObject.graphId});
        await this.metaDataRepository.updateManyRemoveFromList(
          {
            'queryGraphData.graphId': collectionObject.graphId,
          },
          {
            queryGraphData: {
              graphId: collectionObject.graphId,
            },
          },
          [],
        );
      }
    } catch (err) {
      logger.warn(
        `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.insertGraphCoordinatesForOneCollection | N/A | failed to insert graph object keys, err = ${err}`,
      );
      await this.metaDataRepository.updateOneSet(
        {_id: new ObjectId(collectionObject.id)},
        {
          'graphDataGenerateInfo.isGraphDataGenerationOngoing': false,
          'graphDataGenerateInfo.graphDataCalculationFinishedAt': new Date(),
        },
        [],
      );
      throw new HttpErrors.NotAcceptable(`Failed to insert graph object keys`);
    }

    // await this.metaDataRepository.updateById(collectionObject.id, {
    //   graphDataGenerateInfo: {
    //     isGraphDataGenerationOngoing: false,
    //     graphDataCalculationFinishedAt: new Date(),
    //   },
    // });

    await this.metaDataRepository.updateOneSet(
      {_id: new ObjectId(collectionObject.id)},
      {
        'graphDataGenerateInfo.isGraphDataGenerationOngoing': false,
        'graphDataGenerateInfo.graphDataCalculationFinishedAt': new Date(),
      },
      [],
    );

    return graphId;
  }

  /**
   * Use to check validity of graph data calculation
   * @param collection when pre calculate data for collection this parameter should be present other wise pre calculate data for system
   * @returns {isValid: false or true}
   */
  async validateGraphDataGenerationOngoing(collection?: Metadata) {
    const jobType = [JobType.GenerateEmbedding, JobType.UploadEmbedding];
    const jobStatus = [JobStatus.inProgress, JobStatus.queued];
    const _andArray: any[] = [{jobType: {$in: jobType}}, {status: {$in: jobStatus}}];

    if (collection?.id) {
      _andArray.push({'jobSpecificDetails.affectedCollectionIdList': new ObjectId(collection.id)});
    }

    const filter: any = {
      $and: _andArray,
    };

    // console.log(JSON.stringify(filter));

    const onGoingJob = await this.jobRepository.findOneDirectDB(filter);

    if (onGoingJob) {
      return {isValid: false};
    }

    if (collection) {
      if (collection?.graphDataGenerateInfo?.isGraphDataGenerationOngoing) {
        return {isValid: false};
      } else {
        return {isValid: true};
      }
    }

    const systemData = await this.systemDataRepository.findOne({});

    if (
      systemData &&
      systemData.graphDataGenerateInfo &&
      systemData.graphDataGenerateInfo?.isGraphDataGenerationOngoing
    ) {
      return {isValid: false};
    }

    return {isValid: true};
  }

  /**
   * Use to insert system graph coordinates for system (images and datasets)
   * @param isRequiredDataset if true, then insert dataset graph coordinates also
   * @returns
   */
  async insertSystemGraphCoordinates(isRequiredDataset: boolean) {
    const objectTypeList = [ContentType.IMAGE];

    if (isRequiredDataset) {
      objectTypeList.push(ContentType.DATASET);
    }

    const systemData = await this.systemDataRepository.findOne({});

    if (!systemData || !systemData?.teamId) {
      logger.warn(
        `${
          FLOWS.DATALAKE_SELECTION
        } | SearchQueryBuilderService.insertSystemGraphCoordinates | N/A | invalid system data, systemData = ${JSON.stringify(
          systemData,
        )}`,
      );
      return;
    }

    const returnObject = await this.validateGraphDataGenerationOngoing();

    if (!returnObject.isValid) return;

    await this.systemDataRepository.updateById(systemData.id, {
      graphDataGenerateInfo: {
        isGraphDataGenerationOngoing: true,
        graphDataCalculationStartedAt: new Date(),
      },
    });

    const updateObj = systemData?.systemEmbeddingGraphDetail || {
      images: {
        graphId: '',
      },
      datasets: {
        graphId: '',
      },
    };

    const oldImageGraphId = systemData?.systemEmbeddingGraphDetail?.images.graphId;
    const oldDatasetGraphId = systemData?.systemEmbeddingGraphDetail?.datasets.graphId;

    for (const objectType of objectTypeList) {
      const filter: DatalakeSelectionRequest = {
        isAllSelected: true,
        contentType: objectType,
        filterData: {},
        objectStatus: OBJECT_STATUS.ACTIVE,
      };

      try {
        const returnObj = await this.insertGraphObjectKeys(filter, undefined, systemData.teamId.toString(), false);

        const graphId = returnObj.graphId;

        if (objectType == ContentType.IMAGE) {
          updateObj.images.graphId = graphId;
        } else if (objectType == ContentType.DATASET) {
          updateObj.datasets.graphId = graphId;
        } else {
          logger.warn(
            `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.insertSystemGraphCoordinates | N/A | invalid object type, objectType = ${objectType}`,
          );
          continue;
        }
      } catch (err) {
        logger.warn(
          `${FLOWS.DATALAKE_SELECTION} | SearchQueryBuilderService.insertSystemGraphCoordinates | N/A | failed to insert graph object keys, err = ${err}`,
        );
        continue;
      }
    }

    // update system data
    await this.systemDataRepository.updateById(systemData.id, {
      systemEmbeddingGraphDetail: updateObj,
    });

    // if system has already graphId, then delete previous feature graph details from queryGraphDataRepository
    if (oldImageGraphId || oldDatasetGraphId) {
      const _orArray = [];
      const _orArrayMetadata = [];

      // if old image graph id is exists and not equal to new image graph id, then delete old image graph details
      if (oldImageGraphId && oldImageGraphId != updateObj.images.graphId) {
        _orArray.push({graphId: oldImageGraphId});
        _orArrayMetadata.push(oldImageGraphId);
      }

      // if old dataset graph id is exists and not equal to new dataset graph id, then delete old dataset graph details
      if (oldDatasetGraphId && isRequiredDataset && oldDatasetGraphId != updateObj.datasets.graphId) {
        _orArray.push({graphId: oldDatasetGraphId});
        _orArrayMetadata.push(oldDatasetGraphId);
      }

      await this.queryGraphDataRepository.deleteAll({
        or: _orArray,
      });
      await this.queryGraphDetailsRepository.deleteAll({
        or: _orArray,
      });
      await this.metaDataRepository.updateManyRemoveFromList(
        {
          'queryGraphData.graphId': {
            $in: _orArrayMetadata,
          },
        },
        {
          queryGraphData: {
            graphId: {
              $in: _orArrayMetadata,
            },
          },
        },
        [],
      );
    }

    // await this.systemDataRepository.updateById(systemData.id, {
    //   graphDataGenerateInfo: {
    //     isGraphDataGenerationOngoing: false,
    //     graphDataCalculationFinishedAt: new Date(),
    //   },
    // });

    await this.metaDataRepository.updateOneSet(
      {_id: new ObjectId(systemData.id)},
      {
        'graphDataGenerateInfo.isGraphDataGenerationOngoing': false,
        'graphDataGenerateInfo.graphDataCalculationFinishedAt': new Date(),
      },
      [],
    );
  }

  /**
   * Use to get system data
   */
  async getSystemData(teamId: string) {
    const objectTypeTypeWiseCount = await this.systemDataRepository.findOne({
      where: {
        teamId: teamId,
      },
      fields: {
        objectTypeWiseCounts: true,
      },
    });

    return {
      totalImageCount: objectTypeTypeWiseCount?.objectTypeWiseCounts?.images.count,
      totalVideoCount: objectTypeTypeWiseCount?.objectTypeWiseCounts?.videos.count,
      totalOtherCount: objectTypeTypeWiseCount?.objectTypeWiseCounts?.other.count,
    };
  }

  /**
   * Use to get check graph data availability
   * @param filter {DatalakeSelectionRequest} filter for query
   * @returns GraphDataAvailability
   */
  async checkIfGraphEmbeddingAvailable(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    // if collection id is not present then return available
    if (!filter.collectionId) {
      return GraphDataAvailability.AVAILABLE;
    }
    // check if graph data is still in generation
    if (filter.embeddingGraph?.graphId) {
      const graphDetails = await this.queryGraphDetailsRepository.findOne({
        where: {
          graphId: filter.embeddingGraph?.graphId,
        },
      });
      if (graphDetails?.jobId) {
        const jobDetails = await this.jobRepository.findById(graphDetails.jobId);
        if (jobDetails?.status != JobStatus.completed) {
          return GraphDataAvailability.IN_PROGRESS;
        } else {
          return GraphDataAvailability.AVAILABLE;
        }
      }
    }
    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: currentUserProfile.teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };

    const aggregateQuery = await this.getMatchQueryForSelection(currentUserProfile, undefined, datalakeSelection);

    const params = [
      {$match: aggregateQuery?.matchQuery},
      {
        $addFields: {
          embeddingModels: {
            $cond: {
              if: {$isArray: '$embeddingModels'},
              then: '$embeddingModels',
              else: [],
            },
          },
        },
      },
      {
        $group: {
          _id: null, // Group all documents together
          totalCount: {$sum: 1}, // Count all documents
          embeddingAvailableCount: {
            $sum: {
              $cond: [
                {
                  $anyElementTrue: {
                    $map: {
                      input: '$embeddingModels',
                      as: 'model',
                      in: {$eq: ['$$model.modelName', EMBEDDING_COLLECTION]},
                    },
                  },
                },
                1,
                0,
              ],
            },
          }, // Count only documents where "embeddingModels.modelName" is "Resnet50"
        },
      },
    ];

    const counts: [{totalCount: number; embeddingAvailableCount: number}] = await this.metaDataRepository.aggregate(
      params,
    );

    if (counts && Array.isArray(counts) && counts.length > 0) {
      if (counts[0].totalCount != counts[0].embeddingAvailableCount) {
        if (counts[0].embeddingAvailableCount == 0) {
          return GraphDataAvailability.NOT_AVAILABLE;
        } else {
          return GraphDataAvailability.PARTIAL_AVAILABLE;
        }
      } else {
        return GraphDataAvailability.AVAILABLE;
      }
    } else {
      return GraphDataAvailability.NOT_AVAILABLE;
    }
  }
}

export const SEARCH_QUERY_BUILDER_SERVICE = BindingKey.create<SearchQueryBuilderService>(
  'service.searchQueryBuilderService',
);
