/**
 * @class DatalakeTrashService
 * purpose of this service is datalake trashing
 * @description datalake trashing logics
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'bson';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {OTPAuthService, OTP_AUTH_SERVICE} from '../authServices/otp-auth.service';
import {logger} from '../config';
import {DatalakeSelection, DatalakeSelectionRequest} from '../models';
import {JobStatus, JobType} from '../models/job.model';
import {
  ContentType,
  ExplorerDefaultViewDetailsResponse,
  ExplorerFilterV2,
  ItemObject,
  MetaData,
  OBJECT_STATUS,
  TrashDeleteRequest,
  TrashItemCountViewResponse,
  TrashItemListViewResponse,
} from '../models/meta-data.model';
import {
  MetaDataRepository,
  MetaDataUpdateRepository,
  QueryOptionRepository,
  StorageMappingRepository,
} from '../repositories';
import {FLOWS, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DEFAULT_ITEM_SORT_IN_TRASH, OTPTypes} from '../settings/global-field-config';
import {getObjectTypeName, isArrayWithLength, isValidObjectId, regExpEscape} from '../settings/tools';
import {JOB_SERVICE, JobService} from './job.service';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import {SYSTEM_STATS_SERVICE, SystemStatsService} from './system-stats.service';

@injectable({scope: BindingScope.TRANSIENT})
export class DatalakeTrashService {
  constructor(
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(OTP_AUTH_SERVICE) private otpAuthService: OTPAuthService,
    @repository(MetaDataUpdateRepository)
    private metaDataUpdateRepository: MetaDataUpdateRepository,
    @inject(SYSTEM_STATS_SERVICE) private systemStatsService: SystemStatsService,
    @repository('StorageMappingRepository')
    private storageMappingRepository: StorageMappingRepository,
  ) {}

  /**
   * get trash item list
   * @param teamId logged user team id
   * @param searchKey search key
   * @param pageIndex {number} page number [strart from 0]
   * @param pageSize {number} page size [default 12]
   * @returns TrashItemListViewResponse
   */
  async getTrashObjectList(
    filter: ExplorerFilterV2,
    contentType: ContentType,
    teamId: string,
    searchKey: string,
    pageIndex: number,
    pageSize: number,
    currentUserProfile: UserProfileDetailed,
  ) {
    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.getTrashObjectList | N/A | searchKey = ${searchKey} pageIndex = ${pageIndex} pageSize = ${pageSize}`,
    );

    // initialize response
    let trashItemListViewResponse: TrashItemListViewResponse = {
      trashItemList: [],
    };

    let _andArr: any[] = [];

    if (contentType != ContentType.ALL) {
      // get object type from content type
      let objectType = await this.metaDataRepository.getObjectType(true, contentType);
      _andArr.push({objectType: objectType});
    }
    //filter match query create
    let filterMatchQuery: any = await this.searchQueryBuilderService.getMatchQueryForExplorerFilter(
      filter,
      undefined,
      undefined,
      currentUserProfile,
    );

    if (teamId) {
      _andArr.push({teamId: new ObjectId(teamId)});
    }

    // filter by search key
    if (searchKey) {
      const pattern = new RegExp(regExpEscape(searchKey), 'i');
      _andArr.push({name: pattern});
    }

    let skipCount = pageIndex * pageSize;
    let limitCount = pageSize;

    let aggregateQuery = [
      {
        $match: {
          objectStatus: OBJECT_STATUS.TRASHED,
          showInTrash: true,
          $and: _andArr,
          ...filterMatchQuery,
        },
      },
      {
        $sort: DEFAULT_ITEM_SORT_IN_TRASH,
      },
      {
        $skip: skipCount,
      },
      {
        $limit: limitCount,
      },
      {
        $project: {
          _id: 1,
          name: 1,
          frameCount: 1,
          thumbnailUrl: 1,
          objectType: 1,
          uploadingUserId: 1,
          objectStatus: 1,
        },
      },
    ];

    let _content: Partial<MetaData>[] = await this.metaDataRepository.aggregate(aggregateQuery);

    let formattedContent = _content.map((metaObj, idx) => {
      let formattedMetaObj: ItemObject = this.searchQueryBuilderService.getFormattedMetaObject(
        metaObj,
        skipCount,
        idx,
        currentUserProfile,
        undefined,
      );
      return formattedMetaObj;
    });

    trashItemListViewResponse.trashItemList = formattedContent;

    return trashItemListViewResponse;
  }

  /**
   * get trash item count
   * @param teamId logged user team id
   * @param searchKey search key
   * @returns TrashItemCountViewResponse
   */
  async getTrashObjectCount(
    currentUserProfile: UserProfileDetailed,
    searchKey: string,
    filter: ExplorerFilterV2,
    contentType: ContentType,
  ) {
    let teamId = currentUserProfile.teamId;
    let trashItemCountViewResponse: TrashItemCountViewResponse = {
      trashItemCount: 0,
    };

    let matchingCriteria: {[key: string]: any} = {
      teamId: new ObjectId(teamId),
      objectStatus: OBJECT_STATUS.TRASHED,
      showInTrash: true,
    };

    if (
      currentUserProfile &&
      currentUserProfile.userType &&
      currentUserProfile.userType === UserType.USER_TYPE_COLLABORATOR
    ) {
      matchingCriteria = {
        ...matchingCriteria,
        allowedUserIdList: new ObjectId(currentUserProfile.id),
      };
    }

    if (searchKey && searchKey.length > 0) {
      const pattern = new RegExp(regExpEscape(searchKey), 'i');
      matchingCriteria = {
        ...matchingCriteria,
        name: pattern,
      };
    }

    if (contentType != ContentType.ALL) {
      // get object type from content type
      let objectType = await this.metaDataRepository.getObjectType(true, contentType);
      matchingCriteria = {
        ...matchingCriteria,
        objectType: objectType,
      };
    }

    let countObj = await this.metaDataRepository.aggregate([
      {
        $match: matchingCriteria,
      },
      {
        $group: {
          _id: null,
          count: {$sum: 1},
        },
      },
    ]);

    if (isArrayWithLength(countObj)) {
      trashItemCountViewResponse.trashItemCount = countObj[0].count;
    }

    return trashItemCountViewResponse;
  }

  /**
   * use to trash section object
   * @param selectionObj object related to selection id
   * @param teamId logged user team id
   * @returns message how many objects successfully trashed
   */
  async trashSelectionObject(
    selectionObj: DatalakeSelection,
    currentUserProfile: UserProfileDetailed,
    userId?: string,
    userName?: string,
  ) {
    // initialize variable
    let query = selectionObj.selectionRequest.query;
    let collectionId = selectionObj.selectionRequest.collectionId;
    let selectionObjectType = selectionObj.objectType;
    let success = false;
    let teamId = currentUserProfile.teamId;

    let successCount: number = 0;
    let successFileCount: number = 0;
    let successFileNameList: string[] = [];
    let failedFileCount: number = 0;
    let failedCount: number = 0;
    let failedFileNameList: string[] = [];

    let affectedCollectionIdList: any[] = [];

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | N/A | Couldn't find the team`,
      );

      return {
        message: DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST,
        isSuccess: false,
      };
    }

    // get object list related to selection id
    if (!selectionObj.id) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | N/A | Invalid selection id, selection=${selectionObj}`,
      );

      return {
        message: DatalakeUserMessages.INVALID_SELECTION_ID,
        isSuccess: false,
      };
    }
    let matchQueryObj = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      selectionObj.id,
    );
    if (!matchQueryObj) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | N/A | Undefined match query, selection=${selectionObj}`,
      );

      return {
        message: DatalakeUserMessages.INVALID_SELECTION_QUERY,
        isSuccess: false,
      };
    }
    let aggregatePara: any[] = [
      {
        $match: matchQueryObj.matchQuery,
      },
    ];

    // if (
    //   selectionObj?.selectionRequest?.embeddingSelection?.selection &&
    //   selectionObj?.selectionRequest?.embeddingSelection?.graphId
    // ) {
    //   let lookup: GetEmbeddingAggregationForSelectionRes[] =
    //     await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(
    //       selectionObj.selectionRequest.embeddingSelection,
    //     );
    //   aggregatePara.push(...lookup);
    // }

    aggregatePara = [
      ...aggregatePara,
      {
        $project: {
          _id: 1,
          name: 1,
        },
      },
    ];
    let objectIdList = await this.metaDataRepository.aggregate(aggregatePara);

    const sessionId = `trash-${uuidV4()}`;
    let metaDataTypeName = getObjectTypeName(selectionObjectType);
    let trashCount = objectIdList.length || 0;
    let jobName = `${trashCount} ${metaDataTypeName}s`;
    if (objectIdList.length == 1) {
      jobName = objectIdList[0].name;
      // jobName = `${trashCount} ${metaDataTypeName}`;
    }

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      userId,
      teamId,
      userName,
      JobType.Trash,
      0,
      JobStatus.queued,
      {
        selectionId: selectionObj.id,
        collectionId: collectionId ? new ObjectId(collectionId) : undefined,
        contentType: selectionObjectType,
      },
    );

    if (!Array.isArray(objectIdList) || objectIdList.length === 0) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | N/A | No object found to trash, selectionId=${selectionObj.id}`,
      );

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.Trash,
        100,
        JobStatus.completed,
        {
          totalCount: objectIdList.length,
          successCount: successCount,
          successFileCount: successFileCount,
          failedFileCount: failedFileCount,
          failCount: failedCount,
          warning: 'No object found to trash',
        },
      );

      return {
        message: DatalakeUserMessages.NO_OBJECT_FOUND_TO_TRASH,
        isSuccess: false,
      };
    }

    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | N/A | length of objectIdList to be trashed=${objectIdList.length}`,
    );

    let collectionObj: Partial<MetaData> = {};
    if (isValidObjectId(selectionObj.selectionRequest.collectionId)) {
      collectionObj = await this.metaDataRepository.findById(selectionObj.selectionRequest.collectionId);
    }

    let failedReasons: string[] = [];

    // iterate object list
    for (const {_id: objectId} of objectIdList) {
      let metaObject = await this.metaDataRepository.findById(objectId);

      let validation = await this.metaDataRepository.validateObjectToTrash(metaObject, collectionObj);

      if (validation.isSuccess) {
        let trashedInfo = {
          objectStatus: OBJECT_STATUS.TRASHED,
          statPending: true,
          statPendingAt: new Date(),
          trashedAt: new Date(),
          showInTrash: true,
          trashedBy: userId,
          updatedAt: new Date(),
        };

        if (
          [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
            metaObject.objectType,
          )
        ) {
          // Trash collection object (head)
          await this.metaDataRepository.updateById(objectId, trashedInfo);

          // statPending true for already trash objects belong to collection
          // to calculate stats when head only trash
          await this.metaDataRepository.updateManySet(
            {
              vCollectionIdList: new ObjectId(objectId),
              objectStatus: OBJECT_STATUS.TRASHED,
            },
            {statPending: true, statPendingAt: new Date()},
            [],
          );

          // trash children if it is a logical or physical collection
          trashedInfo.showInTrash = false;

          const updateMatchFilter: Record<string, any> = {
            $and: [
              // {$or: [{collectionId: new ObjectId(objectId)}, {vCollectionIdList: new ObjectId(objectId)}]},
              {vCollectionIdList: new ObjectId(objectId)},
              {objectStatus: OBJECT_STATUS.ACTIVE},
            ],
          };
          // Trash objects belong to collection
          await this.metaDataRepository.updateManySet(updateMatchFilter, trashedInfo, []);

          if (metaObject.objectType === ContentType.VIDEO_COLLECTION) {
            await this.unlinkVideoObjectsOfCollectionFromDerivedObjects(metaObject.id);
          }

          let vCollectionIdListObject = await this.metaDataRepository.aggregate([
            {
              $match: {vCollectionIdList: new ObjectId(objectId)},
            },
            {
              $unwind: '$vCollectionIdList',
            },
            {
              $group: {
                _id: null,
                vCollectionIdList: {
                  $addToSet: '$vCollectionIdList',
                },
              },
            },
          ]);

          // update affected collection id list
          if (isArrayWithLength(vCollectionIdListObject)) {
            let vCollectionIdList = vCollectionIdListObject[0].vCollectionIdList;
            let notIncludedVCollectionIdList = vCollectionIdList.filter(
              (vCollectionId: any) => !affectedCollectionIdList.includes(`${vCollectionId}`),
            );
            affectedCollectionIdList = affectedCollectionIdList.concat(notIncludedVCollectionIdList);
          }
        } else if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType)) {
          // Trash leaf object
          await this.metaDataRepository.updateById(objectId, trashedInfo);

          if (metaObject.objectType === ContentType.VIDEO) {
            await this.unlinkVideoObjectFromDerivedObjects(metaObject);
          }

          // update affected collection id list
          let vCollectionIdList = metaObject.vCollectionIdList ? metaObject.vCollectionIdList : [];

          let notIncludedVCollectionIdList = vCollectionIdList.filter(
            (vCollectionId: any) => !affectedCollectionIdList.includes(`${vCollectionId}`),
          );
          affectedCollectionIdList = affectedCollectionIdList.concat(notIncludedVCollectionIdList);
        }

        successFileCount +=
          metaObject.objectType === ContentType.IMAGE_COLLECTION
            ? metaObject.imageCount
            : metaObject.objectType === ContentType.VIDEO_COLLECTION
            ? metaObject.videoCount
            : metaObject.objectType === ContentType.OTHER_COLLECTION
            ? metaObject.otherCount
            : 1;

        successCount += 1;

        if (metaObject.name) successFileNameList.push(metaObject.name);
      } else {
        failedCount += 1;

        failedFileCount +=
          metaObject.objectType === ContentType.IMAGE_COLLECTION
            ? metaObject.imageCount
            : metaObject.objectType === ContentType.VIDEO_COLLECTION
            ? metaObject.videoCount
            : metaObject.objectType === ContentType.OTHER_COLLECTION
            ? metaObject.otherCount
            : 1;

        if (metaObject.name) failedFileNameList.push(metaObject.name);

        logger.error(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | ${teamId} | Failed to trash object: ${objectId}, validation failed: `,
        );

        if (validation.reasons) {
          if (validation.reasons.type && !failedReasons.includes(`${metaObject.name} is Dataset`)) {
            failedReasons.push(`${metaObject.name} is Dataset`);
          }
          if (validation.reasons.annotationProjectList && !failedReasons.includes(`${metaObject.name} is Dataset`)) {
            for (let project of validation.reasons.annotationProjectList) {
              if (!failedReasons.includes(`Used in Annotation Project ${project.name}`))
                failedReasons.push(`Used in Annotation Project ${project.name}`);
            }
          }

          if (validation.reasons.curationProjectList && !failedReasons.includes(`${metaObject.name} is Dataset`)) {
            for (let project of validation.reasons.curationProjectList) {
              if (!failedReasons.includes(`Used in Curation Project ${project.name}`))
                failedReasons.push(`Used in Curation Project ${project.name}`);
            }
          }

          if (validation.reasons.datasetVersionList) {
            for (let dataset of validation.reasons.datasetVersionList) {
              if (!failedReasons.includes(`Used in Dataset ${dataset.datasetGroupName}`))
                failedReasons.push(`Used in Dataset ${dataset.datasetGroupName}`);
            }
          }
        }
      }

      let progress = 0;
      if (objectIdList.length) {
        progress = (successCount / objectIdList.length) * 100;
      }

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.Trash,
        progress,
        JobStatus.inProgress,
        {
          totalCount: objectIdList.length,
          successCount: successCount,
          successFileCount: successFileCount,
          failedFileCount: failedFileCount,
          failCount: failedCount,
          successFileNameList: successFileNameList,
          failedFileNameList: failedFileNameList,
          jobPartFailedReasonList: failedReasons,
        },
      );
    }

    //log for successfully trashed objects with count
    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.trashSelectionObject | ${teamId} | ${successCount} objects successfully trashed for collectionId: ${collectionId}, teamId: ${teamId}`,
    );

    if (affectedCollectionIdList.length > 0) {
      // regenerate query option for affected collection id list
      for (const collectionId of affectedCollectionIdList) {
        this.queryOptionRepository.regenerateQueryOptionForCollection(collectionId, teamId);
      }
      // isFeatureGraphPending true for affected collection id
      await this.metaDataRepository.updateManySet(
        {
          _id: {$in: affectedCollectionIdList.map((collectionId: any) => new ObjectId(collectionId))},
          objectType: {$in: [ContentType.IMAGE_COLLECTION, ContentType.DATASET]},
        },
        {isFeatureGraphPending: true},
        [],
      );
    }

    if (successCount > 0) success = true;

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      userId,
      teamId,
      userName,
      JobType.Trash,
      100,
      JobStatus.completed,
      {
        totalCount: objectIdList.length,
        successCount: successCount,
        successFileCount: successFileCount,
        failedFileCount: failedFileCount,
        failCount: failedCount,
      },
    );

    return {
      message: `${successCount} objects successfully trashed, ${failedCount} objects failed to trash`,
      isSuccess: success,
    };
  }

  /**
   * use to remove link which an video object has with its derived objects
   * @param metaObject MetaData object, which is going to be unlinked from derived objects
   */
  async unlinkVideoObjectFromDerivedObjects(metaObject: MetaData) {
    if (!isValidObjectId(metaObject.frameCollectionId)) {
      return;
    }

    let unlinkParents = [new ObjectId(metaObject.id)];

    if (metaObject.collectionId) {
      unlinkParents.push(new ObjectId(metaObject.collectionId));
    }

    await this.metaDataRepository.updateManyPullAll(
      {parentList: new ObjectId(metaObject.id)},
      {parentList: unlinkParents},
      [],
    );
  }

  /**
   * use to remove link which the video objects of a collection has with its derived objects
   * @param collectionId id of collection, which objects are going to be unlinked from derived objects
   * @returns
   */
  async unlinkVideoObjectsOfCollectionFromDerivedObjects(collectionId?: string) {
    if (!collectionId) return;

    let linkExistsChildren = await this.metaDataRepository.aggregate([
      {
        $match: {
          // $or: [{collectionId: new ObjectId(collectionId)}, {vCollectionIdList: new ObjectId(collectionId)}],
          vCollectionIdList: new ObjectId(collectionId),
          frameCollectionId: {$exists: true},
        },
      },
      {$project: {_id: 1}},
    ]);

    if (isArrayWithLength(linkExistsChildren)) {
      for (const child of linkExistsChildren) {
        let metaObject = await this.metaDataRepository.findById(child._id);
        await this.unlinkVideoObjectFromDerivedObjects(metaObject);
      }
    }
  }

  /**
   * use to add link which an video object has with its derived objects
   * @param metaObject MetaData object, which is going to be linked to derived objects
   */
  async linkVideoObjectToDerivedObjects(metaObject: MetaData) {
    if (!isValidObjectId(metaObject.frameCollectionId)) {
      return;
    }

    //link to frame collection
    let parentsOfFrameCollection = [new ObjectId(metaObject.id)];
    if (metaObject.collectionId) {
      parentsOfFrameCollection.push(new ObjectId(metaObject.collectionId));
    }
    await this.metaDataRepository.updateById(metaObject.frameCollectionId, {parentList: parentsOfFrameCollection});

    //link to frame objects
    parentsOfFrameCollection.push(new ObjectId(metaObject.frameCollectionId));
    await this.metaDataRepository.updateManySet(
      {collectionId: new ObjectId(metaObject.frameCollectionId)},
      {parentList: parentsOfFrameCollection},
      [],
    );
  }

  /**
   * use to add link which the video objects of a collection has with its derived objects
   * @param collectionId id of collection, which objects are going to be linked to derived objects
   * @returns
   */
  async linkVideoObjectsOfCollectionToDerivedObjects(collectionId?: string) {
    if (!collectionId) return;

    let linkExistsChildren = await this.metaDataRepository.aggregate([
      {
        $match: {
          // $or: [{collectionId: new ObjectId(collectionId)}, {vCollectionIdList: new ObjectId(collectionId)}],
          vCollectionIdList: new ObjectId(collectionId),
          frameCollectionId: {$exists: true},
        },
      },
      {$project: {_id: 1}},
    ]);

    if (isArrayWithLength(linkExistsChildren)) {
      for (const child of linkExistsChildren) {
        let metaObject = await this.metaDataRepository.findById(child._id);
        await this.linkVideoObjectToDerivedObjects(metaObject);
      }
    }
  }

  /**
   * rebuild customMeta and Tags fields of collection head without trashed object
   * @param collectionId id of affected collection
   * @param teamId logged user team id
   */
  // async regenerateQueryOption(collectionId: string, teamId?: string) {
  //   if (!teamId) {
  //     logger.error(
  //       `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.regenerateQueryOption | N/A | Couldn't find the team `,
  //     );
  //   }

  //   // get all custom meta data
  //   let customMetaObject = await this.metaDataRepository.aggregate([
  //     {
  //       $match: {
  //         $or: [{vCollectionIdList: new ObjectId(collectionId)}, {_id: new ObjectId(collectionId)}],
  //         teamId: new ObjectId(teamId),
  //         objectStatus: OBJECT_STATUS.ACTIVE,
  //       },
  //     },
  //     {
  //       $group: {
  //         _id: null,
  //         customMeta: {
  //           $mergeObjects: '$customMeta',
  //         },
  //       },
  //     },
  //     {
  //       $project: {
  //         _id: 0,
  //         customMeta: 1,
  //       },
  //     },
  //   ]);

  //   let customMeta: {[k: string]: any} = {};

  //   if (isArrayWithLength(customMetaObject)) {
  //     customMeta = customMetaObject[0].customMeta;
  //   }
  //   customMeta.Tags = ''; // add Tags field to customMeta

  //   console.log('returnObject', customMeta);

  //   // iterate customMeta and update customMeta keys with values array
  //   for (const key in customMeta) {
  //     let distinctKey = '';
  //     if (key === 'Tags') {
  //       distinctKey = 'Tags';
  //     } else {
  //       distinctKey = `customMeta.${key}`;
  //     }

  //     let valueArray = await this.metaDataRepository.distinct(distinctKey, {
  //       $or: [{vCollectionIdList: new ObjectId(collectionId)}, {_id: new ObjectId(collectionId)}],
  //       teamId: new ObjectId(teamId),
  //       objectStatus: OBJECT_STATUS.ACTIVE,
  //     });

  //     customMeta[key] = valueArray;
  //   }
  //   // delete all QueryOption realated to collection id
  //   let deletedCount = await this.queryOptionRepository.deleteAll({
  //     collectionId: collectionId,
  //     teamId: teamId,
  //   });

  //   logger.info(
  //     `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.regenerateQueryOption | ${teamId} | ${deletedCount} QueryOption deleted for collectionId: ${collectionId}`,
  //   );

  //add collection head only meta fields to regenerate list
  // if (
  //   collectionObj.collectionHeadOnlyMeta &&
  //   collectionObj.collectionHeadOnlyMeta.customMeta &&
  //   collectionObj.objectStatus !== OBJECT_STATUS.TRASHED
  // ) {
  //   Object.keys(collectionObj.collectionHeadOnlyMeta?.customMeta).map(key => {
  //     if (!regeneratedCustomMetaObject[key]) {
  //       regeneratedCustomMetaObject[key] = collectionObj.collectionHeadOnlyMeta?.customMeta[key];
  //     }
  //   });
  // }

  //   if (Object.keys(customMeta).length > 0) {
  //     // rebulid QueryOption according to collection head
  //     await this.queryOptionRepository.updateQueryOption(
  //       SearchQueryRootGroup.METADATA,
  //       customMeta,
  //       true,
  //       teamId,
  //       collectionId,
  //     );
  //   }
  // }

  /**
   * to restore trashed object
   * @param objectIdList list of object id
   * @param searchKey search key
   * @param isAllSelected is all selected flag : boolean
   * @param teamId logged user team id
   * @param userId logged user id
   * @returns message message what is the status of the restore process
   */
  async restoreTrashObject(
    objectIdList: string[],
    searchKey: string,
    isAllSelected: boolean,
    currentUserProfile: UserProfileDetailed,
    userId: string | undefined,
    filter: ExplorerFilterV2,
    contentType: ContentType,
    userName?: string,
  ) {
    let affectedCollectionIdList: any[] = [];
    let teamId = currentUserProfile.teamId;

    //get object list
    let objectList: {_id: string}[] = await this.getSelectedObjectListInTrash(
      objectIdList,
      isAllSelected,
      searchKey,
      filter,
      contentType,
      currentUserProfile,
    );

    let jobName = `${objectList.length} Items`;
    if (objectList.length == 1) {
      let metaObject = await this.metaDataRepository.findById(objectList[0]._id);
      jobName = metaObject.name;
      // jobName = `${objectList.length} Item`;
    }

    const sessionId = `restore-${uuidV4()}`;
    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      userId,
      teamId,
      userName,
      JobType.Restore,
      0,
      JobStatus.queued,
      {},
    );

    if (!teamId) {
      logger.error(`${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.restoreTrashObject | N/A | Couldn't find the team`);

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.Restore,
        100,
        JobStatus.completed,
        {
          totalCount: objectList.length,
          warning: "Couldn't find the team",
        },
      );

      return {
        message: DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST,
        isSuccess: false,
      };
    }

    //if no object found to restore
    if (objectList.length == 0) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.restoreTrashObject | ${teamId} | No object found to restore.`,
      );
      objectList;
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.Restore,
        100,
        JobStatus.completed,
        {
          totalCount: objectList.length,
          warning: 'No object found to restore',
        },
      );

      return {
        message: 'No object found to restore.',
        isSuccess: false,
      };
    }

    let successCount: number = 0;
    let successFileCount: number = 0;
    let successFileNameList: string[] = [];
    let failedCount: number = 0;
    let failedFileCount: number = 0;
    let failedFileNameList: string[] = [];

    //iterate object list and restore object
    for (const obj of objectList) {
      let metaObject = await this.metaDataRepository.findById(obj._id);

      try {
        let restoreInfo = {
          objectStatus: OBJECT_STATUS.ACTIVE,
          statPending: true,
          statPendingAt: new Date(),
          showInTrash: false,
          updatedAt: new Date(),
          restoredAt: new Date(),
          restoredBy: userId ? new ObjectId(userId) : undefined,
        };

        if (
          [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
            metaObject.objectType,
          )
        ) {
          // Restore collection object (head)
          await this.metaDataRepository.updateManySet({_id: new ObjectId(metaObject.id)}, restoreInfo, []);

          // statPending true for already Restore objects also belong to collection
          // OBJECT_STATUS.ACTIVE add to calculate stats when head only restore
          //Restore children if collection is logical or physical
          const updateMatchFilter: Record<string, any> = {
            $and: [
              // {$or: [{collectionId: new ObjectId(metaObject.id)}, {vCollectionIdList: new ObjectId(metaObject.id)}]},
              {vCollectionIdList: new ObjectId(metaObject.id)},
              {objectStatus: {$in: [OBJECT_STATUS.TRASHED, OBJECT_STATUS.ACTIVE]}},
            ],
          };

          // Restore objects belong to collection
          await this.metaDataRepository.updateManySet(updateMatchFilter, restoreInfo, []);

          if (metaObject.objectType === ContentType.VIDEO_COLLECTION) {
            await this.linkVideoObjectsOfCollectionToDerivedObjects(metaObject.id);
          }

          // propagate custom meta data to collection head as empty array
          // if (metaObject.id) {
          //   await this.propagateCustomMetaDataToCollectionHead(metaObject.id);
          // }

          let vCollectionIdListObject = await this.metaDataRepository.aggregate([
            {
              $match: {vCollectionIdList: new ObjectId(metaObject.id)},
            },
            {
              $unwind: '$vCollectionIdList',
            },
            {
              $group: {
                _id: null,
                vCollectionIdList: {
                  $addToSet: '$vCollectionIdList',
                },
              },
            },
          ]);

          // update affected collection id list
          if (isArrayWithLength(vCollectionIdListObject)) {
            let vCollectionIdList = vCollectionIdListObject[0].vCollectionIdList;
            let notIncludedVCollectionIdList = vCollectionIdList.filter(
              (vCollectionId: any) => !affectedCollectionIdList.includes(`${vCollectionId}`),
            );
            affectedCollectionIdList = affectedCollectionIdList.concat(notIncludedVCollectionIdList);
          }
        } else if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType)) {
          // check collection head still trash or not ====== This Logic Remove with new scenario ======
          // let isCollectionTrashed = await this.metaDataRepository.find({
          //   where: {
          //     id: metaObject.collectionId,
          //     objectStatus: OBJECT_STATUS.TRASHED,
          //   },
          // });
          // if (isCollectionTrashed) {
          //   await this.metaDataRepository.updateManySet({_id: new ObjectId(metaObject.collectionId)}, restoreInfo, []);
          // }

          // custom meta data propagate to collection head
          // let customMetaObject = metaObject.customMeta ? metaObject.customMeta : {};
          // if (customMetaObject && Object.keys(customMetaObject).length > 0) {
          //   for (const [key, value] of Object.entries(customMetaObject)) {
          //     await this.metaDataRepository.updateManySet(
          //       {
          //         _id: new ObjectId(metaObject.collectionId),
          //         [`customMeta.${key}`]: {$exists: false},
          //       },
          //       {
          //         [`customMeta.${key}`]: [],
          //       },
          //       [],
          //     );
          //   }
          // }

          // Restore leaf objects
          await this.metaDataRepository.updateManySet({_id: new ObjectId(metaObject.id)}, restoreInfo, []);

          if (metaObject.objectType === ContentType.VIDEO) {
            await this.linkVideoObjectToDerivedObjects(metaObject);
          }
          //console.log('restore object', obj);

          // update affected collection id list
          let vCollectionIdList = metaObject.vCollectionIdList ? metaObject.vCollectionIdList : [];

          let notIncludedVCollectionIdList = vCollectionIdList.filter(
            (vCollectionId: any) => !affectedCollectionIdList.includes(`${vCollectionId}`),
          );
          affectedCollectionIdList = affectedCollectionIdList.concat(notIncludedVCollectionIdList);
        }

        // logger msg for object restore
        logger.info(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.restoreTrashObject | ${teamId} | Object restored successfully. Object id: ${metaObject.id}`,
        );
        successCount += 1;

        successFileCount +=
          metaObject.objectType === ContentType.IMAGE_COLLECTION
            ? metaObject.imageCount
            : metaObject.objectType === ContentType.VIDEO_COLLECTION
            ? metaObject.videoCount
            : metaObject.objectType === ContentType.OTHER_COLLECTION
            ? metaObject.otherCount
            : 1;

        if (metaObject.name) successFileNameList.push(metaObject.name);
      } catch (error) {
        failedCount += 1;

        failedFileCount +=
          metaObject.objectType === ContentType.IMAGE_COLLECTION
            ? metaObject.imageCount
            : metaObject.objectType === ContentType.VIDEO_COLLECTION
            ? metaObject.videoCount
            : metaObject.objectType === ContentType.OTHER_COLLECTION
            ? metaObject.otherCount
            : 1;

        if (metaObject.name) failedFileNameList.push(metaObject.name);

        logger.error(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.restoreTrashObject | ${teamId} | Error while restoring object. Object id: ${metaObject.id} | Error: ${error}`,
        );
      }

      let progress = 0;
      if (objectIdList.length) {
        progress = (successCount / objectIdList.length) * 100;
      }

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.Restore,
        progress,
        JobStatus.inProgress,
        {
          totalCount: objectIdList.length,
          successCount: successCount,
          successFileCount: successFileCount,
          failCount: failedCount,
          failedFileCount: failedFileCount,
          successFileNameList: successFileNameList,
          failedFileNameList: failedFileNameList,
        },
      );
    }

    if (affectedCollectionIdList.length > 0) {
      // rebuild tag and custom meta data for affected collection id
      // console.log('affectedCollectionIdList', affectedCollectionIdList);
      for (const collectionId of affectedCollectionIdList) {
        this.queryOptionRepository.regenerateQueryOptionForCollection(collectionId, teamId);
      }
      // isFeatureGraphPending true for affected collection id
      await this.metaDataRepository.updateManySet(
        {
          _id: {$in: affectedCollectionIdList.map(id => new ObjectId(id))},
          objectType: {$in: [ContentType.IMAGE_COLLECTION, ContentType.DATASET]},
        },
        {isFeatureGraphPending: true},
        [],
      );
    }

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      userId,
      teamId,
      userName,
      JobType.Restore,
      100,
      JobStatus.completed,
      {
        totalCount: objectIdList.length,
      },
    );

    return {
      message: 'Object restored successfully.',
      isSuccess: true,
    };
  }

  /**
   * to permanently delete trash object
   * @param trashDeleteRequest TrashDeleteRequest
   * @param teamId id of the team
   * @returns
   */
  async deleteTrashedObjects(
    trashDeleteRequest: TrashDeleteRequest,
    teamId: string,
    currentUserProfile: UserProfileDetailed,
  ) {
    logger.info(
      `${
        FLOWS.DATALAKE_TRASH
      } | DatalakeTrashService.deleteTrashedObjects | ${teamId} | trashDeleteRequest=${JSON.stringify(
        trashDeleteRequest,
      )}, currentUserProfile=${JSON.stringify(currentUserProfile)}`,
    );

    let searchKey = trashDeleteRequest.searchKey ? trashDeleteRequest.searchKey : '';
    let isAllSelected = trashDeleteRequest.isAllSelected ? trashDeleteRequest.isAllSelected : false;
    let objectIdList = Array.isArray(trashDeleteRequest.objectIdList) ? trashDeleteRequest.objectIdList : [];

    let filter: ExplorerFilterV2 = {};
    if (trashDeleteRequest.filterData) filter = trashDeleteRequest.filterData;

    //get object list
    let objectList: {_id: string}[] = await this.getSelectedObjectListInTrash(
      objectIdList,
      isAllSelected,
      searchKey,
      filter,
      trashDeleteRequest.contentType ? trashDeleteRequest.contentType : ContentType.ALL,
      currentUserProfile,
    );

    const sessionId = `permanent-delete-${uuidV4()}`;

    let jobName = `${objectList.length} Items`;
    if (objectList.length == 1) {
      let metaObject = await this.metaDataRepository.findById(objectList[0]._id);
      jobName = metaObject.name;
      // jobName = `${objectList.length} Item`;
    }

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      currentUserProfile.id,
      teamId,
      currentUserProfile.name,
      JobType.PermanentDelete,
      0,
      JobStatus.queued,
      {},
    );

    //validate user type
    if (currentUserProfile.userType != UserType.USER_TYPE_TEAM_ADMIN &&
      currentUserProfile.userType !== UserType.USER_TYPE_SUPER_ADMIN
    ) {
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        currentUserProfile.id,
        teamId,
        currentUserProfile.name,
        JobType.PermanentDelete,
        0,
        JobStatus.failed,
        {
          totalCount: trashDeleteRequest.objectIdList.length,
          jobFailedReasonList: [DatalakeUserMessages.OPERATION_NOT_PERMITTED],
        },
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OPERATION_NOT_PERMITTED);
    }

    // validate otp before delete
    let operationValidity = await this.validatePermanentDeleteOTP(
      trashDeleteRequest.otp.toString(),
      currentUserProfile,
    );

    if (!operationValidity.isValid) {
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        currentUserProfile.id,
        teamId,
        currentUserProfile.name,
        JobType.PermanentDelete,
        0,
        JobStatus.failed,
        {
          totalCount: trashDeleteRequest.objectIdList.length,
          jobFailedReasonList: ['OTP validation failed'],
        },
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OTP);
    }

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      currentUserProfile.id,
      teamId,
      currentUserProfile.name,
      JobType.PermanentDelete,
      0,
      JobStatus.inProgress,
      {
        totalCount: objectList.length,
      },
    );

    //variable declaration for job
    let currentCount = 1;
    let successCount = 0;
    let successFileCount = 0;
    let failedFileCount = 0;
    let failedCount = 0;
    let jobPartFailedReasonList: string[] = [];
    let successFileNameList: string[] = [];
    let failedFileNameList: string[] = [];

    //iterate object list and permanently delete object
    for (const obj of objectList) {
      let metaObject = await this.metaDataRepository.findById(obj._id);

      let totalCount = objectList.length;
      let progress = totalCount ? (currentCount / totalCount) * 100 : 100;

      try {
        if (metaObject.objectStatus !== OBJECT_STATUS.TRASHED) {
          throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OBJECT_NOT_TRASHED);
        }

        if (
          [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
            metaObject.objectType,
          )
        ) {
          let res = await this.permanentCollectionDelete(metaObject);

          if (res.isSuccess) {
            successCount += 1;
            successFileNameList.push(metaObject.name);
            successFileCount += res.successCount;
            await this.jobService.createOrUpdateJob(
              jobName,
              sessionId,
              currentUserProfile.id,
              teamId,
              currentUserProfile.name,
              JobType.PermanentDelete,
              progress,
              JobStatus.inProgress,
              {
                totalCount: objectList.length,
                successCount: successCount,
                jobPartFailedReasonList: jobPartFailedReasonList,
                successFileNameList: successFileNameList,
                successFileCount: successFileCount,
              },
            );
          } else {
            failedCount += 1;
            failedFileNameList.push(metaObject.name);
            failedFileCount += res.failedCount;
            for (let message of res.message) {
              if (!jobPartFailedReasonList.includes(message)) {
                jobPartFailedReasonList.push(message);
              }
            }

            await this.jobService.createOrUpdateJob(
              jobName,
              sessionId,
              currentUserProfile.id,
              teamId,
              currentUserProfile.name,
              JobType.PermanentDelete,
              progress,
              JobStatus.inProgress,
              {
                totalCount: objectList.length,
                successCount: successCount,
                successFileNameList: successFileNameList,
                successFileCount: successFileCount,
                failCount: failedCount,
                jobPartFailedReasonList: jobPartFailedReasonList,
                failedFileNameList: failedFileNameList,
                failedFileCount: failedFileCount,
              },
            );
          }
        } else if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType)) {
          //in case of objects are permanently deleted from trash before cronjob stats propagation, we need to update stats before delete
          if (metaObject.statPending) {
            // update stats of actual parent collection
            if (Array.isArray(metaObject.parentList) && metaObject.parentList.length > 0) {
              for (let parentId of metaObject.parentList) {
                await this.systemStatsService.updateParentStats(parentId.toString());
              }
            }
            //update stats of virtual parent collection
            if (Array.isArray(metaObject.vCollectionIdList) && metaObject.vCollectionIdList.length > 0) {
              for (let vCollectionId of metaObject.vCollectionIdList) {
                await this.systemStatsService.updateParentStats(vCollectionId.toString());
              }
            }
          }

          let res = await this.permanentFileDelete(metaObject);

          if (res.isSuccess) {
            successCount += 1;
            successFileNameList.push(metaObject.name);
            successFileCount +=
              metaObject.objectType === ContentType.IMAGE_COLLECTION
                ? metaObject.imageCount
                : metaObject.objectType === ContentType.VIDEO_COLLECTION
                ? metaObject.videoCount
                : metaObject.objectType === ContentType.OTHER_COLLECTION
                ? metaObject.otherCount
                : 1;
            await this.jobService.createOrUpdateJob(
              jobName,
              sessionId,
              currentUserProfile.id,
              teamId,
              currentUserProfile.name,
              JobType.PermanentDelete,
              progress,
              JobStatus.inProgress,
              {
                totalCount: objectList.length,
                successCount: successCount,
                jobPartFailedReasonList: jobPartFailedReasonList,
                successFileNameList: successFileNameList,
                successFileCount: successFileCount,
              },
            );
          } else {
            failedCount += 1;
            failedFileNameList.push(metaObject.name);
            failedFileCount +=
              metaObject.objectType === ContentType.IMAGE_COLLECTION
                ? metaObject.imageCount
                : metaObject.objectType === ContentType.VIDEO_COLLECTION
                ? metaObject.videoCount
                : metaObject.objectType === ContentType.OTHER_COLLECTION
                ? metaObject.otherCount
                : 1;

            if (!jobPartFailedReasonList.includes(res.message)) {
              jobPartFailedReasonList.push(res.message);
            }

            await this.jobService.createOrUpdateJob(
              jobName,
              sessionId,
              currentUserProfile.id,
              teamId,
              currentUserProfile.name,
              JobType.PermanentDelete,
              progress,
              JobStatus.inProgress,
              {
                totalCount: objectList.length,
                successCount: successCount,
                successFileNameList: successFileNameList,
                successFileCount: successFileCount,
                failCount: failedCount,
                jobPartFailedReasonList: jobPartFailedReasonList,
                failedFileNameList: failedFileNameList,
                failedFileCount: failedFileCount,
              },
            );
          }
        } else {
          logger.error(
            `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.deleteTrashObject | ${teamId} | Invalid object type. Object id: ${metaObject.id}`,
          );

          failedCount += 1;
          failedFileNameList.push(metaObject.name);
          failedFileCount +=
            metaObject.objectType === ContentType.IMAGE_COLLECTION
              ? metaObject.imageCount
              : metaObject.objectType === ContentType.VIDEO_COLLECTION
              ? metaObject.videoCount
              : metaObject.objectType === ContentType.OTHER_COLLECTION
              ? metaObject.otherCount
              : 1;
          jobPartFailedReasonList.push(`Invalid object type. Object id: ${metaObject.id}`);
          await this.jobService.createOrUpdateJob(
            jobName,
            sessionId,
            currentUserProfile.id,
            teamId,
            currentUserProfile.name,
            JobType.PermanentDelete,
            progress,
            JobStatus.inProgress,
            {
              totalCount: objectList.length,
              successCount: successCount,
              successFileNameList: successFileNameList,
              successFileCount: successFileCount,
              failCount: failedCount,
              jobPartFailedReasonList: jobPartFailedReasonList,
              failedFileNameList: failedFileNameList,
              failedFileCount: failedFileCount,
            },
          );
        }
      } catch (error) {
        failedCount += 1;
        failedFileNameList.push(metaObject.name);
        jobPartFailedReasonList.push(`Error while deleting object. Object id: ${metaObject.id} | Error: ${error}`);

        failedFileCount +=
          metaObject.objectType === ContentType.IMAGE_COLLECTION
            ? metaObject.imageCount
            : metaObject.objectType === ContentType.VIDEO_COLLECTION
            ? metaObject.videoCount
            : metaObject.objectType === ContentType.OTHER_COLLECTION
            ? metaObject.otherCount
            : 1;

        await this.jobService.createOrUpdateJob(
          jobName,
          sessionId,
          currentUserProfile.id,
          teamId,
          currentUserProfile.name,
          JobType.PermanentDelete,
          progress,
          JobStatus.inProgress,
          {
            totalCount: objectList.length,
            successCount: successCount,
            failCount: failedCount,
            jobPartFailedReasonList: jobPartFailedReasonList,
            failedFileNameList: failedFileNameList,
            failedFileCount: failedFileCount,
          },
        );
        logger.error(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.deleteTrashObject | ${teamId} | Error while deleting object. Object id: ${metaObject.id} | Error: ${error}`,
        );
      }
      currentCount += 1;
    }

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      currentUserProfile.id,
      teamId,
      currentUserProfile.name,
      JobType.PermanentDelete,
      100,
      JobStatus.completed,
      {
        totalCount: objectList.length,
        successCount: successCount,
        failCount: failedCount,
        jobPartFailedReasonList: jobPartFailedReasonList,
        failedFileNameList: failedFileNameList,
        successFileNameList: successFileNameList,
        successFileCount: successFileCount,
        failedFileCount: failedFileCount,
      },
    );

    return {
      isSuccess: true,
    };
  }

  /**
   * Use to permanently delete a meta object with its related all data
   * permanently delete from both database and cloud storage
   * @param metaObject MetaData
   */
  async permanentFileDelete(metaObject: MetaData) {
    if (metaObject.storagePath) {
      logger.info(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentFileDelete | ${metaObject.teamId} | Deleting object from cloud storage. Object id: ${metaObject.id}, storagePath: ${metaObject.storagePath}, bucket: ${metaObject.bucketName}, objectKey: ${metaObject.objectKey}`,
      );

      let res = await this.storageCrawlerService.storageServiceProvider.deleteObject(
        metaObject.storagePath,
        metaObject.bucketName,
      );

      if (res.isSuccess) {
        logger.info(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentFileDelete | ${metaObject.teamId} | Object deleted from cloud storage. Object id: ${metaObject.id},  storagePath: ${metaObject.storagePath}, bucket: ${metaObject.bucketName}, objectKey: ${metaObject.objectKey}`,
        );

        //delete thumbnail
        if (metaObject.thumbnailKey) {
          await this.storageCrawlerService.storageServiceProvider.deleteObject(metaObject.thumbnailKey);
        } else {
          logger.error(
            `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentFileDelete | ${metaObject.teamId} | Thumbnail key not found. Object id: ${metaObject.id}, storagePath: ${metaObject.storagePath}, bucket: ${metaObject.bucketName}, objectKey: ${metaObject.objectKey}`,
          );
        }

        //delete metaupdates
        await this.metaDataUpdateRepository.deleteAll({
          teamId: metaObject.teamId,
          objectKey: metaObject.objectKey,
        });

        //finally delete meta object
        await this.metaDataRepository.deleteById(metaObject.id);
      } else {
        logger.error(
          `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentFileDelete | ${metaObject.teamId} | Failed to delete object from cloud storage. Object id: ${metaObject.id}, storagePath: ${metaObject.storagePath}, bucket: ${metaObject.bucketName}, objectKey: ${metaObject.objectKey}`,
        );
      }

      return res;
    } else {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentFileDelete | ${metaObject.teamId} | storage path not found. Object id: ${metaObject.id}`,
      );
      return {
        isSuccess: false,
        message: `storage path not found. Object id: ${metaObject.id}`,
      };
    }
  }

  /**
   * Use to permanently delete a collection with its children
   * permanently delete from both database and cloud storage
   * @param collectionObject MetaData
   */
  async permanentCollectionDelete(collectionObject: MetaData) {
    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentCollectionDelete | ${collectionObject.teamId} | Deleting collection from cloud storage. Collection id: ${collectionObject.id}, collection name: ${collectionObject.name}, collection type: ${collectionObject.objectType}`,
    );

    //delete child objects
    let childList: {_id: string}[] = await this.metaDataRepository.aggregate([
      {
        $match: {
          // $or: [
          //   {collectionId: new ObjectId(collectionObject.id)},
          //   {vCollectionIdList: new ObjectId(collectionObject.id)},
          // ],
          vCollectionIdList: new ObjectId(collectionObject.id),
          objectStatus: OBJECT_STATUS.TRASHED,
        },
      },
      {$sort: DEFAULT_ITEM_SORT_IN_TRASH},
      {$project: {_id: 1}},
    ]);

    let successCount = 0;
    let failedCount = 0;
    let failedReason: string[] = [];
    if (Array.isArray(childList) && childList.length > 0) {
      for (const child of childList) {
        let _metaObject = await this.metaDataRepository.findById(child._id);
        let res = await this.permanentFileDelete(_metaObject);

        if (res.isSuccess) {
          // count as success file
          successCount += 1;
        } else {
          // count as failed file
          failedCount += 1;
          if (!failedReason.includes(res.message)) failedReason.push(res.message);
        }
      }
    }

    //check any trash file exists in collection which is not deleted
    let fileExists = await this.metaDataRepository.findOne({
      where: {
        vCollectionIdList: collectionObject.id,
        objectStatus: OBJECT_STATUS.TRASHED,
      },
    });

    if (fileExists) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentCollectionDelete | ${collectionObject.teamId} | Collection contains files which are failed to delete permanently. Collection id: ${collectionObject.id}, collection name: ${collectionObject.name}, collection type: ${collectionObject.objectType}`,
      );
      return {
        isSuccess: false,
        message: failedReason,
        successCount: successCount,
        failedCount: failedCount,
      };
    } else {
      if (collectionObject.isFromVideo) {
        await this.handleDeleteCriteriaForVideoDerivedCollection(collectionObject);
      }
      // before delete collection, collectionId should be removed from all child objects and collectionId should be removed from all vCollectionIdList of child objects
      await this.unlinkCollectionIdFromChildObjects(collectionObject);
      //finally delete meta object
      await this.metaDataRepository.deleteById(collectionObject.id);

      logger.info(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.permanentCollectionDelete | ${collectionObject.teamId} | Collection deleted from cloud storage. Collection id: ${collectionObject.id}, collection name: ${collectionObject.name}, collection type: ${collectionObject.objectType}`,
      );

      return {
        isSuccess: true,
        message: failedReason,
        successCount: successCount,
        failedCount: failedCount,
      };
    }
  }

  /**
   * Use to handle when permanent delete a video derived collection
   * remove frameCollectionId from derived video object and parentList empty for all child objects
   * @param collectionObj collection object
   * @returns
   */
  async handleDeleteCriteriaForVideoDerivedCollection(collectionObj: MetaData) {
    if (!collectionObj.id) return;

    // parentList empty for all child objects
    await this.metaDataRepository.updateManySet(
      {
        collectionId: new ObjectId(collectionObj.id),
      },
      {
        parentList: [],
      },
      [],
    );

    // remove frameCollectionId from derived video object
    await this.metaDataRepository.updateManyUnSet(
      {
        frameCollectionId: new ObjectId(collectionObj.id),
      },
      {
        frameCollectionId: '',
      },
      [],
    );
  }

  /**
   * use to unlink collectionId from all child objects
   * @param collectionId collection id : string
   * @returns
   */
  async unlinkCollectionIdFromChildObjects(collectionObj: MetaData) {
    if (!collectionObj.id) return;

    // unlink collectionId from all child objects
    await this.metaDataRepository.updateManyRemoveFromList(
      {
        vCollectionIdList: new ObjectId(collectionObj.id),
      },
      {
        vCollectionIdList: new ObjectId(collectionObj.id),
      },
      [],
    );
    await this.metaDataRepository.updateManyRemoveFromList(
      {
        parentList: new ObjectId(collectionObj.id),
      },
      {
        parentList: new ObjectId(collectionObj.id),
      },
      [],
    );
    await this.metaDataRepository.updateManyUnSet(
      {
        collectionId: new ObjectId(collectionObj.id),
      },
      {
        collectionId: '',
      },
      [],
    );

    if (collectionObj.storagePath) {
      let storagePath = collectionObj.storagePath;
      let storagePrefixPath = collectionObj.storagePrefixPath ? collectionObj.storagePrefixPath : '';
      let prefixPath = storagePrefixPath ? `${storagePrefixPath}/${storagePath}` : storagePath;

      // find child object with storage path start with prefix path
      let isExists = await this.metaDataRepository.findOne({
        where: {
          storagePath: {
            $regex: new RegExp(`^${prefixPath}`),
          },
          objectType: {
            inq: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER],
          },
        },
      });

      // if there is no object start with prefixPath then delete storage path from storageMappingRepository
      if (!isExists) {
        await this.storageMappingRepository.deleteAll({
          collectionStoragePath: storagePath,
        });
      }
    }
  }

  /**
   *
   * @param otp
   * @param type
   * @param currentUserProfile
   * @returns
   */
  async validatePermanentDeleteOTP(otp: string, currentUserProfile: UserProfileDetailed) {
    let validity = await this.otpAuthService.validateOTP(otp, OTPTypes.PERMANENT_DELETE, currentUserProfile);
    if (validity.success) return {isValid: true};
    else return {isValid: false};
  }

  /**
   * Propagate custom meta data to collection head
   * @param collectionId collection id : string
   */
  async propagateCustomMetaDataToCollectionHead(collectionId: string) {
    let aggregateQuery = [
      {
        $match: {
          collectionId: new ObjectId(collectionId),
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
        },
      },
      {
        $project: {customMeta: {$objectToArray: '$customMeta'}},
      },
      {
        $unwind: '$customMeta',
      },
      {
        $group: {_id: null, keys: {$addToSet: '$customMeta.k'}},
      },
    ];

    let aggregateMatchObjectArray = await this.metaDataRepository.aggregate(aggregateQuery);

    if (Array.isArray(aggregateMatchObjectArray) && aggregateMatchObjectArray.length > 0) {
      let customMetaKeys = aggregateMatchObjectArray[0].keys;

      for (const key of customMetaKeys) {
        await this.metaDataRepository.updateManySet(
          {
            _id: new ObjectId(collectionId),
            [`customMeta.${key}`]: {$exists: false},
          },
          {
            [`customMeta.${key}`]: [],
          },
          [],
        );
      }
    }
  }

  /**
   * this function is used to get object list
   * @param objectIdList object id list
   * @param searchKey search key
   * @param isAllSelected is all selected : boolean
   * @param teamId logged user team id
   * @returns list of object
   */
  async getSelectedObjectListInTrash(
    objectIdList: string[],
    isAllSelected: boolean,
    searchKey: string,
    filter: ExplorerFilterV2,
    contentType: ContentType,
    currentUserProfile: UserProfileDetailed,
  ) {
    let objectList: any[] = [];
    let teamId = currentUserProfile.teamId;

    let matchingCriteria: {[k: string]: any} = {
      objectStatus: OBJECT_STATUS.TRASHED,
      teamId: new ObjectId(teamId),
    };

    if (isAllSelected) {
      if (searchKey) {
        const pattern = new RegExp(regExpEscape(searchKey), 'i');
        matchingCriteria = {
          ...matchingCriteria,
          name: pattern,
        };
      }

      // if content type is not all then get object type from content type
      if (contentType != ContentType.ALL) {
        // get object type from content type
        let objectType = await this.metaDataRepository.getObjectType(true, contentType);
        matchingCriteria = {
          ...matchingCriteria,
          objectType: objectType,
        };
      }

      //filter match query create
      let filterMatchQuery: any = await this.searchQueryBuilderService.getMatchQueryForExplorerFilter(
        filter,
        undefined,
        undefined,
        currentUserProfile,
      );

      // if filterMatchQuery exists then add filterMatchQuery in matchingCriteria
      if (filterMatchQuery) {
        matchingCriteria = {
          ...matchingCriteria,
          ...filterMatchQuery,
        };
      }

      // if got objectIdList when allSelected true, those are unselected objects
      // then get all object list without objectIdList
      if (isArrayWithLength(objectIdList)) {
        matchingCriteria = {
          ...matchingCriteria,
          _id: {$nin: objectIdList.map(id => new ObjectId(id))},
        };
      }
    } else {
      if (isArrayWithLength(objectIdList)) {
        matchingCriteria = {
          ...matchingCriteria,
          _id: {$in: objectIdList.map(id => new ObjectId(id))},
        };
      } else {
        return objectList;
      }
    }

    objectList = await this.metaDataRepository.aggregate([
      {
        $match: matchingCriteria,
      },
      {
        $sort: DEFAULT_ITEM_SORT_IN_TRASH,
      },
      {
        $project: {
          _id: 1,
        },
      },
    ]);

    // log message for object list successfully fetched
    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashService.getSelectedObjectListInTrash | N/A | Object list of length=${objectList.length} successfully fetched.`,
    );

    return objectList;
  }

  async getTrashItemCounts(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let param = [];

    // let datalakeSelection: Partial<DatalakeSelection> = {
    //   teamId: teamId,
    //   selectionRequest: filter,
    //   objectType: filter.contentType,
    // };

    // let aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(undefined, datalakeSelection);

    if (
      currentUserProfile &&
      currentUserProfile.userType &&
      currentUserProfile.userType === UserType.USER_TYPE_COLLABORATOR &&
      currentUserProfile.id
    ) {
      param.push({
        $match: {
          allowedUserIdList: new ObjectId(currentUserProfile.id),
        },
      });
    }

    param.push({
      $match: {
        objectStatus: OBJECT_STATUS.TRASHED,
        objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]},
      },
    });
    param.push({
      $group: {
        _id: '$objectType',
        count: {$sum: 1},
      },
    });

    let itemCountList: {_id: number; count: number}[] = await this.metaDataRepository.aggregate(param);

    let keyValueList: {key: string; value: number}[] = [];
    let totalCount: number = 0;
    for (let item of itemCountList) {
      let itemTypeString = '';
      totalCount += item.count;
      switch (item._id) {
        case ContentType.IMAGE:
          itemTypeString = 'Image Files';
          break;
        case ContentType.VIDEO:
          itemTypeString = 'Video Files';
          break;
        case ContentType.OTHER:
          itemTypeString = 'Other Files';
          break;
        default:
          itemTypeString = '';
          break;
      }
      keyValueList.push({key: itemTypeString, value: item.count});
    }

    return {
      items: keyValueList,
      itemCount: totalCount,
    };
  }

  /**
   * Use to send summary/count of trashed objects
   * @param objectIdList selected object id list
   * @param filter explorer filter
   * @param teamId logged user team id
   * @param query string (match with name)
   * @returns
   */
  async getDetailsToTrash(
    objectIdList: string[],
    filter: ExplorerFilterV2,
    contentType: ContentType,
    teamId: string,
    query?: string,
  ) {
    // initialize response
    let explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse = {
      totalSize: 0,
      objectStatus: OBJECT_STATUS.TRASHED,
      contentType: contentType,
      details: {
        items: {
          totalCount: 0,
          details: {},
        },
        frames: {
          totalCount: 'N/A',
          details: {
            raw: 0,
            machineAnnotated: 0,
            verified: 0,
          },
        },
        labels: {
          totalCount: 'N/A',
          details: {
            labelList: [],
          },
        },
      },
    };

    let matchingCriteria: {[key: string]: any} = {
      teamId: new ObjectId(teamId),
      objectStatus: OBJECT_STATUS.TRASHED,
    };

    if (query && query.length > 0) {
      const pattern = new RegExp(regExpEscape(query), 'i');
      matchingCriteria = {
        ...matchingCriteria,
        name: pattern,
      };
    }

    if (contentType != ContentType.ALL) {
      // get object type from content type
      let objectType = await this.metaDataRepository.getObjectType(true, contentType);
      matchingCriteria = {
        ...matchingCriteria,
        objectType: objectType,
      };
    }

    let countList = await this.metaDataRepository.aggregate([
      {
        $match: matchingCriteria,
      },
      {
        $group: {
          _id: '$objectType',
          count: {$sum: 1},
          size: {$sum: '$fileSize'},
        },
      },
    ]);

    if (Array.isArray(countList) && countList.length > 0) {
      let totalCount = 0;
      let totalSize = 0;
      for (let countObj of countList) {
        totalCount += countObj.count;
        switch (countObj._id) {
          case ContentType.VIDEO:
            explorerDefaultViewDetailsResponse.details.items.details.videos = countObj.count;
            totalSize += countObj.size;
            break;
          case ContentType.VIDEO_COLLECTION:
            explorerDefaultViewDetailsResponse.details.items.details.videoCollections = countObj.count;
            break;
          case ContentType.IMAGE:
            explorerDefaultViewDetailsResponse.details.items.details.images = countObj.count;
            totalSize += countObj.size;
            break;
          case ContentType.IMAGE_COLLECTION:
            explorerDefaultViewDetailsResponse.details.items.details.imageCollections = countObj.count;
            break;
          case ContentType.OTHER:
            explorerDefaultViewDetailsResponse.details.items.details.others = countObj.count;
            totalSize += countObj.size;
            break;
          case ContentType.OTHER_COLLECTION:
            explorerDefaultViewDetailsResponse.details.items.details.otherCollections = countObj.count;
            break;
          case ContentType.DATASET:
            explorerDefaultViewDetailsResponse.details.items.details.datasets = countObj.count;
            break;
          default:
            break;
        }
      }
      explorerDefaultViewDetailsResponse.details.items.totalCount = totalCount;
      explorerDefaultViewDetailsResponse.totalSize = totalSize;
    }

    return explorerDefaultViewDetailsResponse;
  }
  // async trashQueryOptionSuggestions(objectId: string, objectType: ContentType) {

  //   let customFieldList: {key: string; value: any}[] = [];
  //   let collectionId: string = '';

  //   let [metaDataObjectList] = await this.metaDataRepository.aggregate([
  //     {
  //       $match: {
  //         _id: new ObjectId(objectId),
  //       }
  //     },
  //     {
  //       $project: {
  //         Tags: 1,
  //         customMeta: 1,
  //         collectionId: 1
  //       }
  //     }
  //   ])

  //   // logger.info(metaDataObjectList);

  //   if ([ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION].includes(objectType)) {

  //     collectionId = metaDataObjectList._id;

  //     // console.log(metaDataObjectList.customMeta);

  //     if (metaDataObjectList.customMeta) {

  //       for (const [key, values] of Object.entries(metaDataObjectList.customMeta)) {

  //         if (Array.isArray(values)) {
  //           for (const value of values) {

  //             let tempObj = {
  //               key: `metadata.${key}`,
  //               value: value,
  //             };

  //             customFieldList.push(tempObj);
  //           }
  //         }

  //       }
  //     }

  //     if (metaDataObjectList.Tags) {
  //       for (const tag of metaDataObjectList.Tags) {

  //         let tempObj = {
  //           key: `metadata.Tags`,
  //           value: tag,
  //         };

  //         customFieldList.push(tempObj);
  //       }
  //     }

  //   } else if ([ContentType.IMAGE, ContentType.VIDEO].includes(objectType)) {

  //     collectionId = metaDataObjectList.collectionId;

  //     if (metaDataObjectList.customMeta) {

  //       for (const [key, value] of Object.entries(metaDataObjectList.customMeta)) {

  //         let {isAvailable} = await this.checkAvailabilityOfQueryOptionForTrashedObject(collectionId, key, value, QueryOptionType.CUSTOM_META);

  //         console.log('isAvailable', isAvailable);

  //         if (!isAvailable) {
  //           let tempObj = {
  //             key: `metadata.${key}`,
  //             value: value,
  //           };

  //           customFieldList.push(tempObj);
  //         }

  //       }
  //     }

  //     if (metaDataObjectList.Tags) {
  //       for (const tag of metaDataObjectList.Tags) {

  //         let {isAvailable} = await this.checkAvailabilityOfQueryOptionForTrashedObject(collectionId, 'Tags', tag, QueryOptionType.TAGS);

  //         console.log('isAvailableTags', isAvailable);

  //         if (!isAvailable) {

  //           let tempObj = {
  //             key: `metadata.Tags`,
  //             value: tag,
  //           };

  //           customFieldList.push(tempObj);

  //         }

  //       }
  //     }

  //   }

  //   console.log('collectionId', collectionId);
  //   console.log('customField', customFieldList);

  //   for (const customField of customFieldList) {

  //     await this.queryOptionRepository.updateManySet(
  //       {
  //         collectionId: new ObjectId(collectionId),
  //         keyGroup: customField.key,
  //         key: customField.value
  //       },
  //       {
  //         objectStatus: OBJECT_STATUS.TRASHED
  //       },
  //       []
  //     )

  //   }

  // }

  // async checkAvailabilityOfQueryOptionForTrashedObject(collectionId: string, key: string, value: any, queryType: QueryOptionType) {

  //   let aggregateQuery = [
  //     {
  //       $match: {
  //         collectionId: new ObjectId(collectionId),
  //         [queryType == QueryOptionType.CUSTOM_META ? `customMeta.${key}` : `${key}`]: value,
  //         objectStatus: {$ne: OBJECT_STATUS.TRASHED}
  //       }
  //     }
  //   ]

  //   let [matchObject] = await this.metaDataRepository.aggregate(aggregateQuery)

  //   // console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxx', aggregateQuery);

  //   if (matchObject) {
  //     return {
  //       isAvailable: true
  //     }
  //   }

  //   return {
  //     isAvailable: false
  //   }

  // }
}

export const DATALAKE_TRASH_SERVICE = BindingKey.create<DatalakeTrashService>('service.datalakeTrash');
