/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Embeddings related functions
 */

/**
 * @class EmbeddingsService
 * Embeddings related functions embedding insert, search, manage
 * @description
 * <AUTHOR>
 */

import {BindingKey, BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {default as Axios} from 'axios';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, Explore_API_TYPE, OBJECT_STATUS, SimilarArray} from '../models/meta-data.model';
import {QueryOptionRepository} from '../repositories';
import {EmbeddingModelRepository} from '../repositories/embedding-model.repository';
import {EmbeddingVectorRepository} from '../repositories/embedding-vector.repository';
import {JobRepository} from '../repositories/job.repository';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {QueryGraphDataRepository} from '../repositories/query-graph-data.repository';
import {QueryGraphDetailsRepository} from '../repositories/query-graph-details.repository';
import {SimilarImageRepository} from '../repositories/similar-image.repository';
import {EMBEDDING_COLLECTION, FLOWS, SIMILARITY_SCORE_THRESHOLD} from '../settings/constants';
dotenv.config();

const PYTHON_HOST = process.env.PYTHON_BASE_URL;

const VECTOR_DB_HOST = process.env.VECTOR_DB_HOST || '';
const VECTOR_DB_PORT = process.env.VECTOR_DB_PORT || '';
const VECTOR_DB_USER = process.env.VECTOR_DB_USER || '';
const VECTOR_DB_PASS = process.env.VECTOR_DB_PASS || '';
const VECTOR_DB_CONNECTION = process.env.VECTOR_DB_CONNECTION || '';
const VECTOR_DB_SSL = false;
const VECTOR_DB_NAME = process.env.VECTOR_DB_NAME || '';
const VECTOR_DB_COLLECTION = EMBEDDING_COLLECTION;
const address = `${VECTOR_DB_HOST}:${VECTOR_DB_PORT}`;
const username = VECTOR_DB_USER;
const password = VECTOR_DB_PASS;
const ssl = VECTOR_DB_SSL;

@injectable({scope: BindingScope.SINGLETON})
export class EmbeddingsService {
  constructor(
    @repository(EmbeddingModelRepository)
    private embeddingModelRepository: EmbeddingModelRepository,
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
    @repository(SimilarImageRepository)
    public similarImageRepository: SimilarImageRepository,
    @repository(EmbeddingVectorRepository)
    public embeddingVectorRepository: EmbeddingVectorRepository,
    @repository(QueryGraphDataRepository)
    public queryGraphDataRepository: QueryGraphDataRepository,
    @repository(QueryGraphDetailsRepository)
    public queryGraphDetailsRepository: QueryGraphDetailsRepository,
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @repository(JobRepository)
    private jobRepository: JobRepository,
  ) {}

  /**
   * add query option when inserting embeddings
   * @param collectionIdList {ObjectId[]} list of collection ids
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @returns {Promise<void>} void
   */
  async addQueryOptionWhenInsertingEmbeddings(
    collectionIdList: ObjectId[],
    currentUserProfile: UserProfileDetailed,
  ): Promise<void> {
    for (let collectionId of collectionIdList) {
      await this.queryOptionRepository.addEmbeddingQueryOption(
        {
          vCollectionIdList: new ObjectId(collectionId),
        },
        collectionId.toString(),
        currentUserProfile.teamId,
      );
    }
  }

  async getEmbeddingVectors(embeddingUniqueNameArray: string[], embeddingModelName: string) {
    logger.debug(
      `get embedding vector | EmbeddingsService.getEmbeddingVectors | N/A | aggregate start:${embeddingModelName}`,
    );
    let response = await this.embeddingVectorRepository.aggregate([
      {$match: {objectKey: {$in: embeddingUniqueNameArray}, modelName: embeddingModelName}},
      {$project: {uniqueName: '$objectKey', _id: 0, embeddings: 1, modelName: 1, embeddingInfo: 1}},
    ]);
    logger.debug(
      `get embedding vector | EmbeddingsService.getEmbeddingVectors | N/A | aggregate end:${embeddingModelName}`,
    );

    if (response && Array.isArray(response) && response.length > 0) {
      for (let embeddingObject of response) {
        if (embeddingObject['embeddingInfo'] && embeddingObject['embeddingInfo']['npyFileUniqueName']) {
          let fileUniqueName = embeddingObject['embeddingInfo']['npyFileUniqueName'];
          let metaObject = await this.metaDataRepository.aggregate([
            {$match: {objectKey: fileUniqueName}},
            {$project: {url: 1}},
          ]);
          console.log(metaObject);
          if (metaObject && Array.isArray(metaObject) && metaObject.length > 0) {
            embeddingObject['npyEmbeddingsUrl'] = metaObject[0]['url'];
          }
        }
      }
    }

    return response;
  }

  /**
   * delete similar data which are expired
   */
  async deleteSimilarImage(executeNow?: boolean) {
    logger.info(
      `EmbeddingsService | EmbeddingsService.deleteSimilarImage | N/A | delete similar data which are expired`,
    );
    let time = new Date();

    let executeTime = new Date(time.getTime() - 60 * 60 * 1000);

    if (executeNow) {
      executeTime = time;
    }

    this.similarImageRepository.directRemove({createdAt: {$lte: executeTime}});

    this.metaDataRepository.updateManyRemoveFromList(
      {'similarArray.createdAt': {$lte: executeTime}},
      {
        similarArray: {
          createdAt: {$lte: executeTime},
        },
      },
      [],
    );
  }

  /**
   * delete similar data which are expired
   */
  async deleteGraphData(executeNow?: boolean) {
    logger.info(
      `EmbeddingsService | EmbeddingsService.deleteSimilarImage | N/A | delete similar data which are expired`,
    );
    let time = new Date();

    let executeTime = new Date(time.getTime() - 60 * 60 * 1000);

    if (executeNow) {
      executeTime = time;
    }

    this.queryGraphDataRepository.directRemove({createdAt: {$lte: executeTime}, isCronJobDeletable: true});
    this.queryGraphDetailsRepository.directRemove({createdAt: {$lte: executeTime}, isCronJobDeletable: true});

    this.metaDataRepository.updateManyRemoveFromList(
      {'queryGraphData.createdAt': {$lte: executeTime}, 'queryGraphData.isCronJobDeletable': true},
      {
        queryGraphData: {
          createdAt: {$lte: executeTime},
          isCronJobDeletable: true,
        },
      },
      [],
    );
  }

  /**
   * Use for find the text chunk for given search keys
   * @param searchKeys {string[]} search keys
   * @param uniqueNames {string[]} unique names of the files for search
   * @param currentUserProfile {UserProfileDetailed}
   * @returns text chunk list
   */
  async findChunk(
    searchKeys: string[],
    uniqueNames: string[],
    pageIndex: number,
    chunkLimit: number,
    adjacentChunkLimit: number,
    currentUserProfile?: UserProfileDetailed,
  ) {
    try {
      let url = `${PYTHON_HOST}/internal/find/chunk`;

      let response = await Axios({
        url,
        method: 'POST',
        data: {
          searchKeys: searchKeys,
          fileNames: uniqueNames,
          pageIndex: pageIndex,
          chunkLimit: chunkLimit,
          adjacentChunkLimit: adjacentChunkLimit,
        },
      });

      return response.data;
    } catch (err) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | EmbeddingsService.findChunk | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }

  /**
   * Use for find the text chunk for given search keys
   * @param searchKeys {string[]} search keys
   * @param uniqueNames {string[]} unique names of the files for search
   * @param currentUserProfile {UserProfileDetailed}
   * @returns text chunk list
   */
  async findDocumentChunk(searchKeys: string[], uniqueNames: string[], currentUserProfile?: UserProfileDetailed) {
    try {
      let url = `${PYTHON_HOST}/internal/find/document/chunk`;

      let response = await Axios({
        url,
        method: 'POST',
        data: {
          searchKeys: searchKeys,
          fileNames: uniqueNames,
        },
      });

      return response.data;
    } catch (err) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | EmbeddingsService.findDocumentChunk | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }
}
export const EMBEDDINGS_SERVICE = BindingKey.create<EmbeddingsService>('service.embeddings');
