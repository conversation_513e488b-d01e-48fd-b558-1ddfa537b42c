import {BindingScope, inject, injectable} from "@loopback/core";
import {AnalyticObjectiveRepository} from "../repositories/analytic-objective.repository";
import {ObjectId} from "mongodb";
import {logger} from "../config";
import axios from "axios";
import {ObjectiveUpdateStatus, ObjectiveUpdateType} from "../models/analytic-objective.model";

const PYTHON_HOST = process.env.PYTHON_BASE_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class AnalyticObjectiveService {

    constructor(
        @inject('repositories.AnalyticObjectiveRepository')
        private objectiveRepo: AnalyticObjectiveRepository,
    ) { }

    /**
     * Fetch a list of analytic objectives, optionally filtered by a search string.
     * If a search string is provided, it is sanitized and used to filter objectives using a case-insensitive regex.
     * If no search string or an empty search string is provided, all objectives are returned.
     * 
     * @param search Optional search string to filter objectives by their name (case-insensitive).
     *               If empty or only contains quotes, it is ignored.
     * 
     * @returns A promise resolving to an array of objectives matching the criteria or all objectives if no filter is applied.
     */
    async getObjectives(search?: string): Promise<any[]> {
        try {
            const cleanedSearch = search?.trim().replace(/^"|"$/g, '');
            const filter = cleanedSearch
                ? {objective: {$regex: cleanedSearch, $options: 'i'}, is_active: true}
                : {is_active: true};

            const objectives = await this.objectiveRepo.find({where: filter as any});
            return objectives;
        } catch (error) {
            logger.error(
                `Objective Retrieval | AnalyticObjectiveService.getObjectives | N/A | Error fetching objectives: ${error.message}`,
            );
            return [];
        }
    }


    /**
     * Create a new analytic objective
     * @param data The objective details
     * @returns {isSuccess: boolean, message: string}
     */
    async createObjective(data: {objectiveName: string}, userName: string): Promise<{isSuccess: boolean; message: string}> {
        try {

            const objectiveName = data.objectiveName.trim();
            if (!objectiveName) {
                return {isSuccess: false, message: 'Objective name cannot be empty or whitespace'};
            }

            const newObjective = {
                objective: objectiveName,
                created_date: new Date(),
                modified_date: new Date(),
                created_by: userName,
                modified_by: userName,
                is_edit: false,
                updateStatus: ObjectiveUpdateStatus.QUEUED,
                updateType: ObjectiveUpdateType.CREATE
            };

            const createdObjective = await this.objectiveRepo.create(newObjective);

            logger.debug(
                `Objective Creation | AnalyticObjectiveService.createObjective | N/A | Objective created successfully: ${JSON.stringify(newObjective)}`,
            );

            this.callPythonService({
                objective_id: createdObjective._id,
            }).catch(error => {
                logger.error(
                    `Objective Creation | AnalyticObjectiveService.createObjective | N/A | Python service call failed: ${error.message}`,
                );
            });
            return {isSuccess: true, message: 'Objective created successfully'};
        } catch (error) {
            logger.error(
                `Objective Creation | AnalyticObjectiveService.createObjective | N/A | Error creating objective: ${error.message}`,
            );
            return {isSuccess: false, message: 'Failed to create analytic objective'};
        }
    }

    /**
     * Delete an analytic objective by ID
     * @param id The ID of the objective to delete
     * @returns {isSuccess: boolean, message: string}
     */
    async deleteObjective(id: string): Promise<{isSuccess: boolean; message: string}> {
        try {

            if (!ObjectId.isValid(id)) {
                return {isSuccess: false, message: 'Invalid Object ID'};
            }


            const exists = await this.objectiveRepo.exists(id);
            if (!exists) {
                return {isSuccess: false, message: 'Objective not found or already deleted'};
            }

            //if objective is processing, don't allow deletion
            const objective = await this.objectiveRepo.findById(id);
            if (objective?.updateStatus === ObjectiveUpdateStatus.IN_PROGRESS) {
                return {isSuccess: false, message: 'Objective is currently processing and cannot be deleted'};
            }

            await this.objectiveRepo.updateById(id, {is_active: false, modified_date: new Date(), updateStatus: ObjectiveUpdateStatus.QUEUED, updateType: ObjectiveUpdateType.DELETE});

            logger.debug(
                `Objective Deletion | AnalyticObjectiveService.deleteObjective | ${id} | Objective deleted successfully`,
            );

            this.callPythonService({
                objective_id: id,
            }).catch(error => {
                logger.error(
                    `Objective Deletion | AnalyticObjectiveService.deleteObjective | ${id} | Python service call failed: ${error.message}`,
                );
            });

            return {isSuccess: true, message: 'Objective deleted successfully'};
        } catch (error) {
            logger.error(
                `Objective Deletion | AnalyticObjectiveService.deleteObjective | ${id} | Error deleting objective: ${error.message}`,
            );
            return {isSuccess: false, message: 'Failed to delete objective'};
        }
    }


    /**
     * Update an analytic objective by ID
     * @param id The ID of the objective to update
     * @param objectiveName The updated objective name
     * @returns {isSuccess: boolean, message: string}
     */
    async updateObjective(id: string, objectiveName: string, userName: string,): Promise<{isSuccess: boolean; message: string}> {
        try {

            if (!ObjectId.isValid(id)) {
                return {isSuccess: false, message: 'Invalid Object ID'};
            }
            const exists = await this.objectiveRepo.exists(id);
            if (!exists) {
                return {isSuccess: false, message: 'Objective not found'};
            }

            //if objective is in processing, then throw error
            const objective = await this.objectiveRepo.findById(id);
            if (objective.updateStatus === ObjectiveUpdateStatus.IN_PROGRESS) {
                return {isSuccess: false, message: 'Objective is in processing and cannot be updated'};
            }

            //sanitize objective name
            objectiveName = objectiveName.trim();
            if (!objectiveName) {
                return {isSuccess: false, message: 'Objective name cannot be empty or whitespace'};
            }

            const updatedObjective = {
                objective: objectiveName,
                modified_date: new Date(),
                modified_by: userName,
                is_edit: true,
                updateStatus: ObjectiveUpdateStatus.QUEUED,
                updateType: ObjectiveUpdateType.UPDATE
            };

            await this.objectiveRepo.updateById(id, updatedObjective);

            logger.debug(
                `Objective Update | AnalyticObjectiveService.updateObjective | ${id} | Objective updated successfully: ${JSON.stringify(updatedObjective)}`,
            );

            this.callPythonService({
                objective_id: id
            }).catch(error => {
                logger.error(
                    `Objective Update | AnalyticObjectiveService.updateObjective | ${id} | Python service call failed: ${error.message}`,
                );
            });

            return {isSuccess: true, message: 'Objective updated successfully'};
        } catch (error) {
            logger.error(
                `Objective Update | AnalyticObjectiveService.updateObjective | ${id} | Error updating objective: ${error.message}`,
            );
            return {isSuccess: false, message: 'Failed to update objective'};
        }
    }


    /**
     * Get an analytic objective by ID
     * @param id The ID of the objective to fetch
     * @returns {isSuccess: boolean, message: string, data?: any}
     */
    async getObjectiveById(id: string): Promise<{isSuccess: boolean; message: string; data?: any}> {
        try {
            if (!ObjectId.isValid(id)) {
                return {isSuccess: false, message: 'Invalid Object ID'};
            }
            const objective = await this.objectiveRepo.findById(id);

            if (!objective) {
                return {isSuccess: false, message: 'Objective not found'};
            }
            logger.debug(
                `Objective Retrieval | AnalyticObjectiveService.getObjectiveById | ${id} | Objective fetched successfully: ${JSON.stringify(objective)}`,
            );

            return {isSuccess: true, message: 'Objective fetched successfully', data: objective};
        } catch (error) {
            logger.error(
                `Objective Retrieval | AnalyticObjectiveService.getObjectiveById | ${id} | Error fetching objective: ${error.message}`,
            );
            return {isSuccess: false, message: 'Failed to fetch objective'};
        }
    }

    /**
     * Reactivate a soft-deleted analytic objective by setting `is_active` to `true`.
     * This method checks if the given ID is valid, ensures the objective exists,
     * and updates the `is_active` field to re-enable the objective.
     *
     * @param id - The ID of the objective to reactivate.
     * @returns A promise resolving to an object indicating success or failure,
     *          along with an appropriate message.
     */
    async reactivateObjective(id: string): Promise<{isSuccess: boolean; message: string}> {
        try {
            if (!ObjectId.isValid(id)) {
                return {isSuccess: false, message: 'Invalid Object ID'};
            }

            const exists = await this.objectiveRepo.exists(id);
            if (!exists) {
                return {isSuccess: false, message: 'Objective not found'};
            }

            await this.objectiveRepo.updateById(id, {is_active: true});

            logger.debug(
                `Objective Reactivation | AnalyticObjectiveService.reactivateObjective | ${id} | Objective reactivated successfully`,
            );

            return {isSuccess: true, message: 'Objective reactivated successfully'};
        } catch (error) {
            logger.error(
                `Objective Reactivation | AnalyticObjectiveService.reactivateObjective | ${id} | Error reactivating objective: ${error.message}`,
            );
            return {isSuccess: false, message: 'Failed to reactivate objective'};
        }
    }


    /**
     * Call the Python service for objective operations (create, update, delete).
     * @param data The data to send to the Python service.
     * @param action The type of action: 'create', 'update', or 'delete'.
     */
    private async callPythonService(data: {objective_id: string | undefined}) {
        const url = `${PYTHON_HOST}/internal/connection/update/update_layernext_data_dictionary`;

        try {
            await axios({
                url,
                method: 'POST',
                data,
            });

            logger.debug(
                `Python Service Call | AnalyticObjectiveService.callPythonService |  Successfully called Python service with data: ${JSON.stringify(data)}`,
            );
        } catch (error) {
            logger.error(
                `Python Service Call | AnalyticObjectiveService.callPythonService | Failed to call Python service: ${error.message}`,
            );
            throw error; // Optionally rethrow the error if you want to handle it elsewhere
        }
    }

}