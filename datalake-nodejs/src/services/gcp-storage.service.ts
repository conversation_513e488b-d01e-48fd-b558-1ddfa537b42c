/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */

/**
 * @class GCPStorageService
 *
 * @description Class which extends Storage Provider Service that handles specific functions for Google cloud storage
 */
import {Storage} from '@google-cloud/storage';
import {BindingScope, injectable} from '@loopback/core';
import Axios from 'axios';
import dotenv from 'dotenv';
import {URLSearchParams} from 'url';
import {logger} from '../config';
import {AWS_UPLOAD_FILE_URL_VALID_DURATION} from './aws-s3-storage.service';
import {StorageOperationHandlerService} from './storage-operation-handler.service';
import {
  FileListParams,
  FileListResponse,
  StorageProviderService,
} from './storage-provider.service';
import {repository} from '@loopback/repository';
import {MetaDataRepository} from '../repositories';
import {UserProfileDetailed} from '../authServices/custom-token.service';
dotenv.config();

const GCP_FILE_URL_EXPIRE_DURATION = 3 * 24 * 60 * 60;
const GCP_CHUNK_FILE_URL_RENEW_BEFORE_DURATION = 1 * 24 * 60 * 60;

@injectable({scope: BindingScope.TRANSIENT})
export class GcpStorageService extends StorageProviderService {
  googleStorage: any;

  //constructor
  constructor(storageOperationHandlerService: StorageOperationHandlerService,
    @repository('MetaDataRepository') public metaDataRepository: MetaDataRepository,
  ) {
    super(metaDataRepository);
    //Get key file path from environment variable
    let keyFilePath = '/usr/src/app/datalake-nodejs/config/gcs-credentials.json';
    let googleProjectId = process.env.GCP_PROJECT_ID;
    this.googleStorage = new Storage({
      projectId: googleProjectId,
      keyFilename: keyFilePath,
    });
    this.storageOperationHandlerService = storageOperationHandlerService;
  }

  /**
   * Generate a signed URL for accessing an object in Google Cloud Storage.
   * @param key Key of the storage object.
   * @param expires Expiration time in seconds.
   * @param bucket Name of the Google Cloud Storage bucket.
   * @returns Signed URL with expiration settings.
   */
  async generateObjectUrl(
    key: string,
    expires = GCP_FILE_URL_EXPIRE_DURATION,
    bucket?: string,
  ): Promise<string> {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    const file = this.googleStorage.bucket(bucketName).file(key);

    const options = {
      action: 'read',
      version: 'v4',
      expires: Date.now() + expires * 1000, // Convert to milliseconds
    };
    const [url] = await file.getSignedUrl(options);
    return url;
  }

  async generateWriteFileUrl(key: string, expires = 60, bucket?: string) {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    const file = this.googleStorage.bucket(bucketName).file(key);
    const [url] = await file.getSignedUrl({
      action: 'write',
      expires: Date.now() + expires * 1000,
      version: 'v4',
    });
    return url;
  }

  /**
   * Initialize a multipart upload for a file in Google Cloud Storage.
   * @param key Key of the storage object to be created.
   * @param bucket Name of the Google Cloud Storage bucket.
   * @returns Object containing fileId, fileKey, and isExisting properties.
   */
  async initializeMultipartUpload(
    key: string,
    expires = AWS_UPLOAD_FILE_URL_VALID_DURATION,
    bucket?: string,
  ) {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;

    let splittedKey = key.split('/');
    let encodedKey = splittedKey
      .map(keyPart => encodeURIComponent(keyPart))
      .join('/');
    const file = this.googleStorage.bucket(bucketName).file(encodedKey);
    const [url] = await file.createResumableUpload();
    logger.info(`response: ${url} bucketName: ${bucketName}`);

    await Axios({
      url,
      method: 'POST',
    });

    const urlParams = new URLSearchParams(url);
    const uploadId = urlParams.get('upload_id');

    return {
      fileId: uploadId || '1',
      fileKey: key,
      isExisting: false,
    };
  }

  /*
    getUploadSignUrlGenerateParams(isDisableMultipart: boolean = false){
        if(isDisableMultipart){
            return {
                version: 'v4', // Use v4 signing algorithm
                action: 'write', // Allow write access to the generated URL
                expires: Date.now() + GCP_CHUNK_FILE_URL_RENEW_BEFORE_DURATION * 1000, // URL expiration time (15 minutes from now)
                contentType: 'multipart/form-data' // Specify the content type of the file being uploaded
            };
        }else{
            return {
                action: 'resumable',
                expires: Date.now() + GCP_CHUNK_FILE_URL_RENEW_BEFORE_DURATION * 1000, // Convert to milliseconds
                contentType: 'application/octet-stream',
                version: 'v4'
            };
        }
    }*/

  async generateMultipartPreSignedUrls(
    key: string,
    fileId: number,
    parts: number,
    bucket?: string,
    contentType?: string,
    isDisableMultipart?: boolean,
  ) {
    //Temporary handle in case of multipart disabled version of upload from frontend
    if (isDisableMultipart) {
      return this.generateV4UploadSignedUrl(
        key,
        fileId,
        parts,
        bucket,
        contentType,
      );
    }

    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    const file = this.googleStorage.bucket(bucketName).file(key);

    // get signed url for resumable upload
    const params = {
      action: 'resumable',
      expires: Date.now() + GCP_CHUNK_FILE_URL_RENEW_BEFORE_DURATION * 1000, // Convert to milliseconds
      contentType: 'application/octet-stream',
      version: 'v4',
    };
    const [signedUrl] = await file.getSignedUrl(params);

    //logger.debug(`signedUrl: ${signedUrl}`)

    // Start the resumable upload
    let url = signedUrl;
    let response = await Axios({
      url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
        'x-goog-resumable': 'start',
      }, //
    });

    // Get chunk sending url and attach it to chunk parts
    let location = response.headers['location'];
    //logger.debug(`location: ${location}`)
    const partSignedUrlList = [];
    for (let index = 0; index < parts; index++) {
      partSignedUrlList.push({
        signedUrl: location,
        PartNumber: index + 1,
      });
    }

    return {parts: partSignedUrlList};
  }

  /**
   * Temporary method: to support non-resumable upload with Google cloud storage (for Frontend temporarily)
   * @param key
   * @param fileId
   * @param parts
   * @param bucket
   * @param contentType
   * @returns
   */
  async generateV4UploadSignedUrl(
    key: string,
    fileId: number,
    parts: number,
    bucket?: string,
    contentType?: string,
  ) {
    // Reference the target bucket
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    const file = this.googleStorage.bucket(bucketName).file(key);

    // Set the configuration options for the signed URL
    const options = {
      version: 'v4', // Use v4 signing algorithm
      action: 'write', // Allow write access to the generated URL
      expires: Date.now() + GCP_CHUNK_FILE_URL_RENEW_BEFORE_DURATION * 1000, // URL expiration time (15 minutes from now)
      contentType: contentType, // Specify the content type of the file being uploaded
    };

    // Generate the signed URL
    const [url] = await file.getSignedUrl(options);

    return {
      parts: [
        {
          signedUrl: url,
          PartNumber: 1,
        },
      ],
    };
  }

  /**
   * Use for finalize multipart file upload to GCP from frontend and client sdk
   * @param key Key of the storage object to be created.
   * @param fileId
   * @param parts
   * @param bucket Name of the Google Cloud Storage bucket.
   * @param finalizeUrl url for finalize the file chunk upload
   * @returns
   */
  async finalizeMultipartUpload(
    key: string,
    fileId: string,
    parts: any,
    bucket?: string,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    currentUserProfile?: UserProfileDetailed
  ) {
    //If we use full upload instead of resumable in Google (in case of frontend temporarily), then we don't need to call the finalize url to Google cloud storage
    if (isDisableMultipart) {
      return {
        isSuccess: true,
      };
    }
    try {
      // Finalize the chunk upload of file
      let url = finalizeUrl;
      await Axios({
        url,
        method: 'PUT',
        headers: {'Content-Type': 'application/octet-stream'},
      });

      return {isSuccess: true};
    } catch (error) {
      logger.error(
        `File chunk upload generate urls finalize upload Failed | AwsS3StorageService.finalizeMultipartUpload | key: ${key} | upload failde: ${error}`,
      );
      return {isSuccess: false};
    }
  }

  /**
   * Implementation of StorageProvider's getFileList method for Google Storage
   * @param params {FileListParams}   - parameters for the getFileList method
   * @returns list of objects in the bucket
   */
  async getFileList(params: FileListParams): Promise<FileListResponse> {
    const bucketName = params.Bucket || process.env.DEFAULT_BUCKET_NAME;
    const bucket = this.googleStorage.bucket(bucketName);

    let query = {
      prefix: params.Prefix,
      maxResults: 1000,
      pageToken: params.ContinuationToken,
    };

    const [files, nextQuery] = await bucket.getFiles(query);
    let fileList = [];

    for (let file of files) {
      if (file.name.endsWith('/')) {
        // Skip directories
        continue;
      }
      let splitFileObjectKey = file.name.split('/');
      const [metadata] = await file.getMetadata();
      fileList.push({
        fileKey: file.name,
        fileName: splitFileObjectKey[splitFileObjectKey.length - 1],
        fileSize: Number(metadata.size),
        fileLastModified: new Date(metadata.updated),
      });
    }
    logger.info(
      `GcpStorageService | GcpStorageService.getFileList | N/A | ${files.length} file crawled`,
    );
    return {
      isSuccess: true,
      fileList: fileList,
      errMessage: '',
      nextPageRef: {
        NextContinuationToken:
          nextQuery && nextQuery.pageToken ? nextQuery.pageToken : '',
        ContinuationToken: params.ContinuationToken,
      },
      IsTruncated: nextQuery && nextQuery.pageToken ? true : false,
    };
  }

  async deleteObject(key: string, bucket?: string) {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    try {
      const file = this.googleStorage.bucket(bucketName).file(key);
      await file.delete();
      return {isSuccess: true, message: ''};
    } catch (e) {
      logger.error('Failed to delete file from Google storage', e);
      return {
        isSuccess: false,
        message: 'Failed to delete file from Google storage',
      };
    }
  }

  /**
   * Upload a file from a buffer to a Google Cloud Storage bucket.
   * @param buffer Buffer containing the file data.
   * @param key Key to assign to the uploaded file in the bucket.
   * @param bucketName Name of the Google Cloud Storage bucket.
   */
  async uploadFileFromBuffer(buffer: Buffer, key: string, bucket: string) {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;

    try {
      const file = this.googleStorage.bucket(bucketName).file(key);
      await file.save(buffer);
    } catch (e) {
      logger.error('Failed to upload file buffer to Google storage', e);
    }
  }

  /**
   * Check if the object key exists in a Google Cloud Storage bucket.
   * @param key Object key to check.
   * @param bucketName Name of the Google Cloud Storage bucket.
   * @returns Object indicating whether the key exists and if any error occurred.
   */
  async checkObjectKey(
    key: string,
    bucket?: string,
  ): Promise<{isExist: boolean; errorOccur: boolean}> {
    const bucketName = bucket || process.env.DEFAULT_BUCKET_NAME;
    const file = this.googleStorage.bucket(bucketName).file(key);

    try {
      await file.exists();

      return {
        isExist: true,
        errorOccur: false,
      };
    } catch (error) {
      if (error.code === 404) {
        console.error('Object key not found');
        return {
          isExist: false,
          errorOccur: false,
        };
      } else {
        console.error('Error occurred in check:', error);
        return {
          isExist: false,
          errorOccur: true,
        };
      }
    }
  }

  //     /**
  //    * Use to get summary of aws s3 bucket via shell script usimg s3cmd tool
  //    * @param dataCrawlId id of relavent DataCrawl record
  //    * @returns none, DataCrawl DB record will be updated
  //    */
  //     async getCloudStorageSummary(dataCrawlId: string, bucketName?: string) {
  //         logger.debug(`Crawl storage for populate data | GcpStorageService.getCloudStorageSummary | N/A | started`)

  //         bucketName = bucketName || process.env.DEFAULT_BUCKET_NAME;
  //         const bucket = this.googleStorage.bucket(bucketName);
  //         if (!bucketName) {
  //             logger.error(`Crawl GCP for populate data | GcpStorageService.getCloudStorageSummary | N/A | not found - GCP bucket name `)
  //             return
  //         }

  //         try {
  //             const [metadata] = await bucket.getMetadata();

  //             const totalFileCount = metadata.metadata && metadata.metadata['object-count'];
  //             const totalFileSize = metadata.size;

  //             let storageSummary: Partial<DataCrawl> = {
  //                 totalFileCount: totalFileCount,
  //                 totalFileSize: totalFileSize,
  //                 status: CrawlingStatus.FILES_CRAWLING,
  //                 storageSummaryReceivedAt: new Date()
  //             }

  //             // update crawling record statics
  //             let dataCrawlRecord = await this.storageOperationHandlerService.findDataCrawlRecord(dataCrawlId)
  //             if (dataCrawlRecord) {
  //                 if (dataCrawlRecord.status) {
  //                     if (dataCrawlRecord.status > CrawlingStatus.FILES_CRAWLING) {
  //                         storageSummary.status = dataCrawlRecord.status
  //                     }
  //                 }
  //             }
  //             this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, storageSummary)
  //             logger.info(`Crawl GCP for populate data | GcpStorageService.getCloudStorageSummary | N/A | bucket stats: totalFileCount: ${totalFileCount} totalFileSize: ${totalFileSize}`)

  //         } catch (error) {
  //             logger.error(`Crawl GCP for populate data | GcpStorageService.getCloudStorageSummary | N/A | Error retrieving bucket stats: ${error}`)
  //         }
  //     }
}
