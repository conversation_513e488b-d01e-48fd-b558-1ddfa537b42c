/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the services related to crawling data from cloud storages such as AWS S3
 */

/**
 * @class StorageCrawlerService
 * purpose of this service is crawling data from cloud storages such as AWS S3
 * @description crawling data from cloud storages such as AWS S3
 * <AUTHOR> channa
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {default as Axios} from 'axios';
import {ObjectId} from 'bson';
import dotenv from 'dotenv';
import moment from 'moment';
import {logger} from '../config';
import {
  CloudStorageActivityStatus,
  CloudStorageStatus,
  ContentType,
  CrawlHistory,
  CrawlingStatus,
  CrawlingStatusResponse,
  DataCrawl,
  LabelStats,
  MetaData,
  ObjectTypeWiseCountsVal,
  SystemData,
} from '../models';
import {ConnectionSourceType} from '../models/source.model';
import {DataCrawlRepository, MetaDataRepository, SystemDataRepository} from '../repositories';
import {AwsS3StorageService, DiskStorageService, StorageProviderService} from '../services';
import {
  FLOWS,
  OverallDetailsInOverviewCount,
  OverviewCountRes,
  STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS,
  TotalFramesInOverviewCount,
  TotalLabelsInOverviewCount,
} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {byteTransform, getFormattedItemCount} from '../settings/tools';
import {AzureBlobStorageService} from './azure-blob-storage.service';
import {GcpStorageService} from './gcp-storage.service';
import {DataInputType} from './media-processor.service';
import {STORAGE_OPERATION_HANDLER_SERVICE, StorageOperationHandlerService} from './storage-operation-handler.service';
dotenv.config();
const PYTHON_HOST = process.env.PYTHON_BASE_URL;
const storageType: ConnectionSourceType = process.env.STORAGE_TYPE as ConnectionSourceType;
const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;
const bucketNames = process.env.OTHER_BUCKETS;
//const storageName = storageType == "GCP" ? process.env.GCP_BUCKET_NAME :  (storageType == "AZURE" ?
//process.env.AZURE_BUCKET_NAME : process.env.AWS_BUCKET_NAME)

@injectable({scope: BindingScope.TRANSIENT})
export class StorageCrawlerService {
  public storageServiceProvider: StorageProviderService;

  constructor(
    @inject(STORAGE_OPERATION_HANDLER_SERVICE)
    protected storageOperationHandlerService: StorageOperationHandlerService,
    @repository('MetaDataRepository')
    private metaDataRepository: MetaDataRepository,
    @repository('DataCrawlRepository')
    private dataCrawlRepository: DataCrawlRepository,
    @repository('SystemDataRepository')
    private systemDataRepository: SystemDataRepository,
  ) {
    switch (storageType) {
      case ConnectionSourceType.AWS_S3:
        //logger.info(`Connecting to storage | StorageCrawlerService | type: AWS_S3 selected | N/A`)
        this.storageServiceProvider = new AwsS3StorageService(storageOperationHandlerService, metaDataRepository);
        break;
      case ConnectionSourceType.DISK:
        //logger.info(`Connecting to storage | StorageCrawlerService | type: DISK selected | N/A`)
        this.storageServiceProvider = new DiskStorageService(storageOperationHandlerService, metaDataRepository);
        break;
      case ConnectionSourceType.GCS:
        // logger.info(`Connecting to storage | StorageCrawlerService | type: GCP selected | N/A`);
        this.storageServiceProvider = new GcpStorageService(storageOperationHandlerService, metaDataRepository);
        break;

      case ConnectionSourceType.AZURE_BLOB:
        this.storageServiceProvider = new AzureBlobStorageService(storageOperationHandlerService, metaDataRepository);
        break;

      default:
        break;
    }
  }

  /**
   * Use to trigger initial crawling at server boot
   */
  async triggerInitialCralwing() {
    await new Promise(resolve => setTimeout(resolve, 60000));

    //check whether initial crawling record exists

    //get all buckets from env and iterate and crawl
    let _otherCloudStorages = bucketNames?.split(',') || [];
    _otherCloudStorages = _otherCloudStorages.map((bucketName: string) => bucketName.trim());
    //remove empty strings
    let otherCloudStorages = _otherCloudStorages.filter((bucketName: string) => bucketName);
    // default bucket append to otherCloudStorages array as first element
    if (defaultBucketName) {
      otherCloudStorages.unshift(defaultBucketName);
    }

    for (let bucketName of otherCloudStorages) {
      logger.info(
        `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | starting initial crawling for bucket: ${bucketName}`,
      );

      //NOTE: add teamId filtering
      let existingCrawl = await this.dataCrawlRepository.findOne({
        where: {
          isInitialCrawl: true,
          storageType: storageType,
          storageName: bucketName,
        },
      });

      // if initial crawl record exist, resume it if it has failed
      if (existingCrawl) {
        let systemData = await this.systemDataRepository.findOne();
        let teamId = systemData?.teamId;

        //check crawling record is valid
        if (
          !existingCrawl.imageCollectionId ||
          !existingCrawl.videoCollectionId ||
          !existingCrawl.otherCollectionId ||
          !existingCrawl.id
        ) {
          logger.error(
            `Crawl storage for populate data | StorageCrawlerService.triggerInitialCrawling | N/A | Existing initial crawling record found, But it is not valid. existingCrawl: ${existingCrawl}`,
          );
          continue;
        }

        //if initial crawling has completed
        if (existingCrawl.status == CrawlingStatus.COMPLETED) {
          logger.debug(
            `Crawl storage for populate data | StorageCrawlerService.triggerInitialCrawling | N/A | Existing initial crawling record found, it has already completed. CrawlingStatus: ${existingCrawl.status}`,
          );
          continue;
        }
        //if initial crawling has not completed & status has changed to failed status
        else if (existingCrawl.status == CrawlingStatus.FAILED) {
          logger.debug(
            `Crawl storage for populate data | StorageCrawlerService.triggerInitialCrawling | N/A | Existing initial crawling record found, it has not completed. CrawlingStatus: ${existingCrawl.status}`,
          );

          //if crawling from cloud has completed, then it should failed while thumbs generating. So trigger media processing service
          if (existingCrawl.crawlFinishedAt) {
            // update crawl status
            if (existingCrawl.thumbGenStartedAt) {
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.THUMBNAILS_GENERATING,
              });
            } else {
              let thumbnailGeneratableCount = await this.metaDataRepository.count({
                dataCrawlId: existingCrawl.id,
                objectType: {
                  inq: [ContentType.IMAGE, ContentType.VIDEO],
                },
              });
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.THUMBNAILS_GENERATING,
                thumbGenStartedAt: new Date(),
                thumbnailGeneratableCount: thumbnailGeneratableCount.count,
                thumbnailGeneratedCount: 0,
              });
            }
            // call python mediaProcessor
            this.startMediaProcessByCrawlId(
              existingCrawl.id,
              existingCrawl.imageCollectionId,
              existingCrawl.videoCollectionId,
              existingCrawl.otherCollectionId,
              bucketName,
            );
          }
          //if crawling from cloud has not completed, then resume cloud fetching
          else {
            // update crawl status
            await this.dataCrawlRepository.updateById(existingCrawl.id, {
              status: CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
            });
            // call respective storage service to fetch metadata from cloud
            let crawlResult = await this.storageServiceProvider.fetchFilesFromStorageAndPopulateMetaData(
              existingCrawl.imageCollectionId,
              existingCrawl.videoCollectionId,
              existingCrawl.otherCollectionId,
              existingCrawl.id,
              new ObjectId(teamId),
              false,
              bucketName,
            );

            if (crawlResult.success) {
              //update crawl record
              let thumbnailGeneratableCount = await this.metaDataRepository.count({
                dataCrawlId: existingCrawl.id,
                objectType: {
                  inq: [ContentType.IMAGE, ContentType.VIDEO],
                },
              });
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.THUMBNAILS_GENERATING,
                thumbGenStartedAt: new Date(),
                thumbnailGeneratableCount: thumbnailGeneratableCount.count,
                thumbnailGeneratedCount: 0,
              });
              // call python mediaProcessor
              this.startMediaProcessByCrawlId(
                existingCrawl.id,
                existingCrawl.imageCollectionId,
                existingCrawl.videoCollectionId,
                existingCrawl.otherCollectionId,
                bucketName,
              );
            } else {
              //update crawl record
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.FAILED,
              });
            }
          }
        }
        //if initial crawling has not completed & status has not changed to failed status
        else {
          logger.debug(
            `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | Existing initial crawling record found, it has not completed & status has not changed to failed state, CrawlingStatus: ${existingCrawl.status}`,
          );

          if (existingCrawl.status == CrawlingStatus.THUMBNAILS_GENERATING) {
            // call python mediaProcessor
            this.startMediaProcessByCrawlId(
              existingCrawl.id,
              existingCrawl.imageCollectionId,
              existingCrawl.videoCollectionId,
              existingCrawl.otherCollectionId,
              bucketName,
            );
          } else {
            // call respective storage service to fetch metadata from cloud
            let crawlResult = await this.storageServiceProvider.fetchFilesFromStorageAndPopulateMetaData(
              existingCrawl.imageCollectionId,
              existingCrawl.videoCollectionId,
              existingCrawl.otherCollectionId,
              existingCrawl.id,
              new ObjectId(teamId),
              false,
              bucketName,
            );

            if (crawlResult.success) {
              //update crawl record
              let thumbnailGeneratableCount = await this.metaDataRepository.count({
                dataCrawlId: existingCrawl.id,
                objectType: {
                  inq: [ContentType.IMAGE, ContentType.VIDEO],
                },
              });
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.THUMBNAILS_GENERATING,
                thumbGenStartedAt: new Date(),
                thumbnailGeneratableCount: thumbnailGeneratableCount.count,
                thumbnailGeneratedCount: 0,
              });
              // call python mediaProcessor
              this.startMediaProcessByCrawlId(
                existingCrawl.id,
                existingCrawl.imageCollectionId,
                existingCrawl.videoCollectionId,
                existingCrawl.otherCollectionId,
                bucketName,
              );
            } else {
              //update crawl record
              await this.dataCrawlRepository.updateById(existingCrawl.id, {
                status: CrawlingStatus.FAILED,
              });
            }
          }
        }

        await this.removeEmptyCollections(existingCrawl.id);
      }
      /// if initial crawl record not exist, start initial crawling from cloud
      else {
        //add bucket to system data
        let systemData = await this.systemDataRepository.findOne();
        if (systemData) {
          let cloudStorages = systemData.cloudStorages ? systemData.cloudStorages : [];
          let cloudStoragesTypeAndNameList = cloudStorages.map(
            storage => storage.storageType + '-' + storage.storageName,
          );

          if (storageType && bucketName) {
            let cloudStoragesTypeAndName = storageType + '-' + bucketName;
            if (!cloudStoragesTypeAndNameList.includes(cloudStoragesTypeAndName)) {
              cloudStorages.push({
                storageType: storageType,
                storageName: bucketName,
              });
              await this.systemDataRepository.updateById(systemData.id, {
                cloudStorages: cloudStorages,
              });
            }
          }
        }

        let teamId = systemData?.teamId;

        let isDefaultStorageBucket = bucketName == defaultBucketName ? true : false;

        let dataCrawl = await this.createCrawlRecord(true, bucketName, isDefaultStorageBucket, teamId);
        if (
          dataCrawl &&
          dataCrawl.id &&
          dataCrawl.imageCollectionId &&
          dataCrawl.videoCollectionId &&
          dataCrawl.otherCollectionId
        ) {
          // then ok
        } else {
          logger.warn(
            `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | failed creating new data crawling record `,
          );
          continue;
        }

        // call respective storage service to fetch metadata from cloud
        let crawlResult = await this.storageServiceProvider.fetchFilesFromStorageAndPopulateMetaData(
          dataCrawl.imageCollectionId,
          dataCrawl.videoCollectionId,
          dataCrawl.otherCollectionId,
          dataCrawl.id,
          new ObjectId(teamId),
          false,
          bucketName,
        );

        if (crawlResult.success) {
          //update crawl record
          let thumbnailGeneratableCount = await this.metaDataRepository.count({
            dataCrawlId: dataCrawl.id,
            objectType: {
              inq: [ContentType.IMAGE, ContentType.VIDEO],
            },
          });
          await this.dataCrawlRepository.updateById(dataCrawl.id, {
            status: CrawlingStatus.THUMBNAILS_GENERATING,
            thumbGenStartedAt: new Date(),
            thumbnailGeneratableCount: thumbnailGeneratableCount.count,
            thumbnailGeneratedCount: 0,
          });
          // call python mediaProcessor
          this.startMediaProcessByCrawlId(
            dataCrawl.id,
            dataCrawl.imageCollectionId,
            dataCrawl.videoCollectionId,
            dataCrawl.otherCollectionId,
            bucketName,
          );
        } else {
          //update crawl record
          await this.dataCrawlRepository.updateById(dataCrawl.id, {
            status: CrawlingStatus.FAILED,
          });
        }

        await this.removeEmptyCollections(dataCrawl.id);
      }
    }
  }

  /**
   * Use to trigger partial crawling at changed directory.
   * @params changedDirectory: {string} the aboslute path of the changed directory (from the root)
   */
  async partialCrawl(changedDirectory: string) {
    let teamId = process.env.TEAM_ID;
    let isInitialCrawl = false; // set initial crawl flag as false since this is partial crawl
    let isDefaultStorageBucket = true; // Assuming we have only one bucket.
    if (!defaultBucketName || !teamId) {
      logger.info(
        `Crawl storage for populate data | StorageCrawlerService.partialCrawl | N/A | undefined bucket name or team ID `,
      );
      return {staus: 'Failed'};
    } else {
      let dataCrawl = await this.createCrawlRecord(isInitialCrawl, defaultBucketName, isDefaultStorageBucket, teamId);
      logger.info(
        `Crawl storage for populate data | StorageCrawlerService.partialCrawl | changed directory: ${changedDirectory} |  `,
      );
      if (
        dataCrawl &&
        dataCrawl.id &&
        dataCrawl.imageCollectionId &&
        dataCrawl.videoCollectionId &&
        dataCrawl.otherCollectionId
      ) {
        // then ok

        await this.dataCrawlRepository.updateById(dataCrawl.id, {
          status: CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
        });

        // call respective storage service to fetch metadata from cloud

        let crawlResult = await this.storageServiceProvider.fetchFilesFromStorageAndPopulateMetaData(
          dataCrawl.imageCollectionId,
          dataCrawl.videoCollectionId,
          dataCrawl.otherCollectionId,
          dataCrawl.id,
          new ObjectId(teamId),
          true,
          defaultBucketName,
          changedDirectory,
          dataCrawl.updatedAt,
        );
        if (crawlResult.success) {
          //update crawl record
          let thumbnailGeneratableCount = await this.metaDataRepository.count({
            dataCrawlId: dataCrawl.id,
            objectType: {
              inq: [ContentType.IMAGE, ContentType.VIDEO],
            },
          });
          await this.dataCrawlRepository.updateById(dataCrawl.id, {
            status: CrawlingStatus.THUMBNAILS_GENERATING,
            thumbGenStartedAt: new Date(),
            thumbnailGeneratableCount: thumbnailGeneratableCount.count,
            thumbnailGeneratedCount: 0,
          });
          // call python mediaProcessor
          this.startMediaProcessByCrawlId(
            dataCrawl.id,
            dataCrawl.imageCollectionId,
            dataCrawl.videoCollectionId,
            dataCrawl.otherCollectionId,
            defaultBucketName,
          );
        } else {
          //update crawl record
          await this.dataCrawlRepository.updateById(dataCrawl.id, {
            status: CrawlingStatus.FAILED,
          });
        }
        return {staus: 'sucess'};
      } else {
        logger.warn(
          `Crawl storage for populate data | StorageCrawlerService.partialCrawl | N/A | failed creating new data crawling record `,
        );
      }
    }
  }
  /**
   * Use to start media processing python service
   * @param dataCrawlId id of relevant DataCrawl record
   * @param imageCollectionId crawling images belongs to this image collection, crwaling images will be saved under this image collection
   */
  async startMediaProcessByCrawlId(
    dataCrawlId: string,
    imageCollectionId: string,
    videoCollectionId: string,
    otherCollectionId: string,
    bucketName: string,
  ) {
    const thumbnail_prefix = STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS.toString();

    // Here generated thumbsnail count update before start python media process.
    // Because when resume inturrupted media process, the count may be changed
    // Ex: in python side, isMediaProcessingPending: False bulkWrite has triggered and python process has interrupted before count increment db call triggering
    let thumbnailGeneratedCount = await this.metaDataRepository.count({
      dataCrawlId: dataCrawlId,
      objectType: {
        inq: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER],
      },
      isMediaProcessingPending: false,
    });
    await this.dataCrawlRepository.updateById(dataCrawlId, {
      thumbnailGeneratedCount: thumbnailGeneratedCount.count,
    });
    try {
      let url = `${PYTHON_HOST}/internal/media/process`;

      Axios({
        url,
        method: 'POST',
        data: {
          dataInputType: DataInputType.CRAWL.toString(),
          bucketName: bucketName,
          systemSubFolderThumbnails: thumbnail_prefix,
          dataId: dataCrawlId.toString(),
          imageCollectionId: imageCollectionId,
          videoCollectionId: videoCollectionId,
          otherCollectionId: otherCollectionId,
        },
      });
    } catch (err) {
      logger.error(
        `Process thumbnail from input feed | StorageCrawlerService.startMediaProcessByUploadId | N/A | failed to post request to python host, err = ${err}`,
      );
    }

    logger.debug(
      `Crawl storage for populate data | StorageCrawlerService.startMediaProcessByCrawlId | N/A | starting python media processor`,
    );
  }

  /**
   * Use to show latest crawling status in datalake overview section
   * @param timeZoneOffset {number} use to transform UTC timestap into user's timestamp
   * @returns CrawlingStatusResponse
   */
  async getCrawlingStatus(timeZoneOffset: number, teamId?: string) {
    let query: any = teamId
      ? {
          where: {teamId: teamId},
          order: ['crawlStartedAt DESC'],
        }
      : {
          order: ['crawlStartedAt DESC'],
        };

    let latestCrawl = await this.dataCrawlRepository.findOne(query);

    if (!latestCrawl) {
      return;
    }

    let apiResponse: CrawlingStatusResponse = {
      overallStatus: {
        crawlingStatus: latestCrawl.status,
        crawlingResult: {
          crawlingFrom: latestCrawl.storageName,
        },
      },
      storageStatus: [],
    };

    // if crawling & storage summary calculating ongoing
    if (
      latestCrawl.status == CrawlingStatus.FILES_CRAWLING ||
      latestCrawl.status == CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING
    ) {
      apiResponse.overallStatus.crawlingResult.crawledFiles = getFormattedItemCount(latestCrawl.crawledFileCount);

      // if storage total file count exist
      if (latestCrawl.totalFileCount) {
        let remainingFiles = latestCrawl.totalFileCount - latestCrawl.crawledFileCount;
        apiResponse.overallStatus.crawlingResult.filesToBeCrawl = remainingFiles;

        if (latestCrawl.updatedAt && latestCrawl.crawlStartedAt) {
          let diff_ms = latestCrawl.updatedAt.getTime() - latestCrawl.crawlStartedAt.getTime();
          let eta_ms = (diff_ms / latestCrawl.crawledFileCount) * remainingFiles;
          apiResponse.overallStatus.crawlingResult.eta = moment.utc(eta_ms).format('H:mm:ss');
        } else {
          apiResponse.overallStatus.crawlingResult.eta = '--:--:--';
        }
      }
      // if storage total file count not exist
      else {
        apiResponse.overallStatus.crawlingResult.filesToBeCrawl = '--';
        apiResponse.overallStatus.crawlingResult.eta = '--:--:--';
      }

      //if total storage size exist
      if (latestCrawl.totalFileSize) {
        let progressLabel =
          byteTransform(latestCrawl.crawledFileSize, false) + ' / ' + byteTransform(latestCrawl.totalFileSize, false);
        apiResponse.overallStatus.progressString = progressLabel;

        let progressPrecentage = (latestCrawl.crawledFileSize / latestCrawl.totalFileSize) * 100;
        apiResponse.overallStatus.progress = Math.floor(progressPrecentage);
      }
      //if total storage size not exist
      else {
        let progressLabel = byteTransform(latestCrawl.crawledFileCount, false) + ' / Total storage size calculating...';
        apiResponse.overallStatus.progressString = progressLabel;

        //apiResponse.overallStatus.progress = "--"
      }
    }
    // if thumbnails generating
    else if (latestCrawl.status == CrawlingStatus.THUMBNAILS_GENERATING) {
      apiResponse.overallStatus.progressString = 'Generating thumbnails...';

      if (latestCrawl.thumbnailGeneratableCount) {
        let progressPrecentage =
          ((latestCrawl.thumbnailGeneratedCount || 0) / latestCrawl.thumbnailGeneratableCount) * 100;
        apiResponse.overallStatus.progress = Math.floor(progressPrecentage);
      }
      apiResponse.overallStatus.crawlingResult.thumbsPending =
        (latestCrawl.thumbnailGeneratableCount || 0) - (latestCrawl.thumbnailGeneratedCount || 0);
      apiResponse.overallStatus.crawlingResult.thumbsGenerated = latestCrawl.thumbnailGeneratedCount;

      if (latestCrawl.updatedAt && latestCrawl.thumbGenStartedAt) {
        let diff_ms = latestCrawl.updatedAt.getTime() - latestCrawl.thumbGenStartedAt.getTime();

        if (latestCrawl.thumbnailGeneratableCount && latestCrawl.thumbnailGeneratedCount) {
          let eta_ms =
            (diff_ms / latestCrawl.thumbnailGeneratedCount) *
            (latestCrawl.thumbnailGeneratableCount - latestCrawl.thumbnailGeneratedCount);
          apiResponse.overallStatus.crawlingResult.eta = moment.utc(eta_ms).format('H:mm:ss');
        } else {
          apiResponse.overallStatus.crawlingResult.eta = '--:--:--';
        }
      } else {
        apiResponse.overallStatus.crawlingResult.eta = '--:--:--';
      }
    }
    // if crawling finished
    else if (latestCrawl.status == CrawlingStatus.COMPLETED) {
      apiResponse.overallStatus.progressString = 'Last Crawled Status';
      apiResponse.overallStatus.crawlingResult.crawledFiles = getFormattedItemCount(latestCrawl.newlyAddedFileCount);
      apiResponse.overallStatus.crawlingResult.crawledSize = byteTransform(latestCrawl.newlyAddedFileSize, false);

      if (latestCrawl.crawlStartedAt && latestCrawl.completedAt) {
        let diff_ms = latestCrawl.completedAt.getTime() - latestCrawl.crawlStartedAt.getTime();
        apiResponse.overallStatus.crawlingResult.crawledTime = moment.utc(diff_ms).format('H:mm:ss');
      } else {
        apiResponse.overallStatus.crawlingResult.crawledTime = '--:--:--';
      }
    }
    // if crawling failed
    else if (latestCrawl.status == CrawlingStatus.FAILED) {
      apiResponse.overallStatus.progressString = 'Last Crawled Status';
      apiResponse.overallStatus.crawlingResult.crawledFiles = getFormattedItemCount(latestCrawl.newlyAddedFileCount);
      apiResponse.overallStatus.crawlingResult.crawledSize = byteTransform(latestCrawl.newlyAddedFileSize, false);

      if (latestCrawl.crawlStartedAt && latestCrawl.updatedAt) {
        let diff_ms = latestCrawl.updatedAt.getTime() - latestCrawl.crawlStartedAt.getTime();
        apiResponse.overallStatus.crawlingResult.crawledTime = moment.utc(diff_ms).format('H:mm:ss');
      } else {
        apiResponse.overallStatus.crawlingResult.crawledTime = '--:--:--';
      }
    } else {
      logger.error(
        `${FLOWS.CRAWL_STORAGE} | StorageCrawlerService.getCrawlingStatus | N/A | failed getting crawling status for crawlId: ${latestCrawl.id}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATA_CRAWL_GET_STATUS_FAILED);
    }

    let querySystemData = teamId
      ? {where: {teamId: teamId}, fields: {cloudStorages: true}}
      : {fields: {cloudStorages: true}};

    //cloud storage connections //NOTE: add teamId filter
    let systemData = await this.systemDataRepository.findOne(querySystemData);

    if (systemData) {
      if (systemData.cloudStorages) {
        for (let storage of systemData.cloudStorages) {
          let cloudStorageStatus: CloudStorageStatus = {
            storageName: storage.storageName,
            storageConnectionStatus: storage.connectionStatus,
          };
          if (storage.storageName == latestCrawl.storageName) {
            await this.assignCloudStorageActivityStatus(cloudStorageStatus, latestCrawl, timeZoneOffset);
          } else {
            let dataCrawlQuery: any = teamId
              ? {
                  where: {teamId: teamId, storageName: storage.storageName},
                  order: ['crawlStartedAt DESC'],
                }
              : {
                  where: {storageName: storage.storageName},
                  order: ['crawlStartedAt DESC'],
                };

            let latestStorageCrawl = await this.dataCrawlRepository.findOne(dataCrawlQuery);
            if (latestStorageCrawl) {
              await this.assignCloudStorageActivityStatus(cloudStorageStatus, latestStorageCrawl, timeZoneOffset);
            } else {
              cloudStorageStatus.storageActivityStatus = CloudStorageActivityStatus.NOT_CRAWLED;
            }
          }
          apiResponse.storageStatus.push(cloudStorageStatus);
        }
      }
    }

    return apiResponse;
  }

  /**
   * Use to assign cloud storages connection status and activity status to getCrawlStatus API response
   * @param cloudStorageStatus CloudStorageStatus object which is to be assign data
   * @param latestStorageCrawl latest crawling record to perticular cloud storage
   * @param timeZoneOffset user's timezone offset to format time
   */
  async assignCloudStorageActivityStatus(
    cloudStorageStatus: CloudStorageStatus,
    latestStorageCrawl: DataCrawl,
    timeZoneOffset: number,
  ) {
    let dateToformat = new Date(latestStorageCrawl.startedAt.getTime() - timeZoneOffset * 60 * 1000);
    if (
      latestStorageCrawl.status == CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING ||
      latestStorageCrawl.status == CrawlingStatus.FILES_CRAWLING ||
      latestStorageCrawl.status == CrawlingStatus.THUMBNAILS_GENERATING
    ) {
      cloudStorageStatus.storageActivityStatus = CloudStorageActivityStatus.CRAWLING;
      cloudStorageStatus.timeUpdated = 'Crawling started at ' + moment(dateToformat).format('MMM D, YYYY h:mmA');
    } else if (
      latestStorageCrawl.status == CrawlingStatus.COMPLETED ||
      latestStorageCrawl.status == CrawlingStatus.FAILED
    ) {
      cloudStorageStatus.storageActivityStatus = CloudStorageActivityStatus.CRAWLED;
      cloudStorageStatus.timeUpdated = 'Last update time ' + moment(dateToformat).format('MMM D, YYYY h:mmA');
    } else {
      cloudStorageStatus.storageActivityStatus = CloudStorageActivityStatus.NOT_CRAWLED;
    }
  }

  /**
   * Use to get crawling history
   * @param pageIndex page number [strart from 0]
   * @param pageSize page size
   * @returns CrawlHistory[]
   */
  async getCrawlingHistory(pageIndex: number, pageSize: number, timeZoneOffset: number, teamId?: string) {
    let query: any = teamId
      ? {
          where: {
            teamId: teamId,
            status: {
              inq: [CrawlingStatus.COMPLETED, CrawlingStatus.FAILED],
            },
          },
          order: ['crawlStartedAt DESC'],
          limit: pageSize,
          skip: pageIndex * pageSize,
        }
      : {
          where: {
            status: {
              inq: [CrawlingStatus.COMPLETED, CrawlingStatus.FAILED],
            },
          },
          order: ['crawlStartedAt DESC'],
          limit: pageSize,
          skip: pageIndex * pageSize,
        };

    let crawlHistory = await this.dataCrawlRepository.find(query);

    let formattedCrawlHistory: CrawlHistory[] = crawlHistory.map(_history => {
      let dateToformat = new Date(_history.startedAt.getTime() - timeZoneOffset * 60 * 1000);

      let fileCount = _history.newlyAddedFileCount || 0;
      // if ((typeof _history.newlyAddedFileCount) !== 'undefined') {
      //   fileCount = _history.newlyAddedFileCount
      // }
      let fileSize = _history.newlyAddedFileSize || 0;
      // if ((typeof _history.newlyAddedFileSize) !== 'undefined') {
      //   fileSize = _history.newlyAddedFileSize
      // }
      return {
        storageName: _history.storageName,
        lastCrawledAt: moment(dateToformat).format('MMM D, YYYY h:mm A'),
        crawledFiles: getFormattedItemCount(fileCount),
        crawledSize: byteTransform(fileSize, false),
        crawledStatus: _history.status,
      };
    });
    return formattedCrawlHistory;
  }

  /**
   * Get object overview count
   * @param teamId team id
   * @returns overall object overview (overall Detail,totalFrames and totalLabel counts)
   */
  async getObjectOverview(teamId?: string): Promise<OverviewCountRes> {
    let query: Record<string, any> = {
      fields: {
        objectTypeWiseCounts: true,
      },
    };
    if (teamId) query['where'] = {teamId: teamId};

    let systemData: null | Pick<SystemData, 'objectTypeWiseCounts'> = await this.systemDataRepository.findOne(query);
    if (!systemData) {
      logger.error(
        `StorageCrawlerService | StorageCrawlerService.getObjectOverview | teamId :${teamId} | System data not found`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.NOT_FOUND_SYSTEM_DATA);
    }

    let imageSize: number = systemData.objectTypeWiseCounts?.images.size ?? 0;
    let videoSize: number = systemData.objectTypeWiseCounts?.videos.size ?? 0;
    let otherSize: number = systemData.objectTypeWiseCounts?.other.size ?? 0;
    let totalSize: string = byteTransform(imageSize + videoSize + otherSize, false);
    let imageCount: number = systemData.objectTypeWiseCounts?.images?.count ?? 0;
    let videoCount: number = systemData.objectTypeWiseCounts?.videos?.count ?? 0;
    let otherCount: number = systemData.objectTypeWiseCounts?.other?.count ?? 0;
    let totalItems: string = getFormattedItemCount(imageCount + videoCount + otherCount);
    let overallDetails: OverallDetailsInOverviewCount = {
      totalSize: totalSize,
      totalItems: totalItems,
      videoCollections: getFormattedItemCount(systemData.objectTypeWiseCounts?.videoCollections?.count),
      videoFiles: getFormattedItemCount(videoCount),
      imageCollections: getFormattedItemCount(systemData.objectTypeWiseCounts?.imageCollections?.count),
      images: getFormattedItemCount(imageCount),
      otherCollections: getFormattedItemCount(systemData.objectTypeWiseCounts?.otherCollections?.count),
      otherFiles: getFormattedItemCount(otherCount),
    };
    let imagesStats: ObjectTypeWiseCountsVal | undefined = systemData.objectTypeWiseCounts?.images;
    let rawData: number = imagesStats?.raw ?? 0;
    let machineAnnotated: number = imagesStats?.machineAnnotated ?? 0;
    let humanAnnotated: number = imagesStats?.verified ?? 0;
    let totalFrames: TotalFramesInOverviewCount = {
      total: getFormattedItemCount(rawData + machineAnnotated + humanAnnotated),
      rawData: getFormattedItemCount(rawData),
      machineAnnotated: getFormattedItemCount(machineAnnotated),
      humanAnnotated: getFormattedItemCount(humanAnnotated),
    };
    let labelStats: LabelStats | undefined = imagesStats?.labelStats;
    let humanAnnotatedLabel: number = labelStats?.human ?? 0;
    let machineAnnotatedLabel: number = labelStats?.machine ?? 0;
    let totalLabels: TotalLabelsInOverviewCount = {
      size: getFormattedItemCount(humanAnnotatedLabel + machineAnnotatedLabel),
      humanAnnotated: getFormattedItemCount(humanAnnotatedLabel),
      machineAnnotated: getFormattedItemCount(machineAnnotatedLabel),
    };

    let overviewRes: OverviewCountRes = {
      overallDetails: overallDetails,
      totalFrames: totalFrames,
      totalLabels: totalLabels,
    };

    return overviewRes;
  }

  /**
   * Use to get count details to show in datalake overview section
   * @returns DataOverview
   */
  // async getObjectCountsToOverview(teamId?: string) {
  //   let query = teamId
  //     ? {
  //         where: {teamId: teamId},
  //         fields: {
  //           objectTypeWiseCounts: true,
  //         },
  //       }
  //     : {
  //         fields: {
  //           objectTypeWiseCounts: true,
  //         },
  //       };

  //   //NOTE: add teamId filter
  //   let systemData = await this.systemDataRepository.findOne(query);

  //   if (systemData) {
  //     let response: DataOverview = {
  //       counts: [
  //         {
  //           contentType: ContentTypeOverviewTabIndexFrontendConstants.VIDEO,
  //           details: [
  //             {
  //               name: ObjectCountFrontendConstants.VIDEO_LENGTH,
  //               value: getFormattedVideoLength(systemData.objectTypeWiseCounts?.videos.length),
  //             },
  //             {
  //               name: ObjectCountFrontendConstants.VIDEO_FRAME_COUNT,
  //               value: getFormattedItemCount(systemData.objectTypeWiseCounts?.videos.frames),
  //             },
  //             {
  //               name: ObjectCountFrontendConstants.VIDEO_FILES_COUNT,
  //               value: getFormattedItemCount(systemData.objectTypeWiseCounts?.videos.count),
  //             },
  //             {
  //               name: ObjectCountFrontendConstants.VIDEO_SIZE,
  //               value: byteTransform(systemData.objectTypeWiseCounts?.videos.size, false),
  //             },
  //           ],
  //         },
  //         {
  //           contentType: ContentTypeOverviewTabIndexFrontendConstants.IMAGE,
  //           details: [
  //             {
  //               name: ObjectCountFrontendConstants.IMAGE_COUNT,
  //               value: getFormattedItemCount(systemData.objectTypeWiseCounts?.images.count),
  //             },
  //             {
  //               name: ObjectCountFrontendConstants.IMAGE_SIZE,
  //               value: byteTransform(systemData.objectTypeWiseCounts?.images.size, false),
  //             },
  //           ],
  //         },
  //         {
  //           contentType: ContentTypeOverviewTabIndexFrontendConstants.OTHER,
  //           details: [
  //             {
  //               name: ObjectCountFrontendConstants.OTHER_FILE_COUNT,
  //               value: getFormattedItemCount(systemData.objectTypeWiseCounts?.other.count),
  //             },
  //             {
  //               name: ObjectCountFrontendConstants.OTHER_FILE_SIZE,
  //               value: byteTransform(systemData.objectTypeWiseCounts?.other?.size, false),
  //             },
  //           ],
  //         },
  //       ],
  //     };

  //     return response;
  //   } else {
  //     return {
  //       counts: [],
  //     };
  //   }
  // }

  /**
   * Use to get pre signed Url
   * @param storagePath S3 object key
   * @returns
   */
  async getSignedUrl(storagePath: string) {
    return await this.storageServiceProvider.generateObjectUrl(storagePath);
  }

  /**
   * sub method to create crawl record
   */
  async createCrawlRecord(
    isInitialCrawl: boolean,
    bucketName: string,
    isDefaultStorageBucket: boolean,
    teamId?: string,
  ) {
    // let imageCollectionName = `${bucketName}_Images`
    // let videoCollectionName = `${bucketName}_Videos`
    // let otherCollectionName = `${bucketName}_Others`
    let imageCollectionName = `${bucketName}`;
    let videoCollectionName = `${bucketName}`;
    let otherCollectionName = `${bucketName}`;

    let existingImageCollection: MetaData | null = null;
    let existingVideoCollection: MetaData | null = null;
    let existingOtherCollection: MetaData | null = null;

    let isNotStoragePathRequired: boolean = false;

    if (bucketName == defaultBucketName) {
      isNotStoragePathRequired = true;
    }

    if (!isInitialCrawl) {
      let formattedDate = moment().format('YYYY-MM-DD');
      imageCollectionName = `Crawl-Images-${formattedDate}`;
      videoCollectionName = `Crawl-Videos-${formattedDate}`;
      otherCollectionName = `Crawl-Others-${formattedDate}`;

      //check if image and video collection with the name exists
      existingImageCollection = await this.metaDataRepository.findOne({
        where: {
          name: imageCollectionName,
          objectType: ContentType.IMAGE_COLLECTION,
        },
      });
      existingVideoCollection = await this.metaDataRepository.findOne({
        where: {
          name: videoCollectionName,
          objectType: ContentType.VIDEO_COLLECTION,
        },
      });
      existingOtherCollection = await this.metaDataRepository.findOne({
        where: {
          name: otherCollectionName,
          objectType: ContentType.OTHER_COLLECTION,
        },
      });
    }

    let imageCollectionId: string | undefined = undefined;
    if (existingImageCollection) {
      imageCollectionId = existingImageCollection.id;
    } else {
      // create an image collection
      imageCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
        ContentType.IMAGE_COLLECTION,
        new ObjectId(teamId),
        imageCollectionName,
        {},
        isNotStoragePathRequired,
      );
      if (!imageCollectionId) {
        logger.warn(
          `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | failed creating new image collection `,
        );
        return;
      }
    }

    let videoCollectionId: string | undefined = undefined;
    if (existingVideoCollection) {
      videoCollectionId = existingVideoCollection.id;
    } else {
      // create an video collection
      videoCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
        ContentType.VIDEO_COLLECTION,
        new ObjectId(teamId),
        videoCollectionName,
        {},
        isNotStoragePathRequired,
      );
      if (!videoCollectionId) {
        logger.warn(
          `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | failed creating new video collection `,
        );
        return;
      }
    }

    let otherCollectionId: string | undefined = undefined;
    if (existingOtherCollection) {
      otherCollectionId = existingOtherCollection.id;
    } else {
      // create an other collection
      otherCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
        ContentType.OTHER_COLLECTION,
        new ObjectId(teamId),
        otherCollectionName,
        {},
        isNotStoragePathRequired,
      );
      if (!otherCollectionId) {
        logger.warn(
          `Crawl storage for populate data | StorageCrawlerService.triggerInitialCralwing | N/A | failed creating new other collection `,
        );
        return;
      }
    }

    // create a record in DataCrawl Collection
    let dataCrawl = await this.dataCrawlRepository.create({
      storageType: storageType,
      storageName: bucketName,
      startedAt: new Date(),
      crawlStartedAt: new Date(),
      crawledFileSize: 0,
      crawledFileCount: 0,
      unpopulatedCount: 0,
      unpopulatedSize: 0,
      status: CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
      isInitialCrawl: isInitialCrawl,
      isDefaultStorageBucket: isDefaultStorageBucket,
      imageCollectionId: imageCollectionId,
      videoCollectionId: videoCollectionId,
      otherCollectionId: otherCollectionId,
      teamId: teamId,
    });

    return dataCrawl;
  }

  /**
   * Method to reset status of subsequent crawls (set ongoing to failed) at server start
   * Triggered once at server start
   **/
  async resetOngoingSubsequentCrawls() {
    logger.debug(
      `Subsequent crawl | StorageCrawlerService.resetOngoingSubsequentCrawls | N/A | Check for faild crawls - at start `,
    );
    //  find ongoin crawls at server start
    let ongoingCrawlData = await this.dataCrawlRepository.findOne({
      where: {
        isInitialCrawl: false,
        storageType: storageType,
        // storageName: storageName, // have to be loop..................
        status: {
          inq: [
            CrawlingStatus.FILES_CRAWLING,
            CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
            CrawlingStatus.THUMBNAILS_GENERATING,
          ],
        },
      },
    });
    // mark their status as failed
    if (ongoingCrawlData) {
      logger.warn(
        `Failed subsequent crawl | StorageCrawlerService.resetOngoingSubsequentCrawls | failed CrawlId: ${ongoingCrawlData.id} | Failed subsequent crawl found `,
      );
      let crawlId = ongoingCrawlData.id;
      await this.dataCrawlRepository.updateById(crawlId, {
        status: CrawlingStatus.FAILED,
      });
    }

    return;
  }

  /**
   * handle triggering crawling (subsequent) by api call
   */
  async handleTriggerSubsequentCrawling() {
    //get all buckets from env and iterate and crawl
    let otherCloudStorages = bucketNames?.split(',') || [];
    otherCloudStorages = otherCloudStorages.map((bucketName: string) => bucketName.trim());

    for (let bucketName of otherCloudStorages) {
      //check whether initial crawling record exists

      //NOTE: add teamId filtering
      let existingCrawl = await this.dataCrawlRepository.findOne({
        where: {
          isInitialCrawl: true,
          storageType: storageType,
          storageName: bucketName,
        },
      });

      // if initial crawl record exist
      if (existingCrawl) {
        // check if initial crawl is ongoing
        if (existingCrawl.status) {
          if (
            existingCrawl.status == CrawlingStatus.FILES_CRAWLING ||
            existingCrawl.status == CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING ||
            existingCrawl.status == CrawlingStatus.THUMBNAILS_GENERATING
          ) {
            logger.debug(
              `Subsequent crawl | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | Couldn't start subsequent crawl due to initia crawl in status: ${existingCrawl.status} `,
            );
            return;
          }
        }
        // check if ongoing subsequent crawl record exist
        let existingSubsequentCrawlOngoing = await this.dataCrawlRepository.findOne({
          where: {
            isInitialCrawl: false,
            storageType: storageType,
            storageName: bucketName,
            status: {
              inq: [
                CrawlingStatus.FILES_CRAWLING,
                CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
                CrawlingStatus.THUMBNAILS_GENERATING,
              ],
            },
          },
        });

        if (existingSubsequentCrawlOngoing) {
          // check if subsequent crawl ongoing and return
          logger.info(
            `Crawl storage for populate data | StorageCrawlerService.handleTriggerSubsequentCrawling | onoingCrawl: ${existingCrawl} | Existing ongoing subsequent crawling record found, not starting new crawl `,
          );
          return;
        }

        let systemData = await this.systemDataRepository.findOne();
        let teamId = systemData?.teamId;

        // create new image and video collections
        // create a new subsequent crawl collection if subsequent crawl not exist
        let isDefaultStorageBucket = bucketName == defaultBucketName ? true : false;
        let newCeatedCrawl = await this.createCrawlRecord(false, bucketName, isDefaultStorageBucket, teamId);

        if (newCeatedCrawl) {
          existingCrawl = newCeatedCrawl;
        } else {
          logger.error(
            `Crawl storage for populate data | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | New crawling record is not valid`,
          );
          return;
        }

        //check crawling record is valid
        if (
          !existingCrawl.imageCollectionId ||
          !existingCrawl.videoCollectionId ||
          !existingCrawl.otherCollectionId ||
          !existingCrawl.id
        ) {
          logger.error(
            `Crawl storage for populate data | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | New crawling record is not valid. Crawl: ${existingCrawl}`,
          );
          return;
        }

        logger.debug(
          `Crawl storage for populate data (subsequent) | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | Crawl record created`,
        );

        // crawling again -------------------------------------------------------------------
        // update crawl status
        await this.dataCrawlRepository.updateById(existingCrawl.id, {
          status: CrawlingStatus.FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING,
        });
        // call respective storage service to fetch metadata from cloud
        let crawlResult = await this.storageServiceProvider.fetchFilesFromStorageAndPopulateMetaData(
          existingCrawl.imageCollectionId,
          existingCrawl.videoCollectionId,
          existingCrawl.otherCollectionId,
          existingCrawl.id,
          new ObjectId(teamId),
          true,
          bucketName,
        );

        if (crawlResult.success) {
          //update crawl record
          let thumbnailGeneratableCount = await this.metaDataRepository.count({
            dataCrawlId: existingCrawl.id,
            objectType: {
              inq: [ContentType.IMAGE, ContentType.VIDEO],
            },
          });
          await this.dataCrawlRepository.updateById(existingCrawl.id, {
            status: CrawlingStatus.THUMBNAILS_GENERATING,
            thumbGenStartedAt: new Date(),
            thumbnailGeneratableCount: thumbnailGeneratableCount.count,
            thumbnailGeneratedCount: 0,
          });

          if (thumbnailGeneratableCount && thumbnailGeneratableCount.count) {
            logger.info(
              `Crawl storage for populate data (subsequent) | StorageCrawlerService.handleTriggerSubsequentCrawling | new file count: ${thumbnailGeneratableCount.count} | starting media_procesoor`,
            );
            // call python mediaProcessor
            this.startMediaProcessByCrawlId(
              existingCrawl.id,
              existingCrawl.imageCollectionId,
              existingCrawl.videoCollectionId,
              existingCrawl.otherCollectionId,
              bucketName,
            );
          } else {
            logger.info(
              `Crawl storage for populate data (subsequent) | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | No new files crawled`,
            );
            // update status of crawl to complete
            await this.dataCrawlRepository.updateById(existingCrawl.id, {
              status: CrawlingStatus.COMPLETED,
            });
          }
        } else {
          //update crawl record
          await this.dataCrawlRepository.updateById(existingCrawl.id, {
            status: CrawlingStatus.FAILED,
          });
        }
        // check if collections created has child files - delete if no child files available for them
        // await this.metaDataRepository.deleteCollectionIfNoChildren(existingCrawl.imageCollectionId, ContentType.IMAGE)
        // await this.metaDataRepository.deleteCollectionIfNoChildren(existingCrawl.videoCollectionId, ContentType.VIDEO)
        // await this.metaDataRepository.deleteCollectionIfNoChildren(existingCrawl.otherCollectionId, ContentType.OTHER)

        await this.removeEmptyCollections(existingCrawl.id);
      } else {
        // if initial crawl record not exist, start initial crawling from cloud - call initial crawl method
        logger.debug(
          `Crawl storage for populate data (subsequent) | StorageCrawlerService.handleTriggerSubsequentCrawling | N/A | No existing crawl record`,
        );
        await this.triggerInitialCralwing();
        //-------------------------------------------------------------------------
      }
    }
  }

  /**
   * use to remove empty collections which created for crawling
   * @param dataCrawlId id of crawl record
   */
  async removeEmptyCollections(dataCrawlId: string) {
    let crawlDetails = await this.dataCrawlRepository.findById(dataCrawlId);

    if (crawlDetails.imageCollectionId) {
      let isExists = await this.metaDataRepository.findOne({
        where: {collectionId: crawlDetails.imageCollectionId},
      });
      if (!isExists) {
        // this.dataCrawlRepository.updateById(dataCrawlId, {imageCollectionId: undefined})
        this.metaDataRepository.deleteAll({
          _id: new ObjectId(crawlDetails.imageCollectionId),
        });
      }
    }
    if (crawlDetails.videoCollectionId) {
      let isExists = await this.metaDataRepository.findOne({
        where: {collectionId: crawlDetails.videoCollectionId},
      });
      if (!isExists) {
        // this.dataCrawlRepository.updateById(dataCrawlId, {videoCollectionId: undefined})
        this.metaDataRepository.deleteAll({
          _id: new ObjectId(crawlDetails.videoCollectionId),
        });
      }
    }
    if (crawlDetails.otherCollectionId) {
      let isExists = await this.metaDataRepository.findOne({
        where: {collectionId: crawlDetails.otherCollectionId},
      });
      if (!isExists) {
        // this.dataCrawlRepository.updateById(dataCrawlId, {otherCollectionId: undefined})
        this.metaDataRepository.deleteAll({
          _id: new ObjectId(crawlDetails.otherCollectionId),
        });
      }
    }
  }
}

//  /**
//   * test - method
//  * Create a new s3 buckt for processed media saving
//  * sub method - check media bucket name
//  * @returns s3 bucketname
//  */
//  async createNewBucket(){
//    return await this.storageServiceProvider.createNewBucket()
//  }

export const STORAGE_CRAWLER_SERVICE = BindingKey.create<StorageCrawlerService>('service.storageCrawler');
