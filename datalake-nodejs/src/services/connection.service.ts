import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import Axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {CrawlingStatus} from '../models';
import {
  ConnectionCredentials,
  DataSourceConnectionStatus,
  DataSourceConnectionType,
  DataSourceUpdateStatus,
  DataStructureCrawlStatus,
} from '../models/connection.model';
import {ConnectionSourceType} from '../models/source.model';
import {
  ConnectionSchemaRepository,
  DataCrawlRepository,
  SystemDataRepository,
  TableDataRepository,
} from '../repositories';
import {ConnectionRepository} from '../repositories/connection.repository';
import {FLOWS} from '../settings/constants';
import {timeAgo} from '../settings/tools';
import {TABLE_DATA_SERVICE, TableDataService} from './table-data.service';
const PYTHON_HOST = process.env.PYTHON_BASE_URL;
@injectable({scope: BindingScope.TRANSIENT})
export class ConnectionService {
  constructor(
    @inject(TABLE_DATA_SERVICE)
    private tableDataService: TableDataService,
    @repository(ConnectionRepository)
    private connectionDataRepository: ConnectionRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @repository(DataCrawlRepository)
    private dataCrawlRepository: DataCrawlRepository,
    @repository(TableDataRepository)
    private tableDataRepository: TableDataRepository,
    @repository(ConnectionSchemaRepository)
    private connectionSchemaRepository: ConnectionSchemaRepository,
  ) {}

  async isCrawled(connectionId: string): Promise<boolean> {
    const result = await this.connectionDataRepository.findOne({
      where: {connectionId: connectionId},
    });

    let response = false;

    if (
      result &&
      result.isDataStructureCrawled &&
      result?.isDataStructureCrawled === DataStructureCrawlStatus.COMPLETED
    ) {
      response = true;
    }

    return response;
  }

  async returnConnectionId() {
    const result = await this.connectionDataRepository.getAllConnectionDetailsAsJson();
    return result;
  }

  async getLayerNextConnections() {
    const result = await this.connectionDataRepository.getLayerNextConnections();
    return result;
  }

  async getDataSourceOverview(connectionId: string) {
    let result = await this.connectionDataRepository.getDataSourceOverview(connectionId);
    if (result) {
      result = `<p>${result}</p>`;
    }
    return result;
  }

  async getDataDictionarySectionList() {
    const LNconnections = await this.getLayerNextConnections();
    const connectionDetails: {connectionId: string; source: string; type: string}[] = JSON.parse(LNconnections);

    const dataDictionarySections = [];

    for (const connection of connectionDetails) {
      const sectionList = await this.tableDataService.getSectionList(connection.connectionId);

      if (sectionList) {
        dataDictionarySections.push({
          source: connection.source,
          type: ['mysql', 'postgres', 'mssql', 'bigquery', 'snowflake'].includes(connection.type) ? 'sqldb' : 'mongodb',
          source_type: connection.type,
          sections: sectionList,
        });
      }
    }

    return {
      dataDictionaryMapping: dataDictionarySections,
    };
  }

  async getTableSchemas() {
    const LNconnections = await this.getLayerNextConnections();
    const connectionDetails: {
      connectionId: string;
      source: string;
      type: string;
      dataFlow: object;
      dataSourceOverview: string;
    }[] = JSON.parse(LNconnections);

    // Initialize an array to store table schemas
    const tableSchemas: {
      dataSourceName: string;
      tables: any[];
      dataFlow: object;
      dataSourceOverview: string;
    }[] = [];

    // Loop through each connection and fetch table data
    for (const connection of connectionDetails) {
      const tableData = await this.tableDataService.getTableDataByConnectionID(connection.connectionId);
      tableSchemas.push({
        dataSourceName: connection.source,
        tables: tableData,
        dataFlow: connection.dataFlow,
        dataSourceOverview: connection.dataSourceOverview,
      });
    }

    return tableSchemas;
  }

  async updateDataSourceOverview(connectionId: string, dataSourceOverview: string) {
    const sanitizedOverview = dataSourceOverview.replace(/<\/?[^>]+(>|$)/g, '');

    const result = await this.connectionDataRepository.updateByConnectionId(connectionId, {
      dataSourceOverview: sanitizedOverview,
    });

    return result;
  }

  /**
   * Creates a new connection in the database
   *
   * @param sourceName The name of the source
   * @param sourceType The type of the source
   * @param connectionCredentials The credentials of the source
   * @param connectionType The type of connection
   * @param currentUserProfile The user profile of the current user
   * @param additionalData Additional data for creating the connection
   * @returns {Promise<{isSuccess: boolean, message: string}>} The result of the operation
   */
  async createConnection(
    sourceName: string,
    sourceType: ConnectionSourceType,
    connectionCredentials: any,
    connectionType: DataSourceConnectionType,
    currentUserProfile: UserProfileDetailed,
    additionalData?: any,
  ) {
    try {
      // handle empty data
      if (!sourceName || !sourceType || !connectionCredentials || !connectionType) {
        return {isSuccess: false, message: 'Missing required fields'};
      }

      // make sure there is no connection with the same source name
      const existingConnectionWithSource = await this.connectionDataRepository.findOne({
        where: {
          sourceName: sourceName,
        },
      });

      if (existingConnectionWithSource) {
        return {isSuccess: false, message: `Connection with source name ${sourceName} already exists`};
      }

      const connId = uuidV4();

      // Handle credentials for BigQuery
      if (sourceType === ConnectionSourceType.BIGQUERY && additionalData) {
        let credFileContent = JSON.stringify(additionalData);

        const directoryPath = '/app/config';
        if (!fs.existsSync(directoryPath)) {
          fs.mkdirSync(directoryPath, {recursive: true});
        }
        const filePath = path.join(directoryPath, `bigquery-${connId}.json`);
        // Write credentials to a file
        fs.writeFileSync(filePath, credFileContent);

        //remove data from connectionCredentials and store the credential_path
        connectionCredentials.credentials_path = filePath;

        //type handling for database name
        if (connectionCredentials.project_id) {
          connectionCredentials.project_id = connectionCredentials.project_id.toLowerCase();
        }
      }

      //convert the port in the connectionCredentials to int
      if (connectionCredentials.port) {
        connectionCredentials.port = Number(connectionCredentials.port);
      }

      const createObj = {
        sourceName: sourceName,
        type: sourceType,
        connectionCredentials: connectionCredentials,
        connectionType: connectionType,
        name: `${sourceName}->${sourceName}`,
        createdBy: currentUserProfile.username,
        teamId: currentUserProfile.teamId,
        createdAt: new Date(),
        connectionStatus: DataSourceConnectionStatus.CONNECTING,
        connectionId: connId,
        isDataStructureCrawled: DataStructureCrawlStatus.NOT_INITIATED,
      };

      let res = await this.connectionDataRepository.create(createObj);

      if (!res) {
        return {isSuccess: false, message: `Failed to create new connection`};
      }

      // create connection in background
      this.createConnectionInBackground(connId)
        .then(result => {
          if (!result.isSuccess) {
            logger.error(
              `${FLOWS.DB_CRAWLING} | ConnectionService.createConnection | N/A | Error creating connection in background for connectionId: ${connId}, error: ${result.message}`,
            );
          } else {
            logger.info(
              `${FLOWS.DB_CRAWLING} | ConnectionService.createConnection | N/A | Connection created in background for connectionId: ${connId}`,
            );
          }
        })
        .catch(error => {
          logger.error(
            `${FLOWS.DB_CRAWLING} | ConnectionService.createConnection | N/A | Error creating connection in background for connectionId: ${connId}, error: ${error}`,
          );
        });

      return {isSuccess: true, message: `Successfully created new connection`, data: {connectionId: connId}};
    } catch (error) {
      logger.error(
        `Add new data source connection | DataSourceController.addDataSource | N/A | Error while creating connection. Error: ${error}`,
      );
      return {isSuccess: false, message: `Failed to create new connection`};
    }
  }

  async createConnectionInBackground(connectionId: string) {
    logger.info(
      `${FLOWS.DB_CRAWLING} | ConnectionService.createConnectionInBackground | N/A | Creating connection in background for connectionId: ${connectionId}`,
    );

    //update connection status
    const result = await this.callExternalServiceUpdateConnStatus(connectionId);
    if (!result.isSuccess) {
      return {isSuccess: false, message: result.message};
    }

    //fetch schema
    const schemaResult = await this.callExternalServiceFetchSchema(connectionId);

    if (!schemaResult.isSuccess) {
      return {isSuccess: false, message: schemaResult.message};
    }

    //save schema to db
    const schema = schemaResult.data.schema;
    const createdAt = new Date();

    // Check for existing schema entries to avoid duplicates
    const existingSchemaEntries = await this.connectionSchemaRepository.find({
      where: {
        connectionId: connectionId,
      },
      fields: ['tableName'],
    });

    const existingTableNames = new Set(existingSchemaEntries.map(entry => entry.tableName));

    // Filter out tables that already exist in the database
    const newTables = schema.filter((table: any) => !existingTableNames.has(table.tableName));

    if (newTables.length === 0) {
      logger.info(
        `${FLOWS.DB_CRAWLING} | ConnectionService.createConnectionInBackground | ${connectionId} | No new tables to create, all tables already exist in schema`,
      );
      return {isSuccess: true, message: 'Connection connected and schema already exists'};
    }

    const schemaObjects = newTables.map((table: any) => ({
      connectionId: connectionId,
      tableName: table.tableName,
      columns: table.columns,
      schema: table.schema,
      tableType: table.tableType,
      isSelected: false,
      isCrawled: false,
      createdAt: createdAt,
    }));

    const createSchemaResult = await this.connectionSchemaRepository.createAll(schemaObjects);
    if (!createSchemaResult) {
      return {isSuccess: false, message: `Failed to save schema to db`};
    }

    logger.info(
      `${FLOWS.DB_CRAWLING} | ConnectionService.createConnectionInBackground | ${connectionId} | Created ${newTables.length} new schema entries out of ${schema.length} total tables`,
    );

    //mark connection as connected
    await this.connectionDataRepository.updateByConnectionId(connectionId, {
      connectionStatus: DataSourceConnectionStatus.CONNECTED,
    });

    return {isSuccess: true, message: 'Connection connected and schema fetched successfully'};
  }

  async getConnectionSchema(connectionId: string) {
    try {
      const rawConnectionObject = await this.connectionDataRepository.findOneByConnectionId(connectionId);

      if (!rawConnectionObject) {
        logger.error(
          `${FLOWS.DB_CRAWLING} | ConnectionService.getConnectionSchema | N/A | Raw connection not found for connectionId: ${connectionId}`,
        );
        return {isSuccess: false, message: 'Raw connection not found'};
      }

      const result = await this.connectionSchemaRepository.find({
        where: {
          connectionId: connectionId,
        },
        order: ['tableName ASC'],
        fields: ['tableName', 'isSelected'],
      });

      // if isDataStructureCrawled is in DataStructureCrawlStatus.NOT_INITIATED, then set isSelected to true for better UX
      if (rawConnectionObject.isDataStructureCrawled === DataStructureCrawlStatus.NOT_INITIATED) {
        result.forEach(table => {
          table.isSelected = true;
        });
      }

      if (!result) {
        return {isSuccess: false, message: 'Connection schema not found'};
      } else {
        return {isSuccess: true, data: result, message: 'Connection schema fetched successfully'};
      }
    } catch (error) {
      logger.error(
        `ConnectionService.getConnectionSchema | ${connectionId} | Error fetching connection schema: ${error}`,
      );
      return {isSuccess: false, message: 'Error fetching connection schema'};
    }
  }

  async startDataSourceCrawling(connectionId: string, newSchema: {tableName: string; isSelected: boolean}[]) {
    const rawConnectionObject = await this.connectionDataRepository.findOneByConnectionId(connectionId);

    if (!rawConnectionObject) {
      logger.error(
        `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Raw connection not found for connectionId: ${connectionId}`,
      );
      return {isSuccess: false, message: 'Raw connection not found'};
    }

    //change isDataStructureCrawled to DataStructureCrawlStatus.IN_PROGRESS
    await this.connectionDataRepository.updateByConnectionId(connectionId, {
      isDataStructureCrawled: DataStructureCrawlStatus.IN_PROGRESS,
    });

    //fetch existing schema and find newly selected tables and deselected tables
    const existingSchema = await this.connectionSchemaRepository.find({
      where: {
        connectionId: connectionId,
      },
    });

    if (!existingSchema) {
      logger.error(
        `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Connection schema not found for connectionId: ${connectionId}`,
      );
      return {isSuccess: false, message: 'Connection schema not found'};
    }

    const selectedTableNameInNewSchema = newSchema.filter(table => table.isSelected).map(table => table.tableName);
    const deselectedTableNamesInNewSchema = newSchema.filter(table => !table.isSelected).map(table => table.tableName);

    const selectedTableNamesInExistingSchema = existingSchema
      .filter(table => table.isSelected)
      .map(table => table.tableName);
    const deselectedTableNamesInExistingSchema = existingSchema
      .filter(table => !table.isSelected)
      .map(table => table.tableName);

    const newlySelectedTables = selectedTableNameInNewSchema.filter(
      table => !selectedTableNamesInExistingSchema.includes(table),
    );
    const newlyDeselectedTables = deselectedTableNamesInNewSchema.filter(
      table => !deselectedTableNamesInExistingSchema.includes(table),
    );

    logger.info(
      `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Newly selected tables count: ${newlySelectedTables.length}, for connectionId: ${connectionId}, tables:`,
      newlySelectedTables,
    );
    logger.info(
      `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Newly deselected tables count: ${newlyDeselectedTables.length}, for connectionId: ${connectionId}, tables:`,
      newlyDeselectedTables,
    );

    //update the schema in the database
    if (newlySelectedTables.length > 0) {
      const updateSchemaResultSelected = await this.connectionSchemaRepository.updateMany(
        {connectionId: connectionId, tableName: {$in: newlySelectedTables}},
        {$set: {isSelected: true, isCrawled: false}},
        [],
      );

      if (!updateSchemaResultSelected) {
        logger.error(
          `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Error updating schema in the database for connectionId: ${connectionId}`,
        );
        return {isSuccess: false, message: 'Error updating schema in the database'};
      }
    }

    if (newlyDeselectedTables.length > 0) {
      const updateSchemaResultDeselected = await this.connectionSchemaRepository.updateMany(
        {connectionId: connectionId, tableName: {$in: newlyDeselectedTables}},
        {$set: {isSelected: false}},
        [],
      );

      if (!updateSchemaResultDeselected) {
        logger.error(
          `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Error updating schema in the database for connectionId: ${connectionId}`,
        );
        return {isSuccess: false, message: 'Error updating schema in the database'};
      }

      // remove deselected tables related data from the database
      logger.info(
        `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Deleting deselected tables related relationships and cache data for connectionId: ${connectionId}, tables: ${newlyDeselectedTables}`,
      );
      await this.tableDataService.deleteTableData(
        connectionId,
        newlyDeselectedTables,
        rawConnectionObject?.layerNextConnectionId ?? null,
      );
    }

    //if no any newly selected tables or newly deselected tables, then mark as completed and return
    if (newlySelectedTables.length === 0 && newlyDeselectedTables.length === 0) {
      logger.info(
        `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | No newly selected or deselected tables, marking as completed for connectionId: ${connectionId}`,
      );
      await this.connectionDataRepository.updateByConnectionId(connectionId, {
        isDataStructureCrawled: DataStructureCrawlStatus.COMPLETED,
      });
      return {isSuccess: true, message: 'Schema is already up to date'};
    }

    // trigger data structure crawling
    if (rawConnectionObject) {
      this.tableDataService
        .triggerDataStructureCrawling(rawConnectionObject)
        .then(result => {
          if (result.isSuccess) {
            logger.info(
              `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Data structure crawled successfully for connectionId: ${connectionId}`,
            );
          } else {
            logger.error(
              `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Failed to crawl data structure for connectionId: ${connectionId}`,
            );
          }
        })
        .catch(error => {
          logger.error(
            `${FLOWS.DB_CRAWLING} | ConnectionService.startDataSourceCrawling | N/A | Error while crawling data structure for connectionId: ${connectionId}, error: ${error}`,
          );
        });
    }

    return {isSuccess: true, message: 'Schema updated successfully'};
  }

  /**
   * Calls the external service to update the connection status of a data source.
   * @param connectionId the id of the data source to update
   * @returns a promise that resolves when the call is complete
   */
  private async callExternalServiceUpdateConnStatus(connectionId: string) {
    logger.info(`ConnectionService.callExternalServiceUpdateConnStatus | ${connectionId} | Updating connection status`);
    const url = `${PYTHON_HOST}/internal/connection/update/update_connection_status`;
    try {
      const result = await Axios({
        url,
        method: 'POST',
        data: {
          dataSourceId: connectionId,
        },
      });

      if (result.status === 200 && result.data.isSuccess === true) {
        logger.info(
          `ConnectionService.callExternalServiceUpdateConnStatus | ${connectionId} | Connection status updated successfully`,
        );
        return {
          isSuccess: true,
          message: 'Connection status updated successfully',
        };
      } else {
        logger.error(
          `ConnectionService.callExternalServiceUpdateConnStatus | ${connectionId} | Error updating connection status: ${result.statusText}`,
        );
        return {
          isSuccess: false,
          message: 'Error updating connection status',
        };
      }
    } catch (error) {
      logger.error(
        `ConnectionService.callExternalServiceUpdateConnStatus | ${connectionId} | Error updating connection status: ${error}`,
      );
      return {
        isSuccess: false,
        message: 'Error updating connection status',
      };
    }
  }

  private async callExternalServiceFetchSchema(connectionId: string) {
    logger.info(`ConnectionService.callExternalServiceFetchSchema | ${connectionId} | Fetching schema`);
    const url = `${PYTHON_HOST}/internal/connection/fetch/schema`;
    try {
      const result = await Axios({
        url,
        method: 'POST',
        data: {
          dataSourceId: connectionId,
        },
      });

      if (result.status === 200 && result.data.isSuccess === true) {
        logger.info(`ConnectionService.callExternalServiceFetchSchema | ${connectionId} | Schema fetched successfully`);
        return {
          isSuccess: true,
          message: 'Schema fetched successfully',
          data: result.data.data,
        };
      } else {
        logger.error(
          `ConnectionService.callExternalServiceFetchSchema | ${connectionId} | Error fetching schema: ${result.statusText}`,
        );
        return {
          isSuccess: false,
          message: 'Error fetching schema',
        };
      }
    } catch (error) {
      logger.error(
        `ConnectionService.callExternalServiceFetchSchema | ${connectionId} | Error fetching schema: ${error}`,
      );
      return {isSuccess: false, message: 'Error fetching schema'};
    }
  }

  /**
   * Fetches the list of source connections
   * @param pageIndex the page index
   * @param pageSize the page size
   * @returns a promise that resolves to an object with the following properties:
   * - isSuccess: a boolean indicating whether the operation was successful
   * - data: an object with the properties
   */
  async getsourceConnectionList(pageIndex: number = 0, pageSize: number = 20) {
    let sources: {
      connectionId: string;
      connectionName: string;
      sourceType: ConnectionSourceType;
      connectionStatus: DataSourceConnectionStatus;
      connectionType: DataSourceConnectionType;
      isEditable: boolean;
      lastConnectedTime?: string;
      isDataStructureCrawled?: DataStructureCrawlStatus;
      layerNextConnectionId?: string;
      itemCount: string;
      drawerData?: {
        title: string;
        value: string;
      }[];
      updateStatus?: DataSourceUpdateStatus;
    }[] = [];

    let sourceList: {
      sourceName: string;
      type: ConnectionSourceType;
      connectionId: string;
      connectionStatus: DataSourceConnectionStatus;
      lastConnectedAt: Date;
      createdAt: string;
      isDataStructureCrawled?: DataStructureCrawlStatus;
      layerNextConnectionId?: string;
      connectionType: DataSourceConnectionType;
      isConnectionError: boolean;
      connectionError?: string;
      itemCount: string;
      updateStatus?: DataSourceUpdateStatus;
    }[] = [];

    try {
      sourceList = await this.connectionDataRepository.aggregate([
        {
          $match: {
            connectionType: DataSourceConnectionType.RAW_DATABASE,
          },
        },
        {
          $project: {
            sourceName: 1,
            type: 1,
            connectionId: 1,
            connectionStatus: 1,
            lastConnectedAt: 1,
            createdAt: 1,
            isDataStructureCrawled: 1,
            layerNextConnectionId: 1,
            connectionType: 1,
            connectionError: 1,
            updateStatus: 1,
          },
        },
        // {$skip: pageIndex * pageSize},
        // {$limit: pageSize},
      ]);
    } catch (error) {
      logger.error(`ConnectionService.getSourceConnectionList | N/A |Error fetching connections: ${error}`);
      return {isSuccess: false, message: error};
    }

    for (let connection of sourceList) {
      //table count fetching
      let tableDataCounts = {configuredTables: 0, unconfiguredTables: 0, totalTables: 0};

      try {
        //total raw tables
        tableDataCounts.totalTables = await this.tableDataService.getTableCount(connection.connectionId);

        //if layernext connection id exists configured table counts
        if (!connection.layerNextConnectionId || connection.layerNextConnectionId === '') {
          tableDataCounts.configuredTables = 0;
        } else {
          tableDataCounts.configuredTables = await this.tableDataService.getTableCount(
            connection.layerNextConnectionId,
          );
        }

        //unconfigured tables
        tableDataCounts.unconfiguredTables = tableDataCounts.totalTables - tableDataCounts.configuredTables;
      } catch (error) {
        logger.error(
          `ConnectionService.getSourceConnectionList | N/A |Error fetching table counts for connection ${connection.connectionId}: ${error}`,
        );
        tableDataCounts = {configuredTables: 0, unconfiguredTables: 0, totalTables: 0};
      }

      let lastConnectedAt = connection.lastConnectedAt ? timeAgo(new Date(connection.lastConnectedAt)) : 'N/A';

      //connection error
      let isConnErr = false;
      let connErr = '';
      if (connection.connectionError && connection.connectionError !== '') {
        isConnErr = true;
        connErr = connection.connectionError;
      }

      sources.push({
        connectionId: connection.connectionId,
        connectionName: connection.sourceName,
        sourceType: connection.type,
        connectionStatus: connection.connectionStatus,
        connectionType: connection.connectionType,
        isEditable: true,
        lastConnectedTime: lastConnectedAt,
        isDataStructureCrawled: connection.isDataStructureCrawled,
        layerNextConnectionId: connection.layerNextConnectionId,
        itemCount: tableDataCounts.totalTables.toString() || '0',
        drawerData: [
          {
            title: 'Total configured Tables',
            value: tableDataCounts?.configuredTables?.toString() || '0',
          },
          {
            title: 'Total unconfigured Tables',
            value: tableDataCounts?.unconfiguredTables?.toString() || '0',
          },
          ...(isConnErr
            ? [
                {
                  title: 'Connection Error',
                  value: connErr,
                },
              ]
            : []),
        ],
        updateStatus: connection.updateStatus,
      });
    }

    //TODO: need to change below after finished enabling adding storages from frontend
    // get storages in .env from db
    let initialCrawls = [];
    try {
      initialCrawls = await this.dataCrawlRepository.find({
        where: {
          isInitialCrawl: true,
        },
      });
    } catch (error) {
      logger.error(`ConnectionService.getSourceConnectionList | N/A |Error fetching initial crawls: ${error}`);
      return {isSuccess: false, message: error};
    }

    if (initialCrawls && Array.isArray(initialCrawls) && initialCrawls.length > 0) {
      let _connectionId = `${uuidV4()}`;
      for (let storage of initialCrawls) {
        sources.unshift({
          connectionId: _connectionId,
          connectionName: storage.storageName?.split('/').pop() || '',
          sourceType: storage.storageType,
          connectionStatus:
            storage.status == CrawlingStatus.FAILED
              ? DataSourceConnectionStatus.DISCONNECTED
              : DataSourceConnectionStatus.CONNECTED,
          connectionType: DataSourceConnectionType.STORAGE,
          isEditable: false,
          lastConnectedTime: storage.crawlFinishedAt ? timeAgo(new Date(storage.crawlFinishedAt)) : 'N/A',
          drawerData: [],
          layerNextConnectionId: '',
          itemCount: storage.crawledFileCount.toString() || '0',
        });
      }
    }

    return {
      isSuccess: true,
      data: {
        sources: sources,
      },
      message: '',
    };
  }

  /**
   * Fetches the connection credential details for a given connection ID.
   *
   * @param connectionId {string} - The ID of the connection to fetch details for.
   * @returns {Promise<{isSuccess: boolean, data?: {connectionId?: string, sourceName?: string, connectionCredentials?: ConnectionCredentials}, message: string}>}
   * An object containing the isSuccess status, optional connection details, and a message.
   * Returns an error message if the connection is not found or if an error occurs.
   */
  async fetchConnectionDetails(connectionId: string) {
    try {
      let res = await this.connectionDataRepository.findOneByConnectionId(connectionId);

      if (!res) {
        return {isSuccess: false, message: 'Connection not found for id: ' + connectionId};
      }

      const connDetails: {
        connectionId?: string;
        sourceName?: string;
        connectionCredentials?: ConnectionCredentials;
      } = {
        connectionId: res.connectionId,
        sourceName: res.sourceName,
        connectionCredentials: res.connectionCredentials,
      };

      return {isSuccess: true, data: connDetails, message: ''};
    } catch (error) {
      logger.error(`ConnectionService.fetchConnectionDetails | N/A |Error fetching connection details: ${error}`);
      return {isSuccess: false, message: error.toString()};
    }
  }

  /**
   * Updates a connection in the database with new credentials. If the connection
   * is of type BIGQUERY, it handles writing the credentials to a file. It also
   * updates the corresponding Layernext connection if it exists.
   *
   * @param connectionId {string} - The ID of the connection to update.
   * @param connectionCredentials {ConnectionCredentials} - The new connection credentials.
   * @param credFileContent {any} - Optional content for writing to a file if the connection
   * type is BIGQUERY.
   * @returns {Promise<{isSuccess: boolean, message: string}>} - A promise that resolves to an object
   * containing the isSuccess status, and a message.
   * Returns an error message if the connection is not found or if an error occurs.
   */
  async updateConnection(connectionId: string, connectionCredentials: ConnectionCredentials, credFileContent?: any) {
    try {
      const existingConnection = await this.connectionDataRepository.findOne({
        where: {
          connectionId: connectionId,
        },
      });

      if (!existingConnection) {
        logger.error(
          `ConnectionService.updateConnection | ${connectionId} | Connection not found for id: ${connectionId}`,
        );
        return {isSuccess: false, message: 'Connection not found for id: ' + connectionId};
      }

      //if connection is crawling then return error
      if (existingConnection.isDataStructureCrawled === DataStructureCrawlStatus.IN_PROGRESS) {
        logger.warn(
          `ConnectionService.updateConnection | ${connectionId} | Connection is currently crawling for id: ${connectionId}`,
        );
        return {isSuccess: false, message: 'Data source is currently crawling'};
      }

      //change the connectionStatus instantly to connecting
      await this.connectionDataRepository.updateByConnectionId(connectionId, {
        connectionStatus: DataSourceConnectionStatus.CONNECTING,
      });

      //if sourceType is big query then handle the file write operations
      if (existingConnection.type === ConnectionSourceType.BIGQUERY && credFileContent) {
        let cred = JSON.stringify(credFileContent);

        const filePath = path.join('/app/config', `bigquery-${connectionId}.json`);
        if (fs.existsSync(filePath)) {
          fs.writeFileSync(filePath, cred);
        } else {
          const directoryPath = '/app/config';
          if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, {recursive: true});
          }
          // Write credentials to a file
          fs.writeFileSync(filePath, cred);
        }

        connectionCredentials.credentials_path = filePath;
      }

      //convert the port in the connectionCredentials to int
      if (connectionCredentials.port) {
        connectionCredentials.port = Number(connectionCredentials.port);
      }

      // update existing raw connection
      let res = await this.connectionDataRepository.updateByConnectionId(connectionId, {
        connectionCredentials: connectionCredentials,
      });

      if (!res) {
        logger.warn(
          `ConnectionService.updateConnection | ${connectionId} | No update for raw connection: ${connectionId}`,
        );
      }

      //call create connection in background to ping the database and fetch schema and save to db
      this.createConnectionInBackground(connectionId)
        .then(result => {
          if (!result.isSuccess) {
            logger.error(
              `${FLOWS.DB_CRAWLING} | ConnectionService.updateConnection | N/A | Error creating connection in background for connectionId: ${connectionId}, error: ${result.message}`,
            );
          }
        })
        .catch(error => {
          logger.error(
            `${FLOWS.DB_CRAWLING} | ConnectionService.updateConnection | N/A | Error creating connection in background for connectionId: ${connectionId}, error: ${error}`,
          );
        });

      // update layernext connection if exists
      if (existingConnection?.layerNextConnectionId) {
        let res = await this.connectionDataRepository.updateByConnectionId(existingConnection.layerNextConnectionId, {
          connectionCredentials: connectionCredentials,
        });

        if (!res) {
          logger.warn(
            `ConnectionService.updateConnection | ${connectionId} | No update for layernext connection: ${existingConnection.layerNextConnectionId}`,
          );
        }
      }

      logger.info(
        `ConnectionService.updateConnection | ${connectionId} | Connection updated successfully for id: ${connectionId}`,
      );
      return {isSuccess: true, message: 'Updated successfully for id: ' + connectionId};
    } catch (error) {
      logger.error(`ConnectionService.updateConnection | N/A |Error updating connection: ${error}`);
      return {isSuccess: false, message: error.toString()};
    }
  }

  /**
   * Fetches the total table counts for raw and Layernext database connections.
   *
   * This method retrieves connection IDs for both raw database and Layernext database
   * connection types. It then calculates the total number of tables associated with
   * each connection type by calling the `getCountByConnectionIds` method from the
   * `tableDataRepository`.
   *
   * @returns {Promise<{isSuccess: boolean, data: {crawledTables: number, configuredTables: number}, message: string}>}
   * A promise that resolves to an object containing the isSuccess status, the total
   * number of tables for raw and Layernext connections, and a message.
   * Returns an error message if there is an issue fetching the table counts.
   */
  async fetchTotalTableCounts() {
    try {
      //fetch raw connection ids
      let rawConnectionIds = await this.connectionDataRepository.find({
        where: {
          connectionType: DataSourceConnectionType.RAW_DATABASE,
        },
        fields: {
          connectionId: true,
        },
      });

      let connIds = rawConnectionIds.map(item => item.connectionId).filter((id): id is string => id !== undefined);

      //fetch table count where connectionId with connection id
      let crawledTables = await this.tableDataRepository.getCountByConnectionIds(connIds);

      //fetch layernext connection ids
      let layerNextConnectionIds = await this.connectionDataRepository.find({
        where: {
          connectionType: DataSourceConnectionType.LAYERNEXT_DATABASE,
        },
        fields: {
          connectionId: true,
        },
      });

      let lnConnIds = layerNextConnectionIds
        .map(item => item.connectionId)
        .filter((id): id is string => id !== undefined);

      let configuredTables = await this.tableDataRepository.getCountByConnectionIds(lnConnIds);

      return {
        isSuccess: true,
        data: {
          crawledTables: crawledTables,
          configuredTables: configuredTables,
        },
        message: '',
      };
    } catch (error) {
      logger.error(`ConnectionService.fetchTotalTableCounts | N/A |Error fetching total table counts: ${error}`);
      return {isSuccess: false, message: error.toString()};
    }
  }
}

export const CONNECTIONS_SERVICE = BindingKey.create<ConnectionService>('service.connectionService');
