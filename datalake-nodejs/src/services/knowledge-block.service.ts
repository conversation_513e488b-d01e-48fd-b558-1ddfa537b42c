import {BindingKey, /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {logger} from '../config';
import {KnowledgeBlockRepository} from '../repositories/knowledge-block.repository';
import {KnowledgeTreeRepository} from '../repositories/knowledge-tree.repository';
import {SystemDataRepository} from '../repositories/system-data.repository';
import {FLOWS} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@injectable({scope: BindingScope.TRANSIENT})
export class KnowledgeBlockService {
  constructor(
    @repository(KnowledgeBlockRepository)
    private knowledgeBlockRepository: KnowledgeBlockRepository,
    @repository(KnowledgeTreeRepository)
    private knowledgeTreeRepository: KnowledgeTreeRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
  ) {}

  /**
   * Helper function to extract searchable text from any value
   * @param value - The value to extract text from
   * @param visited - Set to prevent circular references
   * @returns A string representation suitable for searching
   */
  private extractSearchableText(value: any, visited: Set<any> = new Set()): string {
    // Handle null/undefined
    if (value === null || value === undefined) {
      return '';
    }

    // Handle strings
    if (typeof value === 'string') {
      return value;
    }

    // Handle numbers and booleans
    if (typeof value === 'number' || typeof value === 'boolean') {
      return value.toString();
    }

    // Handle BigInt
    if (typeof value === 'bigint') {
      return value.toString();
    }

    // Handle Date objects
    if (value instanceof Date) {
      return value.toISOString();
    }

    // Handle functions (skip them as they're not searchable)
    if (typeof value === 'function') {
      return '';
    }

    // Handle Symbols (skip them as they're not searchable)
    if (typeof value === 'symbol') {
      return '';
    }

    // Handle arrays
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return '';
      }
      // Check for circular references in arrays too
      if (visited.has(value)) {
        return '';
      }
      visited.add(value);

      const result = value.map(item => this.extractSearchableText(item, visited)).join(' ');

      // Don't delete from visited - keep it to prevent future circular references
      return result;
    }

    // Handle objects (but not null, which is already handled above)
    if (typeof value === 'object' && value !== null) {
      // Check for circular references
      if (visited.has(value)) {
        return '';
      }
      visited.add(value);

      const textParts: string[] = [];
      for (const key in value) {
        if (value.hasOwnProperty(key)) {
          // Include the key itself
          textParts.push(key);
          // Include the value
          const text = this.extractSearchableText(value[key], visited);
          if (text) {
            textParts.push(text);
          }
        }
      }

      // Don't delete from visited - keep it to prevent future circular references
      return textParts.join(' ');
    }

    // Fallback for any other type
    return String(value);
  }

  /**
   * Create knowledge blocks
   * @param knowledgeBlocks - The knowledge blocks to create
   * @returns The result of the creation
   */
  async createKnowledgeBlocks(knowledgeBlocks: {[key: string]: any}[], knowledgeTree: {[key: string]: any}) {
    if (!knowledgeBlocks || knowledgeBlocks.length === 0) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | No knowledge blocks to create`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_BLOCK_CREATE_FAILED}`};
    }

    if (!knowledgeTree) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | No knowledge tree to create`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_BLOCK_CREATE_FAILED}`};
    }

    try {
      //remove existing all knowledge blocks
      let deletedKnowledgeBlockResult = await this.knowledgeBlockRepository.deleteAll();

      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Deleted ${deletedKnowledgeBlockResult.count} knowledge blocks`,
      );

      //remove existing all knowledge trees
      let deletedKnowledgeTreeResult = await this.knowledgeTreeRepository.deleteAll();

      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Deleted ${deletedKnowledgeTreeResult.count} knowledge trees`,
      );

      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Creating ${knowledgeBlocks.length} knowledge blocks`,
      );
      let formattedKnowledgeBlocks = knowledgeBlocks.map(knowledgeBlock => {
        // let searchString = this.extractSearchableText(knowledgeBlock);

        let searchString = '';

        for (const key in knowledgeBlock) {
          if (knowledgeBlock.hasOwnProperty(key)) {
            searchString += this.extractSearchableText(knowledgeBlock[key]);
          }
        }

        return {
          data: knowledgeBlock,
          searchString: searchString,
          createdAt: new Date(),
          isEnabled: true,
        };
      });

      const res = await this.knowledgeBlockRepository.createAll(formattedKnowledgeBlocks);
      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Created ${res.length} knowledge blocks`,
      );

      //save knowledge tree
      let knowledgeTreeResult = await this.saveKnowledgeTree(knowledgeTree);
      if (!knowledgeTreeResult.isSuccess) {
        logger.error(
          `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Failed to save knowledge tree. Error: ${knowledgeTreeResult.message}`,
        );
        return {isSuccess: false, message: knowledgeTreeResult.message};
      }

      //update system data metaDataLastUpdatedAt
      await this.systemDataRepository.updateOneSetData({}, {metaDataLastUpdatedAt: new Date()}, []);

      return {isSuccess: true};
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.createKnowledgeBlocks | N/A | Failed to create knowledge blocks. Error: ${error.message}`,
      );
      return {isSuccess: false, message: error.message};
    }
  }

  async saveKnowledgeTree(knowledgeTrees: {[key: string]: object[]}) {
    try {
      logger.info(`${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.saveKnowledgeTree | N/A | Saving knowledge tree`);
      let knowledgeTreeList = [];
      for (const key in knowledgeTrees) {
        if (knowledgeTrees.hasOwnProperty(key)) {
          knowledgeTreeList.push({
            parent: key,
            nodes: knowledgeTrees[key],
            createdAt: new Date(),
          });
        }
      }

      let knowledgeTreesCreatedResult = await this.knowledgeTreeRepository.createAll(knowledgeTreeList);
      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.saveKnowledgeTree | N/A | Saved ${knowledgeTreesCreatedResult.length} knowledge trees`,
      );
      return {isSuccess: true};
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.saveKnowledgeTree | N/A | Failed to save knowledge tree. Error: ${error.message}`,
      );
      return {isSuccess: false, message: error.message};
    }
  }

  /**
   * Get knowledge blocks
   * @returns The result of the retrieval
   */
  async getKnowledgeBlocks(pageSize: number, pageIndex: number, searchKey?: string) {
    try {
      let filter: any = {};

      if (searchKey) {
        filter.searchString = {regexp: new RegExp(searchKey, 'i')};
      }

      const knowledgeBlocks = await this.knowledgeBlockRepository.find({
        where: filter,
        skip: pageIndex * pageSize,
        limit: pageSize,
        order: ['_id ASC'],
      });

      let formattedKnowledgeBlocks = knowledgeBlocks.map(knowledgeBlock => {
        return {
          id: knowledgeBlock._id,
          data: knowledgeBlock.data,
          isEnabled: knowledgeBlock.isEnabled,
          createdAt: knowledgeBlock.createdAt,
        };
      });

      return {isSuccess: true, data: formattedKnowledgeBlocks};
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.getKnowledgeBlocks | N/A | Failed to get knowledge blocks. Error: ${error.message}`,
      );
      return {isSuccess: false, message: error.message};
    }
  }

  /**
   * Set visibility of knowledge block
   * @param knowledgeBlockId - The id of the knowledge block
   * @param isEnabled - The visibility of the knowledge block
   * @returns The result of the visibility setting
   */
  async setVisibility(knowledgeBlockId: string, isEnabled: boolean) {
    try {
      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.setVisibility | N/A | Request to set visibility of knowledge block ${knowledgeBlockId} to ${isEnabled}`,
      );
      await this.knowledgeBlockRepository.updateById(knowledgeBlockId, {isEnabled: isEnabled});
      logger.info(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.setVisibility | N/A | Set visibility of knowledge block ${knowledgeBlockId} to ${isEnabled}`,
      );
      let userFriendlyMessage = isEnabled ? 'enabled' : 'disabled';

      //update system data metaDataLastUpdatedAt
      await this.systemDataRepository.updateOneSetData({}, {metaDataLastUpdatedAt: new Date()}, []);

      return {
        isSuccess: true,
        data: {success: true, message: `Knowledge block has been ${userFriendlyMessage} successfully`},
      };
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockService.setVisibility | N/A | Failed to set visibility of knowledge block. Error: ${error.message}`,
      );
      return {isSuccess: false, message: error.message};
    }
  }

  async getKnowledgeBlocksWithTree() {
    const knowledgeBlocks = await this.knowledgeBlockRepository.find({
      where: {isEnabled: true},
    });
    const knowledgeTrees = await this.knowledgeTreeRepository.find({});

    let knowledge_blocks = [];
    let knowledge_tree: {[key: string]: object[]} = {};

    for (const knowledgeBlock of knowledgeBlocks) {
      knowledge_blocks.push(knowledgeBlock.data);
    }

    for (const knowledgeTree of knowledgeTrees) {
      knowledge_tree[knowledgeTree.parent] = knowledgeTree.nodes;
    }

    let data = {
      knowledge_blocks: knowledge_blocks,
      knowledge_tree: knowledge_tree,
    };

    return data;
  }
}

export const KNOWLEDGE_BLOCK_SERVICE = BindingKey.create<KnowledgeBlockService>('services.KnowledgeBlockService');
