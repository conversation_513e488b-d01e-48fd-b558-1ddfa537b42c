/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Propagate custom metadata for parents and children
 */

/**
 * @class MetaFieldPropagatorService
 * Propagate custom metadata upon a meta object update
 * @description  Propagate custom metadata for parents and children
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  EditMetaDataInputFormat,
  EditMetaFieldInputFormat,
  GetMetaFieldsOfSystemRequest,
  InputField,
  InputFieldTypes,
  MetaDataKeyInputList,
  MetaField,
  MetaFieldSuggestionFormat,
} from '../models';
import {
  ContentType,
  DetailTabKeyVal,
  Explore_API_TYPE,
  MetaData,
  TagsAndMetaDataConfig,
} from '../models/meta-data.model';
import {MetaFieldRepository, MetaTagRepository, QueryOptionRepository, SystemDataRepository} from '../repositories';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {DATALAKE_EXPLORER_SERVICE, DatalakeExplorerService} from './datalake-explorer.service';
import {InputMetadataFeedService} from './input-metadata-feed.service';
import {META_DATA_SERVICE, MetaDataService} from './meta-data.service';
import {STATS_CALCULATION_SERVICE, StatsCalculationService} from './stats-calculation.service';

@injectable({scope: BindingScope.TRANSIENT})
export class MetaFieldPropagatorService {
  constructor(
    /* Add @inject to inject parameters */
    @repository('MetaDataRepository') private metaDataRepository: MetaDataRepository,
    @repository(QueryOptionRepository) private queryOptionRepository: QueryOptionRepository,
    @repository(SystemDataRepository) private systemDataRepository: SystemDataRepository,
    @repository(MetaFieldRepository)
    private metaFieldRepository: MetaFieldRepository,
    @inject(META_DATA_SERVICE)
    private metaDataService: MetaDataService,
    @inject(STATS_CALCULATION_SERVICE) private statsCalculationService: StatsCalculationService,
    @inject(DATALAKE_EXPLORER_SERVICE) private datalakeExplorerService: DatalakeExplorerService,
    @service(InputMetadataFeedService) private inputMetadataFeedService: InputMetadataFeedService,
    // @inject(OBJECT_META_UPDATER_SERVICE) private objectMetaUpdaterService: ObjectMetaUpdaterService,
    @repository(MetaTagRepository)
    private metaTagRepository: MetaTagRepository,
  ) {}

  /*
   * Add service methods here
   */

  /**
   * Sub method - to create the metaData object filter for tag update
   */
  async generateFilterForTagUpdate(id: string, isDeleteOperation?: boolean, updateSelfOnly?: boolean) {
    let filterParams: any = {};

    let oid = new ObjectId(id);

    let metaObject = await this.metaDataRepository.findById(id);
    let parentsOfObject: string[] | ObjectId[] = [];

    if (isDeleteOperation) {
      /** then parents will not be updated --------------- need to implement elswhere ?????????????????????*/
    }
    // else if (metaObject.isLogical) {/** then only itself is updated*/}
    else {
      // then only, update parents
      parentsOfObject = metaObject.collectionId ? [metaObject.collectionId] : [];
      // if metaObject has vCollectionIdList, then add them to parents
      if (
        metaObject.vCollectionIdList &&
        Array.isArray(metaObject.vCollectionIdList) &&
        metaObject.vCollectionIdList.length > 0
      ) {
        parentsOfObject = [...parentsOfObject, ...metaObject.vCollectionIdList];
      }
    }

    let updatableObjectList = [id, ...parentsOfObject];

    let updatableOIdList = updatableObjectList.map(id => new ObjectId(id.toString()));

    if (updateSelfOnly) {
      // then only update itself and parents
      filterParams = {
        $or: [
          {_id: {$in: updatableOIdList}}, //itself and parents
        ],
      };
    } else {
      // update itself and children and parents
      filterParams = {
        $or: [
          {_id: {$in: updatableOIdList}}, //itself and parents
          {parentList: oid}, //children
        ],
      };

      // update itself and parentlist
      // let metaObject = await this.metaDataRepository.findById(id)

      // // determine the content type
      // if([ContentType.VIDEO_COLLECTION, ContentType.IMAGE_COLLECTION].includes(metaObject.objectType)){
      //   // update the children and itself
      // }
      // else if(metaObject.objectType == ContentType.VIDEO){
      //   // update child images
      //   // update
      // }

      // switch (metaObject.objectType) {
      //   case ContentType.IMAGE:
      //     // then only add to itself
      //     //
      //     break;
      //   case ContentType.VIDEO:
      // }
    }
    return filterParams;
  }

  /**
   * delete value in an array fields of a file or a collection
   * @param id string (of collection or file)
   * @param fieldName string (field key to be updated)
   * @param value string[] (values to be deleted)
   * @param updateSelfOnly boolean (wheather to propagate to below or not)
   */
  async deleteArrayFieldValueWithCompletePropagation(
    id: string,
    fieldName: string,
    values: string[],
    updateSelfOnly?: boolean,
  ) {
    if (id && fieldName && values) {
      /** then ok */
    } else {
      logger.warn(
        `Add values tags | MetaFieldPropagatorService.deleteArrayFieldValueWithCompletePropagation | N/A | invalid id`,
      );
      return; //HttpErrors[422]
    }

    let filterParams = await this.generateFilterForTagUpdate(id, true, updateSelfOnly);

    let pullParams = {[fieldName]: {$in: values}};

    // update db
    await this.metaDataRepository.updateManyRemoveFromList(filterParams, pullParams, []);

    // if (fieldName == "Tag") {
    //   // delete in parent collections if required
    // }
  }

  /**
   * Handle edit metadata save updates
   * @param id
   * @param updates : MetaData
   * @param updateSelfOnly boolean (wheather to propagate to below or not)
   */
  // async handleUpdateMetadataOnEdit(
  //   id: string,
  //   updates: Partial<MetaData>,
  //   deleteFields?: string[],
  //   updateSelfOnly?: boolean,
  // ) {
  //   // // seperate array fields and updates
  //   // let metaUpdats: Partial<MetaData> = {}
  //   // let metaPushList: Partial<MetaData> = {}
  //   // for (const [key, value] of Object.entries(updates)) {
  //   //   if (Array.isArray(value)) {
  //   //     // metaPushList[key] = value
  //   //   }
  //   //   else {
  //   //     metaUpdats[key] = value
  //   //   }
  //   // }

  //   // filter out system meta data if present (only get custom metadata)
  //   let customFieldArr = this.metaDataRepository.filterCustomFieldsFromMetaData(updates);

  //   // determine object type
  //   let existingObject = await this.metaDataRepository.findById(id);
  //   let teamId: string | undefined = undefined;
  //   if (existingObject.teamId) teamId = existingObject.teamId as unknown as string;
  //   if (!teamId) {
  //     logger.error(
  //       `Update - edit metadata | MetaFieldPropagatorService.handleUpdateMetadataOnEdit | N/A | couldn't find team for metadata id: ${id} `,
  //     );
  //     return;
  //   }
  //   if (!existingObject) {
  //     logger.warn(
  //       `Update - edit metadata | MetaFieldPropagatorService.handleUpdateMetadataOnEdit | N/A | meta data does not exist`,
  //     );
  //     return;
  //   }

  //   // handle delete fields
  //   // seperate deletes
  //   // let deleteFields: string[] = []
  //   // let updateKeysArr = customFieldArr.map(obj => obj.key)
  //   // let customFieldsOfExisting = this.metaDataRepository.filterCustomFieldsFromMetaData(existingObject.customMeta)
  //   // for (let field of customFieldsOfExisting) {
  //   //   if (!updateKeysArr.includes(field.key)) {
  //   //     // then the field is deleted
  //   //     deleteFields.push(field.key)
  //   //   }
  //   // }

  //   // delete the deleted fields
  //   if (isArrayWithLength(deleteFields)) {
  //     await this.handleMetadataFieldDelete(id, deleteFields || []);
  //   }

  //   // handle metadata edit or add
  //   let isEditable: boolean;

  //   if (
  //     [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(existingObject.objectType) &&
  //     existingObject.isOriginalUploadFile
  //     // || existingObject.isLogical
  //   ) {
  //     isEditable = true;
  //   } else {
  //     isEditable = false;
  //   }
  //   for (let customField of customFieldArr) {
  //     if (
  //       existingObject[customField.key] === undefined ||
  //       (existingObject[customField.key] !== undefined &&
  //         isEditable &&
  //         existingObject[customField.key] != customField.value)
  //     ) {
  //       // then property either does not exist (newly added) or changed (edited)

  //       // if (existingObject.isLogical) {
  //       //   // update property in itself only
  //       //   //await this.metaDataRepository.updateById(id, {[customField.key]: customField.value})
  //       //   await this.metaDataRepository.updateOneSet(
  //       //     {_id: new ObjectId(id)},
  //       //     {['customMeta.' + customField.key]: customField.value},
  //       //     [],
  //       //   );

  //       //   this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //       //     SearchQueryRootGroup.METADATA,
  //       //     customField.key,
  //       //     teamId,
  //       //     id,
  //       //     [customField.value],
  //       //   );
  //       // }
  //       if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(existingObject.objectType)) {
  //         // update property in itself
  //         //await this.metaDataRepository.updateById(id, {['customMeta.' + customField.key]: customField.value})
  //         await this.metaDataRepository.updateOneSet(
  //           {_id: new ObjectId(id)},
  //           {['customMeta.' + customField.key]: customField.value},
  //           [],
  //         );

  //         if (existingObject.frameCollectionId) {
  //           // update property in  child images in case of a video
  //           await this.metaDataRepository.updateManySet(
  //             {
  //               collectionId: new ObjectId(existingObject.frameCollectionId),
  //             },
  //             {['customMeta.' + customField.key]: customField.value},
  //             [],
  //           );

  //           // update property array of frame collection in case of the video
  //           await this.metaDataRepository.updateManySet(
  //             {
  //               _id: new ObjectId(existingObject.frameCollectionId),
  //             },
  //             {['customMeta.' + customField.key]: [customField.value]},
  //             [],
  //           );

  //           let distinctValues = await this.metaDataRepository.getAllDistinctValuesForCustomFieldInCollection(
  //             existingObject.frameCollectionId,
  //             customField.key,
  //           );
  //           this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //             SearchQueryRootGroup.METADATA,
  //             customField.key,
  //             teamId,
  //             existingObject.frameCollectionId as unknown as string,
  //             distinctValues,
  //           );
  //         }

  //         // update parent collection of the entity
  //         if (existingObject.collectionId) {
  //           let distinctValues = await this.metaDataRepository.getAllDistinctValuesForCustomFieldInCollection(
  //             existingObject.collectionId.toString(),
  //             customField.key,
  //           );

  //           // update in collection
  //           await this.metaDataRepository.updateOneSet(
  //             {_id: new ObjectId(existingObject.collectionId as unknown as string)},
  //             {['customMeta.' + customField.key]: distinctValues},
  //             [],
  //           );
  //           this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //             SearchQueryRootGroup.METADATA,
  //             customField.key,
  //             teamId,
  //             existingObject.collectionId as unknown as string,
  //             distinctValues,
  //           );
  //         }
  //       } else if (
  //         [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
  //           existingObject.objectType,
  //         )
  //         // && !existingObject.isLogical
  //       ) {
  //         //should be only an "add field" situation

  //         // update property in itself
  //         //await this.metaDataRepository.updateById(id, {[customField.key]: [customField.value]})
  //         await this.metaDataRepository.updateOneSet(
  //           {_id: new ObjectId(id)},
  //           {['customMeta.' + customField.key]: [customField.value]},
  //           [],
  //         );

  //         //add to search query options
  //         await this.queryOptionRepository.updateQueryOption(
  //           SearchQueryRootGroup.METADATA,
  //           {[customField.key]: customField.value},
  //           true,
  //           teamId,
  //           id,
  //         );

  //         if (!updateSelfOnly) {
  //           // update child images and/or videos
  //           await this.metaDataRepository.updateManySet(
  //             {
  //               $and: [
  //                 {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}},
  //                 {
  //                   $or: [{vCollectionIdList: new ObjectId(id)}, {parentList: new ObjectId(id)}],
  //                 },
  //               ],
  //             },
  //             {['customMeta.' + customField.key]: customField.value},
  //             [],
  //           );

  //           if (existingObject.objectType == ContentType.VIDEO_COLLECTION) {
  //             // then should add the new field to frame collections of child videos
  //             await this.metaDataRepository.updateManySet(
  //               {
  //                 $and: [
  //                   {objectType: ContentType.IMAGE_COLLECTION},
  //                   {
  //                     $or: [{vCollectionIdList: new ObjectId(id)}, {parentList: new ObjectId(id)}],
  //                   },
  //                 ],
  //               },
  //               {['customMeta.' + customField.key]: [customField.value]}, // this field is an array in collection. So it has handled separately
  //               [],
  //             );

  //             //build search query option for all derived image collection
  //             let allVideos = await this.metaDataRepository.find({
  //               where: {
  //                 collectionId: id,
  //               },
  //               fields: {
  //                 frameCollectionId: true,
  //               },
  //             });
  //             for (let _video of allVideos) {
  //               if (_video.frameCollectionId) {
  //                 await this.queryOptionRepository.updateQueryOption(
  //                   SearchQueryRootGroup.METADATA,
  //                   {[customField.key]: customField.value},
  //                   true,
  //                   teamId,
  //                   _video.frameCollectionId as unknown as string,
  //                 );
  //               }
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }
  // }

  /**
   * to update metaFieldList of a collection head
   * @param existingObj existing metadata object
   * @param updateCustomMeta new custom metadata object
   * @param fieldConfig field configuration object
   */
  async validateMetaFieldsAndSaveFieldConfigs(existingObj: MetaData, updateInput: EditMetaDataInputFormat) {
    let updatedCustomMetaInfo = updateInput.updates;
    let fieldConfig = updateInput.fieldConfig;

    if (fieldConfig && updatedCustomMetaInfo) {
      // validate and get metaFieldList
      let metaFieldsObject = await this.inputMetadataFeedService.getMetaFieldList(
        updatedCustomMetaInfo,
        fieldConfig,
        existingObj.customMeta,
        updateInput.deleteFields,
      );
      let metaFieldList = metaFieldsObject.metaFieldList;

      if (
        [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
          existingObj.objectType,
        )
      ) {
        //save field configs
        await this.metaDataRepository.updateManySet(
          {_id: new ObjectId(existingObj.id)},
          {metaFieldList: metaFieldList},
          [],
        );
      }

      //replace updated custom meta from validated meta object
      updateInput.updates = metaFieldsObject.metaObject;
    }
  }

  /**
   * Method to handle deleting mtadata fields
   * And propagating to collection and children
   * @param id : string[]
   * @param deleteList : string[]
   */
  // async handleMetadataFieldDelete(id: string, deleteList: string[]) {
  //   let existingMeta = await this.metaDataRepository.findById(id);
  //   let teamId: string | undefined = undefined;
  //   // let collectionId: string | undefined = undefined
  //   // if (existingMeta.collectionId) collectionId = existingMeta.collectionId as unknown as string
  //   if (existingMeta.teamId) teamId = existingMeta.teamId as unknown as string;
  //   if (!teamId) {
  //     logger.error(
  //       `Update - edit metadata | MetaFieldPropagatorService.handleMetadataFieldDelete | N/A | couldn't find team for metadata id: ${id} `,
  //     );
  //     throw new HttpErrors.NotAcceptable('Team not exist for metadta');
  //   }

  //   // handle delete operation
  //   // if (existingMeta.isLogical) {
  //   //   // delete fields dataset
  //   //   for (let deleteField of deleteList) {
  //   //     // delete from itself
  //   //     await this.metaDataRepository.updateManyUnSet(
  //   //       {_id: new ObjectId(id)}, // itself
  //   //       {['customMeta.' + deleteField]: ''},
  //   //       [],
  //   //     );

  //   //     //delete root group value
  //   //     await this.queryOptionRepository.deleteQueryOption(SearchQueryRootGroup.METADATA, deleteField, teamId, id);

  //   //     //delete sub group values
  //   //     let subGroup = SearchQueryRootGroup.METADATA + '.' + deleteField;
  //   //     await this.queryOptionRepository.deleteQueryOption(subGroup, undefined, teamId, id);
  //   //   }
  //   // } else
  //   if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(existingMeta.objectType)) {
  //     // only allowed to delete fields in images and videos
  //     for (let deleteField of deleteList) {
  //       // delete from itself & children
  //       await this.metaDataRepository.updateManyUnSet(
  //         {
  //           $or: [
  //             {_id: new ObjectId(id)}, // itself
  //             {parentList: new ObjectId(id)}, // children
  //             {vCollectionIdList: new ObjectId(id)}, // children
  //           ],
  //         },
  //         {['customMeta.' + deleteField]: ''},
  //         [],
  //       );

  //       if (ContentType.VIDEO == existingMeta.objectType) {
  //         //delete search query option from derived image collection
  //         if (existingMeta.frameCollectionId) {
  //           //delete root group value
  //           await this.queryOptionRepository.deleteQueryOption(
  //             SearchQueryRootGroup.METADATA,
  //             deleteField,
  //             teamId,
  //             existingMeta.frameCollectionId,
  //           );
  //           //delete sub group values
  //           let subGroup = SearchQueryRootGroup.METADATA + '.' + deleteField;
  //           await this.queryOptionRepository.deleteQueryOption(
  //             subGroup,
  //             undefined,
  //             teamId,
  //             existingMeta.frameCollectionId,
  //           );
  //         }
  //       }

  //       if (
  //         !existingMeta.collectionId
  //         // && (!existingMeta.vCollectionIdList ||
  //         //   (Array.isArray(existingMeta.vCollectionIdList) && existingMeta.vCollectionIdList.length == 0))
  //       ) {
  //         return;
  //       }

  //       // check if the value available in any other objects of the parent entities
  //       let deletedVal = existingMeta.customMeta ? existingMeta.customMeta[deleteField] : undefined;
  //       if (!deletedVal) {
  //         return;
  //       }

  //       let otherChildren = await this.metaDataRepository.findOne({
  //         where: {
  //           collectionId: existingMeta.collectionId,
  //           ['customMeta.' + deleteField]: deletedVal,
  //         },
  //       });
  //       if (!otherChildren) {
  //         // deleted field was the only one existed of that value.
  //         // then pull value from the collection
  //         await this.metaDataRepository.updateManyRemoveFromList(
  //           {
  //             _id: new ObjectId(existingMeta.collectionId),
  //           },
  //           {['customMeta.' + deleteField]: deletedVal},
  //           [],
  //         );
  //         // check if colleaction has values remaining for that field and if empty delete the field
  //         let deleteFieldArrayFirstElem = `${deleteField}.0`;
  //         await this.metaDataRepository.updateManyUnSet(
  //           {
  //             _id: new ObjectId(existingMeta.collectionId),
  //             ['customMeta.' + deleteField]: {$exists: true},
  //             ['customMeta.' + deleteFieldArrayFirstElem]: {$exists: false},
  //           },
  //           {['customMeta.' + deleteField]: ''},
  //           [],
  //         );

  //         // rebuild search query
  //         let distinctValues = await this.metaDataRepository.getAllDistinctValuesForCustomFieldInCollection(
  //           existingMeta.collectionId.toString(),
  //           deleteField,
  //         );
  //         this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //           SearchQueryRootGroup.METADATA,
  //           deleteField,
  //           teamId,
  //           existingMeta.collectionId as unknown as string,
  //           distinctValues,
  //         );
  //       }
  //     }
  //   }
  // }

  /**
   * Propagate custom metadata fields from source (video) to children (derrived images)
   * @param source : string id of the source video
   * @param images : string[] objectKey array of children
   */
  async propagateFieldsFromVideoToDerivedImages(source: string | ObjectId, images: string[]) {
    if (!source || !images) {
      return;
    }

    // find source video
    let videoObject = await this.metaDataRepository.findById(source as string);
    if (!videoObject) {
      return;
    }
    // generate custom meta fields
    let _tags = videoObject.Tags || [];
    let _customMeta = videoObject.customMeta || {};

    // update the child images
    await this.metaDataRepository.updateManySet(
      {objectKey: {$in: images}},
      {
        Tags: _tags,
        customMeta: _customMeta,
      },
      [],
    );

    return;
  }

  /**
   * Check meta field available in the system
   * If not registered, then validate and register the field
   * @param fieldName
   * @param teamId
   * @param userName
   * @returns
   */
  async validateAndCreateCustomMetaField(fieldName: string, teamId: string, userName: string) {
    // check the field name already exists
    let existingField = await this.metaFieldRepository.findOne({where: {fieldName: fieldName, teamId: teamId}});

    if (!existingField) {
      // then process to create the field
      let metaFieldInput: InputField = {
        fieldName: fieldName,
        fieldType: InputFieldTypes.TEXT_BOX,
      };
      let response = await this.createMetaFields(metaFieldInput, userName, teamId);
      if (!response?.success && response?.data && response?.data.existingMetaField) {
        return {
          newField: fieldName,
          oldField: response.data.existingMetaField,
        };
      }
    }
  }

  /**
   * to create meta fields
   * @param data InputField
   * @param teamId logged in team id
   * @returns {isSuccess: boolean, message: string}
   */
  async createMetaFields(data: InputField, username: string, teamId: string) {
    // initialize the fields for meta field repository
    let fieldName = data.fieldName;
    let fieldType = data.fieldType;
    let fieldOptions = data.options ? data.options : [];

    // create unique name and check if it already exists
    let uniqueName = fieldName
      .toLowerCase()
      .trim()
      .replace(/[^0-9A-Z]+/gi, '');

    // validate field type
    if (fieldType == InputFieldTypes.TEXT_BOX && fieldOptions.length > 0) {
      logger.info(
        `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | Field type is text box but options are provided`,
      );
      // throw new HttpErrors.NotAcceptable('Options are not required for text box field type');
      return {
        success: false,
        message: 'Options are not required for text box field type',
        data: {},
      };
    }

    if (fieldType == InputFieldTypes.DROP_DOWN && fieldOptions.length == 0) {
      logger.info(
        `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | Field type is drop down but options are not provided`,
      );
      // throw new HttpErrors.NotAcceptable('Options are required for drop down field type');
      return {
        success: false,
        message: 'Options are required for drop down field type',
        data: {},
      };
    }

    // validate field options
    await this.checkDuplicatesOfOptions(fieldOptions);

    // check if meta field already exists
    let existingMetaField = await this.metaFieldRepository.findOne({
      where: {
        uniqueName: uniqueName,
        teamId: teamId,
      },
    });

    if (existingMetaField) {
      logger.info(
        `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | Meta field already exists: ${existingMetaField}`,
      );
      // throw new HttpErrors.NotAcceptable(
      //   `Meta field ${existingMetaField.fieldName} already exists. Cannot create ${fieldName}`,
      // );
      return {
        success: false,
        message: `Meta field ${existingMetaField.fieldName} already exists. Cannot create ${fieldName}`,
        data: {existingMetaField: uniqueName},
      };
    }

    // add meta field to meta field repository
    try {
      let createdMetaField = await this.metaFieldRepository.create({
        fieldName: fieldName,
        fieldType: fieldType,
        options: fieldOptions,
        teamId: teamId,
        uniqueName: uniqueName,
        lastModifiedAt: new Date(),
        modifiedBy: username,
      });

      logger.info(
        `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | Meta field created successfully: ${JSON.stringify(
          createdMetaField,
        )}`,
      );

      if (createdMetaField) {
        let responseObj: MetaFieldSuggestionFormat = {
          id: '',
          fieldName: '',
          fieldType: 1,
          options: [],
          isSelected: false,
          isMandatory: false,
          isDeletable: true,
          isEditable: true,
        };

        responseObj.id = createdMetaField._id == undefined ? '' : createdMetaField._id.toString();
        responseObj.fieldName = createdMetaField.fieldName;
        responseObj.fieldType = createdMetaField.fieldType;
        responseObj.options = createdMetaField.options;

        return {
          success: true,
          message: '',
          data: {success: true, message: 'Meta field created successfully', createdObject: responseObj},
        };
      } else {
        return {
          success: true,
          message: '',
          data: {success: true},
        };
      }
    } catch (e) {
      logger.error(
        `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | message: ${e.message} | stacktrace: ${e.stack}`,
      );
      // throw new HttpErrors.NotAcceptable('Error while creating meta field');
      return {
        success: false,
        message: 'Error while creating meta field',
        data: {},
      };
    }
  }

  async checkDuplicatesOfOptions(options: {valueId: string; value: string}[]) {
    let seen = new Set();
    let hasDuplicates = options.some(function (currentObject) {
      return seen.size === seen.add(currentObject.value).size;
    });

    if (hasDuplicates) {
      logger.info(
        `Create - meta fields | MetaFieldPropagatorService.checkDuplicatesOfOptions | N/A | Duplicate options found`,
      );
      throw new HttpErrors.NotAcceptable('Duplicate options found');
    }
  }

  /**
   * get meta fields list and count
   * @param searchKey search key : string
   * @param teamId logged in team id
   * @returns meta field list
   */
  async getMetaFields(
    searchKey: string,
    teamId: string,
    exportApiType: Explore_API_TYPE,
    pageIndex?: number,
    pageSize?: number,
  ) {
    let aggregateQuery: {}[] = [
      {
        $match: {
          teamId: new ObjectId(teamId),
          fieldName: {$regex: searchKey, $options: 'i'},
        },
      },
      {$sort: {lastModifiedAt: -1}},
    ];

    if (pageIndex != undefined && pageSize != undefined) {
      aggregateQuery.push({$skip: pageIndex * pageSize}, {$limit: pageSize});
    }

    if (exportApiType == Explore_API_TYPE.LIST) {
      let metaField = {
        metaFieldList: [],
      };

      aggregateQuery.push({
        $project: {
          fieldName: 1,
          fieldType: 1,
          options: 1,
          lastModifiedAt: 1,
          modifiedBy: 1,
        },
      });

      metaField.metaFieldList = await this.metaFieldRepository.aggregate(aggregateQuery);

      return metaField;
    } else if (exportApiType == Explore_API_TYPE.COUNT) {
      aggregateQuery.push({$count: 'count'});

      let countObj = await this.metaFieldRepository.aggregate(aggregateQuery);

      if (Array.isArray(countObj) && countObj.length > 0) {
        return {
          count: countObj[0].count,
        };
      }
    }
  }

  /**
   * to get meta fields list for collection
   * @param collectionId collection id
   * @param teamId logged in team id
   * @returns {metaFieldList: metaFieldList}
   */
  async getMetaFieldsOfCollection(collectionId?: string, teamId?: string) {
    // initialize variables
    let metaFieldList: MetaFieldSuggestionFormat[] = [];
    let exisitingMetaFields: any = [];

    // get meta fields from system
    let systemMetaFields = await this.getMetaFieldsListFromSystem(teamId);

    // get existing meta fields for collection if collection id is present
    if (collectionId) {
      let collectionObject = await this.metaDataRepository.findById(collectionId);

      if (collectionObject && collectionObject.metaFieldList) {
        exisitingMetaFields = collectionObject.metaFieldList;
      }
    }

    // compare system meta fields with existing meta fields and set isSelected and isMandatory flags
    if (exisitingMetaFields && exisitingMetaFields.length > 0) {
      for (let i = 0; i < exisitingMetaFields.length; i++) {
        for (let j = 0; j < systemMetaFields.length; j++) {
          if (exisitingMetaFields[i].metaFieldId.toString() === systemMetaFields[j].id.toString()) {
            systemMetaFields[j].isSelected = true;
            systemMetaFields[j].isMandatory = exisitingMetaFields[i].isMandatory;
            break;
          }
        }
      }
    }

    metaFieldList = systemMetaFields;

    return {metaFieldList: metaFieldList};
  }

  /**
   * Use to get system metadata fields for selection with details of isSelected for a given selection
   * @param filter GetMetaFieldsOfSystemRequest
   * @param timeZone time zone
   * @param teamId id of the team
   * @returns
   */
  async getMetaFieldsOfSelection(
    filter: GetMetaFieldsOfSystemRequest,
    timeZone: number,
    currentUserProfile: UserProfileDetailed,
  ) {
    // initialize variables
    let metaFieldList: MetaFieldSuggestionFormat[] = [];
    let teamId = currentUserProfile.teamId;

    // get meta fields from system
    let systemMetaFields = await this.getMetaFieldsListFromSystem(teamId);

    let config: TagsAndMetaDataConfig = {
      allMetaData: false,
      isTagList: false,
    };

    let metaDataListOfSelection = (await this.datalakeExplorerService.getTagsOrMetaData(
      filter,
      currentUserProfile,
      timeZone,
      config,
    )) as DetailTabKeyVal[];

    // compare system meta fields with existing meta fields and set isSelected
    if (metaDataListOfSelection && metaDataListOfSelection.length > 0) {
      for (let i = 0; i < metaDataListOfSelection.length; i++) {
        for (let j = 0; j < systemMetaFields.length; j++) {
          if (metaDataListOfSelection[i]?.fieldId?.toString() === systemMetaFields[j]?.id?.toString()) {
            systemMetaFields[j].isSelected = true;
            break;
          }
        }
      }
    }

    metaFieldList = systemMetaFields;

    return {metaFieldList: metaFieldList};
  }

  /**
   * to get system meta fields list
   * @param teamId logged user team id
   * @returns meta field list from system
   */
  async getMetaFieldsListFromSystem(teamId: string | undefined) {
    let metaFieldList: MetaFieldSuggestionFormat[] = [];

    if (teamId) {
      let aggregateQuery = [
        {
          $match: {
            teamId: new ObjectId(teamId),
          },
        },
        {
          $project: {
            fieldName: 1,
            fieldType: 1,
            options: 1,
          },
        },
      ];

      let metaFields = await this.metaFieldRepository.aggregate(aggregateQuery);

      if (metaFields && metaFields.length > 0) {
        metaFields.forEach((metaField: any) => {
          metaFieldList.push({
            id: metaField._id,
            fieldName: metaField.fieldName,
            fieldType: metaField.fieldType,
            options: metaField.options,
            isSelected: false,
            isMandatory: false,
          });
        });
      }
    }

    return metaFieldList;
  }

  /**
   * to delete meta fields
   * @param metaFieldId meta field id
   * @param teamId logged user team id
   * @returns {isSuccess: boolean}
   */
  async deleteMetaFields(metaFieldId: string, teamId: string) {
    // get meta field object and validate
    let [metaFieldObject] = await this.metaFieldRepository.aggregate([
      {
        $match: {
          _id: new ObjectId(metaFieldId),
          teamId: new ObjectId(teamId),
        },
      },
      {
        $project: {
          fieldName: 1,
          fieldType: 1,
          options: 1,
        },
      },
    ]);

    if (!metaFieldObject) {
      logger.error(`Delete - meta fields | MetaFieldPropagatorService.deleteMetaFields | N/A | Meta field not found`);
      throw new HttpErrors.NotAcceptable('Meta field not found');
    }

    try {
      // mark isMetaFieldsPropagationRequired in relevant collections
      await this.markMetaFieldRecalculationOnSystemFieldDelete(metaFieldObject.fieldName);

      // delete from custom meta field in MetaDataRepository
      let metaDataResult = await this.metaDataRepository.updateManyUnSet(
        {},
        {
          [`customMeta.${metaFieldObject.fieldName}`]: 1,
        },
        [],
      );

      logger.info(
        `Delete - meta fields | MetaFieldPropagatorService.deleteMetaFields | N/A | ${metaDataResult.modifiedCount} records modified for customMeta field fieldName: ${metaFieldObject.fieldName}, teamId: ${teamId}`,
      );

      let keyGroup = `metadata.${metaFieldObject.fieldName}`;
      let key = metaFieldObject.fieldName;

      // delete from query options
      await this.queryOptionRepository.deleteQueryOption(keyGroup, undefined, teamId);
      await this.queryOptionRepository.deleteQueryOption('metadata', key, teamId);

      // delete from metaField list in MetaDataRepository
      let metaDataResults = await this.metaDataRepository.updateManyRemoveFromList(
        {},
        {
          metaFieldList: {
            metaFieldId: new ObjectId(metaFieldId),
          },
        },
        [],
      );

      logger.info(
        `Delete - meta fields | MetaFieldPropagatorService.deleteMetaFields | N/A | ${metaDataResults.modifiedCount} records modified for metaFieldList field metaFieldId: ${metaFieldId}, teamId: ${teamId}`,
      );

      // delete from metaField repository
      let metaField = await this.metaFieldRepository.deleteOne({_id: new ObjectId(metaFieldId)});

      logger.info(
        `Delete - meta fields | MetaFieldPropagatorService.deleteMetaFields | N/A | ${metaField.deletedCount} records deleted from metaField Repository for metaFieldId: ${metaFieldId}, teamId: ${teamId}`,
      );

      return {isSuccessfullyDeleted: true, message: 'Meta field deleted successfully'};
    } catch (e) {
      logger.error(`Delete - meta fields | MetaFieldPropagatorService.deleteMetaFields | N/A | Error while deleting meta field: ${e.message}
      stack: ${e.stack}`);
      throw new HttpErrors.NotAcceptable('Error while deleting meta field');
    }
  }

  /**
   * mark isMetaFieldsPropagationRequired in relevant collections to precalculate meta field stats
   * @param fieldName
   * @returns
   */
  async markMetaFieldRecalculationOnSystemFieldDelete(fieldName: string) {
    if (!fieldName) return;

    logger.debug(
      `Mark meta field recalculation on system field delete | MetaFieldPropagatorService.markMetaFieldRecalculationOnSystemFieldDelete | N/A | requested for deleted field: ${fieldName}`,
    );

    let fieldNameString = `customMeta.${fieldName}`;
    let matchQuery = {[fieldNameString]: {$exists: true}};
    try {
      // get applicable collection list
      let applicableCollectionList = await this.statsCalculationService.findApplicableCollectionsForQuery(matchQuery);
      this.markIsMetaFieldsPropagationRequiredInCollectionList(applicableCollectionList);
    } catch (e) {
      logger.error(
        `Mark meta field recalculation on system field delete | MetaFieldPropagatorService.markMetaFieldRecalculationOnSystemFieldDelete | N/A | Error `,
      );
      logger.error(e);
    }
  }

  /**
   * Mark isMetaFieldsPropagationRequired true in given collection list
   * @param collectionIdList
   */
  async markIsMetaFieldsPropagationRequiredInCollectionList(collectionIdList: string[]) {
    logger.debug(
      `Mark meta field recalculation on system field delete | MetaFieldPropagatorService.markMetaFieldRecalculationOnSystemFieldDelete | N/A | applicable collection list length: ${collectionIdList.length}`,
    );
    let applicableCollectionOidList = collectionIdList.map(collectionId => new ObjectId(collectionId));

    // mark isMetaFieldsPropagationRequired
    try {
      await this.metaDataRepository.updateManySet(
        {_id: {$in: applicableCollectionOidList}},
        {isMetaFieldsPropagationRequired: true},
        [],
      );
    } catch (e) {
      logger.error(
        `Mark meta field recalculation on system field delete | MetaFieldPropagatorService.markMetaFieldRecalculationOnSystemFieldDelete | N/A | Error updating collection flags`,
      );
      logger.error(e);
    }
  }

  /**
   * to get metaData (collecvtionName, tags)
   * @param contentType content type
   * @param teamId logged user team id
   * @param collectionId collection id (optional)
   * @returns metaDataSuggestion : MetaDataKeyInputList
   */
  async getMetaFieldsSuggestions(
    contentType: ContentType,
    currentUserProfile: UserProfileDetailed,
    collectionId?: string,
  ) {
    let teamId = currentUserProfile.teamId;
    let metaDataSuggestion: MetaDataKeyInputList = {
      collectionList: [],
      tagList: {
        allTags: [],
        selectedTags: [],
      },
    };

    // get system data file for the team
    //let systemData: SystemData | null = null
    let allTags: string[] = [];
    if (teamId) {
      //systemData = await this.systemDataRepository.findOne({where: {teamId: teamId}})
      allTags = await this.metaTagRepository.distinct('tag', {});
    }

    // get existing collection tags if collection id is present
    if (collectionId) {
      let existingCollectionObject = await this.metaDataRepository.findById(collectionId);

      if (existingCollectionObject) {
        metaDataSuggestion.tagList.selectedTags = existingCollectionObject.Tags || [];
      }
    }

    // get existing collection list by type
    metaDataSuggestion.collectionList = await this.metaDataService.getExistingCollectionListByType(
      contentType,
      currentUserProfile,
    );
    metaDataSuggestion.tagList.allTags = allTags || [];

    logger.info(
      `Get meta fields suggestions | MetaFieldPropagatorService.getMetaFieldsSuggestions | N/A | Meta fields suggestions fetched successfully`,
    );
    return metaDataSuggestion;
  }

  /**
   * edit meta fields
   * @param data EditMetaFieldInputFormat
   * @param teamId logged user team id
   * @param username logged user username
   * @returns
   */
  async editMetaFields(data: EditMetaFieldInputFormat, teamId: string, username: string) {
    let metaFieldId = data.metaFieldId;
    let fieldConfig: Partial<MetaField> = data.editFieldConfig;

    // get meta field object and validate it
    let metaFieldObject = await this.metaFieldRepository.findById(metaFieldId);

    if (!metaFieldObject) {
      logger.error(`Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | Meta field not found`);
      throw new HttpErrors.NotAcceptable('Invalid meta field id');
    }

    // validate field config
    if (metaFieldObject.fieldType != fieldConfig.fieldType) {
      logger.error(
        `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | Field type cannot be changed`,
      );
      throw new HttpErrors.NotAcceptable('Field type cannot be changed');
    }

    // console.log('fieldConfig', fieldConfig);
    // console.log('metaFieldObject', metaFieldObject);

    // compare fieldConfig obj with metaFieldObject and get updated fields object
    let updatedFields = await this.getUpdatedFields(fieldConfig, metaFieldObject, teamId);

    logger.info(
      `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | Updated fields: ${JSON.stringify(
        updatedFields,
      )}`,
    );

    try {
      // if remove options is present
      if (Array.isArray(updatedFields.removeOptions) && updatedFields.removeOptions.length > 0) {
        for (let option of updatedFields.removeOptions) {
          // delete custom meta field in MetaDataRepository
          let removeMetaDataResult = await this.metaDataRepository.updateManyUnSet(
            {
              [`customMeta.${metaFieldObject.fieldName}`]: option.value,
            },
            {
              [`customMeta.${metaFieldObject.fieldName}`]: option.value,
            },
            [],
          );

          logger.info(
            `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | ${removeMetaDataResult.modifiedCount} records removed from customMeta field fieldName: ${metaFieldObject.fieldName}, fieldValue: ${option.value} teamId: ${teamId}`,
          );

          // delete query options
          await this.queryOptionRepository.deleteQueryOption(
            `metadata.${metaFieldObject.fieldName}`,
            option.value,
            teamId,
          );
        }
      }

      // if modify option is present
      if (Array.isArray(updatedFields.modifyOptions) && updatedFields.modifyOptions.length > 0) {
        // let fieldName = updatedFields.fieldName ? updatedFields.fieldName : metaFieldObject.fieldName;
        let fieldName = metaFieldObject.fieldName;

        for (let option of updatedFields.modifyOptions) {
          if (Array.isArray(metaFieldObject.options) && metaFieldObject.options.length > 0) {
            let existingOption = metaFieldObject.options.find(opt => opt.valueId == option.valueId);

            if (existingOption) {
              // update custom meta field in MetaDataRepository
              let modifyMetaDataResult = await this.metaDataRepository.updateManySet(
                {
                  [`customMeta.${fieldName}`]: existingOption.value,
                },
                {
                  [`customMeta.${fieldName}`]: option.value,
                },
                [],
              );

              logger.info(
                `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | ${modifyMetaDataResult.modifiedCount} records modified for customMeta field fieldName: ${metaFieldObject.fieldName}, existValue: ${existingOption.value} modifedValue: ${option.value} teamId: ${teamId}`,
              );

              // update query options
              let modifyQueryOptionResulst = await this.queryOptionRepository.updateQueryKeyValue(
                `metadata.${fieldName}`,
                option.value,
                existingOption.value,
                teamId,
              );

              logger.info(
                `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | ${modifyQueryOptionResulst.modifiedCount} records modified for queryOption field oldKey: ${existingOption.value}, newKey: ${option.value} teamId: ${teamId}`,
              );
            }
          }
        }
      }

      if (updatedFields.fieldName) {
        // update custom meta field in MetaDataRepository
        let metaDataResult = await this.metaDataRepository.updateManyRename(
          {},
          {
            [`customMeta.${metaFieldObject.fieldName}`]: `customMeta.${updatedFields.fieldName}`,
          },
          [],
        );

        logger.info(
          `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | ${metaDataResult.modifiedCount} records modified for customMeta field fieldName: ${metaFieldObject.fieldName}, teamId: ${teamId}`,
        );

        // update query options
        let modifyCountForKeyGroup = await this.queryOptionRepository.updateManySet(
          {
            keyGroup: `metadata.${metaFieldObject.fieldName}`,
          },
          {
            keyGroup: `metadata.${updatedFields.fieldName}`,
          },
          [],
        );

        let modifyCountForKey = await this.queryOptionRepository.updateQueryKeyValue(
          `metadata`,
          updatedFields.fieldName,
          metaFieldObject.fieldName,
          teamId,
        );

        logger.info(
          `Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | ${
            modifyCountForKeyGroup.modifiedCount + modifyCountForKey.modifiedCount
          } records modified for queryOption field keyGroup: metadata.${metaFieldObject.fieldName}, key: ${
            metaFieldObject.fieldName
          } , teamId: ${teamId}`,
        );
      }

      // update metaField repository
      // add field to field config object
      fieldConfig.uniqueName = fieldConfig.fieldName
        ?.toLowerCase()
        .trim()
        .replace(/[^0-9A-Z]+/gi, '');
      fieldConfig.lastModifiedAt = new Date();
      fieldConfig.modifiedBy = username;
      fieldConfig.options = [
        ...updatedFields.addOptions,
        ...updatedFields.modifyOptions,
        ...updatedFields.notModifiedOptions,
      ];

      await this.metaFieldRepository.updateById(metaFieldId, fieldConfig);
      return {isSuccessfullyEdited: true, message: 'Meta field edited successfully'};
    } catch (e) {
      logger.error(`Edit - meta fields | MetaFieldPropagatorService.editMetaFields | N/A | Error while editing meta field: ${e.message}
      stack: ${e.stack}`);
      throw new HttpErrors.NotAcceptable('Error while editing meta field');
    }
  }

  /**
   * to get updated fields object by comparing fieldConfig and metaFieldObject
   * @param fieldConfig field configuration object for edited meta field
   * @param metaFieldObject existing meta field object
   * @returns updated fields object
   */
  async getUpdatedFields(fieldConfig: Partial<MetaField>, metaFieldObject: MetaField, teamId: string) {
    let updatedFields: any = {
      fieldName: '',
      addOptions: [],
      removeOptions: [],
      modifyOptions: [],
      notModifiedOptions: [],
    };

    // check if field name is changed
    if (fieldConfig.fieldName && fieldConfig.fieldName != metaFieldObject.fieldName) {
      updatedFields.fieldName = fieldConfig.fieldName;

      let uniqueName = updatedFields.fieldName
        ?.toLowerCase()
        .trim()
        .replace(/[^0-9A-Z]+/gi, '');

      // check if meta field already exists
      let existingMetaField = await this.metaFieldRepository.findOne({
        where: {
          uniqueName: uniqueName,
          teamId: teamId,
        },
      });

      if (existingMetaField) {
        logger.info(
          `Create - meta fields | MetaFieldPropagatorService.createMetaFields | N/A | Meta field already exists: ${existingMetaField}`,
        );
        throw new HttpErrors.NotAcceptable('Meta field already exists');
      }
    }

    // check if options are changed
    // iterate over options and check if any option is added, removed or modified according to valueId
    if (Array.isArray(fieldConfig.options)) {
      // check for duplicates
      await this.checkDuplicatesOfOptions(fieldConfig.options);

      let existingOptions = metaFieldObject.options || [];
      let newOptions = fieldConfig.options || [];

      // check if any option is not modified
      let notModifiedOptions = newOptions.filter(newOption => {
        return existingOptions.find(existingOption => {
          return existingOption.valueId == newOption.valueId && existingOption.value == newOption.value;
        });
      });
      updatedFields.notModifiedOptions = notModifiedOptions;

      // check if any option is added
      let addedOptions = newOptions.filter(newOption => {
        return !existingOptions.find(existingOption => {
          if (existingOption.valueId != newOption.valueId) {
            return existingOptions.find(existingOption => {
              return existingOption.value == newOption.value;
            });
          } else return true;
          // return existingOption.valueId == newOption.valueId;
        });
      });
      updatedFields.addOptions = addedOptions;

      // check if any option is removed
      let removedOptions = existingOptions.filter(existingOption => {
        return !newOptions.find(newOption => {
          return existingOption.valueId == newOption.valueId;
        });
      });
      updatedFields.removeOptions = removedOptions;

      // check if any option is modified
      let modifiedOptions = newOptions.filter(newOption => {
        return existingOptions.find(existingOption => {
          // check if value is changed
          if (existingOption.valueId == newOption.valueId && existingOption.value != newOption.value) {
            // check if new value is not duplicate
            return !existingOptions.find(existOption => {
              // if value is duplicate then new option should added to notModifiedOptions and return true
              if (existOption.value == newOption.value) {
                updatedFields.notModifiedOptions.push(existingOption);
                return true;
              } else return false;
            });
          } else return false;
          // return existingOption.valueId == newOption.valueId && existingOption.value != newOption.value;
        });
      });
      updatedFields.modifyOptions = modifiedOptions;
    }

    return updatedFields;
  }
}
export const META_FIELD_PROPAGATOR_SERVICE =
  BindingKey.create<MetaFieldPropagatorService>('service.metaFieldPropagator');
