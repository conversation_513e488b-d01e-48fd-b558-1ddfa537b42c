/**
 * Copyright (c) 2025 LayerNext, Inc.
 * 
 * all rights reserved.
 * 
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 * 
 * Handle the request related to the model provider
 */

/**
 * @description This service is used to get and set the API key for the model provider
 * <AUTHOR>
 */

import {injectable, BindingScope, inject} from '@loopback/core';
import {ModelProviderRepository} from '../repositories/model-provider.repository';
import {logger} from '../config';
import {ModelProvider} from '../models/model-provider.model';
import axios from 'axios';

const PYTHON_HOST = process.env.PYTHON_BASE_URL;
const LLM_AGENT_INTERNAL_URL = process.env.LLM_AGENT_INTERNAL_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class ModelProviderService {
  constructor(
    @inject('repositories.ModelProviderRepository')
    private modelProviderRepository: ModelProviderRepository,
  ) { }



  async createApiKey(data: {apiUrl?: string, apiKey: string, provider: string}, userId: string, userName: string, teamId: string) {
    try {
      logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Creating model provider`);
      const apikey = data.apiKey.trim();
      if (!apikey) {
        logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Api key cannot be empty or whitespace`);
        return {
          isSuccess: false,
          message: 'Api key cannot be empty or whitespace'
        }
      }

      logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Checking if model provider already exists`);

      const validateApiKeyResponse = await this.validateApiKey(data);
      
      if (!validateApiKeyResponse.isSuccess) {
        logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Invalid API key: ${validateApiKeyResponse.moreDetails}`);
        return {
          isSuccess: false,
          message: validateApiKeyResponse.message,
        }
      }
      const existingModelProvider = await this.modelProviderRepository.findOne({where: {provider: data.provider, teamId: teamId}});

      if (existingModelProvider) {
        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider already exists`);
        existingModelProvider.apiKey = apikey;
        existingModelProvider.url = data?.apiUrl;
        existingModelProvider.updatedBy = userName;
        existingModelProvider.updatedAt = new Date();

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Updating model provider`);

        await this.modelProviderRepository.update(existingModelProvider);

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Verifying if model provider is updated`);

        const verifyUpdate = await this.modelProviderRepository.findById(existingModelProvider._id);

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Verifying if model provider is updated`);

        if (!verifyUpdate || !verifyUpdate.updatedAt?.getTime || verifyUpdate.updatedAt?.getTime() !== existingModelProvider.updatedAt?.getTime()) {
          logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Failed to update model provider: ${JSON.stringify(existingModelProvider)}`);
          return {
            isSuccess: false,
            message: 'Failed to update API Key'
          }
        }

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider updated in db successfully: ${JSON.stringify(verifyUpdate)}`);

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Calling python service to update model provider`);

        const pythonResponse = await this.callFlaskBackendPythonServiceToUpdateAPIKey({
          apiKey: existingModelProvider.apiKey,
          apiUrl: existingModelProvider.url,
          provider: existingModelProvider.provider
        });

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Calling fastapi service to update model provider`);

        const fastApiResponse = await this.callFastApiMBackendToUpdateAPIKey({
          apiKey: existingModelProvider.apiKey,
          apiUrl: existingModelProvider.url,
          provider: existingModelProvider.provider,
          teamId: teamId,
          userName: userName
        });

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Verifying if model provider is updated`);

        if (!pythonResponse.isSuccess || !fastApiResponse.isSuccess) {
          logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Failed to update model provider config: ${JSON.stringify(pythonResponse)}`);
        }

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider updated successfully`);

        return {
          isSuccess: true,
          message: 'API Key updated successfully'
        }
      } else {
        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider does not exist, creating new model provider`);

        const newModelProviderObject = {
          apiKey: apikey,
          url: data?.apiUrl,
          provider: data.provider,
          userId: userId,
          teamId: teamId,
          createdBy: userName,
          createdAt: new Date(),
          updatedBy: userName,
          updatedAt: new Date()
        }

        const modelProvider = await this.modelProviderRepository.create(newModelProviderObject as ModelProvider);

        if (!modelProvider) {
          logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Failed to create model provider`);
          return {
            isSuccess: false,
            message: 'Failed to set up API Key'
          }
        }

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider created successfully: ${JSON.stringify(modelProvider)}`);

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Calling python service to update model provider`);

        const pythonResponse = await this.callFlaskBackendPythonServiceToUpdateAPIKey({
          apiKey: modelProvider.apiKey,
          apiUrl: modelProvider.url,
          provider: modelProvider.provider
        });

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Calling fastapi service to update model provider`);

        const fastApiResponse = await this.callFastApiMBackendToUpdateAPIKey({
          apiKey: modelProvider.apiKey,
          apiUrl: modelProvider.url,
          provider: modelProvider.provider,
          teamId: teamId,
          userName: userName
        });

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Verifying if model provider is updated`);

        if (!pythonResponse.isSuccess || !fastApiResponse.isSuccess) {
          logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Failed to update model provider config: ${JSON.stringify(pythonResponse)}`);
        }

        logger.info(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Model provider updated successfully`);

        return {
          isSuccess: true,
          message: 'API key set up successfully'
        }
      }
    } catch (error) {
      logger.error(`Model Provider Creation | ModelProviderService.createApiKey | N/A | Error creating model provider: ${error.message}`);
      return {
        isSuccess: false,
        message: 'Failed to set up API Key'
      }
    }
  }

  async getApiKeys(provider?: string, teamId?: string) {
    try {
      logger.info(`Model Provider Retrieval | ModelProviderService.getApiKeys | N/A | Retrieving model provider`);
      const apiKeys = await this.modelProviderRepository.find({where: {provider: provider, teamId: teamId}});
      if (apiKeys.length > 0) {
        logger.info(`Model Provider Retrieval | ModelProviderService.getApiKeys | N/A | Model provider found`);
        return {
          apiKey: apiKeys[0].apiKey,
          apiUrl: apiKeys[0].url,
          provider: apiKeys[0].provider
        };
      } else {
        logger.info(`Model Provider Retrieval | ModelProviderService.getApiKeys | N/A | No model provider found`);
        return {apiKey: undefined, apiUrl: undefined, provider: undefined};
      }
    } catch (error) {
      logger.error(`Model Provider Retrieval | ModelProviderService.getApiKeys | N/A | Error retrieving model provider: ${error.message}`);
      return {apiKey: undefined, apiUrl: undefined, provider: undefined};
    }
  }


  private async callFlaskBackendPythonServiceToUpdateAPIKey(model_provider: {apiKey: string, apiUrl?: string, provider: string}) {
    logger.info(`Model Provider Update | ModelProviderService.callFlaskBackendPythonServiceToUpdateAPIKey | N/A | Calling python service to update model provider`);
    const url = `${PYTHON_HOST}/internal/model_provider/set_model_provider_config`;
    try {
      const response = await axios.post(url, model_provider);

      logger.info(`Model Provider Update | ModelProviderService.callFlaskBackendPythonServiceToUpdateAPIKey | N/A | Call to python service to update model provider config: ${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      logger.error(`Model Provider Update | ModelProviderService.callFlaskBackendPythonServiceToUpdateAPIKey | N/A | Error updating model provider: ${error.message}`);
      return {
        isSuccess: false,
        message: error
      }
    }
  }


  private async callFastApiMBackendToUpdateAPIKey(model_provider: {apiKey: string, apiUrl?: string, provider: string, teamId: string, userName: string}) {
    logger.info(`Model Provider Update | ModelProviderService.callFastApiMBackendToUpdateAPIKey | N/A | Calling fastapi service to update model provider`);
    const url = `${LLM_AGENT_INTERNAL_URL}/internal/model_provider/set_model_provider_config`;
    try {
      const response = await axios.post(url, model_provider);

      logger.info(`Model Provider Update | ModelProviderService.callFastApiMBackendToUpdateAPIKey | N/A | Call to fastapi service to update model provider config: ${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      logger.error(`Model Provider Update | ModelProviderService.callFastApiMBackendToUpdateAPIKey | N/A | Error updating model provider: ${error.message}`);
      return {
        isSuccess: false,
        message: error
      }
    }
  }


  async validateApiKey(data: {apiKey: string, apiUrl?: string, provider: string}) {
    try {
      const response = await axios.post(`${PYTHON_HOST}/internal/model_provider/validate_api_key`, data);
      logger.info(`Model Provider Validation | ModelProviderService.validateApiKey | N/A | Validation response: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      return {isSuccess: false, message: error.message};
    }
  }
}