/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * cron service for triggering - saving metadata from input queue & trigger initial crawl
 */

/**
 * @class CronTriggerService
 * saving metadata from input queue & trigger initial crawl
 * @description  cron service for triggering - saving metadata from input queue & trigger initial crawl
 * <AUTHOR>
 */

import {Application, bind, BindingKey, BindingScope, CoreBindings, inject} from '@loopback/core';
import dotenv from 'dotenv';
import {logger} from '../config';
import {EMBEDDINGS_SERVICE, EmbeddingsService} from './embeddings.service';
import {JOB_SERVICE, JobService} from './job.service';
import {MEDIA_PROCESS_HANDLER, MediaProcessorService} from './media-processor.service';
import {META_DATA_UPDATE_SERVICE, MetaDataUpdateService} from './meta-data-update.service';
import {META_DATA_SERVICE, MetaDataService} from './meta-data.service';
import {OBJECT_META_UPDATER_SERVICE, ObjectMetaUpdaterService} from './object-meta-updater.service';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {SRC_DEST_CONNECTION_SERVICE, SrcDestConnectionService} from './src-dest-connection.service';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from './system-label.service';
import {SYSTEM_STATS_SERVICE, SystemStatsService} from './system-stats.service';
var CronJob = require('cron').CronJob;
dotenv.config();

const instanceType = process.env.INSTANCE_TYPE;
const subsequentCrawl = process.env.SUBSEQUENT_CRAWL;

@bind({scope: BindingScope.TRANSIENT})
export class CronTriggerService {
  constructor(
    @inject(CoreBindings.APPLICATION_INSTANCE)
    private app: Application,
    @inject(OBJECT_META_UPDATER_SERVICE)
    private objectMetaUpdaterService: ObjectMetaUpdaterService,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(SYSTEM_STATS_SERVICE)
    private systemStatsService: SystemStatsService,
    @inject(META_DATA_SERVICE)
    private metaDataService: MetaDataService,
    @inject(META_DATA_UPDATE_SERVICE)
    private metaDataUpdateService: MetaDataUpdateService,
    @inject(SYSTEM_LABEL_SERVICE)
    private systemLabelService: SystemLabelService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(MEDIA_PROCESS_HANDLER)
    private mediaProcessorService: MediaProcessorService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @inject(EMBEDDINGS_SERVICE)
    private embeddingsService: EmbeddingsService,
    @inject(SRC_DEST_CONNECTION_SERVICE)
    private srcDestConnectionService: SrcDestConnectionService,
  ) {
    if (instanceType == 'master') {
      new Promise(resolve => setTimeout(resolve, 5000)).then(
        async () => await this.systemStatsService.resetSystemFlags(),
      );

      new Promise(resolve => setTimeout(resolve, 5000))
        .then(async () => await this.objectMetaUpdaterService.resetAPIKeyLocks())
        .then(() => this.metaDataInputSavingCronJob());
      if (subsequentCrawl == 'enable') {
        new Promise(resolve => setTimeout(resolve, 5000))
          .then(async () => await this.storageCrawlerService.resetOngoingSubsequentCrawls())
          .then(() => this.subsequentCrawlCronJob());
      }
      new Promise(resolve => setTimeout(resolve, 5000)).then(
        async () => await this.mediaProcessorService.restartOngoingMediaProcessesOnAppStart(),
      ); // restart stopped thumbnail generation on server start
      this.fileUrlUpdateCronJob();
      this.storageCrawlerService.triggerInitialCralwing();
      this.systemStatUpdateCronJob();
      this.searchQueryBuilderService.insertDefaultQueryOptions();
      this.calcMetaDataAnalytics();
      this.fifteenMinutesCronJob();
      this.dailyCronJob();
      // this.dataSourceCronJob();
    }
    // this.dataSourceCronJob();
  }

  /**
   * cron task to read input queue and update metadata and/or metdataUpdates
   **/
  async metaDataInputSavingCronJob() {
    // await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        // cronTime: '*/15 * * * * *', //Fire at every 15 seconds
        cronTime: '* * * * *', //Fire at every 1 minute
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `Processing metaData input Queue | CronTriggerService.metaDataInputSavingCronJob | N/A | triggering metaData input queue saving \n--------------`,
          );
          await this.objectMetaUpdaterService.handleUpdatingMetaDataFromInputQueue(); // update metaData from input queue
          await this.objectMetaUpdaterService.generateThumbnailOfCollection();
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to read input queue and update metadata and/or metdataUpdates
   **/
  async systemStatUpdateCronJob() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        cronTime: '0 */5 * * * *', //Fire at every 5 minute
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `Processing system stat update | CronTriggerService.systemStatUpdateCronJob | N/A | triggering system stat update \n--------------`,
          );

          await this.systemStatsService.updateSystemStats(); // calculate stat counts
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to read input queue and update metadata and/or metdataUpdates
   **/
  async dataSourceCronJob() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        cronTime: '0 */5 * * * *', //Fire at every 5 minute
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `Processing hourly cron job | CronTriggerService.hourlyCronJob | N/A | triggering hourly cron job \n--------------`,
          );

          await this.srcDestConnectionService.syncConnectionJobList(); // calculate connection history
          await this.srcDestConnectionService.syncConnectionStats(); // calculate connection stats
          await this.srcDestConnectionService.syncConnectionSystemStats(); // update system stats about connection
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to update file urls
   **/
  async fileUrlUpdateCronJob() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        cronTime: '0 0 */1 * * *', //Fire at daily '* * */24 * * *' '0 */1 * * * *' '0 0 */1 * * *'
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `Update file urls | CronTriggerService.fileUrlUpdateCronJob | N/A | triggering Update file urls \n--------------`,
          );
          await this.objectMetaUpdaterService.fileUrlRenew();
          await this.systemLabelService.updateSystemLabelImageUrl();
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to calculate the analytics of frames
   **/
  async calcMetaDataAnalytics() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        cronTime: '0 */5 * * * *',
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `MetaData update analytics calculation | CronTriggerService.calcMetaDataAnalytics | N/A | MetaData update analytics calculation \n--------------`,
          );
          await this.metaDataUpdateService.calcMetaDataUpdateAnalytics();

          /**
           * dataset analytics calculation
           */
          await this.metaDataService.calcDatasetAnalyticsWhileModelRunUpdate();
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to detect failed jobs
   **/
  async fifteenMinutesCronJob() {
    try {
      const job = new CronJob({
        cronTime: '0 */15 * * * *',
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `detect failed jobs | CronTriggerService.fiveMinutesCronJob | N/A | detect failed jobs set status \n--------------`,
          );
          await this.searchQueryBuilderService.insertGraphCoordinatesForPendingCollections();
          await this.jobService.setFailedJobsStatus();
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to trigger subsequent crawls
   **/
  async subsequentCrawlCronJob() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        // cronTime: '0 */6 * * *', //Fire at every 6 hours
        cronTime: '0 0 * * *', //Fire at 0 am every day
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `Subsequent crawl | CronTriggerService.subsequentCrawlCronJob | N/A | triggering subsequent crawl from cron`,
          );
          await this.storageCrawlerService.handleTriggerSubsequentCrawling(); // call subsequent crawl
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }

  /**
   * cron task to trigger subsequent crawls
   **/
  async dailyCronJob() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    try {
      const job = new CronJob({
        cronTime: '0 0 * * *', //Fire at 0 am every day
        // cronTime: '0 */1 * * * *',
        context: this,
        timeZone: 'America/Winnipeg',
        onTick: async () => {
          logger.debug(
            `daily cron job | CronTriggerService.subsequentCrawlCronJob | N/A | triggering daily cron job from cron`,
          );
          await this.embeddingsService.deleteSimilarImage();
          await this.embeddingsService.deleteGraphData();
        },
        start: true, // Start the job immediately
      });
    } catch (err) {
      logger.warn(err);
    }
  }
}
export const CRON_JOB_SERVICE = BindingKey.create<CronTriggerService>('service.cronTrigger');
