/*
 * Copyright (c) 2024 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle business logics related to the data dictionary evaluation process
 */

/**
 * @class EvaluationService
 * @description This service use for Handle business logics related to the data dictionary evaluation process
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {default as Axios} from 'axios';
import {ObjectId} from 'bson';
import dotenv from 'dotenv';
import moment from 'moment';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  DataDictTuneUpInfo,
  DifficultyLevel,
  EvalSnap,
  EvalSnapObject,
  EvalSnapRelations,
  QuestionEvalStatus,
  TuneUpUsageStatus,
} from '../models';
import {
  BulkUpdateOneDataFormat,
  DataDictTuneUpStatus,
  EvalSet,
  EvalSetInfo,
  EvalSetRelations,
  EvalSetRunStats,
  EvalSetStatus,
} from '../models/eval-set.model';
import {
  EvalHistRepository,
  EvalSetRepository,
  EvalSetRunRepository,
  EvalSnapRepository,
  SystemDataRepository,
} from '../repositories';
import {EvalFeedbackType, FLOWS} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {timeAgo} from '../settings/tools';
const fs = require('fs');
const csv = require('csvtojson/v2');

dotenv.config();
const LLM_AGENT_INTERNAL_URL = process.env.LLM_AGENT_INTERNAL_URL;
const PYTHON_HOST = process.env.PYTHON_BASE_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class EvaluationService {
  constructor(
    @repository(EvalSetRepository)
    public evalSetRepository: EvalSetRepository,
    @repository(EvalSnapRepository)
    public evalSnapRepository: EvalSnapRepository,
    @repository(EvalHistRepository)
    public evalHistRepository: EvalHistRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @repository(EvalSetRunRepository)
    private evalSetRunRepository: EvalSetRunRepository,
  ) {}

  /**
   * Creates a new evaluation set with a unique number.
   *
   * @returns {Promise<{success: boolean, message: string, data: {evalSetNumber: number, evalSetId: ObjectId}}>}
   * - `success` - true if the evaluation set is created successfully, false otherwise.
   * - `message` - a success message if the evaluation set is created successfully, an error message otherwise.
   * - `data` - an object with the evaluation set number and id.
   */
  async createEvalSet() {
    //increment lastEvalSetNumber by 1 in system data
    const evalNum = await this.systemDataRepository.findOneAndUpdate(
      {},
      {$inc: {lastEvalSetNumber: 1}},
      {
        returnDocument: 'after',
        projection: {lastEvalSetNumber: 1},
      },
    );

    if (!evalNum?.value?.lastEvalSetNumber) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.createEvalSet | N/A | Unable to create eval set. lastEvalSetNumber not found`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_CREATE_EVALUATION_FAILED,
      };
    }

    //create eval set with incremented lastEvalSetNumber
    const evalSet = await this.evalSetRepository.create({
      name: `Evaluation Set Draft ${evalNum.value.lastEvalSetNumber}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      evalSetNumber: evalNum.value.lastEvalSetNumber,
      status: EvalSetStatus.DRAFT,
      isRunning: false,
      totalQuestions: 0,
      lastRunStats: {
        passedQuestions: 0,
        failedQuestions: 0,
        notRunQuestions: 0,
        score: 0,
      },
    });

    if (!evalSet) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.createEvalSet | N/A | Unable to create eval set for eval number: ${evalNum.value.lastEvalSetNumber}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_CREATE_EVALUATION_FAILED,
      };
    }

    logger.info(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.createEvalSet | N/A | Successfully created eval set for eval number: ${evalNum.value.lastEvalSetNumber}`,
    );
    return {
      success: true,
      data: {
        evalSetNumber: evalNum.value.lastEvalSetNumber,
        evalSetId: evalSet._id,
      },
    };
  }

  /**
   * Updates an existing evaluation set with the given name.
   * @param evalSetId - The id of the evaluation set to be updated.
   * @param name - The new name for the evaluation set.
   * @returns {Promise<{success: boolean, message: string, data: {message: string}}>}
   * - `success` - true if the evaluation set is updated successfully, false otherwise.
   * - `message` - a success message if the evaluation set is updated successfully, an error message otherwise.
   * - `data` - an object with the success message.
   */
  async updateEvalSet(evalSetId: string, name: string) {
    try {
      await this.evalSetRepository.updateById(evalSetId, {
        name: name,
        updatedAt: new Date(),
        status: EvalSetStatus.ACTIVE,
      });

      logger.info(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.updateEvalSet | N/A | Successfully updated eval set: ${evalSetId} to name: ${name}`,
      );

      return {
        success: true,
        data: {
          message: DatalakeUserMessages.EVAL_UPDATE_EVALUATION_SUCCESS,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.updateEvalSet | N/A | Failed to update eval set: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_UPDATE_EVALUATION_FAILED,
      };
    }
  }

  /**
   * Returns a list of evaluation sets and data dict tune up status.
   * @returns {Promise<{success: boolean, message: string, data: {evalSets: {evalSetId?: string, name: string, evalSetNumber: number, createdAt: Date, lastRunAt: string | null, score: number, totalQuestions: number, isRunnable: boolean, isRunning: boolean}[], dataDictTuneUpInfo: {tuneUpStatus: DataDictTuneUpStatus, tunedAt: Date | null}}}>}
   * - `success` - true if the evaluation sets are returned successfully, false otherwise.
   * - `message` - a success message if the evaluation sets are returned successfully, an error message otherwise.
   * - `data` - an object with the evaluation sets and data dict tune up status.
   */
  async getEvalSets() {
    const evalSetListResponse: {
      evalSets: {
        evalSetId?: string;
        name: string;
        evalSetNumber: number;
        createdAt: Date;
        lastRunAt: string | null;
        score: number;
        totalQuestions: number;
        isRunnable: boolean;
        isRunning: boolean;
      }[];
      dataDictTuneUpInfo: {
        tuneUpStatus: DataDictTuneUpStatus;
        tunedAt: Date | null;
      };
    } = {
      evalSets: [],
      dataDictTuneUpInfo: {
        tuneUpStatus: DataDictTuneUpStatus.NOT_TUNED,
        tunedAt: null,
      },
    };

    try {
      //get all eval sets
      const [evalSets, dataDictTuneUpInfo] = await Promise.all([
        this.evalSetRepository.find({
          where: {
            status: EvalSetStatus.ACTIVE,
          },
          order: ['evalSetNumber ASC'],
        }),
        this.dataDictTuneUpInfo(),
      ]);

      // assign tune up status to response
      if (dataDictTuneUpInfo.isTuneOngoing) {
        evalSetListResponse.dataDictTuneUpInfo.tuneUpStatus = DataDictTuneUpStatus.IN_PROGRESS;
      } else if (dataDictTuneUpInfo?.revisionNum && dataDictTuneUpInfo.revisionNum > 0) {
        evalSetListResponse.dataDictTuneUpInfo.tuneUpStatus = DataDictTuneUpStatus.TUNED;
        if (dataDictTuneUpInfo.lastTuneUpStartedAt)
          evalSetListResponse.dataDictTuneUpInfo.tunedAt = dataDictTuneUpInfo.lastTuneUpStartedAt;
      }

      // assign eval sets to response
      if (evalSets && evalSets.length > 0) {
        evalSetListResponse.evalSets = evalSets.map(evalSet => {
          const _timeAgo = evalSet.lastRunStartedAt ? timeAgo(evalSet.lastRunStartedAt) : null;

          let _isRunnable: boolean = true;
          //if eval set is running or data dict tune up is ongoing, set isRunnable to false
          if (evalSet.isRunning) {
            _isRunnable = false;
          } else if (dataDictTuneUpInfo.isTuneOngoing) {
            _isRunnable = false;
          }

          const _isRunning = evalSet.isRunning ? evalSet.isRunning : false;

          return {
            evalSetId: evalSet._id,
            name: evalSet.name,
            evalSetNumber: evalSet.evalSetNumber,
            createdAt: evalSet.createdAt,
            lastRunAt: _timeAgo,
            score: evalSet.totalQuestions ? (evalSet.lastRunStartedAt ? evalSet.lastRunStats?.score ?? 0 : -1) : -1,
            totalQuestions: evalSet.totalQuestions,
            isRunnable: _isRunnable,
            isRunning: _isRunning,
          };
        });
      }

      return {
        success: true,
        data: evalSetListResponse,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.getEvalSets | N/A | Failed to get eval sets, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_GET_EVALUATION_LIST_FAILED,
      };
    }
  }

  /**
   * @description Get info of a specific eval set, including its current status (e.g. is running, is runnable, score, etc.)
   * @param evalSetId The id of the eval set
   * @returns {Promise<{success: boolean, data: EvalSetInfo}>}
   */
  async getEvalSetSnapInfo(evalSetId: string) {
    try {
      const [evalSet, dataDictTuneUpInfo] = await Promise.all([
        this.evalSetRepository.findById(evalSetId),
        this.dataDictTuneUpInfo(),
      ]);

      const info = await this.formatEvalSetSnapInfo(evalSet, dataDictTuneUpInfo);

      return {
        success: true,
        data: info,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.getEvalSetSnapInfo | N/A | Failed to get eval set snap info for eval set ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_GET_EVALUATION_LIST_INFO_FAILED,
      };
    }
  }
  /**
   * Formats the evaluation set snapshot information by combining basic info and statistics.
   *
   * @param evalSet - The evaluation set object containing details and statistics of an evaluation.
   * @param dataDictTuneUpInfo - Information about the ongoing status of data dictionary tune-up.
   * @returns {Promise<EvalSetInfo>} - An object containing formatted evaluation set information,
   * including its name, number, run status, and evaluation statistics.
   */
  async formatEvalSetSnapInfo(evalSet: EvalSet & EvalSetRelations, dataDictTuneUpInfo: DataDictTuneUpInfo) {
    const evalSetInfo: EvalSetInfo = {
      name: '',
      evalSetNumber: 0,
      isRunnable: true,
      isRunning: false,
      totalQuestions: 0,
      passedQuestions: 0,
      failedQuestions: 0,
      notRunQuestions: 0,
      score: 0,
      difficultyLevelBreakdown: [],
    };

    let evalSetIsRunnable: boolean = true;
    //if eval set is running or data dict tune up is ongoing, set isRunnable to false
    if (evalSet.isRunning) {
      evalSetIsRunnable = false;
    } else if (dataDictTuneUpInfo.isTuneOngoing) {
      evalSetIsRunnable = false;
    }

    //assign eval set basic info
    evalSetInfo.evalSetId = evalSet._id;
    evalSetInfo.name = evalSet.name;
    evalSetInfo.evalSetNumber = evalSet.evalSetNumber;
    evalSetInfo.lastRunStartedAt = evalSet.lastRunStartedAt;
    evalSetInfo.isRunning = evalSet.isRunning;
    evalSetInfo.isRunnable = evalSetIsRunnable;

    //assign eval set stats
    evalSetInfo.totalQuestions = evalSet.totalQuestions;
    evalSetInfo.passedQuestions = evalSet.totalQuestions
      ? evalSet.lastRunStartedAt
        ? evalSet.lastRunStats?.passedQuestions ?? 0
        : -1
      : -1;
    evalSetInfo.failedQuestions = evalSet.totalQuestions
      ? evalSet.lastRunStartedAt
        ? evalSet.lastRunStats?.failedQuestions ?? 0
        : -1
      : -1;
    evalSetInfo.notRunQuestions = evalSet.totalQuestions ? evalSet.lastRunStats?.notRunQuestions ?? 0 : -1;
    evalSetInfo.score = evalSet.totalQuestions
      ? evalSet.lastRunStartedAt
        ? evalSet.lastRunStats?.score ?? 0
        : -1
      : -1;
    evalSetInfo.difficultyLevelBreakdown = evalSet.lastRunStats?.difficultyLevelBreakdown || [];

    return evalSetInfo;
  }

  async getEvalSetSnapList(evalSetId: string, pageIndex: number = 0, pageSize: number = 20, searchKey?: string) {
    let evalSnapResponse: {
      evalSnapList: EvalSnapObject[];
      evalSetInfo: EvalSetInfo;
    } = {
      evalSnapList: [],
      evalSetInfo: {
        name: '',
        evalSetNumber: 0,
        isRunnable: true,
        isRunning: false,
        totalQuestions: 0,
        passedQuestions: 0,
        failedQuestions: 0,
        notRunQuestions: 0,
        score: 0,
        difficultyLevelBreakdown: [],
      },
    };

    try {
      const evalSet = await this.evalSetRepository.findById(evalSetId);

      let _filter: {[k: string]: any} = {
        evalSetId: evalSetId,
      };

      if (searchKey) {
        _filter = {
          ..._filter,
          question: {regexp: new RegExp(searchKey, 'i')},
        };
      }

      const [evalSnapList, dataDictTuneUpInfo] = await Promise.all([
        this.evalSnapRepository.find({
          where: _filter,
          order: ['qNum ASC'],
          skip: pageIndex * pageSize,
          limit: pageSize,
        }),
        this.dataDictTuneUpInfo(),
      ]);

      //format ground truth info of the eval set
      if (evalSnapList && evalSnapList.length > 0) {
        evalSnapResponse.evalSnapList = evalSnapList.map(evalSnap => {
          let _isRunnable: boolean = true;

          //if eval set is running or data dict tune up is ongoing, set isRunnable to false
          if (evalSet.isRunning) {
            _isRunnable = false;
          } else if (dataDictTuneUpInfo.isTuneOngoing) {
            _isRunnable = false;
          }

          let _questionType: 'easy' | 'normal' | 'hard';
          if (evalSnap.difficulty == DifficultyLevel.EASY) {
            _questionType = 'easy';
          } else if (evalSnap.difficulty == DifficultyLevel.HARD) {
            _questionType = 'hard';
          } else {
            _questionType = 'normal';
          }

          return {
            evalSnapId: evalSnap._id,
            createdAt: evalSnap.createdAt,
            question: evalSnap.question,
            truth: evalSnap.truth,
            qNum: evalSnap.qNum,
            aiAction: evalSnap.aiAction,
            isPassed: evalSnap.questionEvalStatus === QuestionEvalStatus.PASSED,
            isRunnable: _isRunnable,
            isRunning:
              evalSnap.isRunning || (evalSet.isRunning && evalSnap.questionEvalStatus === QuestionEvalStatus.NOT_RUN),
            questionEvalStatus: evalSnap.isRunning ? QuestionEvalStatus.NOT_RUN : evalSnap.questionEvalStatus,
            questionType: _questionType,
          };
        });
      }
      // assign stats info
      evalSnapResponse.evalSetInfo = await this.formatEvalSetSnapInfo(evalSet, dataDictTuneUpInfo);

      return {
        success: true,
        data: evalSnapResponse,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.getEvalSetSnapList | N/A | Failed to get eval set snap for eval set: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_GET_QUESTION_LIST_FAILED,
      };
    }
  }

  /**
   * Delete an eval set.
   * @param evalSetId The id of the eval set to delete
   * @returns {Promise<{success: boolean, data: {success: boolean, message: string}}>}
   * @throws {Error} If data dict tune up is ongoing or eval set is running
   */
  async deleteEvalSet(evalSetId: string) {
    //check if data dict tune up is ongoing
    const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
    if (dataDictTuneUpInfo.isTuneOngoing) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
      };
    }

    try {
      //check if eval set is running
      const evalSet = await this.evalSetRepository.findById(evalSetId);
      if (evalSet && evalSet.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      logger.info(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.deleteEvalSet | N/A | starting to delete eval set: ${evalSetId}`,
      );

      //delete eval set
      await this.evalSetRepository.deleteById(evalSetId);

      logger.info(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.deleteEvalSet | N/A | successfully deleted eval set: ${evalSetId}`,
      );

      //delete eval snaps
      await this.evalSnapRepository.deleteAll({evalSetId: evalSetId});

      logger.info(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.deleteEvalSet | N/A | successfully deleted all eval snaps for eval set: ${evalSetId}`,
      );

      return {
        success: true,
        data: {
          success: true,
          message: DatalakeUserMessages.EVAL_DELETE_EVALUATION_SUCCESS,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.deleteEvalSet | N/A | Failed to delete eval set: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DELETE_EVALUATION_FAILED,
      };
    }
  }

  /**
   * Add a ground truth to an evaluation set.
   * @param evalSetId - Id of the evaluation set to add the ground truth to.
   * @param question - The question asked in the ground truth.
   * @param truth - The answer to the question.
   * @param difficulty - The difficulty level of the question.
   * @param feedbackType - The type of feedback. Defaults to {@link EvalFeedbackType.GROUND_TRUTH}.
   * @param aiAction - The AI action taken when the user asked the question. Only applicable when feedbackType is {@link EvalFeedbackType.COMMENT}.
   * @returns A response with a success status and the eval snap id if the ground truth is added successfully, otherwise a failure status and a message.
   */
  async addTruthToEvalSet(
    evalSetId: string,
    question: string,
    truth: string,
    difficulty: DifficultyLevel,
    feedbackType: EvalFeedbackType = EvalFeedbackType.GROUND_TRUTH,
    aiAction: string = '',
  ) {
    try {
      const evalSet = await this.evalSetRepository.findById(evalSetId);
      if (evalSet.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      //increment lastQuestionNumber by 1 in eval set
      const evalSetQNum = await this.evalSetRepository.findOneAndUpdate(
        {_id: new ObjectId(evalSetId)},
        {$inc: {lastQuestionNumber: 1}},
        {
          returnDocument: 'after',
          projection: {lastQuestionNumber: 1, isRunning: 1},
        },
      );

      const _difficulty = Object.values(DifficultyLevel)
        .filter(value => typeof value === 'number')
        .includes(difficulty)
        ? difficulty
        : DifficultyLevel.NORMAL;

      //add ground truth to eval snap
      const createObj: {
        evalSetId: string;
        question: string;
        truth: string;
        qNum: any;
        difficulty: DifficultyLevel;
        questionEvalStatus: QuestionEvalStatus;
        createdAt: Date;
        updatedAt: Date;
        isRunning: boolean;
        feedbackType: EvalFeedbackType;
        tuneUpUsageStatus?: TuneUpUsageStatus; // Add this property
        aiAction?: string;
        dataDictRevisionNum?: number;
      } = {
        evalSetId: evalSetId,
        question: question,
        truth: truth,
        qNum: evalSetQNum.value.lastQuestionNumber,
        difficulty: _difficulty,
        questionEvalStatus: QuestionEvalStatus.NOT_RUN,
        tuneUpUsageStatus: TuneUpUsageStatus.AI_ACTION_PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
        isRunning: false,
        feedbackType: feedbackType,
      };

      if (feedbackType === EvalFeedbackType.COMMENT) {
        createObj.tuneUpUsageStatus = TuneUpUsageStatus.TUNE_UP_PENDING;
        createObj.aiAction = aiAction;
        // Get the current data dictionary revision number
        const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
        createObj.dataDictRevisionNum = dataDictTuneUpInfo.revisionNum;
      }

      const evalSnap = await this.evalSnapRepository.create(createObj);

      await this.updateEvalSetStats(evalSetId);

      return {
        success: true,
        data: {
          evalSnapId: evalSnap._id,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.addTruthToEvalSet | N/A | Failed to add ground truth to eval set: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_ADD_NEW_QUESTION_FAILED,
      };
    }
  }

  /**
   * Edits a ground truth in an evaluation set.
   * @param evalSnapId - Id of the eval snap to edit.
   * @param question - The question asked in the ground truth.
   * @param truth - The answer to the question.
   * @param difficulty - The difficulty level of the question.
   * @returns A response with a success status and the eval snap id if the ground truth is edited successfully, otherwise a failure status and a message.
   */
  async editTruth(evalSnapId: string, question: string, truth: string, difficulty: DifficultyLevel) {
    //check if data dict tune up is ongoing
    const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
    if (dataDictTuneUpInfo.isTuneOngoing) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
      };
    }

    try {
      //check given eval snap is running
      const evalSnap = await this.evalSnapRepository.findById(evalSnapId);

      if (evalSnap.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_QUESTION_RUNNING,
        };
      }

      //check if eval set is running
      const evalSet = await this.evalSetRepository.findById(evalSnap.evalSetId);
      if (evalSet && evalSet.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      const _difficulty = Object.values(DifficultyLevel)
        .filter(value => typeof value === 'number')
        .includes(difficulty)
        ? difficulty
        : DifficultyLevel.NORMAL;

      await this.evalSnapRepository.updateOne(
        {
          _id: new ObjectId(evalSnapId),
        },
        {
          $set: {
            question: question,
            truth: truth,
            difficulty: _difficulty,
            questionEvalStatus: QuestionEvalStatus.NOT_RUN,
            tuneUpUsageStatus: TuneUpUsageStatus.AI_ACTION_PENDING,
            updatedAt: new Date(),
          },
          $unset: {
            aiAction: '',
            aiAnswer: '',
            conversationId: '',
            dataDictRevisionNum: '',
          },
        },
      );

      await this.updateEvalSetStats(evalSnap.evalSetId);

      return {
        success: true,
        data: {
          evalSnapId: evalSnapId,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.editTruth | N/A | Failed to edit ground truth of eval snap: ${evalSnapId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_EDIT_EVALUATION_FAILED,
      };
    }
  }

  /**
   * Deletes a ground truth from an evaluation set.
   *
   * @param evalSnapId - The ID of the eval snap to be deleted.
   *
   * @returns An object with a success status and a message. If the deletion
   * is successful, the object includes the deleted eval snap ID. If the
   * deletion fails due to the data dictionary tune-up process being ongoing,
   * the eval snap or set being currently running, or an error occurs, the
   * object includes a failure message.
   */
  async deleteTruth(evalSnapId: string) {
    //check if data dict tune up is ongoing
    const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
    if (dataDictTuneUpInfo.isTuneOngoing) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
      };
    }

    try {
      //check given eval snap is running
      const evalSnap = await this.evalSnapRepository.findById(evalSnapId);

      if (evalSnap.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_QUESTION_RUNNING,
        };
      }

      //check if eval set is running
      const evalSet = await this.evalSetRepository.findById(evalSnap.evalSetId);
      if (evalSet && evalSet.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      await this.evalSnapRepository.deleteById(evalSnapId);

      await this.updateEvalSetStats(evalSnap.evalSetId);

      return {
        success: true,
        data: {
          evalSnapId: evalSnapId,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.deleteTruth | N/A | Failed to delete ground truth of eval snap: ${evalSnapId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DELETE_QUESTION_FAILED,
      };
    }
  }

  /**
   * This method is used to update the stats of an eval set after creating or removing an eval snap
   * @param evalSetId - The ID of the evaluation set
   * @returns {Promise<void>} - A promise that resolves if the update is successful
   */
  async updateEvalSetStats(evalSetId?: string) {
    let aggregatePipeline = [
      {$match: {evalSetId: new ObjectId(evalSetId)}},
      {
        $group: {
          _id: '$difficulty',
          totalQuestions: {$sum: 1},
          passedQuestions: {
            $sum: {$cond: [{$eq: ['$questionEvalStatus', 2]}, 1, 0]},
          },
          failedQuestions: {
            $sum: {$cond: [{$eq: ['$questionEvalStatus', 3]}, 1, 0]},
          },
        },
      },
    ];

    const stats: {
      _id: DifficultyLevel;
      totalQuestions: number;
      passedQuestions: number;
      failedQuestions: number;
    }[] = await this.evalSnapRepository.aggregate(aggregatePipeline);

    const lastRunStats: EvalSetRunStats = {
      calculatedAt: new Date(),
      passedQuestions: 0,
      failedQuestions: 0,
      notRunQuestions: 0,
      score: 0,
      difficultyLevelBreakdown: [],
    };

    // calculate overall stats for eval set
    let allTotalQuestions = 0;
    let allPassedQuestions = 0;
    let allFailedQuestions = 0;
    let allEvaluatedQuestions = 0;
    let allNotRunQuestions = 0;
    let allScore = 0;

    if (Array.isArray(stats) && stats.length > 0) {
      for (let i = 0; i < stats.length; i++) {
        allTotalQuestions += stats[i].totalQuestions;
        allPassedQuestions += stats[i].passedQuestions;
        allFailedQuestions += stats[i].failedQuestions;
        allEvaluatedQuestions = allPassedQuestions + allFailedQuestions;
        allNotRunQuestions = allTotalQuestions - allEvaluatedQuestions;
        if (allEvaluatedQuestions > 0) allScore = Math.round((allPassedQuestions / allEvaluatedQuestions) * 100);

        // calculate stats for each difficulty level
        const difficultyLevelTotalQuestions = stats[i].totalQuestions;
        const difficultyLevelPassedQuestions = stats[i].passedQuestions;
        const difficultyLevelFailedQuestions = stats[i].failedQuestions;
        const difficultyLevelEvaluatedQuestions = difficultyLevelPassedQuestions + difficultyLevelFailedQuestions;
        const difficultyLevelNotRunQuestions = difficultyLevelTotalQuestions - difficultyLevelEvaluatedQuestions;
        let difficultyLevelScore = 0;
        if (difficultyLevelEvaluatedQuestions > 0) {
          difficultyLevelScore = Math.round((difficultyLevelPassedQuestions / difficultyLevelEvaluatedQuestions) * 100);
        }

        lastRunStats.difficultyLevelBreakdown.push({
          difficultyLevel: stats[i]._id,
          totalQuestions: difficultyLevelTotalQuestions,
          passedQuestions: difficultyLevelPassedQuestions,
          failedQuestions: difficultyLevelFailedQuestions,
          notRunQuestions: difficultyLevelNotRunQuestions,
          score: difficultyLevelScore,
        });
      }

      //sort lastRunStats.difficultyLevelBreakdown based on difficultyLevel
      lastRunStats.difficultyLevelBreakdown.sort((a, b) => {
        return a.difficultyLevel - b.difficultyLevel;
      });
    }

    lastRunStats.passedQuestions = allPassedQuestions;
    lastRunStats.failedQuestions = allFailedQuestions;
    lastRunStats.notRunQuestions = allNotRunQuestions;
    lastRunStats.score = allScore;

    await this.evalSetRepository.updateById(evalSetId, {
      totalQuestions: allTotalQuestions,
      lastRunStats: lastRunStats,
    });
  }

  /**
   * Changes the evaluation status of an eval snap
   * @param evalSnapId id of the eval snap
   * @param status new evaluation status
   * @returns {success: boolean, message: string, data: {evalSnapId: string}} if successful, otherwise {success: boolean, message: string}
   */
  async changeEvaluationStatus(evalSnapId: string, status: QuestionEvalStatus) {
    try {
      //check if data dict tune up is ongoing
      const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
      if (dataDictTuneUpInfo.isTuneOngoing) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
        };
      }

      const evalSnap = await this.evalSnapRepository.findById(evalSnapId);

      if (evalSnap.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_QUESTION_RUNNING,
        };
      }

      if (evalSnap.questionEvalStatus === status) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_QUESTION_STATUS_ALREADY_IN_THIS_STATUS,
        };
      }

      if (evalSnap.questionEvalStatus === QuestionEvalStatus.FAILED && status === QuestionEvalStatus.PASSED) {
        await this.evalSnapRepository.updateById(evalSnapId, {
          questionEvalStatus: status,
          tuneUpUsageStatus: TuneUpUsageStatus.TUNE_UP_NOT_REQUIRED,
        });
      } else if (evalSnap.questionEvalStatus === QuestionEvalStatus.PASSED && status === QuestionEvalStatus.FAILED) {
        await this.evalSnapRepository.updateById(evalSnapId, {
          questionEvalStatus: status,
          tuneUpUsageStatus: TuneUpUsageStatus.TUNE_UP_PENDING,
        });
      } else {
        logger.error(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.changeEvaluationStatus | N/A | Invalid status change request: evalSnapId: ${evalSnapId}, requested status: ${status}, current status: ${evalSnap.questionEvalStatus}`,
        );
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_INVALID_EVALUATION_STATUS_CHANGE_REQUEST,
        };
      }

      await this.updateEvalSetStats(evalSnap.evalSetId);

      return {
        success: true,
        data: {
          evalSnapId: evalSnapId,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.changeEvaluationStatus | N/A | Failed to change evaluation status of eval snap: ${evalSnapId} to ${status}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_QUESTION_EVALUATION_STATUS_CHANGE_FAILED,
      };
    }
  }

  /**
   * Returns the current data dictionary tune up information.
   * @returns {{
   *   revisionNum: number,
   *   isTuneOngoing: boolean,
   * }}
   */
  async dataDictTuneUpInfo() {
    const _dataDictTuneUpInfo = await this.systemDataRepository.findOne({
      fields: ['dataDictTuneUpInfo'],
    });

    if (!_dataDictTuneUpInfo || !_dataDictTuneUpInfo.dataDictTuneUpInfo) {
      return {
        revisionNum: 0,
        isTuneOngoing: false,
      };
    }

    //if revisionNum is not exist then set it to 0
    if (!_dataDictTuneUpInfo.dataDictTuneUpInfo.revisionNum) {
      _dataDictTuneUpInfo.dataDictTuneUpInfo.revisionNum = 0;
    }

    //if isTuneOngoing is not exist then set it to false
    if (!_dataDictTuneUpInfo.dataDictTuneUpInfo.isTuneOngoing) {
      _dataDictTuneUpInfo.dataDictTuneUpInfo.isTuneOngoing = false;
    }

    return _dataDictTuneUpInfo.dataDictTuneUpInfo;
  }

  /**
   * Runs an evaluation set by initiating a background process to assess the evaluation snaps.
   * Checks if the data dictionary tune-up is ongoing, if the evaluation set is already running,
   * or if there are no ground truths before proceeding. If the evaluation set has run previously,
   * it moves the snaps to history and triggers the evaluation set run in the background.
   * Logs the process and returns the success status and message.
   *
   * @param evalSetId - The ID of the evaluation set to run.
   * @param currentUserProfile - The profile of the current user running the evaluation set.
   * @returns {Promise<{success: boolean, message: string}>} - A promise that resolves with the success status and message.
   */
  async runEvaluationSet(evalSetId: string, currentUserProfile: UserProfileDetailed) {
    try {
      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvaluationSet | N/A | evalSetId: ${evalSetId} triggered by: ${currentUserProfile.id}`,
      );

      //check if data dict tune up is ongoing
      const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
      if (dataDictTuneUpInfo.isTuneOngoing) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
        };
      }

      const evalSet = await this.evalSetRepository.findById(evalSetId);

      //check if evaluation set is running
      if (evalSet.isRunning) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      //check if total questions is 0
      if (!evalSet.totalQuestions) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_QUESTIONS_NOT_FOUND,
        };
      }

      const truthList = await this.evalSnapRepository.find({
        where: {
          evalSetId: evalSetId,
        },
      });

      // if evaluation set has run previously, then move it to history
      if (evalSet.lastRunStartedAt && evalSet._id) {
        await this.moveEvaluationSnapsToHist(evalSet, truthList);
      }

      this.initiateBackgroundRunEvaluationSet(evalSetId, truthList, currentUserProfile, dataDictTuneUpInfo.revisionNum)
        .catch(error => {
          logger.error(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvaluationSet | N/A | Failed to initiate background run evaluation set: ${evalSetId}, error: ${error}`,
          );
        })
        .then(() => {
          logger.info(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvaluationSet | N/A | Successfully completed background run evaluation set: ${evalSetId}`,
          );
        })
        .finally(() => {
          logger.info(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvaluationSet | N/A | Completed run evaluation set: ${evalSetId}`,
          );
        });

      return {
        success: true,
        data: {
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvaluationSet | N/A | Failed to run evaluation set: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING_FAILED,
      };
    }
  }

  /**
   * Moves the evaluation snaps and their corresponding eval set run to their
   * respective history tables.
   * @param evalSet - The evaluation set object.
   * @param truthList - The evaluation snaps to be moved.
   * @returns {Promise<void>} - A promise that resolves if the operation is successful.
   */
  async moveEvaluationSnapsToHist(evalSet: EvalSet & EvalSetRelations, truthList: (EvalSnap & EvalSnapRelations)[]) {
    //move eval run set snap to its hist table
    let evalSetRunObj = await this.evalSetRunRepository.create({
      evalSetId: evalSet._id,
      name: evalSet.name,
      movedAt: new Date(),
      createdAt: evalSet.createdAt,
      updatedAt: evalSet.updatedAt,
      evalSetNumber: evalSet.evalSetNumber,
      status: evalSet.status,
      totalQuestions: evalSet.totalQuestions,
      lastRunStats: evalSet.lastRunStats,
      lastRunStartedAt: evalSet.lastRunStartedAt,
      lastRunCompletedAt: evalSet.lastRunCompletedAt,
      lastRunUserId: evalSet.lastRunUserId,
      lastRunUserName: evalSet.lastRunUserName,
      isUserFeedback: evalSet.isUserFeedback,
      dataDictRevisionNum: evalSet.dataDictRevisionNum,
    });

    //move eval snaps to its eval hist table
    let truthHistList = truthList.map(snap => {
      return {
        evalSnapId: snap._id,
        evalSetRunId: evalSetRunObj._id,
        question: snap.question,
        truth: snap.truth,
        evalSetId: snap.evalSetId,
        questionEvalStatus: snap.questionEvalStatus,
        qNum: snap.qNum,
        aiAction: snap.aiAction,
        aiAnswer: snap.aiAnswer,
        conversationId: snap.conversationId,
        createdAt: snap.createdAt,
        updatedAt: snap.updatedAt,
        difficulty: snap.difficulty,
        tuneUpUsageStatus: snap.tuneUpUsageStatus,
        feedbackType: snap.feedbackType,
        assessedAt: snap.assessedAt,
        assessmentResult: snap.assessmentResult,
        dataDictRevisionNum: snap.dataDictRevisionNum,
        tunedUpAt: snap.tunedUpAt,
      };
    });

    await this.evalHistRepository.insertMany(truthHistList, {ordered: false});

    //reset eval snap answers
    await this.evalSnapRepository.updateMany(
      {
        evalSetId: new ObjectId(evalSet._id),
      },
      {
        $set: {
          questionEvalStatus: QuestionEvalStatus.NOT_RUN,
          tuneUpUsageStatus: TuneUpUsageStatus.AI_ACTION_PENDING,
          isRunning: false,
          updatedAt: new Date(),
        },
        $unset: {
          aiAction: '',
          aiAnswer: '',
          conversationId: '',
          dataDictRevisionNum: '',
          tunedUpAt: '',
          assessedAt: '',
          assessmentResult: '',
        },
      },
    );

    //reset eval set stats
    await this.updateEvalSetStats(evalSet._id);
  }

  /**
   * Runs an evaluation set in the background.
   * @param evalSetId - The ID of the evaluation set to be run.
   * @param truthList - The list of truth entries to be run.
   * @param currentUserProfile - The user profile of the person who requested the run.
   * @param dataDictRevisionNum - The revision number of the data dictionary to be used.
   * @returns nothing
   */
  async initiateBackgroundRunEvaluationSet(
    evalSetId: string,
    truthList: (EvalSnap & EvalSnapRelations)[],
    currentUserProfile: UserProfileDetailed,
    dataDictRevisionNum: number,
  ) {
    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.initiateBackgroundRunEvaluationSet | N/A | evalSetId: ${evalSetId},`,
    );

    if (!currentUserProfile.id || !currentUserProfile.name) {
      logger.warn(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.initiateBackgroundRunEvaluationSet | N/A | username or id not found for current user: ${currentUserProfile}`,
      );
      return;
    }

    //make eval set running
    await this.evalSetRepository.updateById(evalSetId, {
      isRunning: true,
      lastRunStartedAt: new Date(),
      lastRunUserName: currentUserProfile.name,
      lastRunUserId: currentUserProfile.id,
      dataDictRevisionNum: dataDictRevisionNum,
    });

    for (const _truth of truthList) {
      await this.runEval(_truth, currentUserProfile.id, currentUserProfile.name, dataDictRevisionNum);
    }

    //make eval set not running
    await this.evalSetRepository.updateById(evalSetId, {
      isRunning: false,
      lastRunCompletedAt: new Date(),
    });
  }

  /**
   * Run an evaluation snap.
   * @param evalSnapId - The ID of the eval snap to run.
   * @param currentUserProfile - The user profile of the current user.
   * @returns An object indicating the success or failure of the operation,
   *          along with a relevant message if failed.
   */
  async runEvalSnap(evalSnapId: string, currentUserProfile: UserProfileDetailed) {
    if (!currentUserProfile.id || !currentUserProfile.name) {
      logger.warn(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvalSnap | N/A | username or id not found for current user: ${currentUserProfile}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_INSUFFICIENT_USER_INFO,
      };
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvalSnap | N/A | evalSnapId: ${evalSnapId}, by user: ${currentUserProfile.id}`,
    );

    const evalSnap = await this.evalSnapRepository.findById(evalSnapId);

    //check eval snap running
    if (evalSnap.isRunning) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_QUESTION_RUNNING,
      };
    }

    //check if data dict tune up is ongoing
    const dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
    if (dataDictTuneUpInfo.isTuneOngoing) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
      };
    }

    const evalSet = await this.evalSetRepository.findById(evalSnap.evalSetId);

    //check if evaluation set is running
    if (evalSet.isRunning) {
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
      };
    }

    /**
     * NOTE: single eval snap execution will be attached/replaced to the result of its latest eval set execution
     */
    let isSingleEvalRunExecutingAsNewSetRun: boolean;
    //check if previous evalSet execution exist
    if (evalSet.lastRunStartedAt) {
      //check last eval set execution result is matched with currently existing data dict revision number
      if ('dataDictRevisionNum' in evalSet && evalSet.dataDictRevisionNum == dataDictTuneUpInfo.revisionNum) {
        isSingleEvalRunExecutingAsNewSetRun = false;
        //reset existing eval snap answers
        await this.evalSnapRepository.updateOne(
          {
            _id: new ObjectId(evalSnapId),
          },
          {
            $set: {
              questionEvalStatus: QuestionEvalStatus.NOT_RUN,
              tuneUpUsageStatus: TuneUpUsageStatus.AI_ACTION_PENDING,
              isRunning: true,
            },
            $unset: {
              aiAction: '',
              aiAnswer: '',
              conversationId: '',
              dataDictRevisionNum: '',
            },
          },
        );
        await this.updateEvalSetStats(evalSnap.evalSetId);
      } else {
        //move existing evalSet result to the history, then start new eval set run, but only execute requested eval snap
        isSingleEvalRunExecutingAsNewSetRun = true;
        //move eval set result to history
        const truthList = await this.evalSnapRepository.find({
          where: {
            evalSetId: evalSet._id,
          },
        });
        await this.moveEvaluationSnapsToHist(evalSet, truthList);

        //update eval set info
        await this.evalSetRepository.updateById(evalSet._id, {
          lastRunStartedAt: new Date(),
          lastRunUserName: currentUserProfile.name,
          lastRunUserId: currentUserProfile.id,
          dataDictRevisionNum: dataDictTuneUpInfo.revisionNum,
        });
      }
    } else {
      //start new eval set run, but only execute requested eval snap
      isSingleEvalRunExecutingAsNewSetRun = true;
      //There is nothing to move or reset in this case
      //update eval set info
      await this.evalSetRepository.updateById(evalSet._id, {
        lastRunStartedAt: new Date(),
        lastRunUserName: currentUserProfile.name,
        lastRunUserId: currentUserProfile.id,
        dataDictRevisionNum: dataDictTuneUpInfo.revisionNum,
      });
    }

    this.runEval(
      evalSnap,
      currentUserProfile.id,
      currentUserProfile.name,
      dataDictTuneUpInfo.revisionNum,
      isSingleEvalRunExecutingAsNewSetRun,
    )
      .catch(error => {
        logger.error(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvalSnap | N/A | Failed to run eval snap: ${evalSnapId}, error: ${error}`,
        );
      })
      .then(() => {
        logger.info(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvalSnap | N/A | Successfully completed run eval snap: ${evalSnapId}`,
        );
      })
      .finally(() => {
        logger.info(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEvalSnap | N/A | Completed run eval snap: ${evalSnapId}`,
        );
      });

    return {
      success: true,
      data: {
        message: DatalakeUserMessages.EVAL_QUESTION_RUN_STARTED,
      },
    };
  }

  /**
   * Runs an evaluation snap by calling LLM Agent for the given user and conversation
   * @param snap - The evaluation snap object
   * @param userId - The user ID of the user running the evaluation snap
   * @param userName - The user name of the user running the evaluation snap
   * @param dataDictRevisionNum - The revision number of the data dictionary
   * @param isSingleEvalRunExecutingAsNewSetRun - Whether the single evaluation run is executing as a new set run
   * @returns A promise that resolves when the evaluation snap has been run
   */
  async runEval(
    snap: EvalSnap & EvalSnapRelations,
    userId: string,
    userName: string,
    dataDictRevisionNum: number,
    isSingleEvalRunExecutingAsNewSetRun: boolean = false,
  ) {
    //make eval snap is running
    await this.evalSnapRepository.updateById(snap._id, {
      isRunning: true,
      dataDictRevisionNum: dataDictRevisionNum,
    });
    try {
      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEval | N/A | LLM Agent running for truth: ${snap._id}`,
      );

      const res = await this.getLLMAgentAction(snap.question, userId, userName);
      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEval | N/A | LLM Agent response for truth: ${
          snap._id
        }, res: ${JSON.stringify(res)}`,
      );

      if (res?.aiAction && res.conversationId) {
        await this.evalSnapRepository.updateById(snap._id, {
          aiAction: `<pre data-language="plain"> ${res.aiAction} </pre>`,
          aiAnswer: {
            text: res.aiTextAnswer,
            csv: res.aiCSVAnswer,
          },
          conversationId: res.conversationId,
          tuneUpUsageStatus: TuneUpUsageStatus.ASSESSMENT_PENDING,
          updatedAt: new Date(),
        });

        //do assessment
        if (snap._id) await this.assessEvalSnap(snap._id);
      } else {
        logger.debug(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEval | N/A | LLM Agent response is null for truth: ${
            snap._id
          }, res: ${JSON.stringify(res)}`,
        );

        await this.evalSnapRepository.updateById(snap._id, {
          tuneUpUsageStatus: TuneUpUsageStatus.AI_ACTION_FAILED,
          updatedAt: new Date(),
        });
      }
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.runEval | N/A | Failed to run LLM Agent for truth: ${snap._id}, error: ${error}`,
      );
    }
    //make eval snap is not running
    await this.evalSnapRepository.updateById(snap._id, {
      isRunning: false,
    });

    if (isSingleEvalRunExecutingAsNewSetRun) {
      await this.evalSetRepository.updateById(snap.evalSetId, {
        isRunning: false,
        lastRunCompletedAt: new Date(),
      });
    }

    await this.updateEvalSetStats(snap.evalSetId);
  }

  /**
   * Returns the run history of an evaluation set.
   *
   * @param evalSetId the ID of the evaluation set
   * @param pageIndex the page index of the run history, defaults to 0
   * @param pageSize the page size of the run history, defaults to 20
   * @returns an object containing the evaluation set information and the list of run history
   */
  async getEvalSetRunHistory(evalSetId: string, pageIndex: number = 0, pageSize: number = 20) {
    try {
      const evalSet = await this.evalSetRepository.findById(evalSetId);

      const evalSetRunHistoryResponse: {
        evalSetInfo: {
          name: string;
          evalSetNumber: number;
        };
        historyList: {
          runAt?: Date;
          runBy?: string;
          totalQuestions: number;
          passedQuestions: number;
          failedQuestions: number;
          notRunQuestions: number;
          runDuration: string;
          score: number;
        }[];
      } = {
        evalSetInfo: {
          name: evalSet.name,
          evalSetNumber: evalSet.evalSetNumber,
        },
        historyList: [],
      };

      const evalSetRunList = await this.evalSetRunRepository.find({
        where: {
          evalSetId: evalSetId,
        },
        order: ['lastRunStartedAt DESC'],
        skip: pageIndex * pageSize,
        limit: pageSize,
      });

      evalSetRunHistoryResponse.historyList = evalSetRunList.map(evalSetRun => {
        let _runDuration: string;

        if (evalSetRun.lastRunStartedAt && evalSetRun.lastRunCompletedAt) {
          const diff_ms = evalSetRun.lastRunCompletedAt.getTime() - evalSetRun.lastRunStartedAt.getTime();
          _runDuration = moment.utc(diff_ms).format('H:mm:ss');
        } else {
          _runDuration = '--:--:--';
        }

        return {
          runAt: evalSetRun.lastRunStartedAt,
          runBy: evalSetRun.lastRunUserName,
          totalQuestions: evalSetRun.totalQuestions,
          passedQuestions: evalSetRun.totalQuestions
            ? evalSetRun.lastRunStartedAt
              ? evalSetRun.lastRunStats?.passedQuestions ?? 0
              : -1
            : -1,
          failedQuestions: evalSetRun.totalQuestions
            ? evalSetRun.lastRunStartedAt
              ? evalSetRun.lastRunStats?.failedQuestions ?? 0
              : -1
            : -1,
          notRunQuestions: evalSetRun.totalQuestions ? evalSetRun.lastRunStats?.notRunQuestions ?? 0 : -1,
          runDuration: _runDuration,
          score: evalSetRun.totalQuestions
            ? evalSetRun.lastRunStartedAt
              ? evalSetRun.lastRunStats?.score ?? 0
              : -1
            : -1,
        };
      });

      return {
        success: true,
        data: evalSetRunHistoryResponse,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.getEvalSetRunHistory | N/A | Failed to get eval set run history for evalSetId: ${evalSetId}, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_HISTORY_RETRIEVAL_FAILED,
      };
    }
  }

  /**
   * Sends a request to LLM Agent to get the AI action for the given question.
   *
   * @param question the question to get the AI action for
   * @param userId the user ID of the user who asked the question
   * @param userName the user name of the user who asked the question
   * @returns an object containing the AI action and the conversation ID
   */
  async getLLMAgentAction(question: string, userId: string, userName: string) {
    try {
      const url = `${LLM_AGENT_INTERNAL_URL}/internal/conversation`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          user_id: userId,
          user_name: userName,
          content: question,
        },
      });

      logger.debug(
        `${
          FLOWS.DATA_DICT_EVAL
        } | EvaluationService.getLLMAgentAction | N/A | Got LLM agent action for question: ${question}, response: ${JSON.stringify(
          response.data,
        )}`,
      );

      return {
        aiAction: response.data.llm_action,
        aiTextAnswer: response.data.llm_text_answer,
        aiCSVAnswer: response.data.llm_csv_answer,
        conversationId: response.data.conversation_id,
      };
    } catch (err) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.getLLMAgentAction | N/A | Failed to get LLM agent action for question: ${question}, error: ${err}`,
      );

      return {
        aiAction: '',
        aiTextAnswer: '',
        aiCSVAnswer: '',
        conversationId: '',
      };
    }
  }

  /**
   * Sends a request to the backend to assess the given evaluation snap.
   *
   * @param evalSnapId the ID of the evaluation snap to assess
   * @returns an object containing the assessment result if successful, otherwise null
   */
  async assessEvalSnap(evalSnapId: string) {
    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.assessEvalSnap | N/A | triggering assessEvalSnap for evalSnapId: ${evalSnapId}`,
    );
    try {
      const url = `${PYTHON_HOST}/internal/evaluation/assess_eval_run`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          evalSnapId: evalSnapId,
        },
      });

      logger.debug(
        `${
          FLOWS.DATA_DICT_EVAL
        } | EvaluationService.assessEvalSnap | N/A | response for evalSnapId: ${evalSnapId}, response: ${JSON.stringify(
          response.data,
        )}`,
      );

      return response.data;
    } catch (err) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.assessEvalSnap | N/A | Failed to get response for evalSnapId: ${evalSnapId}, error: ${err}`,
      );
    }
  }

  /**
   * Triggers the auto tune data dict task on the backend.
   *
   * @returns an object containing the auto tune result if successful, otherwise null
   */
  async autoTuneDataDict() {
    logger.debug(`${FLOWS.DATA_DICT_EVAL} | EvaluationService.autoTuneDataDict | N/A | triggering autoTuneDataDict`);
    try {
      let url = `${PYTHON_HOST}/internal/evaluation/auto_tune_data_dict`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {},
      });

      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.autoTuneDataDict | N/A | response: ${JSON.stringify(
          response.data,
        )}`,
      );

      return response.data;
    } catch (err) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.autoTuneDataDict | N/A | Failed to get response, error: ${err}`,
      );
    }
  }

  /**
   * Uploads a CSV file containing truths for an evaluation set
   * @param evalSetId - The ID of the evaluation set to upload the truths to
   * @param filePath - The path to the CSV file containing the truths
   * @returns An object indicating the success or failure of the operation,
   *          along with a relevant message if failed.
   * @throws Logs an error if the upload fails due to other reasons.
   */
  async uploadTruthsByCSV(evalSetId: string, filePath: string) {
    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | evalSetId: ${evalSetId} | filePath: ${filePath}`,
    );

    if (!fs.existsSync(filePath)) {
      logger.error(`${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | File not found: ${filePath}`);
      return {
        success: false,
        message: `File not found: ${filePath}`,
      };
    }

    const fileExtension = filePath.split('.').pop();
    if (fileExtension !== 'csv') {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | File must be a CSV file but was a ${fileExtension}`,
      );
      return {
        success: false,
        message: `File must be a CSV file but was a ${fileExtension}`,
      };
    }

    const evalSet = await this.evalSetRepository.findById(evalSetId);
    if (!evalSet) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | Eval set not found: ${evalSetId}`,
      );
      return {
        success: false,
        message: `Eval set not found: ${evalSetId}`,
      };
    }

    const fileContent: {
      Question: string;
      Truth: string;
      Difficulty: string;
    }[] = await csv().fromFile(filePath);
    if (fileContent.length === 0) {
      logger.error(`${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | File is empty: ${filePath}`);
      return {
        success: false,
        message: `File is empty: ${filePath}`,
      };
    }

    const updatesArr: BulkUpdateOneDataFormat[] = [];
    for (const item of fileContent) {
      if (item.Question && item.Truth) {
        // if difficulty is not easy, medium, or hard, set it to medium
        let _difficulty = item.Difficulty ? item.Difficulty.toLowerCase() : '';
        if (_difficulty !== 'easy' && _difficulty !== 'normal' && _difficulty !== 'hard') {
          _difficulty = 'normal';
        }

        const _updateQuery: BulkUpdateOneDataFormat = {
          updateOne: {
            filter: {
              evalSetId: new ObjectId(evalSetId),
              question: item.Question,
              truth: item.Truth,
            },
            update: {
              $setOnInsert: {
                createdAt: new Date(),
              },
              $set: {
                evalSetId: new ObjectId(evalSetId),
                question: item.Question,
                truth: item.Truth,
                difficulty: _difficulty,
                updatedAt: new Date(),
              },
            },
            upsert: true,
          },
        };
        updatesArr.push(_updateQuery);
      } else {
        logger.error(
          `${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | Question or Truth is empty, question: ${item.Question}, truth: ${item.Truth}`,
        );
      }
    }

    try {
      await this.evalSnapRepository.bulkWrite(updatesArr);
      return {
        success: true,
        message: `Uploaded truth to eval set: ${evalSetId}`,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.uploadTruthsByCSV | N/A | Failed to upload truth to eval set: ${evalSetId}, filePath: ${filePath}, error: ${error}`,
      );
      return {
        success: false,
        message: `Failed to upload truth to eval set: ${evalSetId}`,
      };
    }
  }

  /** Save the given user feedback to the evaluation collection with flag isUserFeedback
   * @param chatQuestion - The question asked by the user
   * @param aiAction - The AI action taken by the agent
   * @param feedbackComment - The comment provided by the user
   * @returns success status if the feedback is saved
   */
  async onUserFeedback(chatQuestion: string, aiAction: string, feedbackComment: string) {
    // First find evaluation set with isUserFeedback = true flag, if not create it
    let feedbackEvalSet = await this.evalSetRepository.findOne({
      where: {
        isUserFeedback: true,
      },
    });

    if (!feedbackEvalSet) {
      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.onUserFeedback | N/A | Creating evaluation set for user feedback`,
      );
      //increment lastEvalSetNumber by 1 in system data
      const evalNum = await this.systemDataRepository.findOneAndUpdate(
        {},
        {$inc: {lastEvalSetNumber: 1}},
        {
          returnDocument: 'after',
          projection: {lastEvalSetNumber: 1},
        },
      );
      feedbackEvalSet = await this.evalSetRepository.create({
        name: 'User Feedback',
        isUserFeedback: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        evalSetNumber: evalNum.value.lastEvalSetNumber,
        isRunning: false,
        totalQuestions: 0,
        status: EvalSetStatus.DRAFT,
      });
    }

    if (!feedbackEvalSet || !feedbackEvalSet._id) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.onUserFeedback | N/A | Failed to find or create evaluation set for user feedback`,
      );
      return false;
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.onUserFeedback | N/A | Triggering eval run for user feedback, evalSetId: ${feedbackEvalSet._id}, chatQuestion: ${chatQuestion}`,
    );

    const evalRunResult = await this.addTruthToEvalSet(
      feedbackEvalSet._id,
      chatQuestion,
      feedbackComment,
      DifficultyLevel.NORMAL,
      EvalFeedbackType.COMMENT,
      aiAction,
    );

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationService.onUserFeedback | N/A | Created eval run for user feedback: ${evalRunResult}`,
    );

    return evalRunResult.success;
  }

  /**
   * This function is used to tune up data dictionary. It will check if data dict tune up is ongoing, and will not start
   * if it is. It will also check if there are any ongoing evaluation sets, and will not start if there are. It will
   * then call the python API to tune up data dict.
   *
   * @param {UserProfileDetailed} currentUserProfile The user who is triggering the data dict tune up.
   * @returns {Promise<{success: boolean, message?: string, data?: {noEvaluations: boolean}}>} The result of the tune up.
   * If success is false, it will also contain a message. If success is true, it will contain a data object with a
   * noEvaluations property that is true if there are no evaluations for the current data dict revision num.
   */
  async dataDictTuneUp(currentUserProfile: UserProfileDetailed) {
    try {
      logger.debug(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.dataDictTuneUp | N/A | Triggered by: ${currentUserProfile.id}`,
      );

      //check if data dict tune up is ongoing
      let dataDictTuneUpInfo = await this.dataDictTuneUpInfo();
      if (dataDictTuneUpInfo.isTuneOngoing) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_ONGOING,
        };
      }

      let existingDataDictRevNum: number = dataDictTuneUpInfo.revisionNum;

      //check at least one evaluation exist for current rev num and tuneUpUsageStatus is TuneUpUsageStatus.TUNE_UP_PENDING
      let atLeastSingleEvalObj = await this.evalSnapRepository.findOne({
        where: {
          dataDictRevisionNum: existingDataDictRevNum,
          tuneUpUsageStatus: TuneUpUsageStatus.TUNE_UP_PENDING,
        },
      });

      if (!atLeastSingleEvalObj) {
        return {
          success: true,
          data: {
            noEvaluations: true,
          },
        };
      }

      //check at least one evaluation set is running ongoing
      let atLeastOneOngoingEvalSetObj = await this.evalSetRepository.findOne({
        where: {
          isRunning: true,
        },
      });

      if (atLeastOneOngoingEvalSetObj) {
        return {
          success: false,
          message: DatalakeUserMessages.EVAL_EVALUATION_SET_RUNNING,
        };
      }

      //make dictionary tune up ongoing true
      await this.systemDataRepository.updateOneSetData(
        {},
        {
          'dataDictTuneUpInfo.isTuneOngoing': true,
          'dataDictTuneUpInfo.lastTuneUpStartedAt': new Date(),
          'dataDictTuneUpInfo.triggeredUserId': currentUserProfile.id,
          'dataDictTuneUpInfo.triggeredUserName': currentUserProfile.name,
        },
        [],
      );

      //call python API to tune up data dict
      this.autoTuneDataDict()
        .catch(err => {
          logger.error(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.dataDictTuneUp | N/A | Failed to tune up data dictionary, error: ${err}`,
          );
        })
        .then(() => {
          logger.debug(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.dataDictTuneUp | N/A | successfully completed tune up data dictionary`,
          );
        })
        .finally(() => {
          logger.debug(
            `${FLOWS.DATA_DICT_EVAL} | EvaluationService.dataDictTuneUp | N/A | completed tune up data dictionary`,
          );
        });

      return {
        success: true,
        data: {
          noEvaluations: false,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationService.dataDictTuneUp | N/A | Failed to tune up data dictionary, error: ${error}`,
      );
      return {
        success: false,
        message: DatalakeUserMessages.EVAL_DATA_DICT_TUNEUP_FAILED,
      };
    }
  }
}

export const EVALUATION_SERVICE = BindingKey.create<EvaluationService>('service.evaluation');
