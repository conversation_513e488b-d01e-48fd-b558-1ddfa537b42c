/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the MetaData update related logics
 */

/**
 * @class ObjectMetaUpdaterService
 * purpose of this service is to perfome the MetaData update related logics
 * @description updating metadata
 * <AUTHOR> channa
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable, service} from '@loopback/core';
import {Where, repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import dotenv from 'dotenv';
import _ from 'lodash';
import {ObjectId} from 'mongodb';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AnnotationObject,
  ApiKey,
  ContentType,
  EditMetaDataInputFormat,
  EditMetadataAllFlowsInputFormat,
  InputMetaDataFeed,
  InputMetaDataFeedGroup,
  MetaData,
  MetaDataKeyInputListFormat,
  MetaDataKeyInputType,
  MetaDataUpdate,
  MetaDataUpdateObject,
  OBJECT_STATUS,
  OperationMode,
  OperationType,
  SDKMetaUpdateInput,
  SearchQueryRootGroup,
} from '../models';
import {JobType} from '../models/job.model';
import {
  ApiKeyRepository,
  InputMetaDataFeedRepository,
  MetaDataRepository,
  MetaDataUpdateRepository,
  MetaTagRepository,
  QueryOptionRepository,
  SystemDataRepository,
} from '../repositories';
import {JobRepository} from '../repositories/job.repository';
import {FLOWS, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {AWS_FILE_URL_EXPIRE_DURATION, AWS_FILE_URL_RENEW_BEFORE_DURATION} from './aws-s3-storage.service';
import {InputMetadataFeedService} from './input-metadata-feed.service';
import {JOB_SERVICE, JobService} from './job.service';
import {MEDIA_PROCESS_HANDLER, MediaProcessorService} from './media-processor.service';
import {META_DATA_SERVICE, MetaDataService} from './meta-data.service';
import {META_FIELD_PROPAGATOR_SERVICE, MetaFieldPropagatorService} from './meta-field-propagator.service';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {STATS_CALCULATION_SERVICE, StatsCalculationService} from './stats-calculation.service';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import {SYSTEM_META_SERVICE, SystemMetaService} from './system-meta.service';
dotenv.config();

const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

@injectable({scope: BindingScope.TRANSIENT})
export class ObjectMetaUpdaterService {
  constructor(
    /* Add @inject to inject parameters */
    @repository('InputMetaDataFeedRepository')
    private inputMetaDataFeedRepository: InputMetaDataFeedRepository,
    @repository('MetaDataRepository')
    private metaDataRepository: MetaDataRepository,
    @repository('MetaDataUpdateRepository')
    private metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository('ApiKeyRepository') private apiKeyRepository: ApiKeyRepository,
    @repository('QueryOptionRepository')
    private queryOptionRepository: QueryOptionRepository,
    @repository(JobRepository) private jobRepository: JobRepository,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(MEDIA_PROCESS_HANDLER)
    private mediaProcessorService: MediaProcessorService,
    @inject(META_FIELD_PROPAGATOR_SERVICE)
    private metaFieldPropagatorService: MetaFieldPropagatorService,
    @inject(META_DATA_SERVICE)
    private metaDataService: MetaDataService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @service(InputMetadataFeedService)
    private inputMetadataFeedService: InputMetadataFeedService,
    @repository(MetaTagRepository)
    private metaTagRepository: MetaTagRepository,
    @inject(SYSTEM_META_SERVICE) private systemMetaService: SystemMetaService,
    @inject(STATS_CALCULATION_SERVICE) private statsCalculationService: StatsCalculationService,
  ) {}

  /**
   * Sub method to get updating required ApiKey list
   * based on currentSyncInterval, maxSyncInterval and input count
   * @returns isUpdateNow:boolean
   **/
  async getUpdatingRequiredApiKeyList() {
    const apiKeyDefaultUpdatingThreshold = 50 * 1000; //ms

    // get updating required ApiKey list
    let aggregateParams = [
      {
        $addFields: {
          currentSyncInterval: {
            $subtract: [new Date(), {$ifNull: ['$lastSyncTimestamp', new Date()]}],
          },
        },
      },
      {
        $addFields: {
          timePassingSyncThreshold: {
            $subtract: [
              '$currentSyncInterval',
              {
                $ifNull: ['$apiConfigs.maxSyncInterval', apiKeyDefaultUpdatingThreshold],
              },
            ],
          },
        },
      },
      {$match: {timePassingSyncThreshold: {$gt: 0}}},
      {$match: {isProcessingLocked: {$ne: true}}},
      {
        $lookup: {
          from: 'InputMetaDataFeed',
          as: 'inputItemsOfApiKey',
          let: {key: '$key'},
          pipeline: [
            {$match: {isActive: true}},
            {
              $match: {
                $expr: {$eq: ['$apiKey', '$$key']},
              },
            },
            {$count: 'count'},
          ],
        },
      },
      {$match: {'inputItemsOfApiKey.count': {$gt: 0}}},
      {
        $project: {
          apiKey: '$key',
        },
      },
    ];

    // console.log(JSON.stringify(aggregateParams,null,2))

    let updateRequiredApiKeyDocList = await this.apiKeyRepository.aggregate(aggregateParams);
    return updateRequiredApiKeyDocList;
  }

  /**
   * Method to reset isProcessingLocked to false in all APIKeys at server start
   * Triggered once at server start
   **/
  async resetAPIKeyLocks() {
    // await new Promise(resolve => setTimeout(resolve, 5000));
    // set isProcessingLocked = false - in case server stopped unexpectedly while an inputFeed processing is ongoing with APIKey locked
    await this.apiKeyRepository.updateAll({isProcessingLocked: false}, {isProcessingLocked: true});
  }

  // /**
  // * Sub method to calculate wether to process or skip updating metadata from input data queue
  // * based on DB current operations count and last updated time
  // * @returns isUpdateNow:boolean
  // **/
  //  async checkIsUpdatePossible(){
  //    const dbOpCountThreshold = 2;
  //    const updateIntervalThreshold = 1*60*1000; // sync-interval - get from API key document (related to user)

  //   //  let dbOpCount = await this.inputMetaDataFeedRepository.getDBCurrentOpCount()
  //   let dbOpCount = 0;
  //    logger.debug(`current DB operations count: ${dbOpCount}`)

  //    let lastUpdatedDelay = 1*60*1000 //------------------- should be calculated !!!
  //    logger.debug(`last metaData update delay: ${lastUpdatedDelay}`)

  //    if(dbOpCount <= dbOpCountThreshold){
  //      // db is free
  //      return true; // can update now
  //    }
  //    else if(lastUpdatedDelay > updateIntervalThreshold){
  //      // db busy, updating delayed too long
  //     return true; // can update now
  //    }
  //    else{
  //      // db busy, last updated within threshold
  //      return false; // do not update now
  //    }

  //  }

  /**
   * Sub method to fetch input data from input metaData feed
   * to be implemented similar to a queue
   * @returns metaDataUpdatesList - input metadata updates documents list
   **/
  async dequeueMetaDataUpdates(apiKeyArr: string[]) {
    const batchSize = 250;
    let aggregateParams = [
      {$match: {isActive: true}},
      {$match: {$expr: {$in: ['$apiKey', apiKeyArr]}}},
      {$sort: {apiKey: 1}},
      {$limit: batchSize},
      {
        $group: {
          _id: '$metaDataObject.objectKey',
          metaDataArray: {$push: '$$ROOT'},
        },
      },
      {
        $lookup: {
          from: 'MetaData',
          localField: '_id',
          foreignField: 'objectKey',
          as: 'metaDataObjectForUpdate',
        },
      },
    ];

    // console.log(JSON.stringify(aggregateParams,null,2))

    let metaDataList: InputMetaDataFeedGroup[] = await this.inputMetaDataFeedRepository.aggregate(aggregateParams);

    let apiKeyList = await this.apiKeyRepository.find({
      fields: {key: true, teamId: true},
    });
    let apiKeyHashList = Object.fromEntries(apiKeyList.map(e => [e['key'], e.teamId]));

    // for(let i in metaDataList){
    //   for(let j in metaDataList[i].metaDataObjectForUpdate){
    //     if(metaDataList && metaDataList[i] && metaDataList[i].metaDataObjectForUpdate){
    //       metaDataList[i]?.metaDataObjectForUpdate[j]
    //     }
    //   }
    // }
    //teamId = apiKeyHashList['']

    // console.log(JSON.stringify(metaDataList, null, 2))

    let isNextPageAvailable = false;
    if (metaDataList && metaDataList.length == batchSize) {
      isNextPageAvailable = true;
    }
    return {metaDataList, isNextPageAvailable};
  }

  /**
   * main method to handle dequeue from input data queue and save (insert/update) - in metaData or metaDataUpdate repositories
   * triggered by cron
   **/
  async handleUpdatingMetaDataFromInputQueue() {
    // logger.debug(`initiating input queue processing`)
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.handleUpdatingMetaDataFromInputQueue | N/A | initiating`,
    ); // -- use constants !!!

    // let isUpdateNow = await this.checkIsUpdatePossible(); // method to check db load and to update
    // if(!isUpdateNow){
    //   return
    // }

    // get list of api keys which updating now is required
    let apiKeyList = await this.getUpdatingRequiredApiKeyList();
    // logger.debug(`apiKeyList----------------\n${JSON.stringify(apiKeyList,null,2)}`)
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.handleUpdatingMetaDataFromInputQueue | number of apiKeys to update: ${apiKeyList?.length} | N/A`,
    );

    if (!apiKeyList) {
      return;
    } else if (apiKeyList.length == 0) {
      return;
    }

    // process dequeue input and save to metaData
    await this.updateMetaDataFromInputQueue(apiKeyList);
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.handleUpdatingMetaDataFromInputQueue | N/A | Completed`,
    );

    return;
  }

  /**
   * Sub method to dequeue from input data queue and save (insert/update) - in metaData or metaDataUpdate repositories
   * called from updateMetaDataFromInputQueue
   * @params apiKeyList
   **/
  async updateMetaDataFromInputQueue(apiKeyList: APIKeyListItem[]) {
    let apiKeyArr = apiKeyList.map((element: APIKeyListItem) => {
      // lock APIKey at start of processing
      this.apiKeyRepository.updateById(element._id.toHexString(), {
        isProcessingLocked: true,
      });
      // ---------------------------
      return element.apiKey;
    });

    let metaDataListObject = await this.dequeueMetaDataUpdates(apiKeyArr); // run seperately for each APIKey - input(processable APIKey array)
    // console.log(JSON.stringify(metaDataListObject,null,2))

    let metaDataList = metaDataListObject.metaDataList;
    let isNextPageAvailable = metaDataListObject.isNextPageAvailable;

    let metaDataInsertsList: BulkInsertOneMetaDataFormat[] = [];
    let metaDataUpdatesList: BulkUpdateOneMetaDataFormat[] = [];

    let inputFeedUpdatedMarkingList: BulkUpdateOneInputMetaDataFeedFormat[] = [];
    let apiKeySyncTimeUpdateList: BulkUpdateOneApiKeyFormat[] = [];

    const formatMetaDataListToBulkUpdate = async (
      metaDataGroupObjectKey: string,
      metaDataArray: InputMetaDataFeed[],
    ) => {
      let metaDataUpdatesObjArray = await Promise.all(
        metaDataArray.map(async (metaDataItem: InputMetaDataFeed) => {
          metaDataItem.metaDataObject.updatedAt = new Date();
          if (metaDataItem.metaDataObject.thumbnailKey) metaDataItem.metaDataObject.statPending = true; //To trigger calculation of counts in periodic job
          metaDataItem.metaDataObject.statPendingAt = new Date();

          let metaDataObjectUpdates = metaDataItem.metaDataObject;
          await this.formatMetaDataBeforeUpdateOrInsert(metaDataObjectUpdates); // format dates and object ids of updates (object)

          // create addToSet sub query
          let addToSetData: AddToSetMetaDataType = {};
          if (metaDataItem.metaDataPushList) {
            let metaDataPushListObj = metaDataItem.metaDataPushList;
            await this.formatMetaDataBeforeUpdateOrInsert(metaDataPushListObj); // format dates and objectids of pushlist

            for (const [key, value] of Object.entries(metaDataPushListObj)) {
              let valueData = value;
              addToSetData[key] = {$each: valueData};
            }
          }

          if (metaDataObjectUpdates.Tags) {
            let valueData: any = metaDataObjectUpdates.Tags;
            addToSetData['Tags'] = {$each: valueData};
            delete metaDataObjectUpdates.Tags;
          }

          await this.bundleCustomMeta(metaDataObjectUpdates, true);

          let metaDataUpdateObj: BulkUpdateOneMetaDataFormat = {
            updateOne: {
              filter: {objectKey: metaDataGroupObjectKey},
              update: {
                $set: metaDataObjectUpdates,
                // $addToSet: { parentList: { $each: [] } }
              },
            },
          };

          if (addToSetData && Object.entries(addToSetData).length > 0) {
            metaDataUpdateObj.updateOne.update.$addToSet = addToSetData;
          }
          // console.log(`-=================================================================X-`)
          // console.log(metaDataUpdateObj)
          // console.log(JSON.stringify(metaDataUpdateObj))
          // console.log(`-=================================================================-`)
          return metaDataUpdateObj;
        }),
      );
      // console.log(`-=================================================================1-`)
      // console.log(metaDataUpdatesList)
      // console.log(`-=================================================================-`)
      // console.log(`-=================================================================1.1-`)
      // console.log(metaDataUpdatesObjArray)
      // console.log(`-=================================================================-`)
      metaDataUpdatesList = [...metaDataUpdatesList, ...metaDataUpdatesObjArray];
      // console.log(`-=================================================================2-`)
      // console.log(metaDataUpdatesList)
      // console.log(`-=================================================================-`)
    };

    //to update search query options
    this.searchQueryBuilderService.updateQueryOptionOnMetaDataInputFeedDequeue(metaDataList);

    for (let metaDataGroup of metaDataList) {
      // mark processed items in inpud feed ----------------------------------
      let updateProcessedInputAndApiKey = await this.markProcessedInputAndApiKeys(metaDataGroup.metaDataArray);

      //to mark input feed as inactive
      inputFeedUpdatedMarkingList = [
        ...inputFeedUpdatedMarkingList,
        ...updateProcessedInputAndApiKey.inactiveteInputGroupList,
      ];

      //to update lastsyncTimestamp of ApiKey
      apiKeySyncTimeUpdateList = [
        ...apiKeySyncTimeUpdateList,
        ...updateProcessedInputAndApiKey.markSyncTimeOfApiKeyList,
      ];
      //------------------------------

      // to update MetaData
      if (metaDataGroup.metaDataObjectForUpdate && metaDataGroup.metaDataObjectForUpdate.length > 0) {
        // metaDataItem is available
        await formatMetaDataListToBulkUpdate(metaDataGroup._id, metaDataGroup.metaDataArray);
      } else {
        // metaData is new - should be inserted first
        if (metaDataGroup.metaDataArray && metaDataGroup.metaDataArray[0]) {
          let firstMetaDataItemOfNewGroup = metaDataGroup.metaDataArray.shift();

          if (firstMetaDataItemOfNewGroup) {
            await this.updatefirstMetaDataItem(firstMetaDataItemOfNewGroup);

            let allMetaDataUpdate = firstMetaDataItemOfNewGroup?.metaDataObject;

            await this.formatMetaDataBeforeUpdateOrInsert(allMetaDataUpdate); // format dates and object ids

            if (
              firstMetaDataItemOfNewGroup &&
              firstMetaDataItemOfNewGroup.metaDataPushList &&
              Object.keys(firstMetaDataItemOfNewGroup.metaDataPushList).length > 0
            ) {
              let metaDataPushListObj = firstMetaDataItemOfNewGroup.metaDataPushList;
              await this.formatMetaDataBeforeUpdateOrInsert(metaDataPushListObj); // format dates and objectids of pushlist
              allMetaDataUpdate = {
                ...allMetaDataUpdate,
                ...metaDataPushListObj,
              };
            }
            if (allMetaDataUpdate && Object.keys(allMetaDataUpdate).length > 0) {
              await this.bundleCustomMeta(allMetaDataUpdate, false);

              let metaDataInsertObj = {insertOne: allMetaDataUpdate};
              metaDataInsertsList.push(metaDataInsertObj);
            }
          }

          if (metaDataGroup.metaDataArray[0]) {
            await formatMetaDataListToBulkUpdate(metaDataGroup._id, metaDataGroup.metaDataArray);
          }
        }
      }
    }

    // console.log(
    //   `-=================================================================-`,
    // );
    // // console.log(metaDataInsertsList)
    // console.log(metaDataUpdatesList);
    // console.log(JSON.stringify(metaDataUpdatesList));
    // console.log(
    //   `-=================================================================-`,
    // );

    // console.log(`metaDataInserts`,JSON.stringify(metaDataInsertsList,null,2))
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # metaData items to insert: ${metaDataInsertsList?.length} | N/A`,
    );
    // console.log(`metaDataUpdates`,JSON.stringify(metaDataUpdatesList,null,2))
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # metaData items to update: ${metaDataUpdatesList?.length} | N/A`,
    );
    // console.log(inputFeedUpdatedMarkingList)
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # metaDataInput items to mark: ${inputFeedUpdatedMarkingList?.length} | N/A`,
    );
    // console.log(`apiKey timeStamp Updates`, JSON.stringify(apiKeySyncTimeUpdateList, null, 2))
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # apiKey docs to update timestamp: ${apiKeySyncTimeUpdateList?.length} | N/A`,
    );

    //Generate object and thumbnailUrl
    let _metaDataInsertsList = [];
    // populate metadata from source video
    let metaInsertsListToPopulateSourceMetaData: {
      objectKey: string;
      sourceVideoId: string | ObjectId;
    }[] = [];
    for (let metaDataObject of metaDataInsertsList) {
      let storagePath = metaDataObject.insertOne?.storagePath;
      let objectKey = metaDataObject.insertOne?.objectKey;
      let thumbnailKey = metaDataObject.insertOne?.thumbnailKey;

      if (
        metaDataObject &&
        metaDataObject.insertOne &&
        storagePath &&
        typeof metaDataObject.insertOne.isMediaProcessingPending == 'boolean' &&
        !metaDataObject.insertOne.isMediaProcessingPending
      ) {
        let url = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(storagePath);
        // if (thumbnailKey) {
        //   let thumbnailUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(thumbnailKey);
        //   metaDataObject.insertOne.thumbnailUrl = thumbnailUrl;
        // }

        metaDataObject.insertOne.url = url;
        if (url && url.startsWith('https')) {
          metaDataObject.insertOne.isAccessible = true;
          metaDataObject.insertOne.objectStatus = OBJECT_STATUS.ACTIVE;
        } else metaDataObject.insertOne.objectStatus = OBJECT_STATUS.ACCESSED_FAILED;

        metaDataObject.insertOne.urlExpiredAt = new Date(new Date().getTime() + AWS_FILE_URL_EXPIRE_DURATION * 1000);
      }
      _metaDataInsertsList.push(metaDataObject);

      if (metaDataObject.insertOne?.sourceVideoId && metaDataObject.insertOne.objectKey) {
        // then a to populate metadata from source video list
        let tempObj = {
          objectKey: metaDataObject.insertOne.objectKey,
          sourceVideoId: new ObjectId(metaDataObject.insertOne.sourceVideoId),
        };
        metaInsertsListToPopulateSourceMetaData.push(tempObj);
      }
    }

    //update metadata
    await this.metaDataRepository.bulkWrite(_metaDataInsertsList);
    await this.metaDataRepository.bulkWrite(metaDataUpdatesList);

    // mark processed items in inpud feed
    await this.inputMetaDataFeedRepository.bulkWrite(inputFeedUpdatedMarkingList);
    // mark lastSyncTime in ApiKey
    await this.apiKeyRepository.bulkWrite(apiKeySyncTimeUpdateList);

    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | N/A | Successfully updated metaData of page`,
    );

    // invoke thumbnail generation for the metaData in metaData inserts
    if (_metaDataInsertsList && _metaDataInsertsList.length > 0) {
      logger.debug(
        `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | new inserts #: ${_metaDataInsertsList.length} | Metadata inserts available - thumbnail generation checking`,
      );

      // get unique uploadIds
      let mediaProcessPendingUploadIdSet = new Set<string>();
      let allUploadIdSet = new Set<string>();
      for (let metaDataItem of _metaDataInsertsList) {
        if (metaDataItem.insertOne && metaDataItem.insertOne.fileUploadId) {
          let uploadId = metaDataItem.insertOne.fileUploadId;
          if (metaDataItem.insertOne.isMediaProcessingPending) mediaProcessPendingUploadIdSet.add(uploadId.toString());
          allUploadIdSet.add(uploadId.toString());
        }
      }
      // get collectionIds of uploadIds and invoke media processor for each
      let mediaProcessPendingUploadIdArr = [...mediaProcessPendingUploadIdSet];
      let allUploadIdArr = [...allUploadIdSet];

      if (allUploadIdArr.length > 0) {
        logger.debug(
          `Processing metaData input Queue  | MediaProcessorService.handleThumbnailGenerationInvokingOnMetaDataInsert | media processing required inserts #: ${mediaProcessPendingUploadIdArr.length} | Metadata inserts available - thumbnail generation required`,
        );

        await this.mediaProcessorService.handleUploadJobStatusUpdateInvokingOnUploadIdList(
          allUploadIdArr,
          mediaProcessPendingUploadIdArr.length > 0,
        );

        logger.info(
          `-------------------- length ${mediaProcessPendingUploadIdArr.length}, item: ${JSON.stringify(
            mediaProcessPendingUploadIdArr,
          )}`,
        );
        // Invoke thumbnail generation for all media process pending uploads and do the job progress update for all uploads including other files
        if (mediaProcessPendingUploadIdArr.length > 0)
          this.mediaProcessorService.handleThumbnailGenerationInvokingOnUploadIdList(mediaProcessPendingUploadIdArr);
      }
    } else if (metaDataUpdatesList && metaDataUpdatesList.length > 0) {
      logger.debug(
        `Processing metaData input Queue  | MediaProcessorService.handleUploadJobStatusUpdateInvokingOnMetaDataUpdate | metadata required updates #: ${metaDataUpdatesList.length} | Metadata updates available - upload job status update required`,
      );

      let allUploadIdSet = new Set<string>();
      for (let metaDataItem of metaDataUpdatesList) {
        if (
          metaDataItem.updateOne &&
          metaDataItem.updateOne.update &&
          metaDataItem.updateOne.update.$set?.fileUploadId
        ) {
          let uploadId = metaDataItem.updateOne.update.$set.fileUploadId;
          allUploadIdSet.add(uploadId.toString());
        }
      }
      let allUploadIdArr = [...allUploadIdSet];

      this.mediaProcessorService.handleUploadJobStatusUpdateInvokingOnUploadIdList(allUploadIdArr, false);
    } else {
      logger.debug(
        `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | new inserts #: N/A | no new Metadata inserts`,
      );
    }
    //----------------------------------------------------------
    // populate metadata from source video
    if (metaInsertsListToPopulateSourceMetaData && metaInsertsListToPopulateSourceMetaData.length > 0) {
      for (let insertItem of metaInsertsListToPopulateSourceMetaData) {
        this.metaFieldPropagatorService.propagateFieldsFromVideoToDerivedImages(insertItem.sourceVideoId, [
          insertItem.objectKey,
        ]);
      }
    }
    // --------------------------------------------------------------

    // call next batch (as next page)
    if (isNextPageAvailable) {
      logger.debug(
        `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | N/A | Next page available`,
      );
      await this.updateMetaDataFromInputQueue(apiKeyList); //recursively call this method immediately
    } else {
      // unlock APIKey processing after all pages processed
      for (let apiKey of apiKeyList) {
        await this.apiKeyRepository.updateById(apiKey._id.toHexString(), {
          isProcessingLocked: false,
        });
      }
    }

    return;
  }

  async updateMetaDataFromSkipQueue(metaDataArray: InputMetaDataFeedGroup[]) {
    let metaDataList = metaDataArray;

    let metaDataInsertsList: BulkInsertOneMetaDataFormat[] = [];
    let metaDataUpdatesList: BulkUpdateOneMetaDataFormat[] = [];

    // let inputFeedUpdatedMarkingList: BulkUpdateOneInputMetaDataFeedFormat[] = [];
    // let apiKeySyncTimeUpdateList: BulkUpdateOneApiKeyFormat[] = [];

    const formatMetaDataListToBulkUpdate = async (
      metaDataGroupObjectKey: string,
      metaDataArray: InputMetaDataFeed[],
    ) => {
      let metaDataUpdatesObjArray = await Promise.all(
        metaDataArray.map(async (metaDataItem: InputMetaDataFeed) => {
          metaDataItem.metaDataObject.updatedAt = new Date();
          if (metaDataItem.metaDataObject.thumbnailKey) metaDataItem.metaDataObject.statPending = true; //To trigger calculation of counts in periodic job
          metaDataItem.metaDataObject.statPendingAt = new Date();

          let metaDataObjectUpdates = metaDataItem.metaDataObject;
          await this.formatMetaDataBeforeUpdateOrInsert(metaDataObjectUpdates); // format dates and object ids of updates (object)

          // create addToSet sub query
          let addToSetData: AddToSetMetaDataType = {};
          if (metaDataItem.metaDataPushList) {
            let metaDataPushListObj = metaDataItem.metaDataPushList;
            await this.formatMetaDataBeforeUpdateOrInsert(metaDataPushListObj); // format dates and objectids of pushlist

            for (const [key, value] of Object.entries(metaDataPushListObj)) {
              let valueData = value;
              addToSetData[key] = {$each: valueData};
            }
          }

          if (metaDataObjectUpdates.Tags) {
            let valueData: any = metaDataObjectUpdates.Tags;
            addToSetData['Tags'] = {$each: valueData};
            delete metaDataObjectUpdates.Tags;
          }

          await this.bundleCustomMeta(metaDataObjectUpdates, true);

          let metaDataUpdateObj: BulkUpdateOneMetaDataFormat = {
            updateOne: {
              filter: {objectKey: metaDataGroupObjectKey},
              update: {
                $set: metaDataObjectUpdates,
                // $addToSet: { parentList: { $each: [] } }
              },
            },
          };

          if (addToSetData && Object.entries(addToSetData).length > 0) {
            metaDataUpdateObj.updateOne.update.$addToSet = addToSetData;
          }
          return metaDataUpdateObj;
        }),
      );
      metaDataUpdatesList = [...metaDataUpdatesList, ...metaDataUpdatesObjArray];
    };

    //to update search query options
    this.searchQueryBuilderService.updateQueryOptionOnMetaDataInputFeedDequeue(metaDataList);

    for (let metaDataGroup of metaDataList) {
      // mark processed items in inpud feed ----------------------------------
      let updateProcessedInputAndApiKey = await this.markProcessedInputAndApiKeys(metaDataGroup.metaDataArray);

      // to update MetaData
      if (metaDataGroup.metaDataObjectForUpdate && metaDataGroup.metaDataObjectForUpdate.length > 0) {
        // metaDataItem is available
        await formatMetaDataListToBulkUpdate(metaDataGroup._id, metaDataGroup.metaDataArray);
      } else {
        // metaData is new - should be inserted first
        if (metaDataGroup.metaDataArray && metaDataGroup.metaDataArray[0]) {
          let firstMetaDataItemOfNewGroup = metaDataGroup.metaDataArray.shift();

          if (firstMetaDataItemOfNewGroup) {
            await this.updatefirstMetaDataItem(firstMetaDataItemOfNewGroup);

            let allMetaDataUpdate = firstMetaDataItemOfNewGroup?.metaDataObject;

            await this.formatMetaDataBeforeUpdateOrInsert(allMetaDataUpdate); // format dates and object ids

            if (
              firstMetaDataItemOfNewGroup &&
              firstMetaDataItemOfNewGroup.metaDataPushList &&
              Object.keys(firstMetaDataItemOfNewGroup.metaDataPushList).length > 0
            ) {
              let metaDataPushListObj = firstMetaDataItemOfNewGroup.metaDataPushList;
              await this.formatMetaDataBeforeUpdateOrInsert(metaDataPushListObj); // format dates and objectids of pushlist
              allMetaDataUpdate = {
                ...allMetaDataUpdate,
                ...metaDataPushListObj,
              };
            }
            if (allMetaDataUpdate && Object.keys(allMetaDataUpdate).length > 0) {
              await this.bundleCustomMeta(allMetaDataUpdate, false);

              let metaDataInsertObj = {insertOne: allMetaDataUpdate};
              metaDataInsertsList.push(metaDataInsertObj);
            }
          }

          if (metaDataGroup.metaDataArray[0]) {
            await formatMetaDataListToBulkUpdate(metaDataGroup._id, metaDataGroup.metaDataArray);
          }
        }
      }
    }

    // console.log(`metaDataInserts`,JSON.stringify(metaDataInsertsList,null,2))
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # metaData items to insert: ${metaDataInsertsList?.length} | N/A`,
    );
    // console.log(`metaDataUpdates`,JSON.stringify(metaDataUpdatesList,null,2))
    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | # metaData items to update: ${metaDataUpdatesList?.length} | N/A`,
    );

    //Generate object and thumbnailUrl
    let _metaDataInsertsList = [];
    // populate metadata from source video
    let metaInsertsListToPopulateSourceMetaData: {
      objectKey: string;
      sourceVideoId: string | ObjectId;
    }[] = [];
    for (let metaDataObject of metaDataInsertsList) {
      let storagePath = metaDataObject.insertOne?.storagePath;
      let objectKey = metaDataObject.insertOne?.objectKey;
      let thumbnailKey = metaDataObject.insertOne?.thumbnailKey;

      if (
        metaDataObject &&
        metaDataObject.insertOne &&
        storagePath &&
        typeof metaDataObject.insertOne.isMediaProcessingPending == 'boolean' &&
        !metaDataObject.insertOne.isMediaProcessingPending
      ) {
        let url = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(storagePath);
        // if (thumbnailKey) {
        //   let thumbnailUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(thumbnailKey);
        //   metaDataObject.insertOne.thumbnailUrl = thumbnailUrl;
        // }

        metaDataObject.insertOne.url = url;
        if (url && url.startsWith('https')) {
          metaDataObject.insertOne.isAccessible = true;
          metaDataObject.insertOne.objectStatus = OBJECT_STATUS.ACTIVE;
        } else metaDataObject.insertOne.objectStatus = OBJECT_STATUS.ACCESSED_FAILED;

        metaDataObject.insertOne.urlExpiredAt = new Date(new Date().getTime() + AWS_FILE_URL_EXPIRE_DURATION * 1000);
      }
      _metaDataInsertsList.push(metaDataObject);

      if (metaDataObject.insertOne?.sourceVideoId && metaDataObject.insertOne.objectKey) {
        // then a to populate metadata from source video list
        let tempObj = {
          objectKey: metaDataObject.insertOne.objectKey,
          sourceVideoId: new ObjectId(metaDataObject.insertOne.sourceVideoId),
        };
        metaInsertsListToPopulateSourceMetaData.push(tempObj);
      }
    }

    //update metadata
    await this.metaDataRepository.bulkWrite(_metaDataInsertsList);
    await this.metaDataRepository.bulkWrite(metaDataUpdatesList);

    logger.debug(JSON.stringify(_metaDataInsertsList));

    // mark processed items in inpud feed
    // await this.inputMetaDataFeedRepository.bulkWrite(inputFeedUpdatedMarkingList);
    // mark lastSyncTime in ApiKey
    // await this.apiKeyRepository.bulkWrite(apiKeySyncTimeUpdateList);

    logger.debug(
      `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | N/A | Successfully updated metaData of page`,
    );

    // invoke thumbnail generation for the metaData in metaData inserts
    if (_metaDataInsertsList && _metaDataInsertsList.length > 0) {
      logger.debug(
        `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | new inserts #: ${_metaDataInsertsList.length} | Metadata inserts available - thumbnail generation checking`,
      );

      // get unique uploadIds
      let mediaProcessPendingUploadIdSet = new Set<string>();
      let allUploadIdSet = new Set<string>();
      for (let metaDataItem of _metaDataInsertsList) {
        if (metaDataItem.insertOne && metaDataItem.insertOne.fileUploadId) {
          let uploadId = metaDataItem.insertOne.fileUploadId;
          if (metaDataItem.insertOne.isMediaProcessingPending) mediaProcessPendingUploadIdSet.add(uploadId.toString());
          allUploadIdSet.add(uploadId.toString());
        }
      }
      // get collectionIds of uploadIds and invoke media processor for each
      let mediaProcessPendingUploadIdArr = [...mediaProcessPendingUploadIdSet];
      let allUploadIdArr = [...allUploadIdSet];

      if (allUploadIdArr.length > 0) {
        logger.debug(
          `Processing metaData input Queue  | MediaProcessorService.handleThumbnailGenerationInvokingOnMetaDataInsert | media processing required inserts #: ${mediaProcessPendingUploadIdArr.length} | Metadata inserts available - thumbnail generation required`,
        );

        await this.mediaProcessorService.handleUploadJobStatusUpdateInvokingOnUploadIdList(
          allUploadIdArr,
          mediaProcessPendingUploadIdArr.length > 0,
        );

        logger.info(
          `-------------------- length ${mediaProcessPendingUploadIdArr.length}, item: ${JSON.stringify(
            mediaProcessPendingUploadIdArr,
          )}`,
        );
        // Invoke thumbnail generation for all media process pending uploads and do the job progress update for all uploads including other files
        if (mediaProcessPendingUploadIdArr.length > 0)
          this.mediaProcessorService.handleThumbnailGenerationInvokingOnUploadIdList(mediaProcessPendingUploadIdArr);
      }
    } else if (metaDataUpdatesList && metaDataUpdatesList.length > 0) {
      logger.debug(
        `Processing metaData input Queue  | MediaProcessorService.handleUploadJobStatusUpdateInvokingOnMetaDataUpdate | metadata required updates #: ${metaDataUpdatesList.length} | Metadata updates available - upload job status update required`,
      );

      let allUploadIdSet = new Set<string>();
      for (let metaDataItem of metaDataUpdatesList) {
        if (
          metaDataItem.updateOne &&
          metaDataItem.updateOne.update &&
          metaDataItem.updateOne.update.$set?.fileUploadId
        ) {
          let uploadId = metaDataItem.updateOne.update.$set.fileUploadId;
          allUploadIdSet.add(uploadId.toString());
        }
      }
      let allUploadIdArr = [...allUploadIdSet];

      this.mediaProcessorService.handleUploadJobStatusUpdateInvokingOnUploadIdList(allUploadIdArr, false);
    } else {
      logger.debug(
        `Processing metaData input Queue  | ObjectMetaUpdaterService.updateMetaDataFromInputQueue | new inserts #: N/A | no new Metadata inserts`,
      );
    }
    //----------------------------------------------------------
    // populate metadata from source video
    if (metaInsertsListToPopulateSourceMetaData && metaInsertsListToPopulateSourceMetaData.length > 0) {
      for (let insertItem of metaInsertsListToPopulateSourceMetaData) {
        this.metaFieldPropagatorService.propagateFieldsFromVideoToDerivedImages(insertItem.sourceVideoId, [
          insertItem.objectKey,
        ]);
      }
    }

    return;
  }

  /**
   * Use to add addition fields required in MetaData model for InputMetaDataFeed documents
   * @param firstMetaDataItemOfNewGroup InputMetaDataFeed array
   */
  async updatefirstMetaDataItem(firstMetaDataItemOfNewGroup: InputMetaDataFeed) {
    let name = firstMetaDataItemOfNewGroup.metaDataObject.name;
    if (typeof name == 'string' && name.length != 0) {
      firstMetaDataItemOfNewGroup.metaDataObject.nameInLowerCase = name.toLowerCase();
    }
    firstMetaDataItemOfNewGroup.metaDataObject.updatedAt = new Date();
    firstMetaDataItemOfNewGroup.metaDataObject.teamId = new ObjectId(firstMetaDataItemOfNewGroup.metaDataObject.teamId);
    firstMetaDataItemOfNewGroup.metaDataObject.createdAt = new Date();
    //No thumbnails for other type
    if (
      firstMetaDataItemOfNewGroup.metaDataObject.objectType == ContentType.OTHER ||
      firstMetaDataItemOfNewGroup.metaDataObject.thumbnailKey
    )
      firstMetaDataItemOfNewGroup.metaDataObject.statPending = true; //To trigger calculation of counts in periodic job
    firstMetaDataItemOfNewGroup.metaDataObject.statPendingAt = new Date();

    if (firstMetaDataItemOfNewGroup.metaDataObject.collectionId) {
      firstMetaDataItemOfNewGroup.metaDataObject.parentList = [
        new ObjectId(firstMetaDataItemOfNewGroup.metaDataObject.collectionId),
      ];
    } else {
      firstMetaDataItemOfNewGroup.metaDataObject.parentList = [];
    }

    if (firstMetaDataItemOfNewGroup.metaDataObject.objectType == ContentType.VIDEO) {
      firstMetaDataItemOfNewGroup.metaDataObject.videoCount = 1;
      firstMetaDataItemOfNewGroup.metaDataObject.isLeaf = true;
      firstMetaDataItemOfNewGroup.metaDataObject.verificationStatusCount = {
        raw: firstMetaDataItemOfNewGroup.metaDataObject.frameCount || 0,
        machineAnnotated: 0,
        verified: 0,
      };
    } else if (firstMetaDataItemOfNewGroup.metaDataObject.objectType == ContentType.IMAGE) {
      firstMetaDataItemOfNewGroup.metaDataObject.videoCount = 0;
      firstMetaDataItemOfNewGroup.metaDataObject.isLeaf = true;
      firstMetaDataItemOfNewGroup.metaDataObject.verificationStatusCount = {
        raw: 1,
        machineAnnotated: 0,
        verified: 0,
      };
    } else if (firstMetaDataItemOfNewGroup.metaDataObject.objectType == ContentType.OTHER) {
      firstMetaDataItemOfNewGroup.metaDataObject.otherCount = 1;
      firstMetaDataItemOfNewGroup.metaDataObject.isLeaf = true;
    } else {
      firstMetaDataItemOfNewGroup.metaDataObject.verificationStatusCount = {
        raw: 0,
        machineAnnotated: 0,
        verified: 0,
      };
    }

    // add isOriginalUploadFile flag
    if (firstMetaDataItemOfNewGroup.metaDataObject.isDerivedFile) {
      firstMetaDataItemOfNewGroup.metaDataObject.isOriginalUploadFile = false;
    } else {
      firstMetaDataItemOfNewGroup.metaDataObject.isOriginalUploadFile = true;
    }

    // if (firstMetaDataItemOfNewGroup.metaDataPushList) {
    //   //for each key value pair
    //   for (let key in firstMetaDataItemOfNewGroup.metaDataPushList) {
    //     if (Array.isArray(firstMetaDataItemOfNewGroup.metaDataPushList[key])) {
    //       firstMetaDataItemOfNewGroup.metaDataObject[key] = firstMetaDataItemOfNewGroup.metaDataPushList[key];
    //     } else {
    //       firstMetaDataItemOfNewGroup.metaDataObject[key] = [firstMetaDataItemOfNewGroup.metaDataPushList[key]];
    //     }
    //   }
    // }
  }

  /**
   * Sub method to process metaData on insert and update
   * used to format date objects and objectIds
   * @params metaDataObj
   **/
  async formatMetaDataBeforeUpdateOrInsert(metaDataObj: Partial<MetaData>) {
    // console.log(`----------------------------------------------------------------------------------------`)
    // console.log(metaDataObj)
    // console.log(`----------------------------------------------------------------------------------------`)
    for (const property in metaDataObj) {
      // keys to be updated conditionally here
      if (property == 'urlExpiredAt') {
        //  Date Type
        if (metaDataObj[property]) metaDataObj[property] = new Date(metaDataObj[property]!);
      }
      if (property == 'teamId') {
        //  Date Type
        if (metaDataObj[property]) metaDataObj[property] = new ObjectId(metaDataObj[property]!);
      }
      if (property == 'collectionId') {
        //  ObjectId Type
        if (metaDataObj[property]) metaDataObj[property] = new ObjectId(metaDataObj[property]!);
      }
      if (
        (property == 'parentList' ||
          property == 'taskIdList' ||
          property == 'vCollectionIdList' ||
          property == 'allowedUserIdList') &&
        metaDataObj[property] &&
        Array.isArray(metaDataObj[property])
      ) {
        //  ObjectId Type
        for (let i = 0; i < metaDataObj[property]!.length; i++) {
          metaDataObj[property]![i] = new ObjectId(metaDataObj[property]![i]);
        }
      }
      if (property == 'annotationProjectList' && metaDataObj[property] && Array.isArray(metaDataObj[property])) {
        //  ObjectId Type
        for (let i = 0; i < metaDataObj[property]!.length; i++) {
          metaDataObj[property]![i].id = new ObjectId(metaDataObj[property]![i].id);
        }
      }
    }
    // console.log(`----------------------------------------------------------------------------------------`)
    // console.log(metaDataObj)
    // console.log(`----------------------------------------------------------------------------------------`)
  }

  /**
   * use to identify custom metadata and bundle them into 'customMeta' single field
   * @param metaDataObj metadata object
   * @param isUpdate whether it is going to update a document in db or to insert a document into db
   */
  async bundleCustomMeta(metaDataObj: Partial<MetaData>, isUpdate: boolean) {
    for (const [key, value] of Object.entries(metaDataObj)) {
      if (!MetaData.definition.properties.hasOwnProperty(key)) {
        if (isUpdate) {
          // use with $set operator to update
          metaDataObj['customMeta.' + key] = value;
        } else {
          if (!metaDataObj.customMeta) {
            metaDataObj.customMeta = {};
          }
          // use to insert
          metaDataObj.customMeta[key] = value;
        }

        // delete custom field from metaobject after moving it to customMeta field
        delete metaDataObj[key];
      }
    }
  }

  /**
   * Sub method to mark processed metaData input feed and update apiKey syncTimeStamp
   * called from updateMetaDataFromInputQueue
   * @params metaDataInputArray
   **/
  async markProcessedInputAndApiKeys(metaDataInputArray: InputMetaDataFeed[]) {
    let inactiveteInputGroupList: BulkUpdateOneInputMetaDataFeedFormat[] = [];
    let markSyncTimestampOfApiKeySet = new Set<string>();
    for (let metaDataInputItem of metaDataInputArray) {
      //to mark input feed as inactive
      let markInactiveObj = {
        updateOne: {
          filter: {_id: metaDataInputItem._id},
          update: {$set: {isActive: false}},
        },
      };
      inactiveteInputGroupList.push(markInactiveObj);

      // set filter unique apiKeys to update syncTimeStamp of apiKey
      markSyncTimestampOfApiKeySet.add(metaDataInputItem.apiKey);
    }

    let markSyncTimeOfApiKeyList = Array.from(markSyncTimestampOfApiKeySet).map((apiKey: string) => {
      return {
        updateOne: {
          filter: {key: apiKey},
          update: {$set: {lastSyncTimestamp: new Date()}},
        },
      };
    });

    return {inactiveteInputGroupList, markSyncTimeOfApiKeyList};
  }

  /**
   * Use to update file urls
   * @returns Object url with expire settings
   */
  async fileUrlRenew() {
    //logger.debug('start')
    let params = [
      {
        $match: {
          urlExpiredAt: {
            $lte: new Date(new Date().getTime() + AWS_FILE_URL_RENEW_BEFORE_DURATION * 1000),
          },
        },
      },
      {$limit: 10000},
      {
        $project: {
          _id: 1,
          storagePath: 1,
          thumbnailKey: 1,
          urlExpiredAt: 1,
          objectType: 1,
          bucketName: 1,
        },
      },
    ];
    let filesToUpdate: {
      _id: string;
      storagePath: string;
      thumbnailKey: string;
      urlExpiredAt: Date;
      objectType: number;
      bucketName: string;
    }[] = await this.metaDataRepository.aggregate(params);
    //logger.debug(filesToUpdate)

    let metaDataUpdatedList: {
      updateOne: {
        filter: {_id: ObjectId};
        update: {$set: object};
      };
    }[] = [];
    for (let file of filesToUpdate) {
      let url;
      let collectionObjectTypes = [
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.OTHER_COLLECTION,
      ];
      if (!collectionObjectTypes.includes(file.objectType)) {
        if (file.storagePath)
          url = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
            file.storagePath,
            AWS_FILE_URL_EXPIRE_DURATION,
            file.bucketName,
          );
      }
      let thumbnailUrl;
      if (file.thumbnailKey)
        thumbnailUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
          file.thumbnailKey,
          AWS_FILE_URL_EXPIRE_DURATION,
        );

      logger.debug('Renew the object url: ', file.storagePath, 'thumbnail key', file.thumbnailKey);

      metaDataUpdatedList.push({
        updateOne: {
          filter: {_id: new ObjectId(file._id)},
          update: {
            $set: {
              url: url,
              thumbnailUrl: thumbnailUrl,
              urlExpiredAt: new Date(new Date().getTime() + AWS_FILE_URL_EXPIRE_DURATION * 1000),
            },
          },
        },
      });
    }
    logger.debug(
      `Update file urls | ObjectMetaUpdaterService.fileUrlRenew | N/A | ${metaDataUpdatedList.length} file urls updated`,
    );
    await this.metaDataRepository.bulkWrite(metaDataUpdatedList);

    // re run the function if there are more urls to be renewed
    let checkMore = await this.metaDataRepository.findOne({
      where: {
        urlExpiredAt: {
          $lte: new Date(new Date().getTime() + AWS_FILE_URL_RENEW_BEFORE_DURATION * 1000),
        },
      },
    });
    if (checkMore) {
      await this.fileUrlRenew();
    }
  }

  /**
   * Use to insert or update a list of MetaDataUpdate documents
   * @param operationType {OperationType} type of operation
   * @param operationMode {OperationMode} mode of operation type
   * @param operationId {string} Unique id for perticular operation (ex: if annotation operation then we can use annotation projectId as operationId)
   * @param metaUpdatesArray {MetaDataUpdateObject[]}
   * @param isNormalized true if annotation data is normalized
   * @param sessionData datalake job related data
   * @returns returnArray {operationId: string, objectKey: string, success: boolean}[] status of insert or update of each document
   */
  async updateOperationData(
    operationType: OperationType,
    operationMode: OperationMode,
    operationId: string,
    metaUpdatesArray: MetaDataUpdateObject[],
    teamId?: string,
    isNormalized?: boolean,
    sessionData?: {
      sessionId: string;
      totalImageCount: number;
      uploadedImageCount: number;
    },
    operationName?: string,
    currentUserProfile?: UserProfileDetailed,
  ) {
    let returnArray: {
      operationId: string;
      success: boolean;
      objectKey?: string;
      storagePath?: string;
      bucketName?: string;
    }[] = [];

    let failedArray: string[] = [];
    let allArray: string[] = [];

    for (let metaUpdates of metaUpdatesArray) {
      if (!metaUpdates.bucketName) {
        metaUpdates.bucketName = defaultBucketName;
      }

      let result = await this.upsertMetaDataUpdate(
        operationType,
        operationMode,
        operationId,
        metaUpdates,
        _.cloneDeep(metaUpdates.data),
        teamId,
        isNormalized,
        operationName,
      );

      let uniqueIdentifier = metaUpdates.objectKey
        ? metaUpdates.objectKey
        : metaUpdates.storagePath
        ? `${metaUpdates.bucketName}-${metaUpdates.storagePath}`
        : metaUpdates.jobId
        ? `${metaUpdates.jobId}-${metaUpdates.fileName}`
        : '';

      if (!result.success) {
        failedArray.push(uniqueIdentifier);
      }
      allArray.push(uniqueIdentifier);

      returnArray.push({
        operationId: operationId,
        objectKey: metaUpdates.objectKey,
        storagePath: metaUpdates.storagePath,
        bucketName: metaUpdates.bucketName,
        success: result.success,
      });
    }

    if (sessionData?.sessionId) {
      this.jobService.updateAnnotationUploadJobEntry(
        sessionData.sessionId,
        sessionData.totalImageCount,
        sessionData.uploadedImageCount,
        metaUpdatesArray,
        operationId,
        operationType,
        operationMode,
        teamId,
        currentUserProfile?.id,
        currentUserProfile?.name,
        allArray,
        failedArray,
      );
    }

    return returnArray;
  }

  async removeCreatedAt(metaUpdatesArray: MetaDataUpdateObject[]) {
    for (let i in metaUpdatesArray) {
      if (metaUpdatesArray[i].data.annotationObjects) {
        let _tempObjects = [];
        for (let object of metaUpdatesArray[i].data?.annotationObjects!) {
          delete object.createdAt;
          _tempObjects.push(object);
        }
        metaUpdatesArray[i].data.annotationObjects = _tempObjects;
      }
    }
  }

  /**
   * Use to insert or update MetaDataUpdate single document
   * @param operationType {OperationType} type of operation
   * @param operationMode {OperationMode} mode of operation type
   * @param operationId {string} Unique id for perticular operation (ex: if annotation operation then we can use annotation projectId as operationId)
   * @param objectKey {string} Unique key for object (ex: aws key of the frame)
   * @param metaUpdates {Partial<MetaDataUpdate>} operation data to insert of update
   * @returns returnObj {success: boolean} status of insert or update
   */
  async upsertMetaDataUpdate(
    operationType: OperationType,
    operationMode: OperationMode,
    operationId: string,
    metaUpdateObj: MetaDataUpdateObject,
    metaUpdates: Partial<MetaDataUpdate>,
    teamId?: string,
    isNormalized?: boolean,
    operationName?: string,
  ) {
    let objectKey = metaUpdateObj.objectKey;
    let storagePath = metaUpdateObj.storagePath;
    let bucketName = metaUpdateObj.bucketName;
    let jobId = metaUpdateObj.jobId;
    let fileName = metaUpdateObj.fileName;
    let existingMetaUpdate = null;
    let existingMetaObj = null;

    let uniqueIdentifier = objectKey
      ? objectKey
      : storagePath
      ? `${bucketName}-${storagePath}`
      : jobId
      ? `${jobId}-${fileName}`
      : '';

    logger.info(
      `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | updating objectKey:${uniqueIdentifier} from operationId:${operationId}, operationType:${operationType}, operationMode:${operationMode}, isNormalized: ${isNormalized}, operationName: ${operationName}`,
    );

    // return object, initialize with false and change it to true when job done
    let returnObj: {success: boolean} = {success: false};

    if (objectKey) {
      // generate matching criteria to find existing MetaDataUpdate record
      let matchingCriteria: Where<MetaDataUpdate>[] = [{objectKey: objectKey}, {operationId: operationId}];

      let returnValues = await Promise.all([
        this.metaDataUpdateRepository.findOne({
          where: {
            and: matchingCriteria,
          },
        }),

        this.metaDataRepository.findOne({
          where: {
            objectKey: objectKey,
            objectStatus: OBJECT_STATUS.ACTIVE,
            objectType: ContentType.IMAGE,
          },
        }),
      ]);
      existingMetaUpdate = returnValues[0];
      existingMetaObj = returnValues[1];
    } else if ((storagePath && bucketName) || (jobId && fileName)) {
      // generate matching criteria to find existing MetaObject record
      let matchingCriteria: Where<MetaDataUpdate> = {
        objectStatus: OBJECT_STATUS.ACTIVE,
        objectType: ContentType.IMAGE,
      };

      if (storagePath && bucketName) {
        matchingCriteria.bucketName = bucketName;
        matchingCriteria.storagePath = storagePath;
      } else if (jobId && fileName) {
        try {
          let jobObject = await this.jobRepository.findOne({
            where: {
              _id: new ObjectId(jobId),
              jobType: JobType.fileUpload,
            },
          });
          let uploadId = undefined;

          if (jobObject && jobObject.jobSpecificDetails && jobObject.jobSpecificDetails.uploadId) {
            uploadId = jobObject.jobSpecificDetails.uploadId;
          } else {
            logger.error(
              `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the requested object for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
            );
            return returnObj;
          }

          matchingCriteria.fileUploadIdList = uploadId;
          matchingCriteria.name = fileName;
        } catch (error) {
          logger.error(
            `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the requested object for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
          );
          return returnObj;
        }
      } else {
        logger.error(
          `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the requested object for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
        );
        return returnObj;
      }

      existingMetaObj = await this.metaDataRepository.findOne({
        where: matchingCriteria,
      });

      objectKey = existingMetaObj?.objectKey;

      if (existingMetaObj) {
        existingMetaUpdate = await this.metaDataUpdateRepository.findOne({
          where: {
            and: [{objectKey: objectKey}, {operationId: operationId}],
          },
        });
      }
    } else {
      logger.error(
        `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the requested object for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
      );
      return returnObj;
    }

    if (!existingMetaObj) {
      logger.warn(
        `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the requested object for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
      );
      return returnObj;
    }

    try {
      this.metaDataRepository.updateUpdatedAt(existingMetaObj.id);
      logger.info(
        `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | updateAt updateed for metadataId: ${existingMetaObj.id}`,
      );
    } catch (error) {
      logger.error(
        `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | updateAt update error of metadataId: ${existingMetaObj.id}`,
      );
    }

    if (isNormalized) {
      if (existingMetaObj.resolution == undefined) {
        logger.error(`${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | failed to update MetadataUpdated for,
          operationType: ${operationType},
          operationMode: ${operationMode},
          operationId: ${operationId},
          objectKey: ${objectKey},
          metaUpdates: ${metaUpdates},
          Error: Image resolution missing - required for de-normalization
        `);
        return returnObj;
      }

      let width = existingMetaObj.resolution.width;
      let height = existingMetaObj.resolution.height;
      if (width && height && metaUpdates.annotationObjects) {
        metaUpdates.annotationObjects = await this.reverseNormalizeAnnotation(
          metaUpdates.annotationObjects,
          width,
          height,
        );
      } else {
        logger.warn(
          `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the width, height or metaUpdates.annotationObjects for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}, isNormalized: ${isNormalized}`,
        );
        return returnObj;
      }
    }

    try {
      // set flags for calculations
      if (operationType == OperationType.ANNOTATION && operationMode == OperationMode.AUTO) {
        metaUpdates.analyticsPending = true;
      } else if (operationType == OperationType.ANNOTATION && operationMode == OperationMode.HUMAN) {
        this.metaDataUpdateRepository.updateAll(
          {
            analyticsPending: true,
          },
          {
            objectKey: objectKey,
            operationType: OperationType.ANNOTATION,
            operationMode: OperationMode.AUTO,
          },
        );
      }

      // if MetaDataUpdate record exist, then update it
      if (existingMetaUpdate) {
        //avoid replacing unchangeable fields
        if (metaUpdates.hasOwnProperty('createdAt')) delete metaUpdates.createdAt;
        if (metaUpdates.hasOwnProperty('objectKey')) delete metaUpdates.objectKey;

        //add updatedAt field
        metaUpdates.updatedAt = new Date();
        //operationMode, operationType can be changed for a image for a operationId (example: augmented annotations)
        metaUpdates.operationMode = operationMode;
        metaUpdates.operationType = operationType;
        await this.metaDataUpdateRepository.updateById(existingMetaUpdate.id, metaUpdates);
      }
      // if MetaDataUpdate record not exist, then create it
      else {
        if (existingMetaObj.collectionId) metaUpdates.collectionId = new ObjectId(existingMetaObj.collectionId);
        metaUpdates.createdAt = new Date();
        metaUpdates.updatedAt = new Date();

        metaUpdates.objectKey = objectKey;
        metaUpdates.operationId = operationId;
        metaUpdates.operationMode = operationMode;
        metaUpdates.operationType = operationType;
        metaUpdates.teamId = new ObjectId(teamId);
        metaUpdates.operationName = operationName ? operationName : operationId.toString();
        await this.metaDataUpdateRepository.create(metaUpdates);
      }

      returnObj.success = true;

      if (!objectKey) {
        logger.warn(
          `${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | Unable to find the objectKey for updating metadata, objectKey:${uniqueIdentifier}, operationId:${operationId}`,
        );
        return returnObj;
      }

      this.updateMetaDataOnMetaDataUpdates(operationType, operationMode, objectKey, metaUpdates);
    } catch (e) {
      logger.error(`${FLOWS.METADATA_UPDATE_OPERATIONS} | ObjectMetaUpdaterService.upsertMetaDataUpdate | N/A | failed to update MetadataUpdated for,
        operationType: ${operationType},
        operationMode: ${operationMode},
        operationId: ${operationId},
        objectKey: ${objectKey},
        metaUpdates: ${metaUpdates},
        Error: ${e}
      `);
    }

    return returnObj;
  }

  /**
   * Use to reverse the normalize data of the annotation object array
   * @param annotationObjects annotation objects
   * @param width {number} width of the image
   * @param height {number} height of the image
   * @returns annotation objects
   */
  async reverseNormalizeAnnotation(annotationObjects: AnnotationObject[], width: number, height: number) {
    for (let i in annotationObjects) {
      let boxBoundariesAndDimensions = annotationObjects[i].boxBoundariesAndDimensions;
      if (boxBoundariesAndDimensions) {
        let tempBoxBoundariesAndDimensions = {
          x: boxBoundariesAndDimensions.x * width,
          y: boxBoundariesAndDimensions.y * height,
          w: boxBoundariesAndDimensions.w * width,
          h: boxBoundariesAndDimensions.h * height,
        };
        annotationObjects[i].boxBoundariesAndDimensions = tempBoxBoundariesAndDimensions;
      }
      let points = annotationObjects[i].points;
      if (points) {
        let tempPoints: number[][] = [];
        for (let point of points) {
          let tempPoint = [point[0] * width, point[1] * height];
          tempPoints.push(tempPoint);
        }
        annotationObjects[i].points = tempPoints;
      }
    }
    return annotationObjects;
  }

  /**
   * Use to update MetaData records after MetaDataUpdate operations
   * @param operationType {OperationType} type of operation
   * @param operationMode {OperationMode} mode of operation type
   * @param objectKey {string} Unique key for object (ex: aws key of the frame)
   */
  async updateMetaDataOnMetaDataUpdates(
    operationType: OperationType,
    operationMode: OperationMode,
    objectKey: string,
    metaUpdates: Partial<MetaDataUpdate>,
  ) {
    let metaObject = await this.metaDataRepository.findOne({
      where: {
        objectKey: objectKey,
      },
    });

    if (metaObject) {
      // before update annotation related stats make sure operation type is ANNOTATION
      if (operationType == OperationType.ANNOTATION) {
        // annotation stats update, metaObject should be a frame in order to annotate & annotation related stats update
        if (metaObject.objectType == ContentType.IMAGE) {
          if (metaUpdates.annotationObjects && metaUpdates.annotationObjects.length > 0 /** check box count */) {
            // let currentVerificationStatus = metaObject.frameVerificationStatus
            //   ? metaObject.frameVerificationStatus
            //   : FrameVerificationStatus.RAW;
            // let newVerificationStatus: FrameVerificationStatus;

            let currentVerificationStatusCount = metaObject.verificationStatusCount
              ? metaObject.verificationStatusCount
              : {
                  raw: 1,
                  machineAnnotated: 0,
                  verified: 0,
                };

            let newVerificationStatusCount = _.cloneDeep(currentVerificationStatusCount);

            // let newVerificationStatusCount: VerificationStatusCount = {
            //   raw: 0,
            //   machineAnnotated: 0,
            //   verified: 0,
            // };
            if (operationMode == OperationMode.HUMAN) {
              // newVerificationStatus = FrameVerificationStatus.VERIFIED; // ?????????????????????????????? have to check box availability
              // newVerificationStatusCount.verified = 1;
              newVerificationStatusCount.raw = 0;
              newVerificationStatusCount.verified = 1;
            } else if (operationMode == OperationMode.AUTO) {
              // newVerificationStatus = FrameVerificationStatus.MACHINE_ANNOTATED; // ?????????????????????????????? have to check box availability
              // newVerificationStatusCount.machineAnnotated = 1;
              newVerificationStatusCount.raw = 0;
              newVerificationStatusCount.machineAnnotated = 1;
            } else {
              // newVerificationStatus = FrameVerificationStatus.RAW;
              // newVerificationStatusCount.raw = 1;
              newVerificationStatusCount.raw = 1;
            }

            // write to db only if frame verification status need to change (human verified frame should not downgrade upon auto annotations)
            // if (currentVerificationStatus < newVerificationStatus) {
            //   await this.metaDataRepository.updateById(metaObject.id, {
            //     frameVerificationStatus: newVerificationStatus,
            //     verificationStatusCount: newVerificationStatusCount,
            //     statPending: true,
            //     statPendingAt: new Date()
            //   });
            // }

            // write to db only if verification status count changed
            if (
              currentVerificationStatusCount.raw != newVerificationStatusCount.raw ||
              currentVerificationStatusCount.machineAnnotated != newVerificationStatusCount.machineAnnotated ||
              currentVerificationStatusCount.verified != newVerificationStatusCount.verified
            ) {
              await this.metaDataRepository.updateById(metaObject.id, {
                verificationStatusCount: newVerificationStatusCount,
                statPending: true,
                statPendingAt: new Date(),
              });
            }

            // -------------
            // set annotation related stats pending true
            await this.metaDataRepository.updateById(metaObject.id, {
              annotationStatPending: true,
            });
          } else {
            if (
              metaObject.verificationStatusCount &&
              metaObject.verificationStatusCount.raw == 0
              // metaObject.frameVerificationStatus !=
              // FrameVerificationStatus.RAW /** check if not raw */
            ) {
              //  flag to recalculate verification //
              await this.metaDataRepository.markStatPendingFlagsTrue({
                _id: new ObjectId(metaObject.id),
              });
            }
          }
        }
      }
    }
  }

  /**
   * attachh first object thumbnail url to collection thumbnail
   */
  async generateThumbnailOfCollection() {
    let collectionList = await this.metaDataRepository.find({
      where: {isPendingThumbnail: true},
    });

    for (let collection of collectionList) {
      // let whereQuery: {[k: string]: string | undefined};

      // if (collection.isLogical) {
      //   whereQuery = {vCollectionIdList: collection.id};
      // } else {
      //   whereQuery = {collectionId: collection.id};
      // }

      let firstObject = await this.metaDataRepository.findOne({
        where: {vCollectionIdList: collection.id},
      });

      if (firstObject && firstObject.thumbnailKey) {
        await this.metaDataRepository.updateById(collection.id, {
          thumbnailKey: firstObject.thumbnailKey,
          thumbnailUrl: firstObject.thumbnailUrl,
          isPendingThumbnail: false,
          urlExpiredAt: firstObject.urlExpiredAt,
          updatedAt: new Date(),
        });
      }
    }
  }

  /**
   * Use to get meta tags suggestions for a given team
   * @param teamId id of the team
   * @returns string[] - array of tags
   */
  async getMetaDataTagSuggestions(teamId: string) {
    // get system tags for the team
    let allTagList: string[] = [];
    let systemTagList = await this.metaTagRepository.distinct('tag', {teamId: new ObjectId(teamId)});

    if (systemTagList && Array.isArray(systemTagList) && systemTagList.length > 0) {
      allTagList = systemTagList;
    }

    return {
      suggestions: allTagList,
    };
  }

  /**
   * get metaData Tags of a file or a collection
   * @param id string (of collection or file)
   * @return string[] - array of tags
   */
  async getMetaDataTags(id: string, teamId?: string) {
    if (!id) {
      logger.warn(`Get meta tags | ObjectMetaUpdaterService.getMetaDataTags | N/A | invalid id`);
      return; //HttpErrors[422]
    }

    // get system data file for the team
    let allTagList: string[] | undefined = undefined;
    if (teamId) {
      allTagList = (await this.metaTagRepository.distinct('tag', {})) || [];
    }

    let metaObject = await this.metaDataRepository.findById(id);

    let metaTags: string[] = [];
    if (metaObject && metaObject.Tags && Array.isArray(metaObject.Tags)) {
      metaTags = metaObject.Tags;
    }

    // determine tag deletability
    let isTagDeletable: boolean = false;

    if (
      [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType) &&
      metaObject.isOriginalUploadFile
    ) {
      // then the tag is deletable
      isTagDeletable = true;
    }
    // else if (metaObject.isLogical) {
    //   // then the tag is deletable
    //   isTagDeletable = true;
    // }

    let responseTags = {
      values: metaTags,
      suggestions: allTagList,
      isTagDeletable: isTagDeletable,
    };

    // console.log(metaObject)
    // console.log(responseTags)

    return responseTags;
  }

  /**
   * add metaData Tags of a file or a collection
   * @param id string (of collection or file)
   * @param tags string[] (array of tags)
   * @param updateSelfOnly boolean (wheather to propagate to below or not)
   * @return string[] - array of tags
   */
  // async addMetaDataTags(
  //   id: string,
  //   tags: string[],
  //   deleteTags: string[],
  //   updateSelfOnly?: boolean,
  //   teamId?: string,
  //   userName?: string,
  // ) {
  //   if (!id) {
  //     logger.warn(`Add meta tags | ObjectMetaUpdaterService.addMetaDataTags | N/A | invalid id`);
  //     return; //HttpErrors[422]
  //   }

  //   // delete deleted tags if available
  //   await this.handleTagDelete(id, deleteTags, updateSelfOnly);

  //   // add newly inserted tags
  //   let addedNewTags: string[] | undefined;
  //   if (tags && Array.isArray(tags) && tags.length > 0) {
  //     addedNewTags = await this.metaFieldPropagatorService.addCompletePropagationArrayFieldValues(
  //       id,
  //       'Tags',
  //       tags,
  //       updateSelfOnly,
  //     );
  //   }
  //   // ---------------

  //   //update search queries & set updatedAt
  //   if (deleteTags.length > 0 || (addedNewTags && addedNewTags.length > 0)) {
  //     let metaObj = await this.metaDataRepository.findById(id);

  //     if (!teamId) {
  //       logger.error(
  //         `Update - edit metadata Tags | ObjectMetaUpdaterService.addMetaDataTags | N/A | couldn't find team for metadata id: ${id} `,
  //       );
  //       throw new HttpErrors.NotAcceptable('Team not exist for metadta');
  //     }
  //     if (
  //       metaObj.objectType == ContentType.IMAGE ||
  //       metaObj.objectType == ContentType.VIDEO ||
  //       metaObj.objectType == ContentType.OTHER
  //     ) {
  //       //rebuild query for collection
  //       if (metaObj.collectionId) {
  //         let collectionObj = await this.metaDataRepository.findById(metaObj.collectionId.toHexString());
  //         let distinctTags = collectionObj.Tags;
  //         if (distinctTags) {
  //           this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //             SearchQueryRootGroup.METADATA,
  //             'Tags',
  //             teamId,
  //             metaObj.collectionId as unknown as string,
  //             distinctTags,
  //           );
  //         }
  //       }
  //       //rebuild query for frame collection (in case of video)
  //       if (metaObj.frameCollectionId) {
  //         let frameCollectionObj = await this.metaDataRepository.findById(metaObj.frameCollectionId);
  //         let distinctTags = frameCollectionObj.Tags;
  //         if (distinctTags) {
  //           this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //             SearchQueryRootGroup.METADATA,
  //             'Tags',
  //             teamId,
  //             metaObj.frameCollectionId,
  //             distinctTags,
  //           );
  //         }
  //       }

  //       //rebuid query for logical entites
  //       let logicalCollectionIds: string[] = [];
  //       if (metaObj.vCollectionIdList && metaObj.vCollectionIdList.length > 0) {
  //         logicalCollectionIds = [...logicalCollectionIds, ...metaObj.vCollectionIdList];
  //       }
  //       if (metaObj.datasetVersionList && metaObj.datasetVersionList.length > 0) {
  //         for (let detasetVersion of metaObj.datasetVersionList) {
  //           if (detasetVersion.datasetMetaId)
  //             logicalCollectionIds.push(detasetVersion.datasetMetaId as unknown as string);
  //         }
  //       }
  //       logicalCollectionIds.forEach(vId => {
  //         this.queryOptionRepository.rebuildCollectionQueryOptionForTags(vId);
  //       });
  //     } else if (
  //       metaObj.objectType == ContentType.IMAGE_COLLECTION ||
  //       metaObj.objectType == ContentType.VIDEO_COLLECTION ||
  //       metaObj.objectType == ContentType.DATASET ||
  //       metaObj.objectType == ContentType.OTHER_COLLECTION
  //     ) {
  //       //rebuild query for collection
  //       if (metaObj.isLogical) {
  //         //in case of logical entities, the meta head doesn't contain all tags, so need to aggregate them
  //         this.queryOptionRepository.rebuildCollectionQueryOptionForTags(id);
  //       } else {
  //         //in case of physical entities, the meta head contain all tags
  //         let distinctTags = metaObj.Tags;
  //         if (distinctTags) {
  //           //rebuild query for collection
  //           this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //             SearchQueryRootGroup.METADATA,
  //             'Tags',
  //             teamId,
  //             id,
  //             distinctTags,
  //           );
  //         }
  //       }

  //       if (!updateSelfOnly) {
  //         //rebuild query for all frameCollections in case of video collection
  //         let allVideos = await this.metaDataRepository.find({
  //           where: {
  //             collectionId: id,
  //           },
  //           fields: {
  //             frameCollectionId: true,
  //           },
  //         });
  //         for (let _video of allVideos) {
  //           if (_video.frameCollectionId) {
  //             let frameCollectionObj = await this.metaDataRepository.findById(_video.frameCollectionId);
  //             let distinctTags = frameCollectionObj.Tags;
  //             if (distinctTags) {
  //               this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
  //                 SearchQueryRootGroup.METADATA,
  //                 'Tags',
  //                 teamId,
  //                 _video.frameCollectionId,
  //                 distinctTags,
  //               );
  //             }
  //           }
  //         }
  //       }
  //     }
  //     //set updatedAt of metadata and its parents after add new tag or tags
  //     this.metaDataService.updateUpdatedAt(id);
  //   }

  //   // return the updated tags
  //   return await this.getMetaDataTags(id, teamId);
  // }

  /**
   * handle tag deleting
   * @param id
   * @param deleteTags
   * @param updateSelfOnly
   */
  // async handleTagDelete(id: string, deleteTags: string[], updateSelfOnly?: boolean) {
  //   let metaObject = await this.metaDataRepository.findById(id);
  //   if (
  //     !(
  //       (
  //         [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType) &&
  //         metaObject.isOriginalUploadFile
  //       )
  //       // || metaObject.isLogical
  //     )
  //   ) {
  //     // then delete tags is not allowed
  //     return;
  //   }

  //   if (deleteTags && Array.isArray(deleteTags) && deleteTags.length > 0) {
  //     await this.metaFieldPropagatorService.deleteArrayFieldValueWithCompletePropagation(
  //       id,
  //       'Tags',
  //       deleteTags,
  //       updateSelfOnly,
  //     );

  //     // delete in parent collections if required
  //     // let metaObject = await this.metaDataRepository.findById(id)
  //     if (
  //       [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType) &&
  //       metaObject.collectionId
  //     ) {
  //       let tagPullList: string[] = [];
  //       for (let tag of deleteTags) {
  //         // check whether tag is present anymore
  //         let tagExistance = await this.metaDataRepository.findOne({
  //           where: {
  //             collectionId: metaObject.collectionId,
  //             objectType: metaObject.objectType,
  //             Tags: tag,
  //           },
  //         });
  //         if (!tagExistance) {
  //           // then tag is no longer present in children
  //           tagPullList.push(tag);
  //         }
  //       }
  //       // remove relevent tags from collection
  //       if (tagPullList.length > 0) {
  //         await this.metaDataRepository.updateManyRemoveFromList(
  //           {_id: new ObjectId(metaObject.collectionId)},
  //           {Tags: {$in: tagPullList}},
  //           [],
  //         );
  //       }
  //     }
  //   }
  // }

  /**
   * delete metaData Tags of a file or a collection
   * @param id string (of collection or file)
   * @param tag string (tag value to delete)
   * @param updateSelfOnly boolean (wheather to propagate to below or not)
   * @return string[] - array of tags
   */
  // async deleteMetaDataTag(id: string, tag: string, updateSelfOnly?: boolean, teamId?: string) {
  //   if (!id) {
  //     logger.warn(`Delete meta tags | ObjectMetaUpdaterService.deleteMetaDataTag | N/A | invalid id`);
  //     return; //HttpErrors[422]
  //   }

  //   let tags = [tag];

  //   // await this.metaFieldPropagatorService.deleteArrayFieldValueWithCompletePropagation(id, "Tags", tags, updateSelfOnly)
  //   // delete tags handling
  //   await this.handleTagDelete(id, tags, updateSelfOnly);

  //   // return the updated tags
  //   return await this.getMetaDataTags(id, teamId);
  // }

  /**
   * get metaData fields for edit metaData of a file or a collection
   * @param id string (of collection or file)
   * @return existing fields and suggestions
   */
  // async getFieldsOfMetadata(id: string, teamId?: string) {
  //   if (!id) {
  //     logger.warn(`Edit metadata | ObjectMetaUpdaterService.getFieldsOfMetadata | N/A | invalid id`);
  //     return; //HttpErrors[422]
  //   }

  //   // get metaData object
  //   let metaObject = await this.metaDataRepository.findById(id);
  //   if (!metaObject) {
  //     logger.warn(
  //       `Edit metadata | ObjectMetaUpdaterService.getFieldsOfMetadata | N/A | no metadata item found for the id`,
  //     );
  //     return; //HttpErrors[422]
  //   }

  //   let isEditable: boolean;
  //   let isDeletable: boolean;
  //   let isFormValuesEditable: boolean;
  //   let isChildPropagationPromtRequired: boolean;

  //   // determine update requirement type
  //   if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType)) {
  //     // then propagation to children promt not required
  //     isChildPropagationPromtRequired = false;

  //     if (metaObject.isOriginalUploadFile) {
  //       // then, fields can be edited
  //       isEditable = true;
  //       isDeletable = true;
  //       isFormValuesEditable = true;
  //     } else {
  //       // then, fields cannot be edited
  //       isEditable = false;
  //       isDeletable = false;
  //       isFormValuesEditable = false;
  //     }
  //   } else {
  //     // then propagation to children promt is required
  //     isChildPropagationPromtRequired = true;
  //     // then, fields cannot be edited
  //     isEditable = false;
  //     isDeletable = false;
  //     isFormValuesEditable = false;
  //     // if (metaObject.isLogical) {
  //     //   // then propagation to children promt not required
  //     //   isChildPropagationPromtRequired = false;

  //     //   isEditable = true;
  //     //   isDeletable = true;
  //     //   isFormValuesEditable = true;
  //     // } else {
  //     //   // then propagation to children promt is required
  //     //   isChildPropagationPromtRequired = true;
  //     //   // then, fields cannot be edited
  //     //   isEditable = false;
  //     //   isDeletable = false;
  //     //   isFormValuesEditable = false;
  //     // }
  //   }

  //   // generate existing fields to be sent to the frontend form
  //   let exisitingFields = await this.formatMetaFieldsForEditForm(metaObject, teamId, isEditable, isDeletable);

  //   let responseObj = {
  //     // isEditable: isEditable,
  //     isFormValuesEditable: isFormValuesEditable,
  //     isChildPropagationPromtRequired: isChildPropagationPromtRequired,
  //     fields: exisitingFields,
  //   };
  //   return responseObj;
  // }

  /**
   * format metaData fields to frontend form
   * @param metaObject : MetaData
   * return formatted fields
   */
  // async formatMetaFieldsForEditForm(
  //   metaObject: MetaData,
  //   teamId?: string,
  //   isEditable?: boolean,
  //   isDeletable?: boolean,
  // ) {
  //   // get custom fields list
  //   let customFields = await this.metaDataRepository.filterCustomFieldsFromMetaData(metaObject.customMeta);

  //   // removed below code line since "Tags" field added to the model
  //   // // filter out tags
  //   // customFields = customFields.filter(field => field.key != "Tags")

  //   // get meta fields list
  //   let metaFieldList: metaFieldInput[] = metaObject.metaFieldList ? metaObject.metaFieldList : [];
  //   if ([ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER].includes(metaObject.objectType)) {
  //     let metaCollectionObj = await this.metaDataRepository.findOne(
  //       {
  //         where: {
  //           id: metaObject.collectionId,
  //         },
  //       },
  //       {
  //         fields: {
  //           metaFieldList: true,
  //         },
  //       },
  //     );

  //     if (metaCollectionObj && metaCollectionObj.metaFieldList) {
  //       metaFieldList = metaCollectionObj.metaFieldList;
  //     }
  //   }

  //   // get tags
  //   let tagObject = await this.generateInputFormTagField(metaObject, teamId);

  //   // format custom fields
  //   let formattedCustomFields = await this.inputMetadataFeedService.createInputFormMetaDataFieldsFromExisting(
  //     customFields,
  //     isEditable,
  //     isDeletable,
  //     metaFieldList,
  //   );

  //   let formattedFields: MetaDataKeyInputListFormat[] = [tagObject, ...formattedCustomFields];

  //   return formattedFields;
  // }

  /**
   * Sub method to get input form tag item
   */
  async generateInputFormTagField(metaObject: MetaData, teamId?: string) {
    let tagList = await this.getMetaDataTags(metaObject.id!, teamId);
    // console.log(tagList)

    let tagFormObject: MetaDataKeyInputListFormat = {
      fieldName: 'Add Tags',
      fieldKey: 'Tags',
      values: tagList?.values || [],
      suggestions: tagList?.suggestions || [],
      type: MetaDataKeyInputType.TAG,
      isTagDeletable: tagList?.isTagDeletable,
      isEditable: false,
      isDeletable: false, //?????????????????????????????????????????????????
    };

    return tagFormObject;
  }

  /**
   * validate and update metadata fields
   * @param updateInput update metadata input
   * @param currentUserProfile current user profile
   * @returns success or failure
   */
  async validateAndUpdateMetadataFields(
    updateInput: EditMetaDataInputFormat,
    currentUserProfile?: UserProfileDetailed,
  ) {
    if (!currentUserProfile) {
      logger.warn(`Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | N/A | invalid user profile`);
      throw new HttpErrors.NotAcceptable('invalid user profile');
    }

    // validate tags and create tags if not exist
    let tags: string[] = updateInput.tags ? updateInput.tags : [];

    if (tags && tags.length > 0) {
      for (let tag of tags) {
        await this.systemMetaService.validateAndCreateTag(tag, currentUserProfile);
      }
    }

    let id = updateInput.id;
    let teamId = currentUserProfile ? currentUserProfile.teamId : undefined;

    // let teamId: string | undefined = undefined
    // if (existingObject.teamId) teamId = existingObject.teamId as unknown as string
    if (!teamId) {
      logger.error(
        `Update - edit metadata | MetaFieldPropagatorService.handleUpdateMetadataOnEdit | N/A | couldn't find team for metadata id: ${id} `,
      );
      throw new HttpErrors.NotAcceptable('Team not exist for metadata');
    }

    // if (!id) {
    //   // logger.warn(`Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | N/A | invalid id`);
    //   // throw new HttpErrors.NotAcceptable('invalid id');

    //   // then the input is a selection
    //   // temporarily skipped validation -???????????????????????????????????????????????????????????????????????
    //   logger.debug(`Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | Selection flow | Validation skipped, saving meta updates`);
    //   this.handleUpdateMetadataFieldsV2(updateInput, teamId, currentUserProfile);
    //   return;
    // }
    // // determine object type
    // let existingObject = await this.metaDataRepository.findById(id);

    // if (!existingObject) {
    //   logger.warn(
    //     `Update - edit metadata | MetaFieldPropagatorService.handleUpdateMetadataOnEdit | N/A | meta data does not exist`,
    //   );
    //   return;
    // }

    // if (existingObject.isLogical) {
    //   updateInput.updateSelfOnly = true;
    // }

    // update metaFieldsList in metadata & validate updates with fieldConfigs
    // if (
    //   [
    //     ContentType.IMAGE_COLLECTION,
    //     ContentType.VIDEO_COLLECTION,
    //     ContentType.OTHER_COLLECTION,
    //     ContentType.DATASET,
    //   ].includes(existingObject.objectType)
    // ) {
    //   // only validate and update fieldConfig object using request payload if the object is a collection
    //   await this.metaFieldPropagatorService.validateMetaFieldsAndSaveFieldConfigs(existingObject, updateInput);

    // } else {
    //   // if object is not a collection, then take fieldConfig from the collection head
    //   let metaFieldList = await this.metaFieldPropagatorService.getMetaFieldsOfCollection(
    //     existingObject.collectionId?.toString(),
    //     existingObject.teamId?.toString(),
    //   );
    //   updateInput.fieldConfig = metaFieldList.metaFieldList;

    //   await this.metaFieldPropagatorService.validateMetaFieldsAndSaveFieldConfigs(existingObject, updateInput);
    // } // removed - now the validation is only done with system

    // // validate and update fieldConfig object using request payload if the object is a collection
    // await this.metaFieldPropagatorService.validateMetaFieldsAndSaveFieldConfigs(existingObject, updateInput); // ??????? check necessity of field config
    // // ---

    // // save custom fields and tags if they only applied on collection heads
    // if (updateInput.updateSelfOnly) {
    //   await this.saveCollectionHeadOnlyFields(
    //     updateInput.id,
    //     updateInput.updates,
    //     updateInput.deleteFields,
    //     updateInput.tags,
    //     updateInput.deleteTags,
    //   );
    // }

    try {
      // // update metadata
      // this.updateMetadataFields(updateInput, teamId, currentUserProfile);

      // new method to handle metadata update
      logger.debug(
        `Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | Item flow | Validated, Saving meta updates`,
      );
      this.handleUpdateMetadataFieldsV2(updateInput, currentUserProfile);
    } catch (err) {
      logger.error(
        `Update - edit metadata | MetaFieldPropagatorService.handleUpdateMetadataOnEdit | N/A | error updating metadata`,
      );
      logger.error(err);
    }
  }

  // /**
  //  * Use to save custom fields and tags if they only applied on collection heads
  //  * @param id
  //  * @param updates
  //  * @param deleteFields
  //  * @param tags
  //  * @param deleteTags
  //  */
  // async saveCollectionHeadOnlyFields(
  //   id: string,
  //   updates?: MetadataUpdatesInputType,
  //   deleteFields?: string[],
  //   tags?: string[],
  //   deleteTags?: string[],
  // ) {
  //   let metaHead = await this.metaDataRepository.findById(id);

  //   if (!deleteTags) deleteTags = [];
  //   let allTags = tags ? tags : [];

  //   // let prevHeadOnlyTags = metaHead.collectionHeadOnlyMeta ? metaHead.collectionHeadOnlyMeta.Tags : [];
  //   let prevAllTags = metaHead.Tags || [];

  //   let addedNewTags = allTags.filter(item => !prevAllTags.includes(item));

  //   // let newTags = []
  //   if (addedNewTags) {
  //     for (let tag of addedNewTags) {
  //       if (!prevHeadOnlyTags.includes(tag)) {
  //         prevHeadOnlyTags.push(tag);
  //       }
  //     }
  //   }

  //   for (let deleteTag of deleteTags) {
  //     if (prevHeadOnlyTags.includes(deleteTag)) {
  //       var index = prevHeadOnlyTags.indexOf(deleteTag);
  //       if (index !== -1) {
  //         prevHeadOnlyTags.splice(index, 1);
  //       }
  //     }
  //   }

  //   let prevHeadOnlyCustomMeta = metaHead.collectionHeadOnlyMeta ? metaHead.collectionHeadOnlyMeta.customMeta : {};
  //   let deleteCustomMeta = deleteFields ? deleteFields : [];
  //   let allCustomMeta = updates ? updates : {};
  //   let previousCustomMeta = metaHead.customMeta || {};
  //   let addedNewCustomMeta: any = {};
  //   for (let key in allCustomMeta) {
  //     if (!previousCustomMeta[key]) {
  //       addedNewCustomMeta[key] = allCustomMeta[key];
  //     }
  //   }

  //   Object.keys(addedNewCustomMeta).map(key => {
  //     if (!prevHeadOnlyCustomMeta[key]) {
  //       prevHeadOnlyCustomMeta[key] = addedNewCustomMeta[key];
  //     }
  //   });

  //   for (let deleteField of deleteCustomMeta) {
  //     if (prevHeadOnlyCustomMeta[deleteField]) {
  //       delete prevHeadOnlyCustomMeta[deleteField];
  //     }
  //   }

  //   let collectionHeadOnlyMeta: CollectionHeadOnlyMeta = {
  //     customMeta: prevHeadOnlyCustomMeta,
  //     Tags: prevHeadOnlyTags,
  //   };

  //   await this.metaDataRepository.updateById(id, {
  //     collectionHeadOnlyMeta: collectionHeadOnlyMeta,
  //   });
  // }

  /**
   * update metaData fields of a file or a collection
   * @param updateInput : EditMetaDataInputFormat
   * @param teamId teamId
   */
  // async updateMetadataFields(
  //   updateInput: EditMetaDataInputFormat,
  //   teamId?: string,
  //   currentUserProfile?: Partial<UserProfileDetailed>,
  // ) {
  //   // // handle deleting fields
  //   // if (updateInput.deleteFields && Array.isArray(updateInput.deleteFields) && updateInput.deleteFields.length > 0) {
  //   //   await this.metaFieldPropagatorService.handleMetadataFieldDelete(updateInput.id, updateInput.deleteFields)
  //   // }
  //   logger.info(
  //     `Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | ${currentUserProfile?.id} | meta propagation started, updateInput: `,
  //     updateInput,
  //   );

  //   let startTime = new Date().getTime();

  //   // handle add and edit meta fields
  //   if (updateInput.updates) {
  //     //transform all custom fields to type of string
  //     for (const [key, value] of Object.entries(updateInput.updates)) {
  //       // update query option for custom metadata (custome matadata wil be available in inputMetaDataFeedGroup.metaDataArray)
  //       if (!MetaData.definition.properties.hasOwnProperty(key)) {
  //         updateInput.updates[key] = value.toString();
  //       }
  //     }
  //     await this.metaFieldPropagatorService.handleUpdateMetadataOnEdit(
  //       updateInput.id,
  //       updateInput.updates,
  //       updateInput.deleteFields,
  //       updateInput.updateSelfOnly,
  //     );

  //     //set updatedAt of metadata and its parents after add new tag or tags
  //     this.metaDataService.updateUpdatedAt(updateInput.id);
  //   }

  //   // handle tags update (add and delete)
  //   await this.addMetaDataTags(
  //     updateInput.id,
  //     updateInput.tags || [],
  //     updateInput.deleteTags || [],
  //     updateInput.updateSelfOnly,
  //     teamId,
  //     currentUserProfile?.name,
  //   );

  //   let endTime = new Date().getTime();
  //   let processTime = endTime - startTime;

  //   logger.info(
  //     `Update meta fields | ObjectMetaUpdaterService.updateMetadataFields | ${currentUserProfile?.id} | meta propagation completed, processTime: ${processTime}, updateInput: `,
  //     updateInput,
  //   );

  //   return;
  // }

  /**
   * use to change the unique id of the annotation object
   * @param metaUpdates
   * @returns
   */
  async changeAnnotationShapeId(metaUpdates: Partial<MetaDataUpdate>) {
    if (metaUpdates.annotationObjects && Array.isArray(metaUpdates.annotationObjects)) {
      for (let anno of metaUpdates.annotationObjects) {
        anno.id = 'shape_' + uuidV4();
      }
    }

    return metaUpdates;
  }

  /**
   * use to add or update metadata of a collection by object keys json
   * @param metaUpdateJSON array of meta updates
   * @param teamId teamId
   * @param currentUserProfile current user profile
   */
  async handleAddOrUpdateMetaDataByObjectKeys(
    metaUpdateJSON: SDKMetaUpdateInput[],
    currentUserProfile: UserProfileDetailed,
    bucketName?: string,
    jobId?: string,
    collectionId?: string,
  ) {
    let isSomeUpdateFailed = false;
    let uploadId: ObjectId = undefined as unknown as ObjectId;
    let collectionStoragePath: string = '';
    let teamId = currentUserProfile.teamId;

    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let collectionObj = await this.metaDataRepository.findOne({
        where: {
          _id: new ObjectId(collectionId),
          teamId: new ObjectId(teamId),
          objectStatus: OBJECT_STATUS.ACTIVE,
        },
      });
      let allowedUserIdList = collectionObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    // if job id exist, get upload id from job object.
    // if collection id exist, get storage path from collection object
    if (jobId) {
      try {
        let jobObject = await this.jobRepository.findOne({
          where: {
            _id: new ObjectId(jobId),
            jobType: JobType.fileUpload,
            teamId: new ObjectId(teamId),
          },
        });

        if (jobObject && jobObject.jobSpecificDetails && jobObject.jobSpecificDetails.uploadId) {
          uploadId = jobObject.jobSpecificDetails.uploadId;
        } else {
          logger.error(
            `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | uploadId not exist`,
          );
          return {
            isSuccess: false,
            message: 'UploadId not exist. Please check the job id',
          };
        }
      } catch (err) {
        logger.error(
          `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | can not find job`,
        );
        return {
          isSuccess: false,
          message: 'Can not find job. Please check the job id',
        };
      }
    } else if (collectionId) {
      try {
        let collectionObj = await this.metaDataRepository.findOne({
          where: {
            _id: new ObjectId(collectionId),
            teamId: new ObjectId(teamId),
            objectStatus: OBJECT_STATUS.ACTIVE,
          },
          fields: {
            storagePath: true,
          },
        });

        if (!collectionObj || !collectionObj.storagePath) {
          logger.error(
            `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | collection not exist`,
          );
          return {
            isSuccess: false,
            message: 'Collection not exist. Please check the collection id',
          };
        }

        collectionStoragePath = collectionObj.storagePath;
      } catch (err) {
        logger.error(
          `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | collection not exist`,
        );
        return {
          isSuccess: false,
          message: 'Collection not exist. Please check the collection id',
        };
      }
    } else {
      /** Then ok */
    }

    // iterate through meta updates and add or update metadata for each file by object key
    for (let metaUpdate of metaUpdateJSON) {
      if (!metaUpdate.file) {
        logger.error(
          `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | file object key not exist`,
        );
        isSomeUpdateFailed = true;
        continue;
      }

      if (metaUpdate.metadata && Object.keys(metaUpdate.metadata).length > 0) {
        /** Then ok */
      } else {
        logger.warn(
          `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | Object key: ${metaUpdate.file} | metadata not exist`,
        );
        isSomeUpdateFailed = true;
        continue;
      }

      let matchingCriteria: Where<MetaData> = {
        objectStatus: OBJECT_STATUS.ACTIVE,
        teamId: new ObjectId(teamId),
      };

      // if job id exist, get upload id from job object and add to matching criteria
      // if bucket name exist, add bucket name and storage path to matching criteria (if bucket name is DEFAULT, then use default bucket name)
      // else add object key to matching criteria
      if (jobId) {
        matchingCriteria.fileUploadIdList = uploadId;
        matchingCriteria.name = metaUpdate.file;
      } else if (bucketName) {
        if (bucketName == 'DEFAULT') {
          bucketName = defaultBucketName;
        }
        matchingCriteria.bucketName = bucketName;
        matchingCriteria.storagePath = metaUpdate.file;
      } else if (collectionId) {
        matchingCriteria.objectKey = `${collectionStoragePath}_${metaUpdate.file}`;
      } else {
        matchingCriteria.objectKey = metaUpdate.file;
      }

      // get metadata object id from collection id and object key
      let metaObj = await this.metaDataRepository.findOne({
        where: matchingCriteria,
        fields: {
          id: true,
          objectType: true,
        },
      });

      if (!metaObj || !metaObj.id) {
        logger.error(
          `invalid meta object | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | ${teamId} | object not exist, objectKey: ${metaUpdate.file}`,
        );
        isSomeUpdateFailed = true;
        continue;
      }

      let formattedTags: string[] | undefined;
      if (metaUpdate && metaUpdate.metadata && metaUpdate.metadata.Tags) {
        let tags = metaUpdate.metadata.Tags;
        // if tags is string, convert to array
        if (Array.isArray(tags)) {
          formattedTags = tags;
        } else {
          formattedTags = [tags];
        }

        // delete tags from metaUpdate
        delete metaUpdate.metadata.Tags;
      }

      let updateSelfFlag = true;
      if (metaObj.objectType && metaObj.objectType == ContentType.VIDEO) updateSelfFlag = false; // if video, propagate metadata to frames

      let formattedMetaDataInput: EditMetadataAllFlowsInputFormat = {
        id: metaObj.id,
        updates: metaUpdate.metadata,
        tags: formattedTags,
        updateSelfOnly: updateSelfFlag,
      };

      try {
        await this.handleUpdateMetadataFieldsV2(formattedMetaDataInput, currentUserProfile);
      } catch (err) {
        logger.warn(
          `Update metadata by unique name | ObjectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys | file name: ${metaUpdate.file} | Meta update failed`,
        );
        isSomeUpdateFailed = true;
      }
    }

    let responseMessage = 'Metadata updated successfully';
    if (isSomeUpdateFailed) {
      responseMessage = 'Metadata updated. Some metadata updates may have failed';
    }

    return {
      isSuccess: true,
      message: responseMessage,
    };
  }

  /**
   * add or update metadata by meta object
   * @param collectionName collection name
   * @param metaObject meta object
   * @param isApplyToAllFiles is apply to all files
   * @param teamId team id
   */
  async addOrUpdateMetaDataByMetaObject(
    collectionName: string,
    objectType: ContentType,
    metaObject: Partial<MetaData>,
    isApplyToAllFiles: boolean,
    currentUserProfile: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile.teamId;
    if (!teamId) {
      throw new HttpErrors.NotAcceptable('TeamId is required');
    }

    let collectionObj = await this.metaDataRepository.findOne({
      where: {
        name: collectionName,
        objectType: objectType,
      },
      fields: {
        id: true,
        Tags: true,
        customMeta: true,
      },
    });

    if (!collectionObj || !collectionObj.id) {
      throw new HttpErrors.NotAcceptable(`Collection ${collectionName} not found`);
    }

    let metaUpdateObj: EditMetaDataInputFormat = {
      id: collectionObj.id.toString(),
      updates: {},
      deleteFields: [],
      tags: [],
      deleteTags: [],
      updateSelfOnly: false,
    };

    if (metaObject.Tags) {
      if (Array.isArray(metaObject.Tags)) {
        metaUpdateObj.tags = metaObject.Tags;
      } else {
        metaUpdateObj.tags = [metaObject.Tags];
      }

      delete metaObject.Tags;
    }

    // if (collectionObj.Tags) {
    //   if (metaUpdateObj.tags) {
    //     metaUpdateObj.tags.push(...collectionObj.Tags);
    //   }
    // }

    // if (collectionObj.customMeta && Object.keys(collectionObj.customMeta).length > 0) {
    //   // only filter newly added custom fields by comparing with existing collectionObj customMeta
    //   await this.filterNewlyAddedCustomFields(metaObject, collectionObj.customMeta);
    // }

    metaUpdateObj.updates = metaObject;

    if (!isApplyToAllFiles) {
      metaUpdateObj.updateSelfOnly = true;
    }

    console.log('metaUpdateObj', metaUpdateObj);

    // validate and update metadata fields
    await this.validateAndUpdateMetadataFields(metaUpdateObj, currentUserProfile);

    return {isSuccess: true, message: 'Metadata updated successfully'};
  }

  /**
   * use this method to filter newly added custom fields by comparing with existing customMeta
   * @param metaObject update meta object
   * @param customMeta existing custom meta object
   */
  async filterNewlyAddedCustomFields(metaObject: Partial<MetaData>, customMeta: Partial<MetaData>) {
    // if metaObject key is present in customMeta, then delete it
    for (let key in metaObject) {
      if (customMeta[key]) {
        delete metaObject[key];
      }
    }
  }

  /**
   * use to validate and create tags
   * @param tags tags
   * @param teamId team id
   */
  async sdkTagsValidate(tags: string[], teamId: string, currentUserProfile: UserProfileDetailed) {
    let failedTags: {
      newTag: string;
      oldTag: string;
    }[] = [];
    if (tags && tags.length > 0) {
      for (let tag of tags) {
        let tagDetails:
          | {
              newTag: string;
              oldTag: string;
            }
          | undefined = await this.systemMetaService.validateAndCreateTag(tag, currentUserProfile, true);

        if (tagDetails) {
          failedTags.push(tagDetails);
        }
      }
    }
    return failedTags;
  }

  /**
   * use to validate and create meta fields from sdk
   * @param tags tags
   * @param teamId team id
   */
  async sdkMetaFieldsValidate(fields: string[], teamId: string, currentUserProfile: UserProfileDetailed) {
    let failedFields: {oldField: string; newField: string}[] = [];

    if (fields && fields.length > 0) {
      for (let field of fields) {
        let fieldDetails = await this.metaFieldPropagatorService.validateAndCreateCustomMetaField(
          field,
          teamId,
          currentUserProfile.name || 'Unnamed',
        );

        if (fieldDetails) {
          failedFields.push(fieldDetails);
        }
      }
    }
    // if (failedFields.length > 0) {
    //   logger.debug(
    //     `sdk MetaFields Validate | ObjectMetaUpdaterService.sdkMetaFieldsValidate | N/A | failedFields: ${JSON.stringify(
    //       failedFields,
    //     )}`,
    //   );
    //   throw new HttpErrors.NotAcceptable(JSON.stringify(failedFields));
    // }
    // logger.debug(`sdk MetaFields Validate | ObjectMetaUpdaterService.sdkMetaFieldsValidate | N/A | all fields ok`);
    return failedFields;
  }

  // -----------------------------------------------------------------------------------------------------------------
  // flexible metadata with meta update search and selection implementation

  /**
   * Process metaDataUpdate input logics
   * @param metaUpdates
   * @param teamId
   * @param currentUserProfile
   */
  async handleUpdateMetadataFieldsV2(
    metaUpdates: EditMetadataAllFlowsInputFormat,
    currentUserProfile: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile.teamId;
    try {
      var matchQueryData = await this.generateMatchQueryToUpdateMetadata(metaUpdates, currentUserProfile);
    } catch (e) {
      logger.error(
        `Metadata update | ObjectMetaUpdaterService.handleUpdateMetadataFieldsV2 | N/A | Failed generateMatchQueryToUpdateMetadata`,
      );
      logger.error(e);
      return;
    }
    if (!matchQueryData) return;
    let matchQuery = matchQueryData.matchQuery;

    logger.debug(
      `Metadata update | ObjectMetaUpdaterService.handleUpdateMetadataFieldsV2 | N/A | matchQuery generated`,
    );
    logger.debug(`---------------------------------------------------------------`);
    logger.debug(JSON.stringify(matchQuery, null, 2));
    logger.debug(`---------------------------------------------------------------`);
    // logger.debug(JSON.stringify(metaUpdates, null, 2));
    // logger.debug(`---------------------------------------------------------------`)

    if (!Object.keys(matchQuery).length) {
      logger.error(
        `Metadata update | ObjectMetaUpdaterService.handleUpdateMetadataFieldsV2 | N/A | No match query generated`,
      );
      // throw new HttpErrors.UnprocessableEntity('No match query generated');
      return;
    }

    // Trigger update
    try {
      await this.flexibleMetadataSave(matchQuery, matchQueryData.isCollectionType, metaUpdates, teamId);
    } catch (e) {
      logger.error(
        `Metadata update | ObjectMetaUpdaterService.handleUpdateMetadataFieldsV2 | N/A | Failed flexibleMetadataSave`,
      );
      logger.error(e);
      return;
    }
  }

  /**
   * Generate mongodb match query for meta updates
   * @param metaUpdates
   * @returns
   */
  async generateMatchQueryToUpdateMetadata(
    metaUpdates: EditMetadataAllFlowsInputFormat,
    currentUserProfile: UserProfileDetailed,
  ) {
    let matchQuery: any = {};
    let isCollectionType = false;
    if (metaUpdates.id) {
      // then a file or a collection is selected
      let metaObjData = await this.metaDataRepository.findById(metaUpdates.id);
      if (!metaObjData) throw new HttpErrors.NotFound('Metadata not found');

      if ([ContentType.IMAGE, ContentType.OTHER].includes(metaObjData.objectType)) {
        // then a file is selected
        matchQuery = {_id: new ObjectId(metaUpdates.id)};
      } else if (
        [
          ContentType.IMAGE_COLLECTION,
          ContentType.VIDEO_COLLECTION,
          ContentType.OTHER_COLLECTION,
          ContentType.DATASET,
          ContentType.VIDEO,
        ].includes(metaObjData.objectType)
      ) {
        // then a collection is selected
        if (metaUpdates.updateSelfOnly) {
          // then only apply to selected
          matchQuery = {_id: new ObjectId(metaUpdates.id)};

          if (metaObjData.objectType != ContentType.VIDEO) {
            // to trigger query builder directly on collection(s) only
            isCollectionType = true;
          }
        } else {
          matchQuery = {
            $or: [
              {_id: new ObjectId(metaUpdates.id)},
              {vCollectionIdList: new ObjectId(metaUpdates.id)},
              {
                $and: [
                  {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}},
                  {parentList: new ObjectId(metaUpdates.id)},
                ],
              },
            ],
          };
        }
      }
    } else if (metaUpdates.selectionId) {
      // then a selection tag is given
      let matchQueryObj = await this.searchQueryBuilderService.getMatchQueryForSelection(
        currentUserProfile,
        metaUpdates.selectionId,
      );

      if (matchQueryObj) {
        if ([ContentType.IMAGE, ContentType.OTHER].includes(matchQueryObj.objectType)) {
          // then the query can be used directly
          matchQuery = matchQueryObj.matchQuery;
        } else if (
          [
            ContentType.IMAGE_COLLECTION,
            ContentType.VIDEO_COLLECTION,
            ContentType.OTHER_COLLECTION,
            ContentType.DATASET,
            ContentType.VIDEO,
          ].includes(matchQueryObj.objectType)
        ) {
          if (metaUpdates.updateSelfOnly) {
            // then the query can be used directly
            matchQuery = matchQueryObj.matchQuery;

            if (matchQueryObj.objectType != ContentType.VIDEO) {
              // to trigger query builder directly on collection(s) only
              isCollectionType = true;
            }
          } else {
            // need to update children of the collection

            // get the collection id list
            let collectionDataList = await this.metaDataRepository.aggregate([
              {$match: matchQueryObj.matchQuery},
              {$project: {collectionId: 1}},
            ]);
            let collectionOidList = collectionDataList.map((collectionData: any) => new ObjectId(collectionData._id));

            let matchOrQuery: any[] = [matchQueryObj.matchQuery];

            matchOrQuery.push({
              $and: [
                {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}},
                {$or: [{vCollectionIdList: {$in: collectionOidList}}, {parentList: {$in: collectionOidList}}]},
              ],
            });

            // if (matchQueryObj.objectType == ContentType.DATASET) {
            //   matchOrQuery.push({
            //     $and: [{objectType: ContentType.IMAGE}, {'datasetVersionList.datasetMetaId': {$in: collectionOidList}}],
            //   });
            // } else {
            //   matchOrQuery.push({
            //     $and: [
            //       {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO]}},
            //       {$or: [{vCollectionIdList: {$in: collectionOidList}}, {parentList: {$in: collectionOidList}}]},
            //     ],
            //   });
            // }
            matchQuery = {
              $or: matchOrQuery,
            };
          }
        }
      }
    } else {
      throw new HttpErrors.UnprocessableEntity('Either id or selectionId is required');
    }

    return {matchQuery: matchQuery, isCollectionType: isCollectionType};
  }

  async triggerQueryBuilderForMatchQuery(
    matchQuery?: any,
    isCollectionType?: boolean,
    metaUpdates?: EditMetadataAllFlowsInputFormat,
    teamId?: string,
  ) {
    let collectionIdList: string[] = [];
    if (matchQuery && Object.keys(matchQuery).length > 0 && metaUpdates) {
      // let collectionDataList: {_id: string}[] = [];

      if (isCollectionType) {
        // then the collections are given by the query
        let collectionDataList = await this.metaDataRepository.aggregate([
          {
            $match: {
              objectType: {
                $in: [
                  ContentType.IMAGE_COLLECTION,
                  ContentType.VIDEO_COLLECTION,
                  ContentType.OTHER_COLLECTION,
                  ContentType.DATASET,
                ],
              },
            },
          },
          {$match: matchQuery},
          {$project: {_id: 1}},
        ]);
        if (collectionDataList)
          collectionIdList = collectionDataList.map((collectionData: any) => collectionData._id.toString());
      } else {
        // then should find the collection where query items belong
        // let collectionDataList = await this.metaDataRepository.aggregate([
        //   {$match: matchQuery},
        //   {$unwind: "$vCollectionIdList"},
        //   {$group: {_id: '$vCollectionIdList'}} //distinct?
        // ]);
        let matchQueryFormatted = {
          $and: [{objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}}, matchQuery],
        };
        let distinctCollectionList = await this.metaDataRepository.distinct('vCollectionIdList', matchQueryFormatted);
        if (distinctCollectionList)
          collectionIdList = distinctCollectionList.map((collectionId: any) => collectionId.toString());
      }

      if (collectionIdList && collectionIdList.length > 0) {
        /** Then ok */
      } else {
        logger.warn(
          `Build query for metadata update | ObjectMetaUpdaterService.triggerQueryBuilderForMatchQuery | N/A | No collections found`,
        );
        return [];
      }
      logger.debug(
        `Build query for metadata update | ObjectMetaUpdaterService.triggerQueryBuilderForMatchQuery | N/A | triggering query builder`,
      );
      logger.debug(`---------------------------------------------------------------`);
      logger.debug(JSON.stringify(collectionIdList));

      if (teamId) {
        // trigger Query builder
        // filter out system meta data if present (only get custom metadata)
        let customFieldArr: {key: string; value?: any}[] = this.metaDataRepository.filterCustomFieldsFromMetaData(
          metaUpdates.updates,
        );

        // append deleted fields to query builder triggerer
        if (metaUpdates.deleteFields && metaUpdates.deleteFields.length > 0) {
          let deleteFieldArray = metaUpdates.deleteFields.map((field: string) => {
            return {key: field};
          });
          customFieldArr.push(...deleteFieldArray);
        }

        this.triggerQueryBuilderForCollections(collectionIdList, customFieldArr, teamId);

        // for (let collectionId of collectionIdList) {
        //   logger.debug(`---- triggering query builder for collection: ${collectionId} ----`);
        //   let collection = collectionId.toString();
        //   // trigger query builder for fields
        //   for (let customField of customFieldArr) {
        //     let distinctValues = await this.metaDataRepository.getAllDistinctValuesForCustomFieldInCollection(
        //       collection,
        //       customField.key,
        //     );
        //     this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
        //       SearchQueryRootGroup.METADATA,
        //       customField.key,
        //       teamId,
        //       collection,
        //       distinctValues,
        //     );
        //   }

        //   // trigger query builder for tags
        //   this.queryOptionRepository.rebuildCollectionQueryOptionForTags(collection);
        // }
      }
    }
    return collectionIdList;
  }

  /**
   * Trigger query building for affected collections
   * @param collectionIdList {string[]} affected collection id list
   * @param customFieldArr {{key: string; value?: any}[]} custom field array
   * @param teamId {string} team id
   * @returns void
   */
  async triggerQueryBuilderForCollections(
    collectionIdList: string[],
    customFieldArr: {key: string; value?: any}[],
    teamId: string,
  ): Promise<void> {
    for (let collectionId of collectionIdList) {
      try {
        logger.debug(`---- triggering query builder for collection: ${collectionId} ----`);
        let collection = collectionId.toString();
        // trigger query builder for fields
        for (let customField of customFieldArr) {
          let distinctValues = await this.metaDataRepository.getAllDistinctValuesForCustomFieldInCollection(
            collection,
            customField.key,
          );
          await this.queryOptionRepository.rebuildCollectionQueryOptionForGivenMetadataField(
            SearchQueryRootGroup.METADATA,
            customField.key,
            teamId,
            collection,
            distinctValues,
          );
        }

        // trigger query builder for tags
        await this.queryOptionRepository.rebuildCollectionQueryOptionForTags(collection);
      } catch (err) {
        logger.error(
          `trigger query builder for collection | ObjectMetaUpdaterService.triggerQueryBuilderForCollections | collection id: ${collectionId} |error  `,
          err,
        );
      }
    }
  }
  /**
   * Update custom metadata and tags for a given query
   * Trigger query builder for metadata
   * @param dbQuery
   * @param metaUpdates
   */
  async flexibleMetadataSave(
    dbQuery: any,
    isCollectionType: boolean,
    metaUpdates: EditMetadataAllFlowsInputFormat,
    teamId?: string,
  ) {
    // handle tags
    await this.updateTagsForQuery(dbQuery, metaUpdates.tags, metaUpdates.deleteTags);

    //handle custom metadata
    await this.updateCustomMetaFieldsForQuery(dbQuery, metaUpdates.updates, metaUpdates.deleteFields);

    // trigger query builder
    // let affectedCollectionIdList = await this.triggerQueryBuilderForMatchQuery(dbQuery, metaUpdates, teamId);

    //Update collection isMetaFieldsPropagationRequired flag and do the calculation
    this.metadataSaveCollectionUpdate(dbQuery, isCollectionType, metaUpdates, teamId);
  }

  /**
   * Set isMetaFieldsPropagationRequired true in affected collections and calculate meta fields and tags summary
   * @param {any} dbQuery {any} db query
   * @param {boolean} isCollectionType {boolean} is collection type
   * @param {EditMetadataAllFlowsInputFormat} metaUpdates {EditMetadataAllFlowsInputFormat} meta updates bdy (eg: updates, deleteFields, tags, deleteTags)
   * @param {string} teamId {string} team id
   * @returns void
   */
  async metadataSaveCollectionUpdate(
    dbQuery: any,
    isCollectionType: boolean,
    metaUpdates: EditMetadataAllFlowsInputFormat,
    teamId?: string,
  ): Promise<void> {
    //Find affected collection id list
    let affectedCollectionIdList: string[] = await this.triggerQueryBuilderForMatchQuery(
      dbQuery,
      isCollectionType,
      metaUpdates,
      teamId,
    );

    //convert collection id list to object id list
    let collectionOidList = affectedCollectionIdList.map((collectionId: string) => new ObjectId(collectionId));
    let collectionQuery = {_id: {$in: collectionOidList}};

    //Set isMetaFieldsPropagationRequired true in affected collections
    await this.metaDataRepository.updateManySet(
      collectionQuery,
      {updatedAt: new Date(), isMetaFieldsPropagationRequired: true, 'metDataUpdateInfo.metaUpdateAt': new Date()},
      [],
    );

    let calculateSystemStats: boolean = false;

    //calculate meta fields and tags summary for affected collections
    for (let collectionId of affectedCollectionIdList) {
      /**
       * calculate meta fields and tags summary for affected collections and return whether skip the operation or not
       */
      let {skipOperation} = await this.statsCalculationService.calculateMetaFieldsAndTagsSummary(collectionId);
      if (!skipOperation) calculateSystemStats = true;
    }

    //calculate system stats, if one of affection collection is updated meta fields and tags summary
    if (calculateSystemStats) await this.statsCalculationService.handlePropagateMetaFieldsAndTagsStatsToSystem();
  }

  /**
   * Add, edit or delete tags for a given query
   * @param dbQuery
   * @param tagUpdates
   * @param tagDeletes
   */
  async updateTagsForQuery(dbQuery: any, tagUpdates?: string[], tagDeletes?: string[]) {
    logger.debug(`updateTagsForQuery | ObjectMetaUpdaterService.updateTagsForQuery | N/A | Updating tags`);
    let isUpdated = false;

    // filter out empty tags
    if (tagUpdates && tagUpdates.length > 0) {
      tagUpdates = tagUpdates.filter(tag => {
        try {
          if (tag && tag.trim() != '') return true;
          return false;
        } catch (e) {
          return false;
        }
      });
    }

    // update db if newly added
    if (tagUpdates && tagUpdates.length > 0) {
      let updateQuery = {Tags: {$each: tagUpdates}};
      await this.metaDataRepository.updateManyAddToSetToList(dbQuery, updateQuery, []);
      isUpdated = true;
    }

    // pull Tags if deleted
    if (tagDeletes && tagDeletes.length > 0) {
      let deleteQuery = {Tags: {$in: tagDeletes}};
      await this.metaDataRepository.updateManyRemoveFromList(dbQuery, deleteQuery, []);
      isUpdated = true;
    }

    if (isUpdated) {
      // Update last updated at
      this.metaDataRepository.updateManySet(
        dbQuery,
        {updatedAt: new Date(), isMetaFieldsPropagationRequired: true},
        [],
      );
    }
  }

  /**
   * Add, edit or delete custom metadata for a given query
   * @param dbQuery
   * @param customMetaUpdates
   * @param customMetaDeletes
   */
  async updateCustomMetaFieldsForQuery(
    dbQuery: any,
    customMetaUpdates?: Record<string, any>,
    customMetaDeletes?: string[],
  ) {
    logger.debug(
      `updateCustomMetaFieldsForQuery | ObjectMetaUpdaterService.updateCustomMetaFieldsForQuery | N/A | Updating custom metadata`,
    );
    let isUpdated = false;

    // Update metadata
    if (customMetaUpdates) {
      let customMetaUpdateObj: any = {};
      for (let key in customMetaUpdates) {
        if (customMetaUpdates[key] === undefined || customMetaUpdates[key] === '') continue;
        customMetaUpdateObj[`customMeta.${key}`] = customMetaUpdates[key];
      }
      await this.metaDataRepository.updateManySet(dbQuery, customMetaUpdateObj, []);
      isUpdated = true;
    }

    // delete metadata if deleted
    if (customMetaDeletes && customMetaDeletes.length > 0) {
      let unsetQuery: any = {};
      for (let key of customMetaDeletes) {
        unsetQuery[`customMeta.${key}`] = '';
      }
      await this.metaDataRepository.updateManyUnSet(dbQuery, unsetQuery, []);
      isUpdated = true;
    }

    if (isUpdated) {
      // Update last updated at
      this.metaDataRepository.updateManySet(
        dbQuery,
        {updatedAt: new Date(), isMetaFieldsPropagationRequired: true},
        [],
      );
    }
  }

  //---------------------------------------------------------------------------------
}

export const OBJECT_META_UPDATER_SERVICE = BindingKey.create<ObjectMetaUpdaterService>('service.objectMetaUpdater');

export interface BulkUpdateOneMetaDataFormat {
  updateOne: {
    filter: {_id?: string | ObjectId} | {objectKey?: string};
    update: {
      $set?: Partial<MetaData>;
      $addToSet?: AddToSetMetaDataType;
    };
    upsert?: boolean;
  };
}

export type AddToSetMetaDataType = {
  [FieldName in keyof Partial<MetaData>]: {$each: MetaData[FieldName][]};
};

export interface BulkUpdateOneInputMetaDataFeedFormat {
  updateOne: {
    filter: {_id?: string};
    update: {$set: Partial<InputMetaDataFeed>};
  };
}

export interface BulkInsertOneMetaDataFormat {
  insertOne?: Partial<MetaData>;
}

export interface APIKeyListItem {
  apiKey: string;
  _id: ObjectId;
}

export interface BulkUpdateOneApiKeyFormat {
  updateOne: {
    filter: {key?: string};
    update: {$set: Partial<ApiKey>};
  };
}
