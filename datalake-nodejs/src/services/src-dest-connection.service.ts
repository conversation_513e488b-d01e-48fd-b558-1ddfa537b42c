/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * SrcDestConnectionService is the class use for transfer data from SrcDest
 */

/**
 * @class SrcDestConnectionService
 * purpose of SrcDestConnectionService service to transfer data from  source to destination with using Airbyte
 * @description SrcDestConnectionService is the class use for transfer data from source to destination with using Airbyte
 * <AUTHOR>
 */

import {bind, BindingKey, /* inject, */ BindingScope, inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import Axios from 'axios';
import dotenv from 'dotenv';
import mysql from 'mysql2/promise';
import {v4 as uuidV4} from 'uuid';
import {AdapterFactory} from '../adapters/adapter.factory';
import {DataSourceAdapter} from '../adapters/interfaces/data-source-adapter';
import {MongoDBAdapter} from '../adapters/mongodb-adapter';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {CrawlingStatus} from '../models';
import {
  AirbyteConnectionCredentials,
  ConnectionJob,
  ConnectionListAndTypeRes,
  DataSourceConnectionStatus,
  DataSourceConnectionType,
  DataStructureCrawlStatus,
  DestinationSyncMode,
  SourceSchema,
  Stream,
  SyncJobStatus,
  SyncMode,
} from '../models/connection.model';
import {
  AirbyteConnectionDestinationCredentials,
  ConnectionDestinationCredentials,
  ConnectionDestinationType,
  DestinationDefinitionIds,
} from '../models/destination.model';
import {
  AirbyteConnectionSourceCredentials,
  ConnectionSourceConfiguration,
  ConnectionSourceCredentials,
  ConnectionSourceType,
  SelectionConfiguration,
  SourceDefinitionIds,
} from '../models/source.model';
import {DataCrawlRepository, SystemDataRepository} from '../repositories';
import {ConnectionHistoryRepository} from '../repositories/connection-history.repository';
import {ConnectionRepository} from '../repositories/connection.repository';
import {DestinationRepository} from '../repositories/destination.repository';
import {DictionaryDataRepository} from '../repositories/dictionary-data.repository';
import {SourceRepository} from '../repositories/source.repository';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {timeAgo} from '../settings/tools';
import {TableDataService} from './table-data.service';
dotenv.config();
const AIRBYTE_URL = process.env.AIRBYTE_URL;
const AIRBYTE_USER_NAME = process.env.AIRBYTE_USER_NAME;
const AIRBYTE_PASSWORD = process.env.AIRBYTE_PASSWORD;
const CONNECTION_DB_HOST = process.env.CONNECTION_DB_HOST;
const CONNECTION_DB_HOST_AIRBYTE = process.env.CONNECTION_DB_HOST_AIRBYTE;
const CONNECTION_DB_PORT = process.env.CONNECTION_DB_PORT;
const CONNECTION_MONGODB_ADMIN_PASSWORD = process.env.CONNECTION_MONGODB_ADMIN_PASSWORD;
const PYTHON_HOST = process.env.PYTHON_BASE_URL;
const CONNECTION_MYSQL_DB_HOST = process.env.CONNECTION_MYSQL_DB_HOST;
const CONNECTION_MYSQL_DB_HOST_AIRBYTE = process.env.CONNECTION_MYSQL_DB_HOST_AIRBYTE;
const CONNECTION_MYSQL_DB_PORT = process.env.CONNECTION_MYSQL_DB_PORT;
const MYSQL_ROOT_PASSWORD = process.env.MYSQL_ROOT_PASSWORD;
const WORK_SPACE_ID = process.env.WORK_SPACE_ID;

@bind({scope: BindingScope.TRANSIENT})
export class SrcDestConnectionService {
  private adapters: Map<string, DataSourceAdapter> = new Map();
  constructor(
    @repository(SourceRepository)
    private sourceRepository: SourceRepository,
    @repository(DestinationRepository)
    private destinationRepository: DestinationRepository,
    @repository(ConnectionRepository)
    private connectionRepository: ConnectionRepository,
    @repository(ConnectionHistoryRepository)
    private connectionHistoryRepository: ConnectionHistoryRepository,
    @repository(DictionaryDataRepository)
    private dictionaryDataRepository: DictionaryDataRepository,
    @inject('adapters.AdapterFactory')
    private adapterFactory: AdapterFactory,
    @repository('DataCrawlRepository')
    private dataCrawlRepository: DataCrawlRepository,
    @repository(SystemDataRepository)
    public systemDataRepository: SystemDataRepository,
    @inject('services.TableDataService')
    private tableDataService: TableDataService,
    // Key-value mapping where key is the mapped name and value is the actual table or column name
    private tableFieldMapping = new Map<string, string>(),
    private isTableFieldMappingInitialized = false,
  ) {}

  /**
   * Initialize field mapping for a given data source connection ID.
   * To be called before any database query execution.
   * @param connectionId The connection ID for which to initialize the column mapping.
   *
   */
  private async initializeFieldMapping(connectionId: string): Promise<void> {
    try {
      this.tableFieldMapping = await this.tableDataService.getTableAndFieldMapping(connectionId);
    } catch (error) {
      logger.error(
        `Run database query execute | DataSourceService.retrieveData | N/A | Failed to initialize field mapping: ${error.message}`,
      );
    }
  }

  /**
   * Use to create connection between source and destination in Airbyte
   * @param name {string} name of the connection
   * @param type {ConnectionSourceType} type of the connection
   * @param credentials {ConnectionSourceCredentials} credentials of the connection
   * @param selectionConfiguration {SelectionConfiguration} selection configuration (selected fields) of the connection
   * @returns {Promise<AirbyteConnectionCredentials>}
   */
  async createConnection(
    name: string,
    type: ConnectionSourceType,
    credentials: ConnectionSourceCredentials,
    selectionConfiguration: SelectionConfiguration,
    currentUserProfile?: UserProfileDetailed,
  ) {
    logger.debug(
      `Create airbyte source | SrcDestConnectionService.createSource | N/A | name: ${name}, type: ${type}, credentials: ${credentials}, selectionConfiguration: ${selectionConfiguration}`,
    );

    // validate the name
    await this.validateName(name, type);

    // convert port to number
    if (credentials.port) {
      credentials.port = Number(credentials.port);
    }

    switch (type) {
      case ConnectionSourceType.MONGO_DB:
        // return await this.createDatabaseConnection(name, type, credentials, selectionConfiguration, currentUserProfile);
        return await this.createAirByteConnection(name, type, credentials, selectionConfiguration, currentUserProfile);
      case ConnectionSourceType.QUICK_BOOK:
        return await this.createAirByteConnection(name, type, credentials, selectionConfiguration, currentUserProfile);
      case ConnectionSourceType.MYSQL_DB:
        return await this.createAirByteConnection(name, type, credentials, selectionConfiguration, currentUserProfile);
      case ConnectionSourceType.MSSQL_DB:
        return await this.createAirByteConnection(name, type, credentials, selectionConfiguration, currentUserProfile);
      default:
        break;
    }
  }

  /**
   *
   * @param name
   * @param type
   * @returns
   */
  async getConnectDetailsAirbyteDestination(name: string, type: ConnectionSourceType, connectionCredentials?: any) {
    logger.debug(
      `Connect Airbyte Destination | SrcDestConnectionService.connectAirbyteDestination | N/A | type: ${type}`,
    );

    let destType = ConnectionDestinationType.MONGO_DB;
    let dbName = name;
    let tempDbName;
    let createDestinationPayload: AirbyteConnectionDestinationCredentials;
    let destinationCredentials;
    let password: string;
    switch (type) {
      case ConnectionSourceType.QUICK_BOOK:
        destType = ConnectionDestinationType.MONGO_DB;
        // validate database name and convert it to valid name
        tempDbName = await this.getValidDatabaseNames(name, destType);
        if (tempDbName) {
          dbName = tempDbName;
        }
        // create destination credentails by using .env variables
        destinationCredentials = {
          host: CONNECTION_DB_HOST_AIRBYTE,
          port: Number(CONNECTION_DB_PORT),
          username: 'admin',
          password: CONNECTION_MONGODB_ADMIN_PASSWORD,
          database: dbName,
        };

        connectionCredentials.host = CONNECTION_DB_HOST;
        connectionCredentials.port = Number(CONNECTION_DB_PORT);
        connectionCredentials.username = `admin`;
        connectionCredentials.password = CONNECTION_MONGODB_ADMIN_PASSWORD;
        connectionCredentials.database = dbName;

        // create destination creating and connection check payload
        createDestinationPayload = await this.getCreateDestinationPayload(
          name,
          ConnectionDestinationType.MONGO_DB,
          destinationCredentials,
        );
        return createDestinationPayload;
      case ConnectionSourceType.MONGO_DB:
        destType = ConnectionDestinationType.MONGO_DB;
        // validate database name and convert it to valid name
        tempDbName = await this.getValidDatabaseNames(name, destType);
        if (tempDbName) {
          dbName = tempDbName;
        }
        // create destination credentails by using .env variables
        destinationCredentials = {
          host: CONNECTION_DB_HOST_AIRBYTE,
          port: Number(CONNECTION_DB_PORT),
          username: 'admin',
          password: CONNECTION_MONGODB_ADMIN_PASSWORD,
          database: dbName,
        };

        connectionCredentials.host = CONNECTION_DB_HOST;
        connectionCredentials.port = Number(CONNECTION_DB_PORT);
        connectionCredentials.username = `admin`;
        connectionCredentials.password = CONNECTION_MONGODB_ADMIN_PASSWORD;
        connectionCredentials.database = dbName;

        // create destination creating and connection check payload
        createDestinationPayload = await this.getCreateDestinationPayload(
          name,
          ConnectionDestinationType.MONGO_DB,
          destinationCredentials,
        );
        return createDestinationPayload;
      case ConnectionSourceType.MYSQL_DB:
        destType = ConnectionDestinationType.MYSQL_DB;

        password = uuidV4().replace(/-/g, '').slice(0, 10);

        tempDbName = await this.getValidDatabaseNames(name, destType, password);
        if (tempDbName) {
          dbName = tempDbName;
        } else {
          throw new HttpErrors.NotAcceptable(`Already in use database name - ${name}`);
        }

        // create destination credentials by using .env variables
        destinationCredentials = {
          host: CONNECTION_MYSQL_DB_HOST_AIRBYTE,
          port: Number(CONNECTION_MYSQL_DB_PORT),
          username: 'root',
          password: MYSQL_ROOT_PASSWORD,
          database: dbName,
        };

        // create destination creating and connection check payload
        createDestinationPayload = await this.getCreateDestinationPayload(name, destType, destinationCredentials);

        connectionCredentials.host = CONNECTION_MYSQL_DB_HOST;
        connectionCredentials.port = Number(CONNECTION_MYSQL_DB_PORT);
        connectionCredentials.username = `${dbName}_user`;
        connectionCredentials.password = password;
        connectionCredentials.database = dbName;
        createDestinationPayload.connectionCredentials = connectionCredentials;

        return createDestinationPayload;
      case ConnectionSourceType.MSSQL_DB:
        destType = ConnectionDestinationType.MYSQL_DB;

        password = uuidV4().replace(/-/g, '').slice(0, 10);

        tempDbName = await this.getValidDatabaseNames(name, destType, password);
        if (tempDbName) {
          dbName = tempDbName;
        } else {
          throw new HttpErrors.NotAcceptable(`Already in use database name - ${name}`);
        }

        // create destination credentials by using .env variables
        destinationCredentials = {
          host: CONNECTION_MYSQL_DB_HOST_AIRBYTE,
          port: Number(CONNECTION_MYSQL_DB_PORT),
          username: 'root',
          password: MYSQL_ROOT_PASSWORD,
          database: dbName,
        };

        // create destination creating and connection check payload
        createDestinationPayload = await this.getCreateDestinationPayload(name, destType, destinationCredentials);

        connectionCredentials.host = CONNECTION_MYSQL_DB_HOST;
        connectionCredentials.port = Number(CONNECTION_MYSQL_DB_PORT);
        connectionCredentials.username = `${dbName}_user`;
        connectionCredentials.password = password;
        connectionCredentials.database = dbName;
        createDestinationPayload.connectionCredentials = connectionCredentials;

        return createDestinationPayload;

      default:
        break;
    }
  }

  /**
   * Use to create connection between source and destination in Airbyte
   * @param name {string} name of the connection
   * @param type {ConnectionSourceType} type of the connection
   * @param credentials {ConnectionSourceCredentials} credentials of the connection
   * @param selectionConfiguration {SelectionConfiguration} selection configuration (selected fields) of the connection
   * @param currentUserProfile {UserProfileDetailed} authentication details
   * @returns {Promise<AirbyteConnectionCredentials>}
   */
  async createAirByteConnection(
    name: string,
    type: ConnectionSourceType,
    credentials: ConnectionSourceCredentials,
    selectionConfiguration: SelectionConfiguration,
    currentUserProfile?: UserProfileDetailed,
  ) {
    // create source creating and connection check payload
    let createSourcePayload = await this.getCreateSourcePayload(name, type, credentials);

    // check source connection before creating source
    await this.checkSourceConnection(createSourcePayload);

    let connectionCredentials: any = {};

    let createDestinationPayload = await this.getConnectDetailsAirbyteDestination(name, type, connectionCredentials);

    if (!createDestinationPayload) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DESTINATION_CONNECTION_ERROR);
    }

    // check destination connection before creating source
    await this.checkDestinationConnection(createDestinationPayload);

    // create airbyte source
    let sourceCreateResponse = await this.createAirbyteSource(createSourcePayload);

    // create airbyte destination
    let destinationCreateResponse = await this.createAirbyteDestination(createDestinationPayload);

    // create connection payload
    let sourceId = sourceCreateResponse.sourceId;
    let destinationId = destinationCreateResponse.destinationId;
    let createConnectionPayload = await this.getCreateConnectionPayload(
      name,
      type,
      sourceId,
      destinationId,
      selectionConfiguration,
    );
    // logger.debug(JSON.stringify(createConnectionPayload));
    // create connection
    let connectionCreateResponse = await this.createAirbyteConnection(createConnectionPayload);

    // create source in datalake database
    delete sourceCreateResponse.icon;
    sourceCreateResponse.type = type;
    sourceCreateResponse.createdAt = new Date();
    sourceCreateResponse.connectionConfiguration = createSourcePayload.connectionConfiguration;
    sourceCreateResponse.connectionId = connectionCreateResponse.connectionId;
    sourceCreateResponse.destinationId = destinationCreateResponse.destinationId;
    sourceCreateResponse.createdBy = currentUserProfile?.name;
    sourceCreateResponse.teamId = currentUserProfile?.teamId;
    await this.sourceRepository.create(sourceCreateResponse);

    // create destination in datalake database
    delete destinationCreateResponse.icon;
    destinationCreateResponse.type = type;
    destinationCreateResponse.createdAt = new Date();
    destinationCreateResponse.connectionConfiguration = createDestinationPayload.connectionConfiguration;
    destinationCreateResponse.createdBy = currentUserProfile?.name;
    destinationCreateResponse.teamId = currentUserProfile?.teamId;
    await this.destinationRepository.create(destinationCreateResponse);

    // create connection in datalake database
    delete connectionCreateResponse.source;
    delete connectionCreateResponse.destination;
    // delete connectionCreateResponse.syncCatalog;
    delete connectionCreateResponse.connectionConfiguration;
    connectionCreateResponse.createdAt = new Date();
    connectionCreateResponse.sourceName = name;
    connectionCreateResponse.type = type;
    connectionCreateResponse.createdBy = currentUserProfile?.name;
    connectionCreateResponse.teamId = currentUserProfile?.teamId;
    connectionCreateResponse.connectionCredentials = connectionCredentials;
    await this.connectionRepository.create(connectionCreateResponse);

    await this.syncConnectionJobList(); // calculate connection history
    await this.syncConnectionStats(); // calculate connection stats
    await this.syncConnectionSystemStats(); // update system stats about connection

    //return connection create response;
    return connectionCreateResponse;
  }

  /**
   * Use to create connection between source and destination in database
   * @param name {string} name of the connection
   * @param type {ConnectionSourceType} type of the connection
   * @param credentials {ConnectionSourceCredentials} credentials of the connection
   * @param selectionConfiguration {SelectionConfiguration} selection configuration (selected fields) of the connection
   * @param currentUserProfile {UserProfileDetailed} authentication details
   * @returns {Promise<AirbyteConnectionCredentials>}
   */
  async createDatabaseConnection(
    name: string,
    type: ConnectionSourceType,
    credentials: ConnectionSourceCredentials,
    selectionConfiguration: SelectionConfiguration,
    currentUserProfile?: UserProfileDetailed,
  ) {
    if (!credentials || !credentials.host || !credentials.port) {
      logger.error(
        `Add new data source connection | SrcDestConnectionService.createDatabaseConnection | ${currentUserProfile?.teamId} | Invalid configuration object. Config: `,
        credentials,
      );
      throw new HttpErrors.NotAcceptable(
        `${DatalakeUserMessages.MISSING_REQUIRED_FIELDS} check connectionName, host, port`,
      );
    }

    const adapter = await this.adapterFactory.getAdapter(type, credentials);

    return await this.addDataSource(name, type, adapter, credentials, currentUserProfile);
  }

  // Register an adapter for a data source
  registerAdapter(name: string, adapter: DataSourceAdapter) {
    this.adapters.set(name, adapter);
  }

  /**
   * Use to create connection between source and destination register adapter and generate and save schema
   * @param name {string} name of the connection
   * @param type {ConnectionSourceType} type of the connection
   * @param adapter {DataSourceAdapter} adapter of the connection
   * @param credentials {ConnectionSourceCredentials} credentials of the connection
   * @param currentUserProfile {UserProfileDetailed} authentication details
   * @returns {Promise<AirbyteConnectionCredentials>}
   */
  async addDataSource(
    name: string,
    type: ConnectionSourceType,
    adapter: DataSourceAdapter,
    credentials: ConnectionSourceCredentials,
    currentUserProfile?: UserProfileDetailed,
  ) {
    try {
      this.registerAdapter(name, adapter);
    } catch (err) {
      logger.error(
        `Add new data source connection | SrcDestConnectionService.addDataSource | N/A | Error while register client to system. Error: `,
        err,
      );

      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATA_SOURCE_REGISTRATION_FAILED);
    }
    logger.info(
      `Add new data source connection | SrcDestConnectionService.addDataSource | N/A | Data source successfully registered to system`,
    );

    let connectionSourceConfiguration: ConnectionSourceConfiguration = {
      credentials: {
        host: credentials.host,
        port: credentials.port,
        username: credentials.username ? credentials.username : '',
        password: credentials.password ? credentials.password : '',
        database: credentials.database ? credentials.database : '',
      },
    };
    return await this.generateAndSaveSchema(name, type, connectionSourceConfiguration, currentUserProfile);
  }

  /**
   * Use to generate and save schema
   * @param name {string} name of the connection
   * @param sourceType {ConnectionSourceType} type of the connection
   * @param connectionSourceConfiguration {ConnectionSourceConfiguration} credentials of the connection
   * @param currentUserProfile {UserProfileDetailed} authentication details
   * @returns {Promise<AirbyteConnectionCredentials>}
   */
  async generateAndSaveSchema(
    name: string,
    sourceType: ConnectionSourceType,
    connectionSourceConfiguration: ConnectionSourceConfiguration,
    currentUserProfile?: UserProfileDetailed,
  ) {
    try {
      let sourceId = `${uuidV4()}`;
      let connectionId = `${uuidV4()}`;

      await this.sourceRepository.create({
        name: name,
        type: sourceType,
        createdAt: new Date(),
        createdBy: currentUserProfile?.name,
        teamId: currentUserProfile?.teamId,
        sourceDefinitionId: SourceDefinitionIds.sourceDefinitionIdMongoDB,
        sourceId: sourceId,
        connectionId: connectionId,
        connectionConfiguration: connectionSourceConfiguration,
      });

      let connection = await this.connectionRepository.create({
        name: name,
        sourceName: name,
        type: sourceType,
        createdAt: new Date(),
        createdBy: currentUserProfile?.name,
        teamId: currentUserProfile?.teamId,
        connectionId: connectionId,
        sourceId: sourceId,
        status: 'active',
        connectionStatus: DataSourceConnectionStatus.CONNECTED,
        connectionCredentials: {
          host: connectionSourceConfiguration?.credentials?.host,
          port: connectionSourceConfiguration?.credentials?.port,
          username: connectionSourceConfiguration?.credentials?.username,
          password: connectionSourceConfiguration?.credentials?.password,
          database: connectionSourceConfiguration?.credentials?.database,
        },
      });

      let url = `${PYTHON_HOST}/internal/connection/generate/schema`;
      logger.info(
        `Add new data source connection | SrcDestConnectionService.addDataSource | N/A | Request sent to generate schema, document and data dictionary`,
      );
      let response = await Axios({
        url,
        method: 'POST',
        data: {
          sourceType: sourceType,
          connectionName: name,
          host: connectionSourceConfiguration?.credentials?.host,
          port: connectionSourceConfiguration?.credentials?.port,
          username: connectionSourceConfiguration?.credentials?.username,
          password: connectionSourceConfiguration?.credentials?.password,
          database: connectionSourceConfiguration?.credentials?.database,
        },
      });

      return connection;
    } catch (err) {
      logger.error(
        `Add new data source connection | SrcDestConnectionService.generateAndSaveSchema | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.PYTHON_REQUEST_ERROR);
    }
  }

  /**
   * Use to execute mongo query and retrieve data
   * @param fn function like find, aggregate
   * @param query query it's may be object or list according to function
   * @param collectionName Mongodb collection
   * @param sourceName client mapping name
   * @returns results of mongodb execution
   */
  async retrieveData(
    sourceName: string,
    properties: {
      function?: string;
      query?: any;
      collection?: string;
    },
  ): Promise<any> {
    // Helper function to escape special regex characters
    function escapeRegExp(string: string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    let request_id = uuidV4();
    logger.info(
      `Run source db query execute | SrcDestConnectionService.retrieveData | N/A | Trying to connect mongo db adapter, request_id: ${request_id}, sourceName: ${sourceName}, properties: `,
      properties,
    );
    // let adapter;
    // if (sourceName) {
    //   adapter = await this.adapters.get(sourceName);
    //   if (!adapter) {
    //     logger.error(
    //       `Run mongodb execute | DataSourceService.retrieveData | N/A | No adapter found for source: ${sourceName}`,
    //     );
    //   }
    let adapter;
    let sourceConnectionObj = await this.connectionRepository.findOne({
      where: {
        sourceName: sourceName,
        connectionType: DataSourceConnectionType.LAYERNEXT_DATABASE,
      },
    });

    if (
      !sourceConnectionObj ||
      !sourceConnectionObj.connectionCredentials ||
      !sourceConnectionObj.connectionCredentials.database ||
      !sourceConnectionObj.sourceName ||
      !sourceConnectionObj.type
    ) {
      logger.error(
        `Run database query execute | DataSourceService.retrieveData | N/A | Cannot established db connection. Configuration file: ${sourceConnectionObj}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.DATA_SOURCE_CONNECTION_FAILED} ${sourceName}`);
    }

    let config = {
      host: sourceConnectionObj?.connectionCredentials?.host,
      port: sourceConnectionObj?.connectionCredentials?.port,
      username: sourceConnectionObj?.connectionCredentials?.username,
      password: sourceConnectionObj?.connectionCredentials?.password,
      database: sourceConnectionObj?.connectionCredentials?.database,
      connectionName: sourceConnectionObj?.sourceName,
      credentials_path: sourceConnectionObj?.connectionCredentials?.credentials_path,
      project_id: sourceConnectionObj?.connectionCredentials?.project_id,
      warehouse: sourceConnectionObj?.connectionCredentials?.warehouse,
      schema: sourceConnectionObj?.connectionCredentials?.schema,
      role: sourceConnectionObj?.connectionCredentials?.role,
      account_id: sourceConnectionObj?.connectionCredentials?.account_id,
      is_windows_authentication: sourceConnectionObj?.connectionCredentials?.is_windows_authentication,
      domain: sourceConnectionObj?.connectionCredentials?.domain,
    };
    logger.info(
      `Run database query execute | DataSourceService.retrieveData | N/A | db connector configuration: ${JSON.stringify(
        config?.connectionName,
      )}`,
    );
    adapter = await this.adapterFactory.getAdapter(sourceConnectionObj.type, config);
    logger.info(
      `Run database query execute | DataSourceService.retrieveData | N/A | Successfully connect to db adapter`,
    );
    await adapter.connect();
    // Initialize the table and field mapping if it hasn't been initialized yet
    if (!this.isTableFieldMappingInitialized) {
      await this.initializeFieldMapping(sourceConnectionObj.connectionId || '');
      this.isTableFieldMappingInitialized = true;
    }
    //Temporary map to store the tables or fields actually mapped in SQL
    const tempSQLFieldMap = new Map<string, string>();
    // Update the SQL based on field mapping
    for (const [key, value] of this.tableFieldMapping) {
      if (!properties.query.includes(key)) {
        continue;
      }
      // Replace all instances of the mapped table or field name in the query
      // Use regex with word boundaries to ensure only whole words are replaced
      const regex = new RegExp(`\\b${escapeRegExp(key)}\\b`, 'g');
      properties.query = properties.query.replace(regex, value);
      
      //When adding to temporary map, remove leading and trailing '[' and ']'
      if (value.startsWith('[') ){
        let mappedValue = value.substring(1);
        if (mappedValue.endsWith(']')) {
          mappedValue = mappedValue.substring(0, mappedValue.length - 1);
        }
        tempSQLFieldMap.set(key, mappedValue);
      } else {
        tempSQLFieldMap.set(key, value);
      }
    }

    const data = await adapter.getData(properties);
    await adapter.disconnect();
    
    // Now do the reverse mapping for the data using the temporary map
    for (const [key, value] of tempSQLFieldMap) {
      for (const entry of data) {
        if (entry[value]) {
          entry[key] = entry[value];
          delete entry[value];
        }
      }
    }
    logger.info(
      `Run database query execute | DataSourceService.retrieveData | N/A | request_id: ${request_id}, data: `,
      data,
    );
    return data;
  }

  /**
   * Use to insert data into a specified MongoDB collection
   * @param sourceName client mapping name
   * @param type Connection type (MongoDB in this case)
   * @param collectionName The name of the MongoDB collection
   * @param data Data to be inserted into the specified collection
   * @returns result of the insertion operation
   */
  async insertData(sourceName: string, type: ConnectionSourceType, collectionName: string, data: any[]): Promise<any> {
    const requestId = uuidV4();
    logger.info(
      `Initiating insert operation | SrcDestConnectionService.insertData | N/A | request_id: ${requestId}, sourceName: ${sourceName}, type: ${type}, collectionName: ${collectionName}, data: ${JSON.stringify(
        data,
      )}`,
    );
    let sourceConnectionObj = await this.connectionRepository.findOne({
      where: {
        type: type,
        sourceName: sourceName,
      },
    });

    //check whether the database is writable
    if (!sourceConnectionObj?.isEditable) {
      logger.error(
        `Insert operation failed | SrcDestConnectionService.insertData | N/A | Source is not editable: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`The Source is not editable: ${sourceName}`);
    }

    if (!sourceConnectionObj || !this.isValidConnection(sourceConnectionObj)) {
      logger.error(
        `Insert operation failed | SrcDestConnectionService.insertData | N/A | Invalid connection for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection failed for source: ${sourceName}`);
    }

    const credentials = sourceConnectionObj.connectionCredentials;

    // Check if credentials exist before proceeding
    if (!credentials) {
      logger.error(
        `Insert operation failed | SrcDestConnectionService.insertData | N/A | Invalid or missing credentials for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection credentials are missing for source: ${sourceName}`);
    }

    // Ensure credentials are fully populated
    if (!this.areCredentialsValid(credentials)) {
      logger.error(
        `Insert operation failed | SrcDestConnectionService.insertData | N/A | Invalid or missing credentials for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection credentials are invalid for source: ${sourceName}`);
    }

    // Safely cast credentials to ConnectionSourceCredentials as we've validated them
    const validatedCredentials = credentials as ConnectionSourceCredentials;

    const adapter = await this.adapterFactory.getAdapter(type, validatedCredentials);
    await adapter.connect();

    try {
      logger.info(`Executing insert operation | SrcDestConnectionService.insertData | N/A | request_id: ${requestId}`);
      if (adapter instanceof MongoDBAdapter) {
        // Updated the insertData call to match the new signature
        const insertResult = await adapter.insertData(collectionName, data);
        logger.info(
          `Insert operation successful | SrcDestConnectionService.insertData | N/A | request_id: ${requestId}, insertResult: ${JSON.stringify(
            insertResult,
          )}`,
        );
        return insertResult;
      } else {
        logger.error(
          `Insert operation failed | SrcDestConnectionService.insertData | N/A | Unsupported data source type for insert operation, request_id: ${requestId}`,
        );
        throw new HttpErrors.NotAcceptable('Insert operation is not supported for this data source type.');
      }
    } finally {
      await adapter.disconnect();
      logger.info(`Disconnected from database | SrcDestConnectionService.insertData | N/A | request_id: ${requestId}`);
    }
  }

  /**
   * Use to delete data from a specified MongoDB collection
   * @param sourceName client mapping name
   * @param type Connection type (MongoDB in this case)
   * @param collectionName The name of the MongoDB collection
   * @param filter Filter criteria to identify the records to be deleted
   * @returns result of the deletion operation
   */
  async deleteData(
    sourceName: string,
    type: ConnectionSourceType,
    collectionName: string,
    filter: object,
  ): Promise<any> {
    const requestId = uuidV4();
    logger.info(
      `Initiating delete operation | SrcDestConnectionService.deleteData | N/A | request_id: ${requestId}, sourceName: ${sourceName}, type: ${type}, collectionName: ${collectionName}, filter: ${JSON.stringify(
        filter,
      )}`,
    );
    let sourceConnectionObj = await this.connectionRepository.findOne({
      where: {
        type: type,
        sourceName: sourceName,
      },
    });

    // check whether the database is writable
    if (!sourceConnectionObj?.isEditable) {
      logger.error(
        `Delete operation failed | SrcDestConnectionService.deleteData | N/A | Source is not editable: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`The Source is not editable: ${sourceName}`);
    }

    if (!sourceConnectionObj || !this.isValidConnection(sourceConnectionObj)) {
      logger.error(
        `Delete operation failed | SrcDestConnectionService.deleteData | N/A | Invalid connection for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection failed for source: ${sourceName}`);
    }

    const credentials = sourceConnectionObj.connectionCredentials;

    // Check if credentials exist before proceeding
    if (!credentials) {
      logger.error(
        `Delete operation failed | SrcDestConnectionService.deleteData | N/A | Invalid or missing credentials for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection credentials are missing for source: ${sourceName}`);
    }

    // Ensure credentials are fully populated
    if (!this.areCredentialsValid(credentials)) {
      logger.error(
        `Delete operation failed | SrcDestConnectionService.deleteData | N/A | Invalid or missing credentials for source: ${sourceName}, request_id: ${requestId}`,
      );
      throw new HttpErrors.NotAcceptable(`Connection credentials are invalid for source: ${sourceName}`);
    }

    const validatedCredentials = credentials as ConnectionSourceCredentials;
    const adapter = await this.adapterFactory.getAdapter(type, validatedCredentials);
    await adapter.connect();

    try {
      logger.info(`Executing delete operation | SrcDestConnectionService.deleteData | N/A | request_id: ${requestId}`);
      if (adapter instanceof MongoDBAdapter) {
        const deleteResult = await adapter.deleteData(collectionName, filter);
        logger.info(
          `Delete operation successful | SrcDestConnectionService.deleteData | N/A | request_id: ${requestId}, deleteResult: ${JSON.stringify(
            deleteResult,
          )}`,
        );
        return deleteResult;
      } else {
        logger.error(
          `Delete operation failed | SrcDestConnectionService.deleteData | N/A | Unsupported data source type for delete operation, request_id: ${requestId}`,
        );
        throw new HttpErrors.NotAcceptable('Delete operation is not supported for this data source type.');
      }
    } finally {
      await adapter.disconnect();
      logger.info(`Disconnected from database | SrcDestConnectionService.deleteData | N/A | request_id: ${requestId}`);
    }
  }

  private isValidConnection(connection: any): boolean {
    return Boolean(connection.connectionCredentials && this.areCredentialsValid(connection.connectionCredentials));
  }

  private areCredentialsValid(credentials: Partial<ConnectionSourceCredentials>): boolean {
    return Boolean(
      credentials.host && credentials.port && credentials.username && credentials.password && credentials.database,
    );
  }

  /**
   * Use to create source creating and connection check payload
   * @param name {string} name of the source
   * @param type {ConnectionSourceType} type of the source
   * @param credentials {ConnectionSourceCredentials} credentials of the source
   * @returns Promise<AirbyteConnectionSourceCredentials>
   */
  async getCreateSourcePayload(
    name: string,
    type: ConnectionSourceType,
    credentials: ConnectionSourceCredentials,
  ): Promise<AirbyteConnectionSourceCredentials> {
    let payload: any = {};
    function padZero(number: number) {
      return number.toString().padStart(2, '0');
    }
    switch (type) {
      case ConnectionSourceType.MONGO_DB:
        // return this.getMongoDbPayload(name, credentials);
        payload = {
          name: name,
          sourceDefinitionId: SourceDefinitionIds.sourceDefinitionIdMongoDB,
          workspaceId: WORK_SPACE_ID,
          connectionConfiguration: {
            instance_type: {
              instance: 'standalone',
              port: credentials.port,
              host: credentials.host,
              tls: false,
            },
            auth_source: credentials.authSource || 'admin',
            password: credentials.password,
            database: credentials.database,
            user: credentials.username,
          },
        };
        break;
      case ConnectionSourceType.QUICK_BOOK:
        const currentDate = new Date();
        // Add 30 minutes to the current date and time
        const laterDate = new Date(currentDate.getTime() + 30 * 60000);

        const formattedString = `${laterDate.getFullYear()}-${padZero(laterDate.getMonth() + 1)}-${padZero(
          laterDate.getDate(),
        )}T${padZero(laterDate.getHours())}:${padZero(laterDate.getMinutes())}:${padZero(laterDate.getSeconds())}Z`;

        payload = {
          name: name,
          sourceDefinitionId: SourceDefinitionIds.sourceDefinitionIdQuickBook,
          workspaceId: WORK_SPACE_ID,
          connectionConfiguration: {
            sandbox: credentials.sandbox,
            credentials: {
              auth_type: 'oauth2.0',
              access_token: credentials.access_token,
              refresh_token: credentials.refresh_token,
              realm_id: credentials.realm_id,
              client_id: credentials.client_id,
              client_secret: credentials.client_secret,
              token_expiry_date: formattedString,
            },
            start_date: credentials.start_date,
          },
        };
        break;

      case ConnectionSourceType.MYSQL_DB:
        payload = {
          name: name,
          connectionConfiguration: {
            ssl: true,
            port: credentials.port,
            ssl_mode: {
              mode: 'preferred',
            },
            tunnel_method: {
              tunnel_method: 'NO_TUNNEL',
            },
            replication_method: {
              method: 'CDC',
              initial_waiting_seconds: 300,
            },
            host: credentials.host,
            database: credentials.database,
            password: credentials.password,
            username: credentials.username,
          },
          workspaceId: WORK_SPACE_ID,
          sourceDefinitionId: SourceDefinitionIds.sourceDefinitionIdMysql,
        };
        break;
      case ConnectionSourceType.MSSQL_DB:
        payload = {
          name: name,
          connectionConfiguration: {
            replication_method: {
              method: 'CDC',
              data_to_sync: 'Existing and New',
              snapshot_isolation: 'Snapshot',
              initial_waiting_seconds: 300,
            },
            tunnel_method: {
              tunnel_method: 'NO_TUNNEL',
            },
            ssl_method: {
              ssl_method: 'unencrypted',
            },
            username: credentials.username,
            password: credentials.password,
            database: credentials.database,
            schemas: ['dbo'],
            port: credentials.port,
            host: credentials.host,
          },
          workspaceId: WORK_SPACE_ID,
          sourceDefinitionId: SourceDefinitionIds.sourceDefinitionIdMssql,
        };
    }

    return payload;
  }

  /**
   * create destination creating and connection check payload
   * @param name {string} name of the destination
   * @param type {ConnectionDestinationType} type of the destination
   * @param credentials {ConnectionDestinationCredentials} credentials of the destination
   * @returns Promise<AirbyteConnectionDestinationCredentials>
   */
  async getCreateDestinationPayload(
    name: string,
    type: ConnectionDestinationType,
    credentials: ConnectionDestinationCredentials,
  ): Promise<AirbyteConnectionDestinationCredentials> {
    let payload: any = {};
    switch (type) {
      case ConnectionDestinationType.MONGO_DB:
        payload = {
          name: name,
          destinationDefinitionId: DestinationDefinitionIds.destinationDefinitionIdMongoDB,
          workspaceId: WORK_SPACE_ID,
          connectionConfiguration: {
            tunnel_method: {
              tunnel_method: 'NO_TUNNEL',
            },
            instance_type: {
              instance: 'standalone',
              port: credentials.port,
              host: credentials.host,
              tls: false,
            },
            auth_type: {
              authorization: 'login/password',
              password: credentials.password,
              username: credentials.username,
            },
            database: credentials.database,
          },
        };
        break;
      case ConnectionDestinationType.MYSQL_DB:
        payload = {
          name: name,
          destinationDefinitionId: DestinationDefinitionIds.destinationDefinitionIdMysql,
          workspaceId: WORK_SPACE_ID,
          connectionConfiguration: {
            ssl: true,
            host: credentials.host,
            port: credentials.port,
            database: credentials.database,
            password: credentials.password,
            username: credentials.username,
            tunnel_method: {
              tunnel_method: 'NO_TUNNEL',
            },
          },
        };
        break;
    }

    return payload;
  }

  /**
   * validate name from database sources before start connection
   * @param name {string} name of the source
   * @param type {ConnectionSourceType} type of the source
   */
  async validateName(name: string, type: ConnectionSourceType) {
    let isExists = await this.sourceRepository.findOne({where: {name: name, type: type}});

    if (isExists || !name) {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.checkDestinationConnection | N/A | name: ${name}, type: ${type}, validate name failed`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATA_SOURCE_NAME_ALREADY_EXIST);
    }
    logger.debug(
      `Create airbyte source | SrcDestConnectionService.checkDestinationConnection | N/A | name: ${name}, type: ${type}, validate name success`,
    );
  }

  /**
   * check destination connection before creating source
   * @param payload {AirbyteConnectionDestinationCredentials} payload for destination connection check
   */
  async checkDestinationConnection(payload: AirbyteConnectionDestinationCredentials) {
    let url = `${AIRBYTE_URL}/api/v1/scheduler/destinations/check_connection`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.checkDestinationConnection | N/A | payload: ${payload}, check destination connection failed`,
      );
      throw new HttpErrors.NotAcceptable(err);
    }

    if (response.data.status == 'failed') {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.checkDestinationConnection | N/A | payload: ${payload}, check destination connection failed`,
      );
      throw new HttpErrors.NotAcceptable(response.data.message);
    }

    logger.debug(
      `Create airbyte source | SrcDestConnectionService.checkDestinationConnection | N/A | payload: ${payload}, check destination connection success`,
    );
  }

  /**
   * Use to check source connection status
   * @param payload {AirbyteConnectionSourceCredentials} payload for source connection check
   */
  async checkSourceConnection(payload: AirbyteConnectionSourceCredentials) {
    let url = `${AIRBYTE_URL}/api/v1/scheduler/sources/check_connection`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.checkSourceConnection | N/A | payload: ${payload}, check source connection failed`,
      );
      throw new HttpErrors.NotAcceptable(err);
    }

    if (response.data.status == 'failed') {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.checkSourceConnection | N/A | payload: ${payload}, source connection failed`,
      );
      throw new HttpErrors.NotAcceptable(response.data.message);
    }
    logger.info(
      `Create airbyte source | SrcDestConnectionService.checkSourceConnection | N/A | payload: ${payload}, source connection success`,
    );
  }

  /**
   * create airbyte source
   * @param payload {AirbyteConnectionSourceCredentials} payload for source creation
   * @returns resposne of create airbyte source
   */
  async createAirbyteSource(payload: AirbyteConnectionSourceCredentials) {
    let url = `${AIRBYTE_URL}/api/v1/sources/create`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Create airbyte source | SrcDestConnectionService.createAirbyteSource | N/A | payload: ${payload}, create airbyte source failed`,
      );
      throw new HttpErrors.NotAcceptable(err);
    }

    if (response.data.status == 'failed') {
      throw new HttpErrors.NotAcceptable(response.data.message);
    }

    return response.data;
  }

  /**
   * create airbyte destination
   * @param payload {AirbyteConnectionDestinationCredentials} payload for destination creation
   * @returns resposne of create airbyte destination
   */
  async createAirbyteDestination(payload: AirbyteConnectionDestinationCredentials) {
    let url = `${AIRBYTE_URL}/api/v1/destinations/create`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Create airbyte destination | SrcDestConnectionService.createAirbyteDestination | N/A | payload: ${payload}, create airbyte destination failed`,
      );
      throw new HttpErrors.NotAcceptable(err);
    }

    if (response.data.status == 'failed') {
      throw new HttpErrors.NotAcceptable(response.data.message);
    }

    return response.data;
  }

  async createAirbyteConnection(payload: AirbyteConnectionCredentials) {
    let url = `${AIRBYTE_URL}/api/v1/web_backend/connections/create`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    // logger.debug(JSON.stringify(payload));

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(err);
    }

    if (response.data.status == 'failed') {
      throw new HttpErrors.NotAcceptable(response.data.message);
    }

    return response.data;
  }

  // async updateAirbyteConnection(payload: AirbyteConnectionSourceUpdateCredentials) {
  //   let url = `${AIRBYTE_URL}/api/v1/sources/update`;
  //   let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

  //   // logger.debug(JSON.stringify(payload));

  //   let response: any = {};
  //   try {
  //     response = await Axios({
  //       url,
  //       method: 'POST',
  //       data: payload,
  //       headers: {
  //         Authorization: basicAuthCode,
  //       },
  //     });
  //   } catch (err) {
  //     logger.error(err);
  //   }

  //   if (response.data.status == 'failed') {
  //     throw new HttpErrors.NotAcceptable(response.data.message);
  //   }

  //   return response.data;
  // }

  async createBasicAuthHeader(username?: string, password?: string): Promise<string> {
    // Concatenate the username and password with a colon
    const credentials = `${username}:${password}`;

    // Base64 encode the credentials
    const encodedCredentials = Buffer.from(credentials).toString('base64');

    // Return the Authorization header
    return `Basic ${encodedCredentials}`;
  }

  async getSourceSchema(sourceId: string): Promise<SourceSchema> {
    let url = `${AIRBYTE_URL}/api/v1/sources/discover_schema`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    response = await Axios({
      url,
      method: 'POST',
      data: {
        sourceId: sourceId,
        disable_cache: true,
      },
      headers: {
        Authorization: basicAuthCode,
      },
    });

    if (response.data.status == 'failed') {
      throw new HttpErrors.NotAcceptable(response.data.message);
    }

    return response.data;
  }

  async getCreateConnectionPayload(
    name: string,
    type: ConnectionSourceType,
    sourceId: string,
    destinationId: string,
    selectedConfiguration: SelectionConfiguration,
  ): Promise<AirbyteConnectionCredentials> {
    let payload: any = {};
    let sourceSchema;
    let selectedFields;
    let streams: Stream[] = [];
    switch (type) {
      case ConnectionSourceType.QUICK_BOOK:
        sourceSchema = await this.getSourceSchema(sourceId);
        selectedFields = Object.entries(selectedConfiguration)
          .filter(([key, value]) => value === true)
          .map(([key, _]) => key);

        if (sourceSchema && sourceSchema.catalog && sourceSchema.catalog.streams) {
          for (let streamObj of sourceSchema?.catalog?.streams) {
            let tempStream = {
              stream: streamObj.stream,
              config: {},
            };
            let tempConfig = {...streamObj.config};
            let name: string = streamObj.stream?.name || '';
            if (selectedFields.includes(name)) {
              tempConfig.selected = true;
            } else {
              tempConfig.selected = false;
            }
            tempConfig.syncMode = SyncMode.incremental;
            tempConfig.destinationSyncMode = DestinationSyncMode.APPEND;
            let isIncrementalInclude = streamObj.stream?.supportedSyncModes.includes(SyncMode.incremental);
            if (!isIncrementalInclude) {
              tempConfig.syncMode = SyncMode.full_refresh;
              tempConfig.destinationSyncMode = DestinationSyncMode.OVERWRITE;
              tempConfig.selected = false;
            }
            tempStream.config = tempConfig;
            streams.push(tempStream);
          }
        }

        payload = {
          sourceId: sourceId,
          destinationId: destinationId,
          name: `${name} → ${name}`,
          scheduleType: 'basic',
          scheduleData: {
            basicSchedule: {
              units: 1,
              timeUnit: 'hours',
            },
          },
          namespaceDefinition: 'destination',
          nonBreakingChangesPreference: 'ignore',
          geography: 'auto',
          syncCatalog: {streams: streams},
          status: 'active',
          sourceCatalogId: sourceSchema.catalogId,
        };
        break;
      case ConnectionSourceType.MONGO_DB:
        sourceSchema = await this.getSourceSchema(sourceId);
        // if (sourceSchema.jobInfo.succeeded) {
        //   await this.updateAirbyteConnection();
        //   sourceSchema = await this.getSourceSchema(sourceId);
        // }
        if (sourceSchema && sourceSchema.catalog && sourceSchema.catalog.streams) {
          for (let streamObj of sourceSchema?.catalog?.streams) {
            let tempStream = {
              stream: streamObj.stream,
              config: {},
              id: uuidV4(),
            };
            let tempConfig = {...streamObj.config};
            tempConfig.selected = true;
            tempConfig.syncMode = SyncMode.full_refresh;
            tempConfig.destinationSyncMode = DestinationSyncMode.OVERWRITE;
            tempStream.config = tempConfig;
            streams.push(tempStream);
          }
        }

        payload = {
          sourceId: sourceId,
          destinationId: destinationId,
          name: `${name} → ${name}`,
          scheduleType: 'basic',
          scheduleData: {
            basicSchedule: {
              units: 24,
              timeUnit: 'hours',
            },
          },
          namespaceDefinition: 'destination',
          nonBreakingChangesPreference: 'ignore',
          geography: 'auto',
          syncCatalog: {streams: streams},
          status: 'active',
          sourceCatalogId: sourceSchema.catalogId,
        };
        break;
      case ConnectionSourceType.MYSQL_DB:
        sourceSchema = await this.getSourceSchema(sourceId);

        if (sourceSchema && sourceSchema.catalog && sourceSchema.catalog.streams) {
          for (let streamObj of sourceSchema?.catalog?.streams) {
            let tempStream = {
              stream: streamObj.stream,
              config: {},
              id: uuidV4(),
            };
            let tempConfig = {...streamObj.config};
            tempConfig.selected = true;
            tempConfig.syncMode = SyncMode.incremental;
            tempConfig.destinationSyncMode = DestinationSyncMode.APPEND;
            let isIncrementalInclude = streamObj.stream?.supportedSyncModes.includes(SyncMode.incremental);
            if (!isIncrementalInclude) {
              tempConfig.syncMode = SyncMode.full_refresh;
              tempConfig.destinationSyncMode = DestinationSyncMode.OVERWRITE;
              tempConfig.selected = false;
            }
            tempStream.config = tempConfig;
            streams.push(tempStream);
          }
        }

        payload = {
          sourceId: sourceId,
          destinationId: destinationId,
          name: `${name} → ${name}`,
          scheduleType: 'basic',
          operations: [
            {
              name: 'Normalization',
              workspaceId: WORK_SPACE_ID,
              operatorConfiguration: {
                operatorType: 'normalization',
                normalization: {
                  option: 'basic',
                },
              },
            },
          ],
          scheduleData: {
            basicSchedule: {
              units: 1,
              timeUnit: 'hours',
            },
          },
          namespaceDefinition: 'destination',
          nonBreakingChangesPreference: 'ignore',
          geography: 'auto',
          syncCatalog: {streams: streams},
          status: 'active',
          sourceCatalogId: sourceSchema.catalogId,
        };
        break;
      case ConnectionSourceType.MSSQL_DB:
        sourceSchema = await this.getSourceSchema(sourceId);

        if (sourceSchema && sourceSchema.catalog && sourceSchema.catalog.streams) {
          for (let streamObj of sourceSchema?.catalog?.streams) {
            let tempStream = {
              stream: streamObj.stream,
              config: {},
              id: uuidV4(),
            };
            let tempConfig = {...streamObj.config};
            tempConfig.selected = true;
            tempConfig.syncMode = SyncMode.full_refresh;
            tempConfig.destinationSyncMode = DestinationSyncMode.OVERWRITE;
            tempStream.config = tempConfig;
            streams.push(tempStream);
          }
        }

        payload = {
          sourceId: sourceId,
          destinationId: destinationId,
          name: `${name} → ${name}`,
          scheduleType: 'basic',
          scheduleData: {
            basicSchedule: {
              units: 1,
              timeUnit: 'hours',
            },
          },
          namespaceDefinition: 'destination',
          nonBreakingChangesPreference: 'ignore',
          geography: 'auto',
          syncCatalog: {streams: streams},
          operations: [
            {
              name: 'Normalization',
              workspaceId: WORK_SPACE_ID,
              operatorConfiguration: {
                operatorType: 'normalization',
                normalization: {
                  option: 'basic',
                },
              },
            },
          ],
          status: 'active',
          sourceCatalogId: sourceSchema.catalogId,
        };
        break;
    }

    return payload;
  }

  /**
   * Use to validate database name
   * @param name {string} name of the database
   * @returns Promise<string>
   */
  async getValidDatabaseNames(
    name: string,
    type: ConnectionDestinationType,
    password?: string,
  ): Promise<string | undefined> {
    switch (type) {
      case ConnectionDestinationType.MONGO_DB:
        let dbName = name.replace(/[\s.$]/g, '_');

        // MongoDB database names should not contain null character,This line removes any null character just in case
        dbName = dbName.replace(/\0/g, '');

        // Convert to lowercase to avoid issues with case sensitivity
        dbName = dbName.toLowerCase();

        return dbName;
      case ConnectionDestinationType.MYSQL_DB:
        try {
          let dbName = name.substring(0, 64);

          // Replace spaces and other special characters not allowed in MySQL identifiers
          dbName = dbName.replace(/[^a-zA-Z0-9_]/g, '_');

          // If the name starts with a number, prefix it with an underscore
          if (/^\d/.test(dbName)) {
            dbName = '_' + dbName;
          }
          // Create a connection object
          const connection = await mysql.createConnection({
            host: CONNECTION_MYSQL_DB_HOST || '', // Replace with your host name
            port: Number(CONNECTION_MYSQL_DB_PORT), // Replace with your port
            user: 'root', // Replace with your database username
            password: MYSQL_ROOT_PASSWORD, // Replace with your database password
            // You might also want to specify other options like port
          });

          logger.debug(
            `Create MYSQL connection | SrcDestConnectionService.getValidDatabaseNames | N/A | Connected to the MySQL server`,
          );

          // Create a new database
          const [rows, fields] = await connection.execute(`CREATE DATABASE ${dbName}`);
          logger.debug('Database created');

          // Create a new user
          await connection.execute(`CREATE USER IF NOT EXISTS '${dbName}_user' IDENTIFIED BY '${password}'`);

          // Grant privileges to the user on the new database
          await connection.execute(`GRANT SELECT, INSERT, UPDATE, DELETE ON ${dbName}.* TO '${dbName}_user'`);

          // Apply changes
          await connection.execute('FLUSH PRIVILEGES');

          await connection.end();
          return dbName;
        } catch (err) {
          logger.error(
            `Create MYSQL connection | SrcDestConnectionService.getValidDatabaseNames | N/A | Connected to the MySQL server error occurred: ${err}`,
          );
          return undefined;
        }
      default:
        return name;
    }
  }

  /**
   * Get source connection list
   * @param searchKey {string} search key
   * @param type {ConnectionSourceType} source type
   * @param pageIndex {number} page index
   * @param pageSize {number} page size
   * @returns source connection list
   */
  async getsourceConnectionList(
    searchKey?: string,
    type?: ConnectionSourceType,
    pageIndex: number = 0,
    pageSize: number = 20,
  ) {
    let filter: any = {};
    if (searchKey) {
      filter.name = {$regex: searchKey, $options: 'i'};
    }
    if (type) {
      filter.type = type;
    }

    //Add filter for layernext databases(only fetch raw and storages)
    filter = {
      ...filter,
      connectionType: {
        $in: [DataSourceConnectionType.RAW_DATABASE, DataSourceConnectionType.STORAGE],
      },
    };

    let sources: {
      connectionId: string;
      connectionName: string;
      sourceType: ConnectionSourceType;
      connectionStatus: DataSourceConnectionStatus;
      connectionType: DataSourceConnectionType;
      itemCount: number;
      itemSize: number;
      isEditable: boolean;
      lastCrawled?: string;
      lastSyncTime: string;
      isDataStructureCrawled?: DataStructureCrawlStatus;
      layerNextConnectionId: string;
      drawerData?: {
        title: string;
        value: string;
      }[];
    }[] = [];

    let sourceList: {
      sourceName: string;
      type: ConnectionSourceType;
      itemCount: number;
      itemSize: number;
      connectionId: string;
      connectionStatus: DataSourceConnectionStatus;
      lastSyncTime: string;
      lastCrawled: string;
      createdAt: string;
      isDataStructureCrawled?: DataStructureCrawlStatus;
      layerNextConnectionId: string;
      connectionType: DataSourceConnectionType;
    }[] = [];
    try {
      sourceList = await this.connectionRepository.aggregate([
        {$match: filter},
        {
          $project: {
            sourceName: 1,
            type: 1,
            itemCount: 1,
            itemSize: 1,
            connectionId: 1,
            connectionStatus: 1,
            lastSyncTime: 1,
            lastCrawled: 1,
            createdAt: 1,
            isDataStructureCrawled: 1,
            layerNextConnectionId: 1,
            connectionType: 1,
          },
        },
        // {$skip: pageIndex * pageSize},
        // {$limit: pageSize},
      ]);
    } catch (error) {
      logger.error(`SrcDestConnectionService.getSourceConnectionList | N/A |Error fetching connection list: ${error}`);
    }

    for (let connection of sourceList) {
      let lastSyncTime = connection.lastSyncTime || connection.createdAt;

      let tableDataCounts;
      try {
        tableDataCounts = await this.tableDataService.getTableCountsByConnectionID(connection.connectionId);
      } catch (error) {
        logger.error(
          `SrcDestConnectionService.getSourceConnectionList | N/A |Error fetching table counts for connection ${connection.connectionId}: ${error}`,
        );
        tableDataCounts = {configuredTables: 0, unconfiguredTables: 0};
      }

      let configuredTableCount = tableDataCounts.configuredTables;
      let unconfiguredTableCount = tableDataCounts.unconfiguredTables;
      let totalTables = configuredTableCount + unconfiguredTableCount;

      let lastCrawled = connection.lastCrawled ? timeAgo(new Date(connection.lastCrawled)) : 'N/A';

      sources.push({
        connectionId: connection.connectionId,
        connectionName: connection.sourceName,
        sourceType: connection.type,
        connectionStatus: connection.connectionStatus,
        connectionType: connection.connectionType,
        itemCount:
          connection.connectionType === DataSourceConnectionType.RAW_DATABASE ? totalTables : connection.itemCount,
        itemSize: connection.itemSize,
        isEditable: true,
        lastCrawled: lastCrawled,
        lastSyncTime: timeAgo(new Date(lastSyncTime)),
        isDataStructureCrawled: connection.isDataStructureCrawled,
        layerNextConnectionId: connection.layerNextConnectionId,
        drawerData: [
          {
            title: 'Total configured Tables',
            value: configuredTableCount?.toString() || '0',
          },
          {
            title: 'Total unconfigured Tables',
            value: unconfiguredTableCount?.toString() || '0',
          },
          {
            title: 'Crawl Frequency',
            value: 'Daily',
          },
        ],
      });
    }
    //TODO: need to change below after finished enabling adding storages from frontend
    // get storages in .env from db
    let initialCrawls = await this.dataCrawlRepository.find({
      where: {
        isInitialCrawl: true,
      },
    });

    if (initialCrawls && Array.isArray(initialCrawls) && initialCrawls.length > 0) {
      let _connectionId = `${uuidV4()}`;
      for (let storage of initialCrawls) {
        sources.unshift({
          connectionId: _connectionId,
          connectionName: storage.storageName?.split('/').pop() || '',
          sourceType: storage.storageType,
          connectionStatus:
            storage.status == CrawlingStatus.FAILED
              ? DataSourceConnectionStatus.DISCONNECTED
              : DataSourceConnectionStatus.CONNECTED,
          connectionType: DataSourceConnectionType.STORAGE,
          itemCount: storage.crawledFileCount,
          itemSize: storage.crawledFileSize,
          isEditable: false,
          lastCrawled: '',
          lastSyncTime: storage?.completedAt ? timeAgo(storage?.completedAt) : 'N/A',
          drawerData: [],
          layerNextConnectionId: '',
        });
      }
    }

    // get system stats
    // create summary array
    let systemStats = await this.systemDataRepository.findOne();
    let totalFileCount =
      (systemStats?.objectTypeWiseCounts?.images?.count || 0) +
      (systemStats?.objectTypeWiseCounts?.videos?.count || 0) +
      (systemStats?.objectTypeWiseCounts?.other?.count || 0);
    let summary = [];
    if (systemStats && systemStats.sourceConnectionStats) {
      let priorityObj = {
        'Total Records': 1,
        'Total Tables': 2,
        'Total Files': 3,
        'Total Collections': 4,
      };
      for (let key in systemStats.sourceConnectionStats) {
        switch (key) {
          case 'totalRecords':
            if (systemStats.sourceConnectionStats[key]) {
              summary.push({
                id: priorityObj['Total Records'],
                fieldText: 'Total Records',
                fieldCount: systemStats.sourceConnectionStats[key],
                fieldIcon: 'icon-records-fill',
              });
            }
            break;
          case 'totalTables':
            if (systemStats.sourceConnectionStats[key]) {
              let index = summary.findIndex(item => item.fieldText === 'Total Tables');
              if (index === -1) {
                summary.push({
                  id: priorityObj['Total Tables'],
                  fieldText: 'Total Tables',
                  fieldCount: systemStats.sourceConnectionStats[key],
                  fieldIcon: 'icon-table-count',
                });
              } else {
                summary[index].fieldCount += systemStats.sourceConnectionStats[key];
              }
            }
            break;
          case 'totalFiles':
            if (systemStats.sourceConnectionStats[key]) {
              summary.push({
                id: priorityObj['Total Files'],
                fieldText: 'Total Files',
                fieldCount: totalFileCount || systemStats.sourceConnectionStats[key],
                fieldIcon: 'icon-document',
              });
            }
            break;
          // add collection count to table count
          case 'totalCollections':
            if (systemStats.sourceConnectionStats[key]) {
              let index = summary.findIndex(item => item.fieldText === 'Total Tables');
              if (index === -1) {
                summary.push({
                  id: priorityObj['Total Collections'],
                  fieldText: 'Total Tables',
                  fieldCount: systemStats.sourceConnectionStats[key],
                  fieldIcon: 'icon-table-count',
                });
              } else {
                summary[index].fieldCount += systemStats.sourceConnectionStats[key];
              }
            }
            break;
        }
      }
    }

    // sort summary as [Total Records > Total Tables > Total Files]
    summary.sort((a, b) => a.id - b.id);

    return {
      sources: sources,
      summary: summary,
    };
  }

  /**
   * Use to sync connection job list from airbyte with metalake connection history collection
   * Runs every 60 minutes by default
   * Get job status and sync item count and sync item size
   * perform bulk update to update job status, item count and item size
   */
  async syncConnectionJobList() {
    // let connectionList = await this.connectionRepository.find({where: {type: ConnectionSourceType.QUICK_BOOK}});
    logger.info(
      `Connection stats update | SrcDestConnectionService.syncConnectionJobList | N/A | sync connection job list from airbyte`,
    );

    let connectionList = await this.connectionRepository.aggregate([
      {
        $match: {
          type: {
            $in: [
              ConnectionSourceType.QUICK_BOOK,
              ConnectionSourceType.MYSQL_DB,
              ConnectionSourceType.MSSQL_DB,
              ConnectionSourceType.MONGO_DB,
            ],
          },
        },
      },
    ]);
    let bulkUpdateOneArr: {
      updateOne: {
        filter: {type?: string; jobId?: number; connectionId?: string};
        update: {
          $set: {
            type?: string;
            jobId?: number;
            createdAt?: Date;
            updatedAt?: Date;
            status?: SyncJobStatus;
            itemCount?: number;
            itemSize?: number;
            connectionId?: string;
          };
        };
        upsert: boolean;
      };
    }[] = [];
    for (let connection of connectionList) {
      let connectionJobDetails: {jobs: ConnectionJob[]} = await this.getConnectionJobDetails(connection.connectionId);
      let jobs = connectionJobDetails?.jobs || [];

      for (let jobObj of jobs) {
        if (!jobObj.job) continue;
        let status = SyncJobStatus.RUNNING;
        let itemCount = 0;
        let itemSize = 0;
        if (jobObj.job.status === 'failed') {
          status = SyncJobStatus.FAILED;
        } else if (jobObj.job.status === 'succeeded') {
          status = SyncJobStatus.COMPLETED;
        } else if (jobObj.job.status === 'running') {
          status = SyncJobStatus.RUNNING;
        }
        for (let attempt of jobObj.attempts) {
          if (!attempt.totalStats) continue;
          itemCount += attempt.totalStats.recordsCommitted || 0;
          itemSize += attempt.totalStats.bytesEmitted || 0;
        }
        bulkUpdateOneArr.push({
          updateOne: {
            filter: {type: connection.type, jobId: jobObj.job.id, connectionId: connection.connectionId},
            update: {
              $set: {
                jobId: jobObj.job.id,
                type: connection.type,
                connectionId: connection.connectionId,
                createdAt: new Date(jobObj.job.createdAt * 1000),
                updatedAt: new Date(jobObj.job.updatedAt * 1000),
                status: status,
                itemCount: itemCount,
                itemSize: itemSize,
              },
            },
            upsert: true,
          },
        });
      }
    }

    await this.connectionHistoryRepository.bulkWrite(bulkUpdateOneArr);
  }

  /**
   * Get connection job details from air byte server
   * @param connectionId {string} - connection id
   * @returns {Promise<any>} job details list
   */
  async getConnectionJobDetails(connectionId?: string) {
    let url = `${AIRBYTE_URL}/api/v1/jobs/list`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: {
          configId: connectionId,
          configTypes: ['sync', 'reset_connection'],
          pagination: {
            pageSize: 30,
            rowOffset: 0,
          },
        },
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Get connection job details | SrcDestConnectionService.getConnectionJobDetails | N/A | connectionId: ${connectionId}, get connection job details failed`,
      );
      return {};
    }

    return response.data;
  }

  /**
   * Use for update connection stats and connection status
   * Get connection status from airbyte
   * Get connection item count and sync item size from connection history
   */
  async syncConnectionStats() {
    logger.info(
      `Connection stats update | SrcDestConnectionService.syncConnectionStats | N/A | sync connection stats and connection status`,
    );
    let connectionList = await this.connectionRepository.aggregate([
      {
        $match: {
          type: {
            $in: [
              ConnectionSourceType.QUICK_BOOK,
              ConnectionSourceType.MYSQL_DB,
              ConnectionSourceType.MSSQL_DB,
              ConnectionSourceType.MONGO_DB,
            ],
          },
        },
      },
    ]);
    for (let connection of connectionList) {
      let connectionStats: [
        {
          itemCount: number;
          itemSize: number;
          count: number;
          type: ConnectionSourceType;
          initItemCount: number;
          initItemSize: number;
          lastSyncTime: Date;
        },
      ] = await this.connectionHistoryRepository.aggregate([
        {$match: {connectionId: connection.connectionId}},
        {
          $group: {
            _id: null,
            itemCount: {$sum: '$itemCount'},
            itemSize: {$sum: '$itemSize'},
            count: {$sum: 1},
            type: {$first: '$type'},
            initItemCount: {$last: '$itemCount'},
            initItemSize: {$last: '$itemSize'},
            lastSyncTime: {$last: '$updatedAt'},
          },
        },
      ]);

      let connectionStatusObj: [
        {
          connectionId: string;
          lastSyncJobStatus: string;
          lastSyncJobId: number;
          lastSyncAttemptNumber: number;
          isRunning: boolean;
        },
      ] = await this.getConnectionStatus(connection.connectionId);

      let connectionObj = await this.connectionRepository.findOne({where: {connectionId: connection.connectionId}});

      let status = DataSourceConnectionStatus.CONNECTING;
      if (!connectionStatusObj[0]) {
        status = DataSourceConnectionStatus.DISCONNECTED;
      } else {
        if (connectionStatusObj[0].lastSyncJobStatus == 'succeeded') {
          status = DataSourceConnectionStatus.CONNECTED;
        } else if (connectionStatusObj[0].lastSyncJobStatus == 'failed') {
          status = DataSourceConnectionStatus.DISCONNECTED;
        } else if (connectionStatusObj[0].lastSyncJobStatus == 'running') {
          status = DataSourceConnectionStatus.CONNECTING;
        }
      }
      let itemCount = 0;
      let itemSize = 0;
      let collectionCount = 0;
      let tableCount = 0;
      let lastSyncTime = undefined;
      if (connectionStats && Array.isArray(connectionStats) && connectionStats.length > 0) {
        if (
          connectionStats[0].type &&
          (connectionStats[0].type == ConnectionSourceType.MSSQL_DB ||
            connectionStats[0].type == ConnectionSourceType.MONGO_DB)
        ) {
          if (connectionStats[0].initItemCount) itemCount = connectionStats[0].initItemCount;
          if (connectionStats[0].initItemSize) itemSize = connectionStats[0].initItemSize;
        } else {
          if (connectionStats[0].itemCount) itemCount = connectionStats[0].itemCount;
          if (connectionStats[0].itemSize) itemSize = connectionStats[0].itemSize;
        }
        if (connectionStats[0].lastSyncTime) {
          lastSyncTime = new Date(connectionStats[0].lastSyncTime);
        }
      }

      if (connectionObj) {
        if (
          connectionObj.type == ConnectionSourceType.MSSQL_DB ||
          connectionObj.type == ConnectionSourceType.MYSQL_DB
        ) {
          for (let stream of connectionObj.syncCatalog?.streams) {
            if (stream.config?.selected) tableCount += 1;
          }
        } else if (connectionObj.type == ConnectionSourceType.MONGO_DB) {
          for (let stream of connectionObj.syncCatalog?.streams) {
            if (stream.config?.selected) collectionCount += 1;
          }
        }
      }
      await this.connectionRepository.updateById(connection._id, {
        itemCount: itemCount,
        itemSize: itemSize,
        collectionCount: collectionCount,
        tableCount: tableCount,
        connectionStatus: status,
        lastSyncTime: lastSyncTime,
      });
    }
  }

  /**
   * Use for update connection system stats
   * Get connection item count and sync item size from connection and data crawls
   */
  async syncConnectionSystemStats() {
    logger.info(
      `Connection stats update | SrcDestConnectionService.syncConnectionSystemStats | N/A | pdate connection system stats`,
    );
    let connectionList: {
      itemCount: number;
      itemSize: number;
      collectionCount: number;
      tableCount: number;
    }[] = await this.connectionRepository.aggregate([
      {
        $match: {},
      },
      {
        $project: {
          _id: 0,
          itemCount: 1,
          itemSize: 1,
          collectionCount: 1,
          tableCount: 1,
        },
      },
    ]);
    let totalRecords = 0;
    let totalTables = 0;
    let totalCollections = 0;
    let totalFiles = 0;
    let totalSize = 0;

    for (let connection of connectionList) {
      if (connection.itemCount) totalRecords += connection.itemCount;
      if (connection.tableCount) totalTables += connection.tableCount;
      if (connection.collectionCount) totalCollections += connection.collectionCount;
      if (connection.itemSize) totalSize += connection.itemSize;
    }

    //TODO: need to change below after finished enabling adding storages from frontend
    let initialCrawls = await this.dataCrawlRepository.find({
      where: {
        isInitialCrawl: true,
      },
    });

    if (initialCrawls && Array.isArray(initialCrawls) && initialCrawls.length > 0) {
      for (let storage of initialCrawls) {
        if (storage.crawledFileSize) totalSize += storage.crawledFileSize;
        if (storage.crawledFileCount) totalFiles += storage.crawledFileCount;
      }
    }
    let systemData = await this.systemDataRepository.findOne({});
    await this.systemDataRepository.updateById(systemData?.id, {
      sourceConnectionStats: {
        totalFiles: totalFiles,
        totalRecords: totalRecords,
        totalTables: totalTables,
        totalCollections: totalCollections,
        totalSize: totalSize,
      },
    });
  }

  /**
   * Get connection status from airbyte
   * @param connectionId {string} - connection id
   * @returns {Promise<any>} connection status
   */
  async getConnectionStatus(connectionId?: string) {
    let url = `${AIRBYTE_URL}/api/v1/connections/status`;
    let basicAuthCode = await this.createBasicAuthHeader(AIRBYTE_USER_NAME, AIRBYTE_PASSWORD);

    let response: any = {};
    try {
      response = await Axios({
        url,
        method: 'POST',
        data: {connectionIds: [connectionId]},
        headers: {
          Authorization: basicAuthCode,
        },
      });
    } catch (err) {
      logger.error(
        `Get connection job details | SrcDestConnectionService.getConnectionJobDetails | N/A | connectionId: ${connectionId}, get connection job details failed`,
      );
      return {};
    }

    return response.data;
  }

  /**
   * Get connection history
   * @param connectionId {string} - connection id
   * @param pageIndex {number} - page index
   * @param pageSize {number} - page size
   * @returns {Promise<any>} connection history list
   */
  async getsourceConnectionHistory(connectionId?: string, pageIndex: number = 0, pageSize: number = 10) {
    let connectionHistoryList = await this.connectionHistoryRepository.aggregate([
      {
        $match: {
          connectionId: connectionId,
          $or: [
            {itemCount: {$ne: 0}}, // Match documents where itemCount is not 0
            {itemSize: {$ne: 0}}, // Match documents where itemSize is not 0
          ],
        },
      },
      {$sort: {createdAt: -1}},
      {$skip: pageIndex * pageSize},
      {$limit: pageSize},
    ]);
    return connectionHistoryList;
  }

  /**
   * Get connection details
   * @param connectionId {string} - connection id
   * @returns {Promise<any>} connection details
   */
  async getsourceConnectionDetails(connectionId?: string) {
    if (!connectionId) {
      throw HttpErrors.NotAcceptable(`${DatalakeUserMessages.CONNECTION_FAILED} Connection id is required`);
    }

    let sources: {
      connectionId?: string;
      connectionName?: string;
      sourceType?: ConnectionSourceType;
      connectionStatus?: DataSourceConnectionStatus;
      connectionType?: DataSourceConnectionType;
      itemCount?: number;
      itemSize?: number;
      isEditable?: boolean;
    };

    let connection = await this.connectionRepository.findOne({where: {connectionId: connectionId}});
    if (!connection) {
      throw HttpErrors.NotFound(`${DatalakeUserMessages.CONNECTION_FAILED}, Connection not found`);
    }

    sources = {
      connectionId: connection.connectionId,
      connectionName: connection.sourceName,
      sourceType: connection.type,
      connectionStatus: connection.connectionStatus,
      connectionType: connection.connectionType,
      itemCount: connection.itemCount,
      itemSize: connection.itemSize,
      isEditable: true,
    };

    return sources;
  }

  async generateDataDictionary() {
    try {
      let url = `${PYTHON_HOST}/internal/connection/generate/dictionary/data`;
      logger.info(
        `Generate data dictionary | DataSourceService.generateDataDictionary | N/A | Request sent to generate data dictionary`,
      );
      let response = Axios({
        url,
        method: 'GET',
      });

      // let responseData = response.data;
      // console.log(responseData);
    } catch (err) {
      logger.error(
        `Generate data dictionary | DataSourceService.generateDataDictionary | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.PYTHON_REQUEST_ERROR);
    }
  }

  /**
   * Use to get dictionary data
   * @returns if it is success return dictionary data other wise return failed reason with message
   */
  async getDictionaryData() {
    try {
      let dictionaryObj = await this.dictionaryDataRepository.findOne({});
      if (dictionaryObj) {
        return {
          isSuccess: true,
          data: dictionaryObj.data,
        };
      } else {
        return {
          isSuccess: false,
          message: 'There is no any dictionary data. Generate dictionary data first and try again.',
        };
      }
    } catch (err) {
      logger.error(
        `Get dictionary data | DataSourceService.getDictionaryData | N/A | Error occur getting dictionary data, err = ${err}`,
      );
      return {
        isSuccess: false,
        message: 'Error occur getting dictionary data',
      };
    }
  }

  /**
   * Use to execute mongo query and retrieve data
   * @param fn function like find, aggregate
   * @param query query it's may be object or list according to function
   * @param collectionName Mongodb collection
   * @param sourceName client mapping name
   * @returns results of mongodb execution
   */
  async getDatabaseSchema(sourceName: string, type: ConnectionSourceType): Promise<any> {
    logger.info(
      `Get DB Schema | SrcDestConnectionService.getDatabaseSchema | N/A | Trying to connect db adapter, sourceName: ${sourceName}, type: ${type}`,
    );
    let adapter;
    let sourceConnectionObj = await this.connectionRepository.findOne({
      where: {
        type: type,
        sourceName: sourceName,
      },
    });

    if (
      !sourceConnectionObj ||
      !sourceConnectionObj.connectionCredentials ||
      !sourceConnectionObj.connectionCredentials.host ||
      !sourceConnectionObj.connectionCredentials.port ||
      !sourceConnectionObj.connectionCredentials.username ||
      !sourceConnectionObj.connectionCredentials.password ||
      !sourceConnectionObj.connectionCredentials.database ||
      !sourceConnectionObj.sourceName
    ) {
      logger.error(
        `Get DB Schema | DataSourceService.getDatabaseSchema | N/A | Cannot established db connection. Configuration file: ${sourceConnectionObj}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.DATA_SOURCE_CONNECTION_FAILED} ${sourceName}:${type}`);
    }

    let config = {
      host: sourceConnectionObj?.connectionCredentials?.host,
      port: sourceConnectionObj?.connectionCredentials?.port,
      username: sourceConnectionObj?.connectionCredentials?.username,
      password: sourceConnectionObj?.connectionCredentials?.password,
      database: sourceConnectionObj?.connectionCredentials?.database,
      connectionName: sourceConnectionObj?.sourceName,
    };
    logger.info(
      `Get DB Schema | DataSourceService.getDatabaseSchema | N/A | Mongodb connector configuration: ${JSON.stringify(
        config,
      )}`,
    );
    adapter = await this.adapterFactory.getAdapter(type, config);
    logger.info(`Get DB Schema | DataSourceService.getDatabaseSchema | N/A | Successfully connect to db adapter`);
    await adapter.connect();
    const data = await adapter.getSchema();
    await adapter.disconnect();
    return data;
  }

  /**
   * Get connection list and type for a give sources
   * @param sourceList list of sources to be filtered
   * @returns connection list
   */
  async getConnectionSourceType(sourceList: string[]): Promise<ConnectionListAndTypeRes[]> {
    let param: any[] = [
      {$match: {sourceName: {$in: sourceList}}},
      {
        $project: {
          _id: 0,
          sourceName: 1,
          type: {
            $ifNull: ['$type', ''],
          },
        },
      },
    ];
    let connectionList: ConnectionListAndTypeRes[] = await this.connectionRepository.aggregate(param);
    return connectionList ?? [];
  }
}
export const SRC_DEST_CONNECTION_SERVICE = BindingKey.create<SrcDestConnectionService>(
  'service.SrcDestConnectionService',
);
