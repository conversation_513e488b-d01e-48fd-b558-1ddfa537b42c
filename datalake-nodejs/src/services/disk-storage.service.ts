/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * disk storage service handle the request to the local disk storage in the server, a subclass of the StorageProviderService
 */

/**
 * @class DiskStorage service
 * @description An abstract class to represent a storage bucket of a local disk storage .To handle storage operations such as 
 * upload, download, generate presigned URL. in a polymorphic way.
 * <AUTHOR>
 */

import {BindingKey, BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import * as fs from 'fs';
import jwt from 'jsonwebtoken';
import * as path from 'path';
import readdirp from 'readdirp';
import {promisify} from 'util';
import {v4 as uuidv4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {MetaDataRepository} from '../repositories';
import {DiskStorageConfiguration} from '../settings/disk.configuration';
import {StorageOperationHandlerService} from './storage-operation-handler.service';
import {FileListParams, StorageFileHeader, StorageProviderService} from './storage-provider.service';

const DISK_BUCKET_NAME = DiskStorageConfiguration.DISK_BUCKET_NAME;
const DISK_BASE_URL = DiskStorageConfiguration.DISK_BASE_URL;
const DISK_SECRET_KEY = DiskStorageConfiguration.DISK_SECRET_KEY;
const DISK_BASE_PATH = DiskStorageConfiguration.DISK_BASE_PATH;

export const DISK_FILE_URL_EXPIRE_DURATION = 3 * 24 * 60 * 60; // in seconds
export const DISK_FILE_URL_RENEW_BEFORE_DURATION = 1 * 24 * 60 * 60; // in seconds
export const DISK_CHUNK_FILE_URL_RENEW_BEFORE_DURATION = 1 * 24 * 60 * 60;
export const DISK_UPLOAD_FILE_URL_VALID_DURATION =  12*60*60;

const stat = promisify(fs.stat);
let file_list : string[] = []; //Global list to save the read file list from the bucket (local disk storage)
@injectable({scope: BindingScope.TRANSIENT})
export class DiskStorageService extends StorageProviderService {
  constructor(
    storageOperationHandlerService: StorageOperationHandlerService,
    @repository('MetaDataRepository') public metaDataRepository: MetaDataRepository,
  ) {
    super(metaDataRepository);
    this.storageOperationHandlerService = storageOperationHandlerService;

  }

  /**
   * Generates Presigned URL to download a file in the  local storage disk
   * @param key {string} path of the file
   * @param expires {number} time in seconds till the file experies
   * @param bucket {string} abosolute path to the folder in the local disk storage that acts as our bucket
   * @returns presigned URL
  */
  async generateObjectUrl(key: string, expires = DISK_FILE_URL_EXPIRE_DURATION, bucket?: string): Promise<string> {
    const bucketName = bucket ? bucket : DISK_BUCKET_NAME;
    const expiresAt = Math.floor(Date.now() / 1000) + expires;
    const payload = {
      bucket: bucketName,
      keyValue:key,
      exp: expiresAt,
    };
    //Genrating secret token
    const token = jwt.sign(payload, DISK_SECRET_KEY, { algorithm: 'HS256' });
    const apiPath = '/api/download/file/localDisk';
    const queryString = `token=${encodeURIComponent(token)}`;
    const preSignedUrl = `${DISK_BASE_URL}${apiPath}?${queryString}`;
    return preSignedUrl;
}

/**
   * Checks if the folder path given as bucket actually exists in the local storage system
   * @param bucketName {string} abosolute path to the folder in the local disk storage that acts as our bucket
   * @returns an error if access denied or if the path doesnt exist else returns null if the path exists
  */
async checkBucketExistence(bucketName: string): Promise<{ errMessage?: string }> {
  return new Promise((resolve) => {
    const returnData: { errMessage?: string } = {};
    const pathToCheck = path.normalize(DISK_BASE_PATH + bucketName);

    fs.access(pathToCheck, fs.constants.F_OK, (err) => {
      if (err) {
        if (err.code === 'ENOENT') {
          returnData.errMessage = `Path does not exist: ${pathToCheck}`;
        } else if (err.code === 'EACCES') {
          returnData.errMessage = `Permission denied: ${pathToCheck}`;
        } else {
          returnData.errMessage = `Error accessing path: ${pathToCheck}`;
        }
        resolve(returnData);
      } else {
        // Path exists
        resolve(returnData);
      }
    });
  });
}

/**
   * When a bucket name is given then returns infomation about the files in that directory
   * @param params {FileListParams} includes bucket name which is the  absolute path to folder we are considering as the bucket
   * @returns  the file list with data of filekey ,filename, last modified, file size
  */
async  getFileList(params: FileListParams) {
  let returnData = {
    isSuccess: false,
    errMessage: 'File list failed',
    fileList: <StorageFileHeader[]>[],
    nextPageRef: {
      NextContinuationToken: '',
      ContinuationToken: '',
    },
    IsTruncated: false,
  };
    //If bucket name is not given then the defuaklt bucket is taken
    const bucketName = params.Bucket ? params.Bucket : DISK_BUCKET_NAME;
    if (!bucketName) {
      returnData.errMessage = 'No bucket given';
      return returnData;
    }
    //Checking if the bucket file acctually exists
    try {
      const result = await this.checkBucketExistence(bucketName);
      if (result.errMessage) {
        console.error(result.errMessage);
        returnData.errMessage = result.errMessage;
        return returnData;
      }
    } catch (error) {
      returnData.errMessage = 'Error checking bucket existence:';
      return returnData;
    }

    try {
      //Reading the and getting the paths of the file in the bucks
      logger.debug('Reading file list ')
      logger.debug(params)


      let lowerLimit = 0; //Llower limit for processing the file path list
      let upperLimit = 0;//upper limit for processing the file path list
      if (!params.ContinuationToken){ //if no continuation is given then starting the process
        lowerLimit = 0;
        upperLimit = 1000;
        file_list = await new Promise((resolve, reject) => { //saving the file structure
          const files: string[] = [];
          let crawlDirectory = DISK_BASE_PATH + bucketName;
          if (params.changedDirectory){ //if partial crawl triggered then taking the that directory
            crawlDirectory = params.changedDirectory;
          }
          let stream = readdirp(crawlDirectory, { type: 'files' });

          stream.on('data', (entry: readdirp.EntryInfo) => {
            files.push(entry.fullPath);
          });

          stream.on('end', () => {
            resolve(files);
          });

          stream.on('error', (err: Error) => {
            reject(err);
          });
        });
      }
      else{
        lowerLimit = Number(params.ContinuationToken); //upadting the lower limit with the continuation
        upperLimit = lowerLimit + 1000;
      }
      logger.debug('Read file list len: ' + file_list.length)
      if (upperLimit > file_list.length) //stopping condition
      {
        upperLimit = file_list.length;
      }
      
      //From the paths extracted getting the infromation
      if (!params.updatedAt)
      {
        params.updatedAt = new Date(0);
      }

      let bucketBasePath = DISK_BASE_PATH + bucketName; //Absolute path of the bucket location
      for (let i = lowerLimit; i < upperLimit; i++) {
        const filePath = file_list[i];
        const stats = await stat(filePath);
          const fileHeader: StorageFileHeader = {
            fileKey: path.relative(bucketBasePath, filePath), // Path relative to the bucket
            fileName: path.basename(filePath), // File name
            fileLastModified: stats.mtime,
            fileSize: stats.size,
          };
          returnData.fileList.push(fileHeader);
          logger.debug('Crawled file ' + filePath)
      }
      if (upperLimit == file_list.length) //Stopping another round
      {
        returnData.nextPageRef.NextContinuationToken = '';
        file_list.length = 0;
        returnData.IsTruncated = false;
      }
      else{
        returnData.nextPageRef.NextContinuationToken = upperLimit.toString();
        returnData.IsTruncated = true;
      }

      returnData.nextPageRef.ContinuationToken = lowerLimit.toString();
      //Since the process was successful setting up the return
      returnData.isSuccess = true;
      returnData.errMessage = '';

       // Assuming no pagination handling in this snippet

      return returnData;
    } catch (error) {
      console.error(`File list failed | ${error}`);
      returnData.errMessage = error?.message ? error.message : 'File list failed';
      return returnData;
    }
  }

  /**
   * Initalizes the upload proceedure
   * @param key {string} File path to saving the file relative to the bucket
   * @param expires {number} number of seconds till expiring
   * @param bucket{string} includes bucket name which is the  absolute path to folder we are considering as the bucket
   * @returns  feildID (a random number its not nessesary) fieldkey same as the key
  */
  async initializeMultipartUpload(key: string, expires = DISK_UPLOAD_FILE_URL_VALID_DURATION, bucket?: string) {
    const bucketName = bucket ? bucket : DISK_BUCKET_NAME;
    const params = {
      bucket: bucketName,
      Key: key,
    };
    const uniqueID = uuidv4();
    return {fileId:uniqueID, fileKey:key, isExisting: false};
  }

  /**
   * Generating upload presigned URL to the local disk storage
   * @param key {string} File path to saving the file relative to the bucket
   * @param fieldId {number} random number its not needed for the implentation
   * @param parts {number} number of parts but not required
   * @param bucket{string} includes bucket name which is the  absolute path to folder we are considering as the bucket
   * @returns  the file list of presigned URL it will always only have one URL
  */
  async generateMultipartPreSignedUrls(
    key: string,
    fileId: number,
    parts: number,
    bucket?: string,
    contentType?: string,
    isDisableMultipart?: boolean
  ): Promise<{ parts: { signedUrl: string; PartNumber: number; }[] }> {
    try {
      const expiresAt = Math.floor(Date.now() / 1000) + DISK_CHUNK_FILE_URL_RENEW_BEFORE_DURATION;

      const bucketName = bucket ? bucket : DISK_BUCKET_NAME;
      const params = {
        bucket: bucketName,
        exp: expiresAt,
        keyValue: key,
        UploadId: fileId,
        parts: 1,
      };
      // Sign the params with JWT
      const token = await jwt.sign(params, DISK_SECRET_KEY, { algorithm: 'HS256' });

      const apiPath = '/api/fileUpload/saveToLocalDisk';
      const queryString = `token=${encodeURIComponent(token)}`;
      const preSignedUrl = `${DISK_BASE_URL}${apiPath}?${queryString}`;
      parts = 1;
      // Generate presigned URLs for each part
      const signedUrls = Array(parts).fill(preSignedUrl); // Generate an array of the same presigned URL for each part

      // Assign part numbers to each signed URL
      const partSignedUrlList = signedUrls.map((signedUrl, index) => ({
        signedUrl: signedUrl,
        PartNumber: index + 1,
      }));

      // Return the list of part signed URLs
      return { parts: partSignedUrlList };
    } catch (error) {
      // Handle errors
      console.error('Error generating presigned URL:', error);
      return { parts: [] }; // Return an empty parts array on error
    }
  }

  /**
   * When a bucket name is given then returns infomation about the files in that directory
   * @param key {string} File path to saving the file relative to the bucket
   * @param fieldId {number} random number its not needed for the implentation
   * @param parts {number} number of parts but not required
   * @returns  the file list with data of filekey ,filename, last modified, file size
  */
  async finalizeMultipartUpload(
    key: string,
    fileId: string,
    parts: any,
    bucket?: string,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    currentUserProfile?: UserProfileDetailed,
  ) {
    
    let bucketName = bucket ? bucket : DISK_BUCKET_NAME;

    try {
      if (!bucketName ) {
        throw new Error('Missing bucket ');
      }
      if (!key) {
        throw new Error('Missing  key');
      }
      // Ensure the directory exists before attempting to save the file
      const filePath = path.join(DISK_BASE_PATH + bucketName, key);
      if (!fs.existsSync(filePath)) {
        logger.warn(`finalizeMultipartUpload: Path ${filePath} doesn't exist`)
        return { isSuccess: false };
      } else {
        return { isSuccess: true };
      }
    }
    catch (error) {
      logger.error(
        `File chunk upload generate urls finalize upload Failed | diskStorageService.finalizeMultipartUpload | key: ${key} | upload failde: ${error}`,
      );
      return {isSuccess: false};
    }
  }
}
export const DISK_STORAGE_SERVICE = BindingKey.create<DiskStorageService>('service.diskStorage');

