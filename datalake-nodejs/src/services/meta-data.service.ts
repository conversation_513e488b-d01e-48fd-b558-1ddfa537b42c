/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the MetaData insert, modify, read related logics
 */

/**
 * @class MetaDataService
 * purpose of this service is to perfome the MetaData insert, modify related logics
 * @description updating metadata
 * <AUTHOR> channa
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {DataObject, Where, repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  ACTIVITY_TYPE,
  Analytics,
  AnalyticsUpdateAgg,
  AnnotationProjectList,
  ContentType,
  DatalakeSelection,
  DatalakeSelectionRequest,
  DatasetLabelAggregate,
  DatasetVersionList,
  GetJobPartFailedReasonListForObjectRemoveRes,
  GetTagsAndCustomMetaDataOfSelectedCollectionsRes,
  LabelAnalytics,
  MetaData,
  MetaDataInputFeedObject,
  MetaDataStudioResponseType,
  OBJECT_STATUS,
  ObjectCount,
  RemoveCollectionIfPossibleRes,
  RemoveObjectsFromCollectionsAgg,
  RemoveObjectsJobData,
  SearchQueryRootGroup,
  SearchQuerySubGroup,
  UpdateJobConfig,
  VerificationStatusCount,
} from '../models';
import {JobSpecificDetails, JobStatus, JobType} from '../models/job.model';
import {
  DatalakeSelectionRepository,
  MetaDataRepository,
  MetadataHistoryRepository,
  QueryOptionRepository,
  StorageMappingRepository,
} from '../repositories';
import {JobRepository} from '../repositories/job.repository';
import {FLOWS, ObjectRemoveMeSsages, STORAGE_SYSTEM_FOLDER, SYSTEM_BOT, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DEFAULT_ITEM_SORT_IN_COLLECTION} from '../settings/global-field-config';
import {
  SuccessResponse,
  convertValuesToArrays,
  getLabelWiseCount,
  getObjectTypeName,
  isValidObjectId,
} from '../settings/tools';
import {DATALAKE_TRASH_SERVICE, DatalakeTrashService} from './datalake-trash.service';
import {JOB_SERVICE, JobService} from './job.service';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {STATS_CALCULATION_SERVICE, StatsCalculationService} from './stats-calculation.service';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import {SYSTEM_STATS_SERVICE, SystemStatsService} from './system-stats.service';
dotenv.config();
const DEFAULT_BUCKET_NAME = process.env.DEFAULT_BUCKET_NAME;

@injectable({scope: BindingScope.TRANSIENT})
export class MetaDataService {
  constructor(
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(SYSTEM_STATS_SERVICE)
    private systemStatsService: SystemStatsService,
    @inject(DATALAKE_TRASH_SERVICE)
    private datalakeTrashService: DatalakeTrashService,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @repository(MetadataHistoryRepository)
    public metaDataHistoryRepository: MetadataHistoryRepository,
    @inject(STATS_CALCULATION_SERVICE) private statsCalculationService: StatsCalculationService,
    @repository(JobRepository) private jobRepository: JobRepository,
    @repository('StorageMappingRepository') private storageMappingRepository: StorageMappingRepository,
  ) {}

  /**
   * This method is used to remove object from a collection(image, video or other) or heads
   * @param filter {ObjectRemoveBody} filter object for remove objects from collection
   * @param teamId {string} team id
   * @param selectionTag {string} selection tag
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @returns {Promise<{jobId: string}>} return job id
   */
  async removeObjects(
    filter: DatalakeSelectionRequest,
    teamId: string,
    selectionTag: string,
    currentUserProfile: UserProfileDetailed,
    config?: UpdateJobConfig,
  ): Promise<{jobId: string}> {
    let datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };

    //get match query for selection
    let aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );

    if (!aggregateQuery?.matchQuery) throw new HttpErrors.UnprocessableEntity('Invalid selection query');
    let objectType: ContentType = aggregateQuery.objectType;

    if (objectType == ContentType.DATASET) {
      throw new HttpErrors.UnprocessableEntity('Cannot remove objects from dataset');
    }

    const sessionId: string = `remove_object-${uuidV4()}`;
    let metaDataTypeName: string = getObjectTypeName(objectType);
    let jobName: string = `Remove ${metaDataTypeName}s`;
    let jobDetails: JobSpecificDetails = {
      contentType: objectType,
      startedAt: new Date(),
      selectionId: selectionTag,
    };

    if (isValidObjectId(filter.collectionId)) jobDetails.collectionId = new ObjectId(filter.collectionId);

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      currentUserProfile?.userId,
      teamId,
      currentUserProfile?.userName,
      JobType.ObjectRemove,
      0,
      JobStatus.queued,
      jobDetails,
    );

    let existingSession = await this.jobRepository.findOne({
      where: {
        'jobSpecificDetails.sessionId': sessionId,
        jobType: JobType.ObjectRemove,
      },
    });

    const jobId: string = existingSession?._id ?? '';
    if (typeof config == 'object') config.objectRemoveJobId = jobId;
    const childObjectType: ContentType[] = [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER];

    let jobData: RemoveObjectsJobData = {jobName: jobName, sessionId: sessionId};

    if (childObjectType.includes(objectType)) {
      //check if collection id is valid
      if (!filter.collectionId || !isValidObjectId(filter.collectionId))
        throw new HttpErrors.UnprocessableEntity('Valid collection id was not provided');

      this.handleObjectRemoveFromCollection(
        filter.collectionId,
        aggregateQuery.matchQuery,
        teamId,
        objectType,
        jobData,
        currentUserProfile,
        config,
      );
    } else {
      this.handleMultipleCollectionRemove(
        aggregateQuery.matchQuery,
        teamId,
        objectType,
        jobData,
        currentUserProfile,
        config,
      );
    }

    return {jobId: jobId};
  }

  /**
   * This method is used to remove collection if possible and delete the collection
   * @param matchQuery {any} match query
   * @param teamId {string} team id
   * @param objectType {ContentType} object type
   * @param jobData {RemoveObjectsJobData} It includes job name and session id of the job remove
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @returns {Promise<SuccessResponse>} return whether success or not
   */
  async handleMultipleCollectionRemove(
    matchQuery: any,
    teamId: string,
    objectType: ContentType,
    jobData: RemoveObjectsJobData,
    currentUserProfile: UserProfileDetailed,
    config?: UpdateJobConfig,
  ): Promise<SuccessResponse> {
    const {sessionId, jobName} = jobData;
    const {userId, userName} = currentUserProfile;
    let paramsGetIdList: any = [
      {$match: matchQuery},
      {
        $group: {
          _id: null,
          idList: {$push: '$_id'},
        },
      },
    ];

    let tempIdList = await this.metaDataRepository.aggregate(paramsGetIdList);
    let idList: string[] = [];

    if (Array.isArray(tempIdList) && tempIdList.length > 0) {
      idList = tempIdList[0].idList ? tempIdList[0].idList : [];
    }
    let totalObjectCount: number = idList.length;
    //Get collection id list
    let collectionIdList: ObjectId[] = idList.map(id => new ObjectId(id));
    let success: boolean = true;

    let successCount: number = 0;
    let failedCount: number = 0;
    let progress: number = 0;
    let collectionRemoveWarningCount: number = 0;
    let jobStatus: JobStatus = JobStatus.inProgress;
    let removeFailedObjectIdList: string[] = [];
    let iteration: number = 0;
    let removeAvailableCount: number = 0;
    let successRemovedFileNameList: string[] = [];
    let failedRemovedFileNameList: string[] = [];
    let jobPartFailedReasonList: string[] = [];

    for (let id of idList) {
      iteration++;
      let collectionData: Pick<MetaData, 'name'> = await this.metaDataRepository.findById(id, {fields: {name: true}});
      const {isSuccess, error, warning, removeUnavailable} = await this.removeCollectionIfPossible(
        id,
        objectType,
        teamId,
        collectionIdList,
      );

      let name: string = collectionData.name;
      if (!removeUnavailable) {
        removeAvailableCount++;
      } else {
        collectionRemoveWarningCount++;
        jobPartFailedReasonList.push(`${name} has object only belong to it`);
      }

      if (!isSuccess) {
        failedRemovedFileNameList.push(name);
        failedCount++;
        success = false;
      } else {
        successRemovedFileNameList.push(name);
        successCount++;
      }
      let jobDetails: JobSpecificDetails = {
        totalObjectToBeRemovedCount: totalObjectCount,
        jobPartFailedReasonList: jobPartFailedReasonList,
        objectRemovedFailedCount: failedCount,
        objectRemoveSuccessCount: successCount,
        failedRemovedFileNameList: failedRemovedFileNameList,
        successRemovedFileNameList: successRemovedFileNameList,
      };

      if (config?.objectRemoveJobId) jobDetails.objectRemoveJobId = new ObjectId(config.objectRemoveJobId);

      if (error) {
        jobStatus = JobStatus.failed;
        jobDetails.error = error;
        removeFailedObjectIdList.push(id);
      }
      if (warning) {
        jobDetails.warning = warning;
      }

      jobDetails.collectionRemoveWarningCount = collectionRemoveWarningCount;
      jobDetails.removeFailedObjectIdList = removeFailedObjectIdList;
      //In the final iteration total file count is equal to remove available count
      let totalFileCount: number = iteration == idList.length ? removeAvailableCount : totalObjectCount;

      progress = (successCount / totalFileCount) * 100;

      if (iteration == idList.length) jobDetails.finishedAt = new Date();

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        parseFloat(progress.toFixed(2)),
        jobStatus,
        jobDetails,
      );

      let mergeProgress: number = parseFloat(progress.toFixed(2)) / 2 + 50;
      await this.updateMergeJob(config, jobDetails, jobStatus, mergeProgress);
    }

    if (successCount == 0) {
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        0,
        JobStatus.failed,
        {finishedAt: new Date(), warning: ObjectRemoveMeSsages.NO_COLLECTION_REMOVED},
      );

      await this.updateMergeJob(config, {warning: ObjectRemoveMeSsages.NO_COLLECTION_REMOVED});
    }
    return {isSuccess: success};
  }

  /**
   * This method is used to remove objects from a collection, if every object has more than one collection and delete the collection
   * @param collectionId {string} collection id
   * @param objectType {ContentType} object type
   * @param teamId {string} team id
   * @param collectionIdList {ObjectId[]} collection id list
   * @returns {Promise<SuccessResponse>} return whether success or not
   */
  async removeCollectionIfPossible(
    collectionId: string,
    objectType: ContentType,
    teamId: string,
    collectionIdList: ObjectId[],
  ): Promise<RemoveCollectionIfPossibleRes> {
    let aggregateParam: any[] = [
      {$match: {vCollectionIdList: new ObjectId(collectionId)}},
      {$unwind: '$vCollectionIdList'},
      {
        $lookup: {
          from: 'MetaData',
          let: {vCollectionId: '$vCollectionIdList'},
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$vCollectionId'],
                },
                objectType: objectType,
                _id: {$nin: collectionIdList},
              },
            },
            {$project: {_id: 1}},
          ],
          as: 'collectionIdList',
        },
      },
      {$addFields: {collectionIdList: {$first: '$collectionIdList._id'}}},
      {
        $group: {
          _id: '$_id',
          collectionIdList: {
            $addToSet: '$collectionIdList',
          },
        },
      },
      {$match: {collectionIdList: []}},
      {$limit: 1},
    ];
    let objectIdList: {_id: string; collectionIdList: string[]}[] = await this.metaDataRepository.aggregate(
      aggregateParam,
    );

    if (objectIdList.length == 1) {
      return {isSuccess: false, warning: ObjectRemoveMeSsages.CAN_NOT_REMOVE_COLLECTION, removeUnavailable: true};
    }

    try {
      let metaObjectParam: Record<string, any>[] = [
        {$match: {vCollectionIdList: new ObjectId(collectionId)}},
        {$project: {_id: 1}},
      ];
      let metaObjList: {_id: string}[] = await this.metaDataRepository.aggregate(metaObjectParam);
      let bulkWriteArray: any[] = [
        {
          updateMany: {
            filter: {
              vCollectionIdList: new ObjectId(collectionId),
              collectionId: new ObjectId(collectionId),
            },
            update: {$unset: {collectionId: ''}, $set: {updatedAt: new Date()}},
          },
        },
        {
          updateMany: {
            filter: {
              vCollectionIdList: new ObjectId(collectionId),
            },
            update: {
              $pull: {
                vCollectionIdList: new ObjectId(collectionId),
                parentList: new ObjectId(collectionId),
              },
              $set: {updatedAt: new Date()},
            },
          },
        },
        {
          updateMany: {
            filter: {
              parentList: new ObjectId(collectionId),
            },
            update: {
              $pull: {
                parentList: new ObjectId(collectionId),
              },
              $set: {updatedAt: new Date()},
            },
          },
        },
      ];

      await this.metaDataRepository.bulkWrite(bulkWriteArray);

      if (Array.isArray(metaObjList) && metaObjList.length > 0) {
        for (let metaObj of metaObjList) {
          await this.handleAllowedUsersForObjectRemove(metaObj._id.toString());
        }
      }

      await this.handleCollectionDeleteInObjectRemove(collectionId, teamId);
      return {isSuccess: true, removeUnavailable: false};
    } catch (err) {
      logger.error(
        `Remove collection if possible | DatalakeExplorerService.removeCollectionIfPossible
        | collection Id: ${collectionId}|
         Error in recalculation collection  stats: `,
        err,
      );
      return {isSuccess: false, removeUnavailable: false, error: err};
    }
  }

  /**
   * This method is used to remove objects of a collection and recalculate stats, meta fields and tags summary
   * @param collectionId {string} collection id
   * @param matchQuery {any} match query
   * @param teamId {string} team id
   * @param objectType {ContentType} object type
   * @param jobData {RemoveObjectsJobData} It includes job name and session id of the job remove
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @returns {Promise<SuccessResponse>} return whether success or not
   */
  async handleObjectRemoveFromCollection(
    collectionId: string,
    matchQuery: any,
    teamId: string,
    objectType: ContentType,
    jobData: RemoveObjectsJobData,
    currentUserProfile: UserProfileDetailed,
    config?: UpdateJobConfig,
  ): Promise<SuccessResponse> {
    const {sessionId, jobName} = jobData;
    const {userId, userName} = currentUserProfile;
    const parentContentTypeObj: Partial<Record<ContentType, ContentType>> = {
      [ContentType.IMAGE]: ContentType.IMAGE_COLLECTION,
      [ContentType.VIDEO]: ContentType.VIDEO_COLLECTION,
      [ContentType.OTHER]: ContentType.OTHER_COLLECTION,
    };

    let group: Record<keyof Required<RemoveObjectsFromCollectionsAgg>, any> = {
      _id: '$_id',
      collectionId: {$first: '$collectionId'},
      name: {$first: '$name'},
    };

    let aggregateParam: any[] = [
      {
        $match: matchQuery,
      },
      {$unwind: '$vCollectionIdList'},
      {
        $lookup: {
          from: 'MetaData',
          let: {vCollectionId: '$vCollectionIdList'},
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$vCollectionId'],
                },
                objectType: parentContentTypeObj[objectType],
                _id: {$ne: new ObjectId(collectionId)},
              },
            },
            {$project: {_id: 1}},
          ],
          as: 'collectionIdList',
        },
      },
      {$match: {collectionIdList: {$ne: []}}},
      {
        $group: group,
      },
    ];

    let objectIdList: RemoveObjectsFromCollectionsAgg[] = await this.metaDataRepository.aggregate(aggregateParam);

    let {jobPartFailedReasonList, failedRemovedFileNameList, totalObjectCount} =
      await this.getJobPartFailedReasonListForObjectRemove(matchQuery, objectIdList, collectionId);

    if (!Array.isArray(objectIdList) || objectIdList.length == 0) {
      let jobDetails: JobSpecificDetails = {
        jobFailedReasonList: jobPartFailedReasonList,
        failedRemovedFileNameList: failedRemovedFileNameList,
        totalObjectToBeRemovedCount: totalObjectCount,
        objectRemovedFailedCount: failedRemovedFileNameList.length,
        warning: ObjectRemoveMeSsages.NO_OBJECT_TO_REMOVE_FROM_COLLECTION,
        finishedAt: new Date(),
      };

      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        0,
        JobStatus.failed,
        jobDetails,
      );

      await this.updateMergeJob(config, jobDetails);

      return {isSuccess: false, message: 'Can not remove objects from collection'};
    }

    let successCount: number = 0;
    let failedCount: number = 0;
    let progress: number = 0;
    let jobStatus: JobStatus = JobStatus.inProgress;
    let removeAvailableCount: number = objectIdList.length;
    let successRemovedFileNameList: string[] = [];

    for (let object of objectIdList) {
      const {isSuccess, error} = await this.updateMetaDataWhenRemoveFromCollection(object, collectionId, objectType);
      if (isSuccess) {
        successCount++;
        successRemovedFileNameList.push(object.name);
      } else {
        failedRemovedFileNameList.push(object.name);
        jobStatus = JobStatus.failed;
        failedCount++;
      }

      let jobDetails: JobSpecificDetails = {
        totalObjectToBeRemovedCount: totalObjectCount,
        objectRemovedFailedCount: failedCount,
        objectRemoveSuccessCount: successCount,
        failedRemovedFileNameList: failedRemovedFileNameList,
        successRemovedFileNameList: successRemovedFileNameList,
      };

      if (error) {
        jobPartFailedReasonList.push(`error in ${object.name}`);
        jobDetails.error = error;
      }
      jobDetails.jobPartFailedReasonList = jobPartFailedReasonList;
      progress = (successCount / removeAvailableCount) * 100;
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        parseFloat(progress.toFixed(2)),
        jobStatus,
        jobDetails,
      );

      let mergeProgress: number = parseFloat(progress.toFixed(2)) / 2 + 50;
      await this.updateMergeJob(config, jobDetails, jobStatus, mergeProgress);
    }

    let bulkWriteArray: any[] = [
      {
        updateOne: {
          filter: {
            _id: new ObjectId(collectionId),
            isFeatureGraphPending: {$exists: true},
            objectType: {$in: [ContentType.IMAGE_COLLECTION, ContentType.DATASET]},
          },
          update: {$set: {isFeatureGraphPending: true, updatedAt: new Date()}},
        },
      },
      // set isMetaFieldsPropagationRequired flag true to calculate meta fields and tags summary
      {
        updateOne: {
          filter: {
            _id: new ObjectId(collectionId),
            objectType: {
              $in: [
                ContentType.IMAGE_COLLECTION,
                ContentType.VIDEO_COLLECTION,
                ContentType.OTHER_COLLECTION,
                ContentType.DATASET,
              ],
            },
          },
          update: {
            $set: {
              isMetaFieldsPropagationRequired: true,
              updatedAt: new Date(),
              'metDataUpdateInfo.metaUpdateAt': new Date(),
            },
          },
        },
      },
    ];

    await this.metaDataRepository.bulkWrite(bulkWriteArray);
    await this.updateCollectionUniqueAnnotationProjectAndDateSetList(collectionId);
    //If all selected objects can not be removed
    if (totalObjectCount != objectIdList.length) {
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        progress,
        jobStatus,
        {
          warning: ObjectRemoveMeSsages.ALL_SELECTED_OBJECT_NOT_REMOVE_FROM_COLLECTION,
          finishedAt: new Date(),
        },
      );
    }

    /**
     * Recalculate stats, meta fields and tags summary and regenerate query options
     */
    const {isSuccess, error} = await this.reCalculateStatsWhenRemoveFromCollection(collectionId, teamId);
    if (!isSuccess) {
      await this.jobService.createOrUpdateJob(
        jobName,
        sessionId,
        userId,
        teamId,
        userName,
        JobType.ObjectRemove,
        progress,
        JobStatus.failed,
        {error: error},
      );
    }
    await this.updateMergeJob(config, {error: error});

    return {isSuccess: isSuccess};
  }

  /**
   * This method is used to update annotationProjectList and dataSetList according to the children(get unique annotation project list and dataset list)
   * @param collectionId {string} collection id
   * @returns {Promise<void>} return void
   */
  async updateCollectionUniqueAnnotationProjectAndDateSetList(collectionId: string): Promise<void> {
    let annoParam: Record<string, any>[] = [
      {
        $match: {
          vCollectionIdList: new ObjectId(collectionId),
        },
      },
      {$unwind: '$annotationProjectList'},
      {
        $group: {
          _id: '$annotationProjectList.id',
          annotation: {$first: '$annotationProjectList'},
        },
      },
    ];

    let annotationList: {_id: string; annotation: AnnotationProjectList}[] | undefined =
      await this.metaDataRepository.aggregate(annoParam);

    let dataSetParam: Record<string, any>[] = [
      {
        $match: {
          vCollectionIdList: new ObjectId(collectionId),
        },
      },
      {$unwind: '$datasetVersionList'},
      {
        $group: {
          _id: '$datasetVersionList.datasetVersionId',
          datasetVersionList: {
            $first: {
              datasetVersionId: '$datasetVersionList.datasetVersionId',
              datasetMetaId: '$datasetVersionList.datasetMetaId',
              datasetGroupId: '$datasetVersionList.datasetGroupId',
              datasetGroupName: '$datasetVersionList.datasetGroupName',
              isNew: '$datasetVersionList.isNew',
            },
          },
        },
      },
    ];

    let dataSetList: {_id: string; datasetVersionList: DatasetVersionList}[] | undefined =
      await this.metaDataRepository.aggregate(dataSetParam);

    let update = {} as MetaData;
    if (Array.isArray(annotationList)) {
      let formatList: AnnotationProjectList[] = annotationList.map(obj => obj.annotation);
      update.annotationProjectList = formatList;
    }
    if (Array.isArray(dataSetList)) {
      let formatDatasetList: DatasetVersionList[] = dataSetList.map(obj => obj.datasetVersionList);
      update.datasetVersionList = formatDatasetList;
    }

    if (JSON.stringify(update) != '{}') {
      await this.metaDataRepository.updateById(collectionId, update);
    }
  }

  /**
   * This method is used to delete collection id and its query options. also delete storage path if there is no object with that storage path
   * @param collectionId {string} collection id
   * @param teamId {string} team id
   * @returns {Promise<void>} return void
   */
  async handleCollectionDeleteInObjectRemove(collectionId: string, teamId: string): Promise<void> {
    let collectionObj: MetaData = await this.metaDataRepository.findById(collectionId);
    // delete all QueryOption related to collection id
    await this.queryOptionRepository.deleteAll({
      collectionId: collectionId,
      teamId: teamId,
    });

    if (collectionObj.storagePath) {
      let storagePath = collectionObj.storagePath;
      let storagePrefixPath = collectionObj.storagePrefixPath ? collectionObj.storagePrefixPath : '';
      let prefixPath = storagePrefixPath ? `${storagePrefixPath}/${storagePath}` : storagePath;

      // find child object with storage path start with prefix path
      let isExists = await this.metaDataRepository.findOne({
        where: {
          storagePath: {
            $regex: new RegExp(`^${prefixPath}`),
          },
          objectType: {
            inq: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER],
          },
        },
      });

      // if there is no object start with prefixPath then delete storage path from storageMappingRepository
      if (!isExists) {
        await this.storageMappingRepository.deleteAll({
          collectionStoragePath: storagePath,
        });
      }
    }

    await this.metaDataRepository.deleteAll({_id: new ObjectId(collectionId)});
  }

  /**
   * this method return list of job partial failed reason list, failed removed file name list and total object to be removed
   * @param matchQuery {any} match query
   * @param availableIdList {RemoveObjectsFromCollectionsAgg[]} available id list(available id list means objects that can be removed)
   * @param collectionId {string} collection id
   * @returns return list of job partial failed reason list, failed removed file name list and total object to be removed
   */
  async getJobPartFailedReasonListForObjectRemove(
    matchQuery: string,
    availableIdList: RemoveObjectsFromCollectionsAgg[],
    collectionId: string,
  ): Promise<GetJobPartFailedReasonListForObjectRemoveRes> {
    let collectionData: Pick<MetaData, 'name'> = await this.metaDataRepository.findById(collectionId, {
      fields: {name: true},
    });
    let availableIdListString: string[] = availableIdList.map(obj => obj._id.toString());

    let param: Record<string, any>[] = [{$match: matchQuery}, {$project: {_id: 1, name: 1}}];

    let allObjectList: {_id: string; name: string}[] = await this.metaDataRepository.aggregate(param);

    let jobPartFailedReasonList: string[] = allObjectList
      .filter(obj => !availableIdListString.includes(obj._id.toString()))
      .map(obj => `${obj.name} only belongs to ${collectionData.name}`);

    let failedRemovedFileNameList: string[] = allObjectList
      .filter(obj => !availableIdListString.includes(obj._id.toString()))
      .map(obj => obj.name);

    return {
      jobPartFailedReasonList: jobPartFailedReasonList,
      failedRemovedFileNameList: failedRemovedFileNameList,
      totalObjectCount: allObjectList.length,
    };
  }

  /**
   * This method is used to update meta data when remove object from collection (remove collection Id, vCollectionIdList and parentList)
   * @param metaObj {RemoveObjectsFromCollectionsAgg} meta data object (collectionId, _id, vCollectionIdList and parentList)
   * @param collectionId {string} collection id
   * @param objectType {ContentType} object type of the meta data
   * @returns {Promise<{isSuccess: boolean}>} return whether success or not
   *
   */
  async updateMetaDataWhenRemoveFromCollection(
    metaObj: RemoveObjectsFromCollectionsAgg,
    collectionId: string,
    objectType: ContentType,
  ): Promise<SuccessResponse> {
    let updateObj: Record<string, any> = {};

    try {
      if (metaObj.collectionId && metaObj.collectionId.toString() == collectionId) {
        updateObj['$unset'] = {collectionId: ''};
      }

      let pullObj: Record<keyof MetaData, any> = {
        vCollectionIdList: new ObjectId(collectionId),
        parentList: new ObjectId(collectionId),
      };

      updateObj['$pull'] = pullObj;
      let res = await this.metaDataRepository.updateAllMetaData({_id: new ObjectId(metaObj._id)}, updateObj);
      if (res.modifiedCount == 0) return {isSuccess: false};
      // set isFeatureGraphPending flag true to regenerate feature graph

      if (objectType == ContentType.VIDEO) {
        //remove video collection id from all image collection and images belong
        let updateMatchFilter: Record<keyof MetaData, any> = {parentList: new ObjectId(metaObj._id)};
        await this.metaDataRepository.updateAllMetaData(updateMatchFilter, {
          $pull: {
            parentList: new ObjectId(collectionId),
          },
        });
      }
      await this.handleAllowedUsersForObjectRemove(metaObj._id.toString());
      return {isSuccess: true};
    } catch (err) {
      logger.error(
        `Update meta data when removing object | MetaDataService.updateMetaDataWhenRemoveFromCollection |
          | collection Id: ${collectionId} | Error in updateMetaDataWhenRemoveFromCollection : ${err}`,
        err,
      );
      return {isSuccess: false, error: err};
    }
  }

  /**
   * If collaborator is not exist in allowedUserIdList of the at least one collection, remove it from allowedUserIdList
   * @param metObjectId {string} meta data object id
   * @returns {Promise<void>} return void
   */
  async handleAllowedUsersForObjectRemove(metObjectId: string): Promise<void> {
    let metaData = await this.metaDataRepository.findById(metObjectId);
    let collectionIdList: ObjectId[] = metaData?.vCollectionIdList
      ? metaData.vCollectionIdList.map(id => new ObjectId(id))
      : [];

    let allowedUserIdList: ObjectId[] = metaData?.allowedUserIdList ?? [];

    if (allowedUserIdList.length == 0) return;

    for (let userId of allowedUserIdList) {
      let matchFilter: Record<string, any> = {
        _id: {$in: collectionIdList},
        allowedUserIdList: new ObjectId(userId),
      };

      let param: Record<string, any>[] = [{$match: matchFilter}, {$limit: 1}, {$project: {_id: 1}}];

      try {
        let accessedGrantedCollection: {_id: string}[] = await this.metaDataRepository.aggregate(param);

        if (accessedGrantedCollection?.[0]) continue;

        let updateObj: Record<string, any> = {
          $pull: {
            allowedUserIdList: new ObjectId(userId),
          },
        };
        await this.metaDataRepository.updateAllMetaData({_id: new ObjectId(metObjectId)}, updateObj);
      } catch (err) {
        logger.error(
          `Handle object remove for collaborator| MetaDataService.handleObjectRemoveForCollaborator | collaborator Id: ${userId} | Error : ${err}`,
        );
        continue;
      }
    }
  }

  /**
   * this method is used to recalculate stats, meta fields and tags summary nad regenerate query options when remove object from collection
   * @param collectionId {string} collection id
   * @param teamId {string} team id
   * @returns {Promise<void>} return void
   */
  async reCalculateStatsWhenRemoveFromCollection(collectionId: string, teamId: string): Promise<SuccessResponse> {
    try {
      /**
       * Update parent stats
       */
      await this.systemStatsService.updateParentStats(collectionId.toString());

      let {skipOperation} = await this.statsCalculationService.calculateMetaFieldsAndTagsSummary(collectionId);
      if (!skipOperation) {
        await this.statsCalculationService.handlePropagateMetaFieldsAndTagsStatsToSystem();
      }

      /**
       * Regenerate query options
       */
      await this.queryOptionRepository.regenerateQueryOptionForCollection(collectionId, teamId);
      return {isSuccess: true};
    } catch (err) {
      logger.error(
        `Re calculate meta field summary and stats of a collection | DatalakeExplorerService.reCalculateStatsWhenRemoveFromCollection
          | collection Id: ${collectionId} and team id:${teamId}|
           Error in recalculation collection  stats: `,
        err,
      );
      return {isSuccess: false, error: err};
    }
  }

  /*
   * Add service methods here
   */

  /**
   * method to handle getDataArraytWithChildFiles and create corresponding image collection for videos
   */
  /*
    async handleGetDataArraytWithChildFiles(idList: string[], projectId: string, projectName: string, teamId: string) {
      let dataList = await this.getDataArraytWithChildFiles(idList, projectId, projectName)
      // console.log(JSON.stringify(dataList,null,2))

      if (!dataList) {
        logger.warn(`Get file info including child files list | MetaDataRepository.handleGetDataArraytWithChildFiles | N/A | failed getting dataList`)
        return
      }
      let updatedDataList: Partial<MetaData> = []
      for (let data of dataList) {
        // to add project details to the source (Collection/video/frmae-collection)
        // let sourceMetadataUpdate = {
        //   $addToSet: {
        //     annotationProjectList: {
        //       name: projectName,
        //       id: projectId
        //     }
        //   }
        // }
        //-----------------
        if (data.objectType == ContentType.VIDEO && !data.frameCollectionId) {
          // create a new collection for video
          let newCollectionId = await this.createImageFrameCollectionForVideo(data, projectId, projectName, teamId)
          data.frameCollectionId = newCollectionId
        } else if (data.objectType == ContentType.VIDEO && data.frameCollectionId) {
          // update project list if relevent collection
          // update Metadata of source and collection  - add projectData
          // this.updateById(data._id, sourceMetadataUpdate)
          // this.updateById(data.frameCollectionId, sourceMetadataUpdate)
          this.updateProjectListInParents(new Set([data.frameCollectionId]), projectName, projectId)
          //--------------------------------------------
        }
        else {
          // update Metadata of source - add projectData
          // this.updateById(data._id, sourceMetadataUpdate)
          //--------------------------------------------
        }
        updatedDataList.push(data)
      }

      return updatedDataList
    }

  */

  /**
   * method to handle getDataArraytWithChildFiles and create corresponding image collection for videos
   */
  async handleGetMetaDataArraytWithChildFiles(
    idList: string[],
    projectId: string,
    projectName: string,
    currentUserProfile: UserProfileDetailed,
    objectType: ContentType,
  ) {
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.warn(
        `Get file info including child files list | MetaDataRepository.handleGetDataArraytWithChildFiles | N/A | failed getting teamId`,
      );
      throw new HttpErrors.NotAcceptable(`Error : 'Basic Authentication failed`);
    }
    let teamId = currentUserProfile.teamId;
    let dataList = await this.getMetaDataArraytWithChildFiles(
      idList,
      projectId,
      projectName,
      objectType,
      currentUserProfile,
    );

    let updatedDataList: Partial<MetaData> = [];

    if (!dataList) {
      logger.warn(
        `Get file info including child files list | MetaDataRepository.handleGetDataArraytWithChildFiles | N/A | failed getting dataList`,
      );
      return {
        objectType: objectType,
        fileList: updatedDataList,
      };
    }

    if (objectType == ContentType.VIDEO) {
      for (let data of dataList) {
        if (data.objectType == ContentType.VIDEO && !data.frameCollectionId) {
          // create a new collection for video
          let newCollectionObj = await this.createImageFrameCollectionForVideo(
            data,
            projectId,
            projectName,
            currentUserProfile,
          );
          let newCollectionId = newCollectionObj.collectionId;
          data.frameCollectionName = newCollectionObj.collectionName;
          data.frameCollectionId = newCollectionId;
        } else if (data.objectType == ContentType.VIDEO && data.frameCollectionId) {
          let collectionData = await this.metaDataRepository.findById(data.frameCollectionId);
          if (collectionData) {
            data.frameCollectionName = collectionData.name;
          }
          let collectionParentList = new Set<ObjectId | string>();
          collectionParentList.add(data.frameCollectionId);
          if (data.vCollectionIdList && data.vCollectionIdList.length > 0) {
            data.vCollectionIdList.forEach(item => collectionParentList.add(item));
          }

          let vCollectionIdListForFrameCollection = await this.metaDataRepository.aggregate([
            {$match: {collectionId: data.frameCollectionId}},
            {
              $project: {
                vCollectionIdList: 1,
              },
            },
            {
              $unwind: '$vCollectionIdList',
            },
            {
              $group: {
                _id: null,
                vCollectionIdList: {$addToSet: '$vCollectionIdList'},
              },
            },
          ]);

          if (vCollectionIdListForFrameCollection && vCollectionIdListForFrameCollection.length > 0) {
            let parentList: ObjectId[] | string[] = vCollectionIdListForFrameCollection[0].vCollectionIdList;
            parentList.forEach(item => collectionParentList.add(item));
          }

          // update project list if relevent collection
          // update Metadata of source and collection  - add projectData
          this.updateProjectListInParents(collectionParentList, projectName, projectId);
          this.metaDataRepository.updateUpdatedAt(data.frameCollectionId);
        }
        updatedDataList.push(data);
      }
    } else {
      updatedDataList = dataList;
    }

    return {
      objectType: objectType,
      fileList: updatedDataList,
    };
  }

  /**
   * use to retrive file information of a id list including child files
   * @param idList ObjectId[]
   * @returns data : file info
   */
  /*
    async getDataArraytWithChildFiles(idList: string[], projectId: string, projectName: string) {
      if (idList && Array.isArray(idList) && idList.length > 0) {
        //  then ok
      }
      else {
        logger.debug(`Get file info including child files list | MetaDataRepository.getDataArraytWithChildFiles | invalid input (idList) | Failed`)
        return
      }
      logger.debug(`Get file info including child files list | MetaDataRepository.getDataArraytWithChildFiles | file list length: ${idList.length} | retrieving`)

      let objectIdList = idList.map((id: string) => new ObjectId(id))

      let aggregateProjectParam = {
        $project: {
          //  id: "$_id",
          name: 1,
          //  fileSize:1,
          objectKey: 1,
          objectType: 1,
          _id: 1,
          frameCollectionId: 1,
          thumbnailKey: 1,
          thumbnailUrl: 1,
          resolution: 1,
          parentList: 1
        }
      }

      let parentParams = [
        {$match: {_id: {$in: objectIdList}}},
        aggregateProjectParam
      ]
      let parentDataList: Partial<MetaData>[] = await this.metaDataRepository.aggregate(parentParams)

      let finalDataList: Partial<MetaData>[] = []

      let parentIdList: ObjectId[] = []

      let allParentListIdSet = new Set<ObjectId | string>()

      let parentItemObjectType: ContentType = ContentType.UNSUPPORTED //default

      for (let parentItem of parentDataList) {

        // add parentList Ids of source objects to a set
        if (parentItem.parentList && parentItem.parentList.length > 0) {
          parentItem.parentList.forEach(item => allParentListIdSet.add(item)) // add parentList of the object to set to update project details
        }
        if (parentItem._id) allParentListIdSet.add(parentItem._id) // add id of the object to set to update project details
        //-------------------------------------------------

        if (parentItem.objectType && [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION].includes(parentItem.objectType)) {
          // item is a collection
          if (parentItem._id) parentIdList.push(new ObjectId(parentItem._id))

          if (parentItemObjectType == ContentType.UNSUPPORTED) {
            // assign a type filter
            if (parentItem.objectType == ContentType.IMAGE_COLLECTION) parentItemObjectType = ContentType.IMAGE
            else if (parentItem.objectType == ContentType.VIDEO_COLLECTION) parentItemObjectType = ContentType.VIDEO
          }

          // // update Metadata of source - add projectData
          // this.updateById(parentItem._id, sourceMetadataUpdate)
          // //--------------------------------------------
        } else if (parentItem.objectKey) {
          // item is a child file
          finalDataList.push(parentItem)
        }
      }

      if (parentIdList.length > 0) {
        // get child files
        let childParams = [
          // {$match: {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO]}}},
          {$match: {objectType: parentItemObjectType}},
          {$match: {parentList: {$in: parentIdList}}},
          aggregateProjectParam
        ]
        let childDataList: Partial<MetaData>[] = await this.metaDataRepository.aggregate(childParams)
        childDataList.forEach(item => {
          if (item._id) allParentListIdSet.add(item._id) // add id of the object to set to update project details
        })

        finalDataList = [
          ...finalDataList,
          ...childDataList
        ]
      }

      // bulkWrite projrectList set of parents
      this.updateProjectListInParents(allParentListIdSet, projectName, projectId)
      //----------------

      // add exisiting frame indexes field
      finalDataList = await this.handleFindExisitingFrameIdsOfVideoCollectionList(finalDataList)

      return finalDataList
    }
  */

  /**
   * use to retrive file information of a id list including child files
   * @param idList ObjectId[]
   * @returns data : file info
   */
  async getMetaDataArraytWithChildFiles(
    idList: string[],
    projectId: string,
    projectName: string,
    objectType: ContentType,
    currentUserProfile: UserProfileDetailed,
  ) {
    if (idList && Array.isArray(idList) && idList.length > 0) {
      //  then ok
    } else {
      logger.debug(
        `Get file info including child files list | MetaDataRepository.getDataArraytWithChildFiles | invalid input (idList) | Failed`,
      );
      return;
    }
    logger.debug(
      `Get file info including child files list | MetaDataRepository.getDataArraytWithChildFiles | file list length: ${idList.length} | retrieving`,
    );

    let objectIdList = idList.map((id: string) => new ObjectId(id));

    let aggregateProjectParam = {
      $project: {
        //  id: "$_id",
        name: 1,
        //  fileSize:1,
        objectKey: 1,
        objectType: 1,
        _id: 1,
        frameCollectionId: 1,
        thumbnailKey: 1,
        thumbnailUrl: 1,
        resolution: 1,
        parentList: 1,
        url: 1,
        vCollectionIdList: 1,
      },
    };

    let parentParams = [
      {$match: {_id: {$in: objectIdList}}},
      {$sort: DEFAULT_ITEM_SORT_IN_COLLECTION},
      aggregateProjectParam,
    ];
    let parentDataList: Partial<MetaData>[] = await this.metaDataRepository.aggregate(parentParams);

    let finalDataList: Partial<MetaData>[] = [];

    let parentIdList: ObjectId[] = [];

    let allParentListIdSet = new Set<ObjectId | string>();

    let parentItemObjectType: ContentType = ContentType.UNSUPPORTED; //default

    for (let parentItem of parentDataList) {
      //logger.debug(`Get file info including child files list | MetaDataRepository.getDataArraytWithChildFiles | file objectKey ${parentItem.objectKey}`)

      // add parentList Ids of source objects to a set
      if (parentItem.parentList && parentItem.parentList.length > 0) {
        parentItem.parentList.forEach(item => allParentListIdSet.add(item)); // add parentList of the object to set to update project details
      }
      if (parentItem._id) allParentListIdSet.add(parentItem._id); // add id of the object to set to update project details
      if (parentItem.vCollectionIdList && parentItem.vCollectionIdList.length > 0) {
        parentItem.vCollectionIdList.forEach(item => allParentListIdSet.add(item)); // add id of the object to set to update project details
      }
      //-------------------------------------------------

      if (
        parentItem.objectType &&
        [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION].includes(parentItem.objectType)
      ) {
        // item is a collection
        if (parentItem._id) parentIdList.push(new ObjectId(parentItem._id));

        if (parentItemObjectType == ContentType.UNSUPPORTED) {
          // assign a type filter
          if (parentItem.objectType == ContentType.IMAGE_COLLECTION) parentItemObjectType = ContentType.IMAGE;
          else if (parentItem.objectType == ContentType.VIDEO_COLLECTION) parentItemObjectType = ContentType.VIDEO;
        }

        // // update Metadata of source - add projectData
        // this.updateById(parentItem._id, sourceMetadataUpdate)
        // //--------------------------------------------
      } else if (parentItem.objectKey) {
        if (objectType == ContentType.IMAGE) {
          // item is a child file
          finalDataList.push({
            objectKey: parentItem.objectKey,
            resolution: parentItem.resolution,
          });
        } else if (objectType == ContentType.VIDEO) {
          finalDataList.push(parentItem);
        }
      }
    }

    if (parentIdList.length > 0) {
      // get child files
      let childParams = [
        // {$match: {objectType: {$in: [ContentType.IMAGE, ContentType.VIDEO]}}},
        {$match: {objectType: parentItemObjectType}},
        {$match: {parentList: {$in: parentIdList}}},
        {$sort: DEFAULT_ITEM_SORT_IN_COLLECTION},
        aggregateProjectParam,
      ];
      let childDataList: Partial<MetaData>[] = await this.metaDataRepository.aggregate(childParams);
      childDataList.forEach(item => {
        if (item._id) allParentListIdSet.add(item._id); // add id of the object to set to update project details
        if (item.vCollectionIdList && item.vCollectionIdList.length > 0) {
          item.vCollectionIdList.forEach(item => allParentListIdSet.add(item)); // add id of the object to set to update project details
        }
      });

      finalDataList = [...finalDataList, ...childDataList];
    }

    // allParentListIdSet convert to array
    let allParentListIdArr: ObjectId[] = [];
    allParentListIdSet.forEach(item => allParentListIdArr.push(new ObjectId(item.toString())));

    let _vCollectionIds: [{uniqueVCollectionIds: string[]}] = await this.metaDataRepository.aggregate([
      {
        $match: {
          _id: {$in: allParentListIdArr},
          objectType: ContentType.VIDEO,
        },
      },
      {
        $project: {_id: 0, vCollectionIdList: 1},
      },
      {$unwind: '$vCollectionIdList'},
      {$group: {_id: null, uniqueVCollectionIds: {$addToSet: '$vCollectionIdList'}}},
      {$project: {_id: 0, uniqueVCollectionIds: 1}},
    ]);

    // if _vCollectionIds is not empty, then add uniqueVCollectionIds to allParentListIdSet
    if (_vCollectionIds && _vCollectionIds.length > 0) {
      let uniqueVCollectionIds = _vCollectionIds[0].uniqueVCollectionIds;
      uniqueVCollectionIds.forEach(item => allParentListIdSet.add(item));
    }

    // bulkWrite projrectList set of parents
    // put await because project tag should be finished to avoid file duplication when adding files to existing projects
    await this.updateProjectListInParents(allParentListIdSet, projectName, projectId);
    //----------------

    // add exisiting frame indexes field
    finalDataList = await this.handleFindExisitingFrameIdsOfVideoCollectionList(
      finalDataList,
      projectId,
      currentUserProfile,
    );

    return finalDataList;
  }

  /**
   * sub method to create a corresponding frame image collection for video
   */
  async createImageFrameCollectionForVideo(
    videoObjectData: Partial<MetaData>,
    projectId: string,
    projectName: string,
    currentUserProfile: UserProfileDetailed,
  ) {
    logger.info(
      `Create corresponding frame collection for video | MetaDataRepository.createImageFrameCollectionForVideo | N/A | creating`,
    );

    let videoObject = await this.metaDataRepository.findById(videoObjectData._id);

    let collectionName = `${videoObject.name}_frame-collection`;

    let collectionParentList = [];
    collectionParentList.push(new ObjectId(videoObject.id));
    if (videoObject.parentList) {
      collectionParentList = [...collectionParentList, ...videoObject.parentList];
    }

    // get metadata from source video formatted to collection
    //let customMetaFromSource = this.metaDataRepository.filterAndGetCustomMetaObject(videoObject.customMeta || {}, true)
    let customMetaFromSource = convertValuesToArrays(videoObject.customMeta || {});

    let collectionMetaData: Partial<MetaData> = {
      annotationProjectList: [
        {
          id: new ObjectId(projectId),
          name: projectName,
        },
      ],
      parentList: collectionParentList,
      storagePrefixPath: 'LayerNext/video-frames',
      thumbnailKey: videoObject.thumbnailKey,
      thumbnailUrl: videoObject.thumbnailUrl,
      // objectKey:videoObject.thumbnailKey, // assigned thumbnail key also as objectKey
      // url:videoObject.thumbnailUrl, // assigned thumbnail url
      urlExpiredAt: new Date(),
      isFromVideo: true,
      Tags: videoObject.Tags || [],
      customMeta: customMetaFromSource, // append custom fields from source video to collection
    };
    let collectionObj = await this.metaDataRepository.handleCreateCollection(
      collectionName,
      ContentType.IMAGE_COLLECTION,
      collectionMetaData,
      currentUserProfile,
    );

    let collectionId = collectionObj.collectionId;

    // update videoMetadata - add frameCollectionId, projectData
    // let videoMetadataUpdate = {frameCollectionId:collectionId};
    let videoMetadataUpdate = {
      $set: {frameCollectionId: collectionId},
      $addToSet: {
        annotationProjectList: {
          id: new ObjectId(projectId),
          name: projectName,
        },
      },
    };

    this.metaDataRepository.updateById(videoObject.id, videoMetadataUpdate);
    this.metaDataRepository.updateUpdatedAt(videoObject.id);

    //update query option for add project name suggestion
    // create a object from key,value pair
    let tempObj: {[k: string]: any} = {};
    tempObj[SearchQuerySubGroup.ANNOTATION_PROJECT] = projectName;
    // call query update function
    await this.queryOptionRepository.updateQueryOption(
      SearchQueryRootGroup.ANNOTATION,
      tempObj,
      true,
      currentUserProfile.teamId,
      collectionId,
    );

    return collectionObj;
  }

  /**
   * sub method to update projectIdList of source objects' parents
   */
  async updateProjectListInParents(parentIdSet: Set<ObjectId | string>, projectName: string, projectId: string) {
    let sourceMetadataUpdate = {
      $addToSet: {
        annotationProjectList: {
          id: new ObjectId(projectId),
          name: projectName,
        },
      },
      $set: {
        updatedAt: new Date(),
      },
    };

    let bulkUpdateOneArr: {
      updateOne: {
        filter: {_id: ObjectId};
        update: {$addToSet: {annotationProjectList: {id: ObjectId; name: string}}};
      };
    }[] = [];

    parentIdSet.forEach(item => {
      let updateOneItem = {
        updateOne: {
          filter: {_id: new ObjectId(item)},
          update: sourceMetadataUpdate,
        },
      };
      bulkUpdateOneArr.push(updateOneItem);
    });

    if (bulkUpdateOneArr && bulkUpdateOneArr.length > 0) await this.metaDataRepository.bulkWrite(bulkUpdateOneArr);

    this.updateQueryOptionsWithProjectDetails(parentIdSet, projectName);
  }

  /**
   * Use to update annotation project related serach query options
   * @param parentIdSet MetaData ids
   * @param projectName Name of the annotation project
   */
  async updateQueryOptionsWithProjectDetails(parentIdSet: Set<ObjectId | string>, projectName: string) {
    logger.debug('projectName: ', projectName);

    let parentCollections = await this.metaDataRepository.find({
      where: {
        id: {
          inq: [...parentIdSet],
        },
        objectType: {
          inq: [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION],
        },
      },
    });

    for (let col of parentCollections) {
      let teamId: string | undefined = undefined;
      let collectionId: string | undefined = undefined;
      if (col.teamId) teamId = col.teamId as unknown as string;
      if (col.id) collectionId = col.id;
      // create a object from key,value pair
      let tempObj: {[k: string]: any} = {};
      tempObj[SearchQuerySubGroup.ANNOTATION_PROJECT] = projectName;
      // call query update function
      await this.queryOptionRepository.updateQueryOption(
        SearchQueryRootGroup.ANNOTATION,
        tempObj,
        true,
        teamId,
        collectionId,
      );
    }
  }

  /**
   * sub method to get already existing frames in a corresponding colletion of a video list
   * @param dataList Object[]
   * @returns data (with existing frames)
   */
  async handleFindExisitingFrameIdsOfVideoCollectionList(
    dataList: Partial<MetaData>[],
    projectId: string,
    currentUserProfile: UserProfileDetailed,
  ) {
    let updatedDataList: MetaDataStudioResponseType[] = [];

    for (let data of dataList) {
      if (data.objectType == ContentType.VIDEO && data.frameCollectionId) {
        //check frameCollection has trashed or not
        let frameCollection = await this.metaDataRepository.findById(data.frameCollectionId);
        if (frameCollection.objectStatus == OBJECT_STATUS.TRASHED) {
          await this.datalakeTrashService.restoreTrashObject(
            [data.frameCollectionId],
            '',
            false,
            currentUserProfile,
            undefined,
            {},
            ContentType.ALL,
            SYSTEM_BOT,
          );
        }

        data.existingFrames = await this.findExisitingFrameIdsOfVideoCollection(data.frameCollectionId);
        data.existingFramesInProject = await this.findExistingFrameIdsInProject(data.frameCollectionId, projectId);
      }
      updatedDataList.push(data);
    }
    return updatedDataList;
  }

  /**
   * sub method to get already existing frames in a corresponding colletion of a video
   */
  async findExisitingFrameIdsOfVideoCollection(collectionId: string) {
    logger.debug(
      `Get file info including child files list | MetaDataRepository.findExisitingFrameIdsOfVideoCollection | collectionId:${collectionId} | finding exisiting frames`,
    );
    let params = [
      {$match: {collectionId: collectionId}},
      {$match: {objectType: ContentType.IMAGE}},
      {
        $group: {
          _id: null,
          frameArr: {
            $push: {
              idx: '$videoFrameIndex',
              objectKey: '$objectKey',
            },
          },
        },
      },
    ];
    let videoFrameData = await this.metaDataRepository.aggregate(params);

    let videoFrameList: number[] = [];
    if (videoFrameData && videoFrameData.length > 0) {
      videoFrameList = videoFrameData[0].frameArr;
    }
    return videoFrameList;
  }

  /**
   * sub method to get already existing frames in a corresponding project of a video
   */
  async findExistingFrameIdsInProject(collectionId: string, projectId: string) {
    logger.debug(
      `Get file info including child files list in project | MetaDataRepository.findExistingFrameIdsInProject | collectionId:${collectionId} | finding existing frames in projectId: ${projectId}`,
    );
    let params = [
      {
        $match: {
          collectionId: new ObjectId(collectionId),
          'annotationProjectList.id': new ObjectId(projectId),
        },
      },
      {$match: {objectType: ContentType.IMAGE}},
      {
        $group: {
          _id: null,
          frameArr: {$push: '$videoFrameIndex'},
        },
      },
    ];
    let videoFrameData = await this.metaDataRepository.aggregate(params);

    let videoFrameList: number[] = [];
    if (videoFrameData && videoFrameData.length > 0) {
      videoFrameList = videoFrameData[0].frameArr;
    }
    return videoFrameList;
  }

  /**
   * Use when deleting project
   * Update projectList in metaData
   * @param projectId string
   * @returns response
   */
  public async removeProjectFromMetaData(projectId: string) {
    let projectOid = new ObjectId(projectId);
    let params = {'annotationProjectList.id': projectOid};
    let pullParams = {annotationProjectList: {id: projectOid}};

    // mark stat and verification status pending of the metaData
    await this.metaDataRepository.markStatPendingFlagsTrue(params);

    // remove from project
    const response = await this.metaDataRepository.updateManyRemoveFromList(params, pullParams, []);

    //  await (this.dataSource.connector as any)
    //    .collection('MetaData')
    //    .updateMany(params, {$pull: pullParams})
    logger.debug(
      `Remove project from metaData | MetaDataService.removeProjectFromMetaData | projectId: ${projectId} | removing`,
    );
    return response;
  }

  /**
   * Use when editing project name to update metadata and metadata update
   * Update projectList in metaData
   * @param projectId {string} id of the project
   * @param projectName {string} name of the project
   * @returns null
   */
  public async editProjectFromMetaData(projectId: string, projectName: string, teamId: string) {
    let projectOid = new ObjectId(projectId);

    // edit annotationProjectList
    await this.metaDataRepository.updateManySet(
      {'annotationProjectList.id': projectOid},
      {'annotationProjectList.$[project].name': projectName},
      [{'project.id': projectOid}],
    );

    // edit operationList
    await this.metaDataRepository.updateManySet(
      {'operationList.operationId': projectOid},
      {'operationList.$[operation].operationName': projectName},
      [{'operation.operationId': projectOid}],
    );

    // edit virtual collection name
    let newVirtualCollectionNameString = await this.createVirtualCollectionNameForProject(projectName, teamId);

    if (newVirtualCollectionNameString) {
      await this.metaDataRepository.updateManySet(
        {studioProjectId: projectOid},
        {
          name: newVirtualCollectionNameString,
          updatedAt: new Date(),
        },
        [],
      );
    }

    // mark stat and verification status pending of the metaData
    // await this.metaDataRepository.markStatPendingFlagsTrue(
    //   {"annotationProjectList.id": projectOid}
    // )

    logger.debug(
      `Edit project from metaData | MetaDataService.editProjectFromMetaData | projectId: ${projectId} | editing`,
    );
    return;
  }

  /**
   * delete metaData updates and remove task from taskIdList in metaData per task
   * @param taskIdList
   */
  public async removeTasksFromMetaData(taskIdList: string[]) {
    // delete metaData updates and remove task from taskIdList in metaData per task
    for (let taskId of taskIdList) {
      await this.removeTaskFromMetaData(taskId);
    }
  }

  /**
   * Use when deleting task
   * Update taskIdList in metaData
   * @param taskId string
   * @returns response
   */
  public async removeTaskFromMetaData(taskId: string) {
    let taskOid = new ObjectId(taskId);
    let params = {taskIdList: taskOid};
    // remove from task
    const response = await this.metaDataRepository.updateManyRemoveFromList(params, params, []);
    // mark stat pending flags true
    await this.metaDataRepository.markStatPendingFlagsTrue(params);

    // await (this.dataSource.connector as any)
    //   .collection('MetaData')
    //   .updateMany(params, { $pull: params })
    logger.debug(`Remove task from metaData | MetaDataService.removeTaskFromMetaData | taskId: ${taskId} | removing`);
    return response;
  }

  /**
   * Method to get list of collections by type
   * @param contentType - to filter out collection names
   * @param teamId
   */
  async getExistingCollectionListByType(contentType: ContentType, currentUserProfile: UserProfileDetailed) {
    let teamId = currentUserProfile.teamId;
    if (
      [ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(contentType)
    ) {
      /* then ok */
    } else {
      logger.error(
        `Get metaData input fields | MetaDataService.getCollectionDataByType | contentType: ${contentType} | invalid contentType - not a collection type`,
      );
      return [];
    }

    let matchObject: any = {
      objectType: contentType,
      teamId: teamId,
      objectStatus: OBJECT_STATUS.ACTIVE,
    };

    if (
      currentUserProfile &&
      currentUserProfile.userType &&
      currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR
    ) {
      matchObject['allowedUserIdList'] = currentUserProfile.id;
    }

    let collectionList = await this.metaDataRepository.find({
      where: matchObject,
      fields: {
        id: true,
        name: true,
      },
    });
    return collectionList;
  }

  /**
   * Use to get total frame count of a datasetVersion
   * @param datasetVersionId
   */
  async frameCountDatasetVersion(datasetVersionId: string) {
    logger.info(
      `${FLOWS.DATASET_MANAGER_REQ} | MetaDataService.frameCountDatasetVersion | N/A | request frameCount of datasetVersionId= ${datasetVersionId}`,
    );
    let totalDatasetVersionFrameCount = await this.metaDataRepository.count({
      'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
      objectType: ContentType.IMAGE,
      objectStatus: OBJECT_STATUS.ACTIVE,
    });
    logger.info(
      `${FLOWS.DATASET_MANAGER_REQ} | MetaDataService.frameCountDatasetVersion | N/A | request frameCount of datasetVersionId= ${datasetVersionId}, totalDatasetVersionFrameCount= ${totalDatasetVersionFrameCount.count}`,
    );
    return totalDatasetVersionFrameCount;
  }

  /**
   * Use to get list of all collections with selected counts for a given selectionId
   * @param selectionId string datalake side selection id
   * @param datasetVersionId string dataset manager side version id
   * @param isSelectedOnly if ture, then send details of selected frame collection only, if false, then send all collection with selected details
   * @returns
   */
  async getFrameCollectionsWithSelectionDetails(
    isSelectedOnly: boolean,
    currentUserProfile: UserProfileDetailed,
    selectionId?: string,
    datasetVersionId?: string,
    projectId?: string,
  ) {
    logger.info(
      `${FLOWS.DATALAKE_SELECTION} | DatasetManagerInterfaceService.getFrameCollectionsWithSelectionDetails | N/A | selectionId= ${selectionId}, datasetVersionId= ${datasetVersionId},  isSelectedOnly= ${isSelectedOnly}`,
    );

    if (!selectionId && !datasetVersionId && !projectId) {
      return {collectionList: []};
    }

    let matchFilter: {[k: string]: any} = {};
    let objectType: ContentType | undefined = undefined;

    if (selectionId && isValidObjectId(selectionId)) {
      let selectionQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
        currentUserProfile,
        selectionId,
      );

      matchFilter = {
        ...matchFilter,
        ...selectionQuery?.matchQuery,
      };
      objectType = selectionQuery?.objectType;
    } else if (datasetVersionId && isValidObjectId(datasetVersionId)) {
      // let embeddingSelection: any = {};
      // let filter: any = {};
      // let filterMatchQuery: any = await this.searchQueryBuilderService.getMatchQueryForExplorerFilter(
      //   filter,
      //   embeddingSelection,
      //   undefined,
      //   currentUserProfile,
      // );
      matchFilter = {
        ...matchFilter,
        // ...filterMatchQuery,
        //replace flag with enum
        objectStatus: OBJECT_STATUS.ACTIVE,
        // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
        'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
      };
      // then group from images to het collection wise selected count
      objectType = ContentType.IMAGE;
    } else if (projectId && isValidObjectId(projectId)) {
      // let embeddingSelection: any = {};
      // let filter: any = {};
      // let filterMatchQuery: any = await this.searchQueryBuilderService.getMatchQueryForExplorerFilter(
      //   filter,
      //   embeddingSelection,
      //   undefined,
      //   currentUserProfile,
      // );
      matchFilter = {
        ...matchFilter,
        // ...filterMatchQuery,
        //replace flag with enum
        objectStatus: OBJECT_STATUS.ACTIVE,
        // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
        'annotationProjectList.id': new ObjectId(projectId),
      };
      // then group from images to het collection wise selected count
      objectType = ContentType.IMAGE;
    }

    let selectedFrameCollections = await this.getSelectedFrameCollections(matchFilter, objectType);

    let whereFilter: Where<MetaData> = {
      objectType: ContentType.IMAGE_COLLECTION,
      // isLogical: {neq: true},
      objectStatus: OBJECT_STATUS.ACTIVE,
    };
    if (currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
      whereFilter.allowedUserIdList = new ObjectId(currentUserProfile.id);
    }

    if (isSelectedOnly) {
      console.log(isSelectedOnly);
      let _idList = selectedFrameCollections.map(elem => {
        return elem._id;
      });
      whereFilter = {
        ...whereFilter,
        id: {inq: _idList},
      };
    }

    let allFrameCollections = await this.metaDataRepository.find({
      where: whereFilter,
      order: ['name ASC'],
      fields: {
        id: true,
        name: true,
        frameCount: true,
      },
    });

    let hashSelectedFrameCollections = Object.fromEntries(
      selectedFrameCollections.map(elem => [elem._id, elem.selectedCount]),
    );

    let allFrameCollectionsWithSelectedCount = allFrameCollections.map(collection => {
      return {
        collectionId: collection.id,
        name: collection.name,
        totalCount: collection.frameCount ? collection.frameCount : 0,
        selectedCount: collection.id
          ? hashSelectedFrameCollections[collection.id]
            ? hashSelectedFrameCollections[collection.id]
            : 0
          : 0,
      };
    });

    return {collectionList: allFrameCollectionsWithSelectedCount};
  }

  /**
   * Use to get list of all collections with selected counts for a given selectionId
   * @param selectionId string datalake side selection id
   * @param datasetVersionId string dataset manager side version id
   * @param isSelectedOnly if ture, then send details of selected frame collection only, if false, then send all collection with selected details
   * @returns
   */
  async getVideosWithSelectionDetails(
    isSelectedOnly: boolean,
    currentUserProfile: UserProfileDetailed,
    selectionId?: string,
  ) {
    logger.info(
      `${FLOWS.DATALAKE_SELECTION} | DatasetManagerInterfaceService.getVideosWithSelectionDetails | N/A | selectionId= ${selectionId},  isSelectedOnly= ${isSelectedOnly}`,
    );

    if (!selectionId) {
      return {videoList: []};
    }

    let matchFilter: {[k: string]: any} = {};
    let objectType: ContentType | undefined = undefined;

    if (selectionId && isValidObjectId(selectionId)) {
      let selectionQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
        currentUserProfile,
        selectionId,
      );

      matchFilter = {
        ...matchFilter,
        ...selectionQuery?.matchQuery,
      };
      objectType = selectionQuery?.objectType;
    }

    let selectedVideos = await this.getSelectedVideos(matchFilter, objectType);

    let whereFilter: Where<MetaData> = {
      objectType: ContentType.VIDEO,
    };

    if (isSelectedOnly) {
      let _idList = selectedVideos.map(elem => {
        return elem._id;
      });
      whereFilter = {
        ...whereFilter,
        id: {inq: _idList},
      };
    }

    let allVideos = await this.metaDataRepository.find({
      where: whereFilter,
      order: ['name ASC'],
      fields: {
        id: true,
        name: true,
      },
    });

    let hashSelectedVideos = Object.fromEntries(selectedVideos.map(elem => [elem._id, 1]));

    let allVideosWithSelectedCount = allVideos.map(video => {
      return {
        videoId: video.id,
        name: video.name,
        totalCount: 1,
        selectedCount: video.id ? (hashSelectedVideos[video.id] ? 1 : 0) : 0,
      };
    });

    return {videoList: allVideosWithSelectedCount};
  }

  /**
   * Use to get list of collections with selected counts for a given selectionId
   * @param matchFilter filter to select frames
   * @param objectType get decision how to group to get counts
   * @returns
   */
  async getSelectedFrameCollections(matchFilter: {[k: string]: any}, objectType?: ContentType) {
    let pipeline: any[] = [];

    if (objectType == ContentType.IMAGE) {
      pipeline = [
        {$match: matchFilter},
        {
          $unwind: '$vCollectionIdList',
        },
        {
          $group: {
            _id: '$vCollectionIdList',
            selectedCount: {$count: {}},
          },
        },
      ];
    } else if (objectType == ContentType.IMAGE_COLLECTION || objectType == ContentType.DATASET) {
      let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
        {$match: matchFilter},
        {$project: {_id: 1}},
      ]);

      let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));

      pipeline = [
        {
          $match: {
            _id: {$in: collectionObjectIdList},
            objectStatus: OBJECT_STATUS.ACTIVE,
            // $or: [
            //   {
            //     collectionId: {$in: collectionObjectIdList},
            //     //replace flag with enum
            //     objectStatus: OBJECT_STATUS.ACTIVE,
            //     // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
            //   },
            //   {
            //     vCollectionIdList: {$in: collectionObjectIdList},
            //     //replace flag with enum
            //     objectStatus: OBJECT_STATUS.ACTIVE,
            //     // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
            //   },
            // ],
          },
        },
        {
          $project: {
            _id: 1,
            selectedCount: '$frameCount',
          },
        },
      ];
    }
    // else if (objectType == ContentType.DATASET) {
    //   let _datasetMetaIds: {_id: string}[] = await this.metaDataRepository.aggregate([
    //     {$match: matchFilter},
    //     {$project: {_id: 1}},
    //   ]);
    //   let datasetMetaObjectIdList = _datasetMetaIds.map(elem => new ObjectId(elem._id));

    //   pipeline = [
    //     {
    //       $match: {'datasetVersionList.datasetMetaId': {$in: datasetMetaObjectIdList}},
    //     },
    //   ];
    // }

    // group from physical collections
    // pipeline.push({
    //   $group: {
    //     _id: '$collectionId',
    //     selectedCount: {$count: {}},
    //   },
    // });

    let selectedCollectionList: {
      _id: string;
      selectedCount: number;
    }[] = await this.metaDataRepository.aggregate(pipeline);

    return selectedCollectionList;
  }

  async getFrameStat(matchFilter: any) {
    const param: any = [
      {$match: matchFilter}, // ---> matching meta objects
      {$unwind: '$datasetVersionList'},
      {$match: matchFilter},
      {
        $addFields: {
          augmentedCount: {
            $cond: {
              if: {$eq: ['$datasetVersionList.isAugmentedImage', true]},
              then: 1,
              else: 0,
            },
          },
        },
      },
      {$group: {_id: null, totalFrames: {$sum: 1}, augmentedFrames: {$sum: '$augmentedCount'}}},
    ];
    let data = await this.metaDataRepository.aggregate(param);
    return data;
  }

  /**
   * get label list
   * @param {any} matchFilter match filter
   * @returns label list
   */
  async getLabelList(matchFilter: any, selectedOperationIdList: string[]) {
    const group: any = {
      _id: '$labelList.label',
      count: {$sum: '$labelList.count'},
    };

    const lookup: any = {
      from: 'SystemLabel',
      let: {label: '$_id'},
      pipeline: [{$match: {$expr: {$eq: ['$$label', '$label']}}}, {$project: {labelText: 1, _id: 0}}],
      as: 'labels',
    };

    const project: any = {
      _id: 0,
      count: 1,
      mainLabel: '$_id',
      labels: 1,
    };

    const sort: any = {'labels.labelText': 1};

    //operation based match filter
    let formattedSelectedOperationIdList = selectedOperationIdList.map(elem => {
      if (isValidObjectId(elem)) {
        return new ObjectId(elem);
      } else {
        return elem;
      }
    });

    const param: any = [
      {$match: matchFilter}, // ---> matching meta objects
      {
        $project: {
          operationList: 1,
          _id: 0,
        },
      },
      {
        $unwind: '$operationList',
      },
      {
        $match: {
          'operationList.operationId': {$in: formattedSelectedOperationIdList}, // --> matching operations
        },
      },
      {
        $project: {
          labelList: '$operationList.labelList',
        },
      },
      {$unwind: '$labelList'},
      {$group: group},
      {$lookup: lookup},
      {$sort: sort},
      {$project: project},
    ];

    //console.log(JSON.stringify(param, null, 2))

    let data = await this.metaDataRepository.aggregate(param);
    return data;
  }

  /**
   * get label list
   * @param {any} matchFilter match filter
   * @returns label list
   */
  async getLabelListFromMetaDatUpdates(matchFilter: any, selectedOperationIdList: string[]) {
    const group: any = {
      _id: '$MetaDataUpdate.annotationObjects.label.label',
      count: {$sum: 1},
    };

    const lookup: any = {
      from: 'SystemLabel',
      let: {label: '$_id'},
      pipeline: [{$match: {$expr: {$eq: ['$$label', '$label']}}}, {$project: {labelText: 1, _id: 0}}],
      as: 'labels',
    };

    const project: any = {
      _id: 0,
      count: 1,
      mainLabel: '$_id',
      labels: 1,
    };

    const sort: any = {'labels.labelText': 1};

    //operation based match filter
    let formattedSelectedOperationIdList = selectedOperationIdList.map(elem => {
      if (isValidObjectId(elem)) {
        return new ObjectId(elem);
      } else {
        return elem;
      }
    });

    const param: any = [
      {$match: matchFilter}, // ---> matching meta objects
      {$lookup: {from: 'MetaDataUpdate', foreignField: 'objectKey', localField: 'objectKey', as: 'MetaDataUpdate'}},
      {$unwind: '$MetaDataUpdate'},
      {
        $match: {
          'MetaDataUpdate.operationId': {$in: formattedSelectedOperationIdList}, // --> matching operations
        },
      },
      {$unwind: '$MetaDataUpdate.annotationObjects'},
      {$group: group},
      {$lookup: lookup},
      {$sort: sort},
      {$project: project},
    ];

    //console.log(JSON.stringify(param, null, 2))

    let data = await this.metaDataRepository.aggregate(param);
    return data;
  }

  /**
   * Use to get list of collections with selected counts for a given selectionId
   * @param matchFilter filter to select frames
   * @returns
   */
  async getSelectedVideos(matchFilter: {[k: string]: any}, objectType?: ContentType) {
    let pipeline: any[] = [
      {
        $match: matchFilter,
      },
    ];
    if (objectType == ContentType.VIDEO) {
      pipeline.push({
        $project: {
          _id: '$_id',
        },
      });
    } else if (objectType == ContentType.VIDEO_COLLECTION) {
      let videoCollectionList: {_id: string}[] = await this.metaDataRepository.aggregate([
        ...pipeline,
        {
          $project: {
            _id: '$_id',
          },
        },
      ]);
      let videoCollectionIdList = videoCollectionList.map(elem => new ObjectId(elem._id));

      pipeline = [
        {
          $match: {
            vCollectionIdList: {$in: videoCollectionIdList},
            // $or: [{collectionId: {$in: videoCollectionIdList}}, {vCollectionIdList: {$in: videoCollectionIdList}}],
            objectType: ContentType.VIDEO,
          },
        },
        {
          $project: {
            _id: '$_id',
          },
        },
      ];
    }

    let selectedVideoList: {
      _id: string;
    }[] = await this.metaDataRepository.aggregate(pipeline);

    return selectedVideoList;
  }

  /**
   * use to retrive file information of a list
   * @param idList ObjectId[]
   * @returns data : file info
   */
  async getFileArray(isSelectedOnly: boolean, selectionId: string, currentUserProfile: UserProfileDetailed) {
    let selectiobObj = await this.datalakeSelectionRepository.findById(selectionId);
    let selectionObjList: {
      id?: string;
      name: string;
      totalCount?: number;
      selectedCount?: number;
      isCollection: boolean;
    }[] = [];

    let childObejctType: ContentType = ContentType.UNSUPPORTED;
    if (
      selectiobObj.objectType == ContentType.IMAGE ||
      selectiobObj.objectType == ContentType.IMAGE_COLLECTION ||
      selectiobObj.objectType == ContentType.DATASET
    ) {
      let collection = await this.getFrameCollectionsWithSelectionDetails(
        isSelectedOnly,
        currentUserProfile,
        selectionId,
      );
      selectionObjList = collection.collectionList.map(elem => {
        return {
          id: elem.collectionId,
          name: elem.name,
          totalCount: elem.totalCount,
          selectedCount: elem.selectedCount,
          isCollection: true,
        };
      });
      childObejctType = ContentType.IMAGE;
    } else if (
      selectiobObj.objectType == ContentType.VIDEO ||
      selectiobObj.objectType == ContentType.VIDEO_COLLECTION
    ) {
      let videos = await this.getVideosWithSelectionDetails(isSelectedOnly, currentUserProfile, selectionId);
      selectionObjList = videos.videoList.map(elem => {
        return {
          id: elem.videoId,
          name: elem.name,
          totalCount: elem.totalCount,
          selectedCount: elem.selectedCount,
          isCollection: false,
        };
      });
      childObejctType = ContentType.VIDEO;
    } else if (
      selectiobObj.objectType == ContentType.OTHER ||
      selectiobObj.objectType == ContentType.OTHER_COLLECTION
    ) {
      childObejctType = ContentType.OTHER;
    }

    return {
      selectionObjList: selectionObjList,
      childObejctType: childObejctType,
    };
  }

  async getProjectCollectionList(projectId: string, currentUserProfile: UserProfileDetailed) {
    let selectionObjList: {
      id?: string;
      name: string;
      totalCount?: number;
      selectedCount?: number;
      isCollection: boolean;
    }[] = [];
    let collection = await this.getFrameCollectionsWithSelectionDetails(
      true,
      currentUserProfile,
      undefined,
      undefined,
      projectId,
    );
    selectionObjList = collection.collectionList.map(elem => {
      return {
        id: elem.collectionId,
        name: elem.name,
        totalCount: elem.totalCount,
        selectedCount: elem.selectedCount,
        isCollection: true,
      };
    });

    return {
      selectionObjList: selectionObjList,
    };
  }

  /**
   *  update label and frame of dataset
   * @param data it contains label stats and frame count
   * @param datasetGroupId dataset group id
   */
  async updateLabelAndFramesOfDataset(
    data: {
      labelStats: DatasetLabelAggregate[];
      frameCount: ObjectCount[];
    },
    datasetGroupId: string,
  ) {
    let verificationStatusCount = this.defaultVerificationStats;
    let frameCount: number = 0;
    let fileSize: number = 0;

    if (data.frameCount && data.frameCount.length > 0) {
      let firstElement: ObjectCount = data.frameCount[0];

      if (firstElement.count) frameCount = firstElement.count;
      if (firstElement.machineAnnotatedSum) verificationStatusCount.machineAnnotated = firstElement.machineAnnotatedSum;
      if (firstElement.rawSum) verificationStatusCount.raw = firstElement.rawSum;
      if (firstElement.verifiedSum) verificationStatusCount.verified = firstElement.verifiedSum;
      if (firstElement.size) fileSize = firstElement.size;
    }

    let labelList: {
      label: string;
      count: number;
    }[] = [];

    if (data.labelStats && data.labelStats.length > 0) {
      labelList = data.labelStats.map(label => {
        delete label.labels;

        return {
          label: label.mainLabel,
          count: label.count,
        };
      });
    }

    let datasetMetaHead = await this.metaDataRepository.findOne({
      where: {
        datasetGroupId: datasetGroupId,
      },
    });

    if (datasetMetaHead) {
      let teamId: string | undefined = undefined;
      let collectionId: string | undefined = undefined;
      if (datasetMetaHead.id) collectionId = datasetMetaHead.id as unknown as string;
      if (datasetMetaHead.teamId) teamId = datasetMetaHead.teamId as unknown as string;

      //update query option for label classes in dataset
      labelList.forEach(_label => {
        // create a object from key,value pair
        let tempObj: {[k: string]: any} = {};
        tempObj[SearchQuerySubGroup.ANNOTATION_LABEL] = _label.label;
        // call query update function
        this.queryOptionRepository.updateQueryOption(
          SearchQueryRootGroup.ANNOTATION,
          tempObj,
          true,
          teamId,
          collectionId,
        );
      });

      let updateData: DataObject<MetaData> = {
        frameCount: frameCount,
        verificationStatusCount: verificationStatusCount,
        labelList: labelList,
        fileSize: fileSize,
        updatedAt: new Date(),
      };
      await this.metaDataRepository.updateById(datasetMetaHead.id, updateData);
    } else {
      logger.error(
        `${FLOWS.META_UPDATE} | MetaDataService.updateLabelAndFramesOfDataset | N/A | couldn't find datasetMetaObj for datasetGroupId= ${datasetGroupId}`,
      );
      return;
    }
  }

  /**
   * Use to calculate dataset analytics using datasetStatPending flag
   */
  async calcDatasetAnalyticsWhileModelRunUpdate() {
    let currentTime = new Date();

    let statPendingDatasetList: any[] = [];
    let _statPendingDatasetListawait: {_id: string}[] = await this.metaDataRepository.aggregate([
      {$match: {datasetStatPending: true, frameAnalyticsCalcAt: {$lte: currentTime}}},
      {$unwind: '$datasetVersionList'},
      {$group: {_id: '$datasetVersionList.datasetMetaId'}},
    ]);
    await this.metaDataRepository.updateAll(
      {datasetStatPending: false},
      {datasetStatPending: true, frameAnalyticsCalcAt: {lt: currentTime}},
    );

    if (_statPendingDatasetListawait && _statPendingDatasetListawait.length > 0) {
      statPendingDatasetList = _statPendingDatasetListawait.filter(elem => elem._id);
      statPendingDatasetList = statPendingDatasetList.map(elem => {
        if (elem._id) {
          return new ObjectId(elem._id);
        }
      });
    }

    logger.debug(
      `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalytics | N/A | stat pending dataset list: ${statPendingDatasetList}`,
    );

    for (let datasetId of statPendingDatasetList) {
      const analyticsUpdateList: AnalyticsUpdateAgg[] = await this.metaDataRepository.aggregate([
        // {$match: {'datasetVersionList.datasetMetaId': datasetId}},
        {$match: {vCollectionIdList: datasetId}},

        {$unwind: '$analytics'},
        {
          $unwind: '$analytics.labelWiseAnalytics',
        },
        {
          $group: {
            _id: {
              operationId: '$analytics.operationId',
              label: '$analytics.labelWiseAnalytics.label',
            },
            truePositive: {$sum: '$analytics.labelWiseAnalytics.truePositive'},
            falsePositive: {$sum: '$analytics.labelWiseAnalytics.falsePositive'},
            falseNegative: {$sum: '$analytics.labelWiseAnalytics.falseNegative'},
          },
        },
        {
          $group: {
            _id: '$_id.operationId',
            truePositive: {$sum: '$truePositive'},
            falsePositive: {$sum: '$falsePositive'},
            falseNegative: {$sum: '$falseNegative'},
            labelData: {
              $push: {
                label: '$_id.label',
                truePositive: '$truePositive',
                falsePositive: '$falsePositive',
                falseNegative: '$falseNegative',
              },
            },
          },
        },
        {$project: {operationId: '$_id', truePositive: 1, falsePositive: 1, falseNegative: 1, labelData: 1, _id: 0}},
      ]);

      let analytics: Analytics[] = [];
      for (let analyticsObj of analyticsUpdateList) {
        let precision =
          analyticsObj.truePositive + analyticsObj.falsePositive > 0
            ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falsePositive)
            : 'NaN';
        let recall =
          analyticsObj.truePositive + analyticsObj.falseNegative > 0
            ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falseNegative)
            : 'NaN';
        let f1Score =
          2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive > 0
            ? (100 * 2 * analyticsObj.truePositive) /
              (2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive)
            : 'NaN';
        let labelWiseAnalytics: LabelAnalytics[] = getLabelWiseCount(analyticsObj.labelData);

        let tempAnalyticsObj: Analytics = {
          truePositive: analyticsObj.truePositive,
          falseNegative: analyticsObj.falseNegative,
          falsePositive: analyticsObj.falsePositive,
          operationId: analyticsObj.operationId,
          precision: precision,
          recall: recall,
          f1Score: f1Score,
          labelWiseAnalytics: labelWiseAnalytics,
        };
        analytics.push(tempAnalyticsObj);
      }

      try {
        await this.metaDataRepository.updateById(String(datasetId), {analytics: analytics});
        logger.debug(
          `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalytics | N/A | analytics of dataset id: ${datasetId} updated`,
        );
      } catch (err) {
        logger.error(
          `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalytics | N/A | dataset analytics update failed with error:  ${err}`,
        );
      }
    }
  }

  /**
   * calculate analytics while change filed of dataset
   * @param datasetGroupId dataset meta id
   * @returns nothing
   */
  async calcDatasetAnalyticsWhileChangeFiles(datasetGroupId: string) {
    let datasetMetaObj = await this.metaDataRepository.findOne({where: {datasetGroupId: datasetGroupId}});
    let datasetMetaId = datasetMetaObj?.id;

    if (!datasetMetaId) return;

    logger.debug(
      `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalyticsWhileChangeFiles | N/A | calculate analytics while change filed of dataset: ${datasetMetaId} started`,
    );

    const analyticsUpdateList: AnalyticsUpdateAgg[] = await this.metaDataRepository.aggregate([
      // {$match: {'datasetVersionList.datasetMetaId': new ObjectId(datasetMetaId)}},
      {$match: {vCollectionIdList: new ObjectId(datasetMetaId)}},
      {$unwind: '$analytics'},
      {
        $unwind: '$analytics.labelWiseAnalytics',
      },
      {
        $group: {
          _id: {
            operationId: '$analytics.operationId',
            label: '$analytics.labelWiseAnalytics.label',
          },
          truePositive: {$sum: '$analytics.labelWiseAnalytics.truePositive'},
          falsePositive: {$sum: '$analytics.labelWiseAnalytics.falsePositive'},
          falseNegative: {$sum: '$analytics.labelWiseAnalytics.falseNegative'},
        },
      },
      {
        $group: {
          _id: '$_id.operationId',
          truePositive: {$sum: '$truePositive'},
          falsePositive: {$sum: '$falsePositive'},
          falseNegative: {$sum: '$falseNegative'},
          labelData: {
            $push: {
              label: '$_id.label',
              truePositive: '$truePositive',
              falsePositive: '$falsePositive',
              falseNegative: '$falseNegative',
            },
          },
        },
      },
      {$project: {operationId: '$_id', truePositive: 1, falsePositive: 1, falseNegative: 1, labelData: 1, _id: 0}},
    ]);

    let analytics: Analytics[] = [];
    for (let analyticsObj of analyticsUpdateList) {
      let precision =
        analyticsObj.truePositive + analyticsObj.falsePositive > 0
          ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falsePositive)
          : 'NaN';
      let recall =
        analyticsObj.truePositive + analyticsObj.falseNegative > 0
          ? (100 * analyticsObj.truePositive) / (analyticsObj.truePositive + analyticsObj.falseNegative)
          : 'NaN';
      let f1Score =
        2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive > 0
          ? (100 * 2 * analyticsObj.truePositive) /
            (2 * analyticsObj.truePositive + analyticsObj.falseNegative + analyticsObj.falsePositive)
          : 'NaN';

      let labelWiseAnalytics: LabelAnalytics[] = getLabelWiseCount(analyticsObj.labelData);

      let tempAnalyticsObj: Analytics = {
        truePositive: analyticsObj.truePositive,
        falseNegative: analyticsObj.falseNegative,
        falsePositive: analyticsObj.falsePositive,
        operationId: analyticsObj.operationId,
        precision: precision,
        recall: recall,
        f1Score: f1Score,
        labelWiseAnalytics: labelWiseAnalytics,
      };
      analytics.push(tempAnalyticsObj);
    }

    try {
      await this.metaDataRepository.updateById(String(datasetMetaId), {analytics: analytics});
      logger.debug(
        `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalyticsWhileChangeFiles | N/A | analytics of dataset id: ${datasetMetaId} updated`,
      );
    } catch (err) {
      logger.error(
        `${FLOWS.ANALYTICS_CALCULATION} | MetaDataService.calcDatasetAnalyticsWhileChangeFiles | N/A | dataset analytics update failed with error:  ${err}`,
      );
    }
  }

  /**
   * Merge selected collections(image, video and other) and after merging selected collection will be deleted.
   * @example A and B collections get merged  to create collection C. After merging C, A and B will be deleted.
   * @param teamId {string} team id
   * @param selectionId {string} selection id
   * @param vCollectionName {string} name of virtual collection
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @returns {Promise<{jobId: string}>} job id of the merge
   */
  async mergeCollection(
    teamId: string,
    selectionId: string,
    vCollectionName: string,
    currentUserProfile: UserProfileDetailed,
  ): Promise<{jobId: string}> {
    //validate selection id
    if (!isValidObjectId(selectionId)) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_SELECTION_ID);
    let selectionObj = await this.datalakeSelectionRepository.findById(selectionId);
    let filter: DatalakeSelectionRequest = selectionObj.selectionRequest;
    let objectType: ContentType = selectionObj.objectType;

    //Merge available collection types
    const mergeAvailableCollection: ContentType[] = [
      ContentType.IMAGE_COLLECTION,
      ContentType.VIDEO_COLLECTION,
      ContentType.OTHER_COLLECTION,
    ];

    //check if merge can be done for selected collection type
    if (!mergeAvailableCollection.includes(objectType)) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.MERGE_UNAVAILABLE);
    }

    //check if collection name is exists in selected collection type
    let isExist = await this.metaDataRepository.findOne({
      where: {
        name: vCollectionName,
        objectType: objectType,
        teamId: teamId,
      },
    });

    if (isExist) {
      logger.error(
        `Merge collection | MetaDataRepository.createCollectionMeta | ${teamId} | failed to create new collection collectionName:${vCollectionName}  collectionObjectType:${objectType} teamId:${teamId}, similar collection exist for created collection: ${isExist.id}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.SIMILAR_OBJECT_EXIST);
    }
    const sessionId: string = `merge_coll-${uuidV4()}`;

    let metaDataTypeName: string = getObjectTypeName(objectType);
    let jobName: string = `Merge ${metaDataTypeName}s`;
    let jobDetails: JobSpecificDetails = {
      contentType: objectType,
      startedAt: new Date(),
      selectionId: selectionId,
    };

    if (isValidObjectId(filter.collectionId)) jobDetails.collectionId = new ObjectId(filter.collectionId);

    await this.jobService.createOrUpdateJob(
      jobName,
      sessionId,
      currentUserProfile?.id,
      teamId,
      currentUserProfile?.name,
      JobType.CollectionMerge,
      0,
      JobStatus.queued,
      jobDetails,
    );

    const config: UpdateJobConfig = {
      isAwait: true,
      jobName: jobName,
      userId: currentUserProfile?.id,
      userName: currentUserProfile?.name,
      sessionId: sessionId,
      teamId: teamId,
    };

    this.handleMergeCollection(teamId, selectionId, vCollectionName, filter, config, currentUserProfile);

    let existingSession = await this.jobRepository.findOne({
      where: {
        'jobSpecificDetails.sessionId': sessionId,
        jobType: JobType.CollectionMerge,
      },
    });

    return {jobId: existingSession?._id ?? ''};
  }

  /**
   * This method update job of merging
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @param jobDetails {JobSpecificDetails} job details
   * @param jobStatus {JobStatus} job status
   * @param progress {number} progress of job
   * @returns {Promise<void>} void
   */
  async updateMergeJob(
    config?: UpdateJobConfig,
    jobDetails?: JobSpecificDetails,
    jobStatus: JobStatus = JobStatus.failed,
    progress: number = 0,
  ): Promise<void> {
    if (!config?.sessionId || !config?.jobName) return;

    let jobData: JobSpecificDetails = {finishedAt: new Date()};
    if (jobDetails) jobData = {...jobData, ...jobDetails};
    await this.jobService.createOrUpdateJob(
      config.jobName,
      config.sessionId,
      config?.userId,
      config?.teamId,
      config?.userName,
      JobType.CollectionMerge,
      progress,
      jobStatus,
      jobData,
    );
  }

  /**
   * This method handle creating new collection and delete the all selected collections
   * @param teamId {string} team id
   * @param selectionId {string} selection id
   * @param vCollectionName {string} name of virtual collection
   * @param filter {DatalakeSelectionRequest} filter of selection
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @param currentUserProfile {UserProfileDetailed} current user profile
   *@returns {Promise<void>} void
   */
  async handleMergeCollection(
    teamId: string,
    selectionId: string,
    vCollectionName: string,
    filter: DatalakeSelectionRequest,
    config: UpdateJobConfig,
    currentUserProfile: UserProfileDetailed,
  ): Promise<void> {
    try {
      const {collectionId} = await this.updateVirtualCollection(
        currentUserProfile,
        selectionId,
        config,
        undefined,
        vCollectionName,
      );

      /**
       * Get unique tags and custom meta data of selected collections
       */
      const {tags, customMeta} = await this.getTagsAndCustomMetaDataOfSelectedCollections(
        currentUserProfile,
        filter,
        teamId,
        config,
      );

      await this.metaDataRepository.updateById(collectionId, {
        customMeta: customMeta,
        Tags: tags,
      });

      await this.removeObjects(filter, teamId, selectionId, currentUserProfile, config);
    } catch (err) {
      logger.error(
        `Merge collection | MetaDataService.handleMergeCollection | ${teamId} |error in merge collection handle`,
        err,
      );
    }
  }

  /**
   * This is used to get unique tags and custom meta data applied to the selected collections
   * @param currentUserProfile {UserProfileDetailed} current user profile
   * @param filter {DatalakeSelectionRequest} filter of selection
   * @param teamId {string} team id
   * @param config {UpdateJobConfig} job config(like session id, user id, user name, team id)
   * @returns {Promise<GetTagsAndCustomMetaDataOfSelectedCollectionsRes>} tags and custom meta data
   */
  async getTagsAndCustomMetaDataOfSelectedCollections(
    currentUserProfile: UserProfileDetailed,
    filter: DatalakeSelectionRequest,
    teamId: string,
    config: UpdateJobConfig,
  ): Promise<GetTagsAndCustomMetaDataOfSelectedCollectionsRes> {
    let datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };
    let aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );
    if (!aggregateQuery?.matchQuery) {
      await this.updateMergeJob(config, {error: DatalakeUserMessages.FAILED_REQUEST});
      throw new HttpErrors.UnprocessableEntity('Invalid selection query');
    }

    //get tag data
    let tagParam: Record<string, any>[] = [
      {$match: aggregateQuery.matchQuery},
      {$unwind: '$Tags'},
      {
        $group: {
          _id: null,
          tags: {$addToSet: '$Tags'},
        },
      },
    ];

    let tagListAgg: {_id: null; tags: string[]}[] | undefined = await this.metaDataRepository.aggregate(tagParam);
    let tags: string[] = tagListAgg?.[0]?.tags && Array.isArray(tagListAgg[0].tags) ? tagListAgg[0].tags : [];

    //get custom meta data
    let customMetaDataParam: Record<string, any>[] = [
      {$match: aggregateQuery.matchQuery},
      {
        $project: {name: 1, keyValueArray: {$objectToArray: '$customMeta'}},
      },
      {$unwind: '$keyValueArray'},

      {
        $group: {
          _id: {
            key: '$keyValueArray.k',
            value: '$keyValueArray.v',
          },
        },
      },
      {
        $group: {
          _id: '$_id.key',
          customMeta: {$addToSet: '$_id.value'},
        },
      },
    ];

    let customMetaAgg: {_id: string; customMeta: string[]}[] | undefined = await this.metaDataRepository.aggregate(
      customMetaDataParam,
    );
    let customMetaArray: {_id: string; customMeta: string[]}[] = Array.isArray(customMetaAgg) ? customMetaAgg : [];

    const customMeta: {[key: string]: any} = customMetaArray.reduce((obj, currentValue) => {
      obj[`${currentValue._id}`] = currentValue.customMeta.join(',');
      return obj;
    }, {} as {[key: string]: any});

    return {
      tags: tags,
      customMeta: customMeta,
    };
  }

  /**
   * use to create a virtual collection using a selectionId
   */
  async updateVirtualCollection(
    currentUserProfile: UserProfileDetailed,
    selectionId: string,
    config: UpdateJobConfig,
    vCollectionId?: string,
    vCollectionName?: string,
    customMeta?: {[k: string]: any},
  ) {
    let sessionId = uuidV4();
    let teamId = currentUserProfile?.teamId;

    logger.info(
      `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | update virtual collection  selectionId: ${selectionId}, vCollectionId: ${vCollectionId}, vCollectionName: ${vCollectionName}`,
    );

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | invalid user profile`,
      );
      throw new HttpErrors.NotAcceptable(`invalid user profile`);
    }

    let matchQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(currentUserProfile, selectionId);

    if (!matchQuery) {
      logger.error(
        `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | invalid matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
      );
      await this.updateMergeJob(config, {error: DatalakeUserMessages.FAILED_REQUEST});
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FAILED_REQUEST);
    }

    // find child type of selected object type
    let childType = await this.metaDataRepository.getChildObjectTypeByObjectType(matchQuery.objectType);

    if (childType == ContentType.UNSUPPORTED) {
      logger.error(
        `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | unsupported child type matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
      );
      await this.updateMergeJob(config, {error: DatalakeUserMessages.INVALID_OBJECT_TYPE});
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    let jobId = '';

    // if collection exist, then add matched files to the existing collection
    if (vCollectionId) {
      let vCollectionObj = await this.metaDataRepository.findById(vCollectionId);

      let job = await this.jobService.createOrUpdateJob(
        `Update ${vCollectionObj.name}`,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        0,
        JobStatus.queued,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );
      jobId = job._id;

      let parentObjectType = await this.metaDataRepository.getCollectionObjectTypeByObjectType(childType);

      if (parentObjectType != vCollectionObj.objectType) {
        logger.error(
          `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | invalid object selection to add existing virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
        );

        let job = await this.jobService.createOrUpdateJob(
          `Update ${vCollectionObj.name}`,
          sessionId,
          currentUserProfile?.name,
          currentUserProfile?.teamId,
          currentUserProfile?.name,
          JobType.VirtualCollection,
          0,
          JobStatus.failed,
          {
            collectionId: new ObjectId(vCollectionId),
            error: `invalid object selection to add existing virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
          },
        );
        jobId = job._id;

        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
      }

      if (
        currentUserProfile &&
        currentUserProfile.userType &&
        currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR &&
        currentUserProfile.id &&
        vCollectionObj.allowedUserIdList &&
        Array.isArray(vCollectionObj.allowedUserIdList) &&
        vCollectionObj.allowedUserIdList.length > 0
      ) {
        let allowedUserStringIdList: string[] = [];
        allowedUserStringIdList = vCollectionObj.allowedUserIdList.map(elem => elem.toString());

        if (!allowedUserStringIdList.includes(currentUserProfile.id.toString())) {
          logger.error(
            `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | user is not allowed to update virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
          );

          let job = await this.jobService.createOrUpdateJob(
            `Update ${vCollectionObj.name}`,
            sessionId,
            currentUserProfile?.name,
            currentUserProfile?.teamId,
            currentUserProfile?.name,
            JobType.VirtualCollection,
            0,
            JobStatus.failed,
            {
              collectionId: new ObjectId(vCollectionId),
              error: `user is not allowed to update virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
            },
          );
          jobId = job._id;

          throw new HttpErrors.NotAcceptable(DatalakeUserMessages.USER_NOT_ALLOWED);
        } else {
          /* then ok */
        }
      }

      // tag selected objects to the collection
      this.tagFilesToVirtualCollection(
        vCollectionId,
        matchQuery,
        sessionId,
        currentUserProfile,
        vCollectionObj.allowedUserIdList ? vCollectionObj.allowedUserIdList.map(id => new ObjectId(id)) : [],
      );

      // if virtual collection is Image collection or dataset, then isFeatureGraphPending flag should be set to true
      if (
        vCollectionObj.objectType == ContentType.IMAGE_COLLECTION ||
        vCollectionObj.objectType == ContentType.DATASET
      ) {
        await this.metaDataRepository.updateById(vCollectionId, {isFeatureGraphPending: true});
      }

      //check whether given collection is a virtual collection
      // if (vCollectionObj.isLogical) {
      //   let parentObjectType = await this.metaDataRepository.getCollectionObjectTypeByObjectType(childType);

      //   if (parentObjectType != vCollectionObj.objectType) {
      //     logger.error(
      //       `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | invalid object selection to add existing virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
      //     );

      //     await this.jobService.createOrUpdateJob(
      //       `Update ${vCollectionObj.name}`,
      //       sessionId,
      //       currentUserProfile?.name,
      //       currentUserProfile?.teamId,
      //       currentUserProfile?.name,
      //       JobType.VirtualCollection,
      //       0,
      //       JobStatus.failed,
      //       {
      //         collectionId: new ObjectId(vCollectionId),
      //         error: `invalid object selection to add existing virtual collection matchQuery: ${matchQuery} for selectionId: ${selectionId}`,
      //       },
      //     );
      //     throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
      //   }

      //   // tag selected objects to the collection
      //   this.tagFilesToVirtualCollection(vCollectionId, matchQuery, sessionId, currentUserProfile);
      // } else {
      //   logger.error(
      //     `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | collection is not virtual, collectionId: ${vCollectionId}`,
      //   );

      //   await this.jobService.createOrUpdateJob(
      //     `Update ${vCollectionObj.name}`,
      //     sessionId,
      //     currentUserProfile?.name,
      //     currentUserProfile?.teamId,
      //     currentUserProfile?.name,
      //     JobType.VirtualCollection,
      //     0,
      //     JobStatus.failed,
      //     {
      //       collectionId: new ObjectId(vCollectionId),
      //       error: `collection is not virtual, collectionId: ${vCollectionId}`,
      //     },
      //   );
      //   throw new HttpErrors.NotAcceptable(DatalakeUserMessages.NOT_VIRTUAL_COLLECTION);
      // }
    }
    // if collection does not exist, then create a new collection add matched files to the new collection
    else {
      //create new virtual collection
      if (!vCollectionName) {
        logger.error(
          `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | invalid name for vCollectionName: ${vCollectionName}`,
        );
        let jobSpecificDetails: JobSpecificDetails = {
          collectionId: new ObjectId(vCollectionId),
          error: `invalid name for vCollectionName: ${vCollectionName}`,
        };
        let job = await this.jobService.createOrUpdateJob(
          `Create ${vCollectionName}`,
          sessionId,
          currentUserProfile?.name,
          currentUserProfile?.teamId,
          currentUserProfile?.name,
          JobType.VirtualCollection,
          0,
          JobStatus.failed,
          jobSpecificDetails,
        );

        await this.updateMergeJob(config, jobSpecificDetails);
        jobId = job._id;
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_NAME);
      }

      let customMetaData = customMeta ? customMeta : {};
      let allowedUserIdListObj: any = {allowedUserIdList: []};

      if (
        currentUserProfile &&
        currentUserProfile.userType &&
        currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR &&
        currentUserProfile.id
      ) {
        allowedUserIdListObj.allowedUserIdList = [new ObjectId(currentUserProfile.id)];
      }

      let job = await this.jobService.createOrUpdateJob(
        `Create ${vCollectionName}`,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        0,
        JobStatus.queued,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );
      jobId = job._id;

      // create new virtual collection head
      vCollectionId = await this.metaDataRepository.createCollectionMeta(
        vCollectionName,
        childType,
        new ObjectId(teamId),
        customMetaData,
        false,
        allowedUserIdListObj,
      );

      // tag selected objects to the collection
      if (vCollectionId) {
        if (config.isAwait) {
          config.virtualCollectionJobId = jobId;
          await this.tagFilesToVirtualCollection(vCollectionId, matchQuery, sessionId, currentUserProfile, [], config);
        } else {
          this.tagFilesToVirtualCollection(vCollectionId, matchQuery, sessionId, currentUserProfile);
        }
      } else {
        logger.error(
          `${FLOWS.VIRTUAL_COLLECTION} | MetaDataService.updateVirtualCollection | ${teamId} | failed to create collection head selectionId: ${selectionId}, vCollectionId: ${vCollectionId}, vCollectionName: ${vCollectionName}`,
        );
        let jobSpecificDetails: JobSpecificDetails = {
          collectionId: new ObjectId(vCollectionId),
          error: `failed to create collection head selectionId: ${selectionId}, vCollectionId: ${vCollectionId}, vCollectionName: ${vCollectionName}`,
        };

        let job = await this.jobService.createOrUpdateJob(
          `Create ${vCollectionName}`,
          sessionId,
          currentUserProfile?.name,
          currentUserProfile?.teamId,
          currentUserProfile?.name,
          JobType.VirtualCollection,
          0,
          JobStatus.failed,
          jobSpecificDetails,
        );

        await this.updateMergeJob(config, jobSpecificDetails);

        jobId = job._id;
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FAILED_REQUEST);
      }
    }

    return {collectionId: vCollectionId, jobId: jobId};
  }

  /**
   * Use to tag videos and images to virtual collections
   */
  async tagFilesToVirtualCollection(
    vCollectionId: string,
    matchObj: {
      matchQuery: any;
      objectType: ContentType;
    },
    sessionId: string,
    currentUserProfile?: UserProfileDetailed,
    allowedUserIdList?: ObjectId[],
    config?: UpdateJobConfig,
  ) {
    let jobProgress: number = 10;
    let jobStatus: JobStatus = JobStatus.inProgress;
    await this.jobService.createOrUpdateJob(
      ``,
      sessionId,
      currentUserProfile?.name,
      currentUserProfile?.teamId,
      currentUserProfile?.name,
      JobType.VirtualCollection,
      jobProgress,
      jobStatus,
      {
        collectionId: new ObjectId(vCollectionId),
      },
    );

    let jobDetails: JobSpecificDetails | undefined = config?.virtualCollectionJobId
      ? {virtualCollectionJobId: new ObjectId(config.virtualCollectionJobId)}
      : undefined;

    await this.updateMergeJob(config, jobDetails, jobStatus, jobProgress / 2);

    let annotationProjectList: AnnotationProjectList[] = [];
    let datasetVersionInfo: {
      datasetVersionId: ObjectId;
      datasetMetaId: ObjectId;
      datasetGroupId: ObjectId;
      datasetGroupName: string;
    }[] = [];

    let allowedUserObjectIdList = allowedUserIdList && allowedUserIdList.length > 0 ? allowedUserIdList : [];

    if (
      matchObj.objectType == ContentType.IMAGE ||
      matchObj.objectType == ContentType.VIDEO ||
      matchObj.objectType == ContentType.OTHER
    ) {
      jobProgress = 40;

      annotationProjectList = await this.getAnnotationProjectListByMatchQuery(matchObj.matchQuery);
      datasetVersionInfo = await this.getDatasetVersionInfoByMatchQuery(matchObj.matchQuery);

      await this.jobService.createOrUpdateJob(
        ``,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        jobProgress,
        jobStatus,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );

      await this.updateMergeJob(config, undefined, jobStatus, jobProgress / 2);

      await this.metaDataRepository.updateManyAddToSetToList(
        matchObj.matchQuery,
        {
          vCollectionIdList: new ObjectId(vCollectionId),
          allowedUserIdList: {$each: allowedUserObjectIdList},
        },
        [],
      );

      jobProgress = 60;
      await this.jobService.createOrUpdateJob(
        ``,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        jobProgress,
        jobStatus,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );

      await this.updateMergeJob(config, undefined, jobStatus, jobProgress / 2);
    } else if (
      matchObj.objectType == ContentType.IMAGE_COLLECTION ||
      matchObj.objectType == ContentType.VIDEO_COLLECTION ||
      matchObj.objectType == ContentType.OTHER_COLLECTION ||
      matchObj.objectType == ContentType.DATASET
    ) {
      let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
        {
          $match: matchObj.matchQuery,
        },
        {
          $project: {_id: 1},
        },
      ]);
      let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));

      let matchQuery = {
        vCollectionIdList: {$in: collectionObjectIdList},
      };

      annotationProjectList = await this.getAnnotationProjectListByMatchQuery(matchQuery);
      datasetVersionInfo = await this.getDatasetVersionInfoByMatchQuery(matchQuery);
      jobProgress = 40;
      await this.jobService.createOrUpdateJob(
        ``,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        jobProgress,
        jobStatus,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );

      await this.updateMergeJob(config, undefined, jobStatus, jobProgress / 2);

      await this.metaDataRepository.updateManyAddToSetToList(
        matchQuery,
        {
          vCollectionIdList: new ObjectId(vCollectionId),
          allowedUserIdList: {$each: allowedUserObjectIdList},
        },
        [],
      );
      jobProgress = 60;
      await this.jobService.createOrUpdateJob(
        ``,
        sessionId,
        currentUserProfile?.name,
        currentUserProfile?.teamId,
        currentUserProfile?.name,
        JobType.VirtualCollection,
        jobProgress,
        jobStatus,
        {
          collectionId: new ObjectId(vCollectionId),
        },
      );
      await this.updateMergeJob(config, undefined, jobStatus, jobProgress / 2);
    }
    // else if (matchObj.objectType == ContentType.DATASET) {
    //   let _datasetIds: {_id: string}[] = await this.metaDataRepository.aggregate([
    //     {
    //       $match: matchObj.matchQuery,
    //     },
    //     {
    //       $project: {_id: 1},
    //     },
    //   ]);

    //   let datasetObjectIdList = _datasetIds.map(elem => new ObjectId(elem._id));
    //   let matchQuery = {
    //     'datasetVersionList.datasetMetaId': {$in: datasetObjectIdList},
    //     objectType: ContentType.IMAGE,
    //   };

    //   await this.metaDataRepository.updateManyAddToSetToList(
    //     matchQuery,
    //     {
    //       vCollectionIdList: new ObjectId(vCollectionId),
    //     },
    //     [],
    //   );
    // }

    if ((annotationProjectList && annotationProjectList.length > 0) || datasetVersionInfo.length > 0) {
      await this.metaDataRepository.updateManyAddToSetToList(
        {_id: new ObjectId(vCollectionId)},
        {
          annotationProjectList: {$each: annotationProjectList},
          datasetVersionList: {$each: datasetVersionInfo},
        },
        [],
      );
    }

    if (
      currentUserProfile &&
      currentUserProfile.userType &&
      currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR
    ) {
      await this.metaDataRepository.updateManyAddToSetToList(
        {
          $or: [{_id: new ObjectId(vCollectionId)}, {vCollectionIdList: new ObjectId(vCollectionId)}],
        },
        {
          allowedUserIdList: new ObjectId(currentUserProfile.id),
        },
        [],
      );
    }

    await this.jobService.createOrUpdateJob(
      ``,
      sessionId,
      currentUserProfile?.name,
      currentUserProfile?.teamId,
      currentUserProfile?.name,
      JobType.VirtualCollection,
      80,
      JobStatus.inProgress,
      {
        collectionId: new ObjectId(vCollectionId),
      },
    );

    await this.updateMergeJob(config, undefined, jobStatus, 40);

    await this.vCollectionAsyncUpdateSystem(vCollectionId);

    await this.jobService.createOrUpdateJob(
      ``,
      sessionId,
      currentUserProfile?.name,
      currentUserProfile?.teamId,
      currentUserProfile?.name,
      JobType.VirtualCollection,
      100,
      JobStatus.completed,
      {
        collectionId: new ObjectId(vCollectionId),
      },
    );

    await this.updateMergeJob(config, undefined, JobStatus.inProgress, 50);
  }

  /**
   * use to get annotation project list by match query
   * @param matchQuery match query of child object
   * @returns annotation project List
   */
  async getAnnotationProjectListByMatchQuery(matchQuery: any) {
    let returnList: AnnotationProjectList[] = [];

    let annotationProjectList = await this.metaDataRepository.aggregate([
      {
        $match: matchQuery,
      },
      {
        $project: {
          annotationProjectList: 1,
        },
      },
      {
        $unwind: '$annotationProjectList',
      },
      {
        $group: {
          _id: '$annotationProjectList.projectId',
          annotationProjectList: {$addToSet: '$annotationProjectList'},
        },
      },
      {
        $project: {
          _id: 0,
          annotationProjectList: 1,
        },
      },
    ]);

    if (annotationProjectList && annotationProjectList.length > 0) {
      returnList = annotationProjectList[0].annotationProjectList;
    }

    return returnList;
  }

  /**
   * Use to get dataset version info list by match query
   * @param matchQuery match query of child object
   * @returns dataset object list
   */
  async getDatasetVersionInfoByMatchQuery(matchQuery: any) {
    let returnList: {
      datasetVersionId: ObjectId;
      datasetMetaId: ObjectId;
      datasetGroupId: ObjectId;
      datasetGroupName: string;
    }[] = [];

    let datasetVersionInfo = await this.metaDataRepository.aggregate([
      {
        $match: matchQuery,
      },
      {
        $project: {
          datasetVersionList: 1,
        },
      },
      {
        $unwind: '$datasetVersionList',
      },
      {
        $group: {
          _id: null,
          datasetVersionList: {
            $addToSet: {
              datasetVersionId: '$datasetVersionList.datasetVersionId',
              datasetMetaId: '$datasetVersionList.datasetMetaId',
              datasetGroupId: '$datasetVersionList.datasetGroupId',
              datasetGroupName: '$datasetVersionList.datasetGroupName',
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          datasetVersionList: 1,
        },
      },
    ]);

    if (datasetVersionInfo && datasetVersionInfo.length > 0) {
      returnList = datasetVersionInfo[0].datasetVersionList;
    }

    return returnList;
  }

  /**
   * use to update system stat data for virtual collection
   * @param vCollectionId virtual collection id
   */
  async vCollectionAsyncUpdateSystem(vCollectionId: string) {
    this.systemStatsService.updateParentStats(vCollectionId);
    this.metaDataRepository.updateUpdatedAt(vCollectionId);
    this.queryOptionRepository.rebuildCollectionQueryOptionForTags(vCollectionId);
    //calculate meta fields and tags summary and update in collection
    this.statsCalculationService.handleMetaFieldsAndTagsSummaryPropagation(vCollectionId, false);
  }

  defaultVerificationStats: VerificationStatusCount = {
    raw: 0,
    machineAnnotated: 0,
    verified: 0,
  };

  // /**
  //  * Use for update the metadata updated at with its parents
  //  * @param metadataId {string} id of the metadata
  //  */
  // async updateUpdatedAt(metadataId?: string, objectKey?: string) {
  //   if (!metadataId && !objectKey) return;
  //   let metaObj;
  //   //set updatedAt
  //   if (metadataId) {
  //     metaObj = await this.metaDataRepository.findById(metadataId);
  //   } else {
  //     metaObj = await this.metaDataRepository.findOne({where: {objectKey: objectKey}});
  //   }

  //   if (metaObj) {
  //     let updateList = metaObj.vCollectionIdList ? metaObj.vCollectionIdList : [];
  //     let updateObjectIdList = updateList.map(obj => new ObjectId(obj));
  //     updateObjectIdList.push(new ObjectId(metadataId));
  //     if (updateObjectIdList && updateObjectIdList.length > 0) {
  //       logger.debug(
  //         `Update - edit metadata Tags | MetaDataService.updateUpdatedAt | N/A | edit updateAt parent list: ${updateObjectIdList} `,
  //       );
  //       this.metaDataRepository.updateManySet({_id: {$in: updateObjectIdList}}, {updatedAt: new Date()}, []);
  //     }
  //   }
  // }

  /**
   * use to get object type by object id
   * @params id : string
   * @returns {objectType: number}
   */
  async getObjectTypeById(id: string) {
    let metaDataObj = await this.metaDataRepository.findOne({where: {id: id}, fields: {objectType: true}});
    return {objectType: metaDataObj?.objectType};
  }

  /**
   * Use to remove files from virtual collection
   * @param vCollectionId {string} id of the virtual collection
   * @param selectionId {string} id of the selection
   * @returns
   */
  async removeFilesInVirtualCollection(
    vCollectionId: string,
    selectionId: string,
    currentUserProfile: UserProfileDetailed,
  ) {
    //validate collection is virtual
    // let collection = await this.metaDataRepository.findById(vCollectionId);

    // if (!collection.isLogical) {
    //   logger.error(
    //     `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeFilesInVirtualCollection | N/A | Collection is not virtual`,
    //   );
    //   throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_IS_NOT_VIRTUAL);
    // }

    let isVirtualObjectExist = await this.metaDataRepository.findOne({
      where: {
        vCollectionIdList: new ObjectId(vCollectionId),
      },
    });

    if (!isVirtualObjectExist) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeFilesInVirtualCollection | N/A | Collection is not virtual`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_IS_NOT_VIRTUAL);
    }

    // get object list related to selection id
    let matchQueryObj = await this.searchQueryBuilderService.getMatchQueryForSelection(currentUserProfile, selectionId);

    await this.metaDataRepository.updateManyRemoveFromList(
      matchQueryObj?.matchQuery,
      {vCollectionIdList: new ObjectId(vCollectionId)},
      [],
    );

    this.systemStatsService.updateParentStats(vCollectionId);
    this.metaDataRepository.updateUpdatedAt(vCollectionId);
    this.queryOptionRepository.rebuildCollectionQueryOptionForTags(vCollectionId);
  }

  /**
   * Get the pre-signed downloadable url of given object key
   * Fetch the url from metadata collection and return
   * @param objectKey {string} object key of the file
   * @returns {string} pre-signed url
   */
  async getDownloadableUrl(objectKey: string) {
    let metaObject = await this.metaDataRepository.findOne({
      where: {objectKey: objectKey},
      fields: {url: true},
    });
    if (!metaObject) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | MetaDataService.getDownloadableUrl | key: ${objectKey} | Failed to get metadata`,
      );
      throw new HttpErrors.NotFound(DatalakeUserMessages.FAILED_REQUEST);
    }
    return metaObject.url;
  }

  /**
   * Use to tag taskId to images
   * @param metaDataInputObject
   */
  async tagImageToProject(metaDataInputObject: MetaDataInputFeedObject) {
    let bulkUpdateOneArr: {
      updateOne: {
        filter: {objectKey: string};
        update: {
          $addToSet: {
            // annotationProjectList?: {$each: {id: ObjectId, name: string}[]},
            taskIdList?: {$each: ObjectId[]};
          };
          // $set: {
          //   resolution?: {
          //     width?: number;
          //     height?: number;
          //   },
          //   statPending?: boolean
          // }
        };
      };
    }[] = [];

    for (let metaData of metaDataInputObject.metaDataList) {
      let objectKey = metaData.metaDataObject.objectKey;
      if (!objectKey) continue;

      let resolution = metaData.metaDataObject.resolution;
      let projectList = metaData.metaDataPushList.annotationProjectList?.map(project => {
        return {
          name: project.name,
          id: new ObjectId(project.id),
        };
      });
      if (!projectList) projectList = [];
      let taskIdList = metaData.metaDataPushList.taskIdList?.map(id => new ObjectId(id));
      if (!taskIdList) taskIdList = [];

      bulkUpdateOneArr.push({
        updateOne: {
          filter: {objectKey: objectKey},
          update: {
            $addToSet: {
              // handleGetMetaDataArraytWithChildFiles already doing project tagging
              // annotationProjectList: {$each: projectList},
              taskIdList: {$each: taskIdList},
            },
            // $set: {
            //   resolution: resolution,
            //   statPending: true
            // }
          },
        },
      });
    }

    if (bulkUpdateOneArr && bulkUpdateOneArr.length > 0) {
      await this.metaDataRepository.bulkWrite(bulkUpdateOneArr);
    }
  }

  /**
   * update annotation project info list to metadata
   * @param metaDataInputObject metaDataInputObject
   */
  async updateAnnotationProjectInfoList(metaDataInputObject: MetaDataInputFeedObject) {
    let bulkUpdateOneArr: {
      updateOne: {
        filter: {objectKey: string};
        update: {
          $addToSet: {
            annotationProjectList?: {$each: {id: ObjectId; name: string}[]};
            taskIdList?: {$each: ObjectId[]};
            parentList?: {$each: ObjectId[]};
          };
          $set: {
            statPending?: boolean;
            statPendingAt?: Date;
            sourceVideoId?: ObjectId;
            isDerivedFile?: boolean;
            videoFrameIndex?: number;
            isOriginalUploadFile?: boolean;
          };
        };
      };
    }[] = [];

    for (let metaData of metaDataInputObject.metaDataList) {
      let objectKey = metaData.metaDataObject.objectKey;
      if (!objectKey) continue;
      let sourceVideoId = metaData.metaDataObject.sourceVideoId;
      let isDerivedFile = metaData.metaDataObject.isDerivedFile;
      let videoFrameIndex = metaData.metaDataObject.videoFrameIndex;

      let projectList = metaData.metaDataPushList.annotationProjectList?.map(project => {
        return {
          name: project.name,
          id: new ObjectId(project.id),
        };
      });
      if (!projectList) projectList = [];
      let taskIdList = metaData.metaDataPushList.taskIdList?.map(id => new ObjectId(id));
      if (!taskIdList) taskIdList = [];
      let parentList = metaData.metaDataPushList.parentList?.map(id => new ObjectId(id));
      if (!parentList) parentList = [];

      bulkUpdateOneArr.push({
        updateOne: {
          filter: {objectKey: objectKey},
          update: {
            $addToSet: {
              annotationProjectList: {$each: projectList},
              taskIdList: {$each: taskIdList},
              parentList: {$each: parentList},
            },
            $set: {
              statPending: true,
              statPendingAt: new Date(),
              sourceVideoId: new ObjectId(sourceVideoId),
              isDerivedFile: isDerivedFile,
              videoFrameIndex: videoFrameIndex,
              isOriginalUploadFile: false,
            },
          },
        },
      });
    }

    if (bulkUpdateOneArr && bulkUpdateOneArr.length > 0) {
      logger.debug(
        `Update - edit metadata Tags | MetaDataService.updateAnnotationProjectInfoList | N/A | edit updateAt parent list: ${JSON.stringify(
          bulkUpdateOneArr,
        )} `,
      );
      await this.metaDataRepository.bulkWrite(bulkUpdateOneArr);
    }
  }

  async deleteCloudPrefix(prefix: string) {
    // prefix delete only allowed for STORAGE_SYSTEM_FOLDER
    if (!prefix.startsWith(STORAGE_SYSTEM_FOLDER)) {
      logger.error(
        `MetadataService| MetadataService.deleteCloudPrefix | N/A | Delete prefix: ${prefix} error: prefix delete only allowed for STORAGE_SYSTEM_FOLDER`,
      );
      return;
    }

    try {
      let param = {
        Bucket: '',
        ContinuationToken: undefined,
        Prefix: prefix,
        NextQueryParam: undefined,
      };
      let list;
      do {
        list = await this.storageCrawlerService.storageServiceProvider.getFileList(param);
        param = {
          Bucket: '',
          ContinuationToken: list.nextPageRef.ContinuationToken,
          Prefix: prefix,
          NextQueryParam: undefined,
        };
        for (let object of list.fileList) {
          logger.info(
            `MetadataService| MetadataService.deleteCloudPrefix | N/A | Delete object ${JSON.stringify(object)}`,
          );
          await this.storageCrawlerService.storageServiceProvider.deleteObject(object.fileKey);
          await this.metaDataRepository.deleteAll({
            storagePath: object.fileKey,
            bucketName: DEFAULT_BUCKET_NAME,
          });
        }
      } while (list.nextPageRef.ContinuationToken);
      logger.info(`MetadataService| MetadataService.deleteCloudPrefix | N/A | Delete prefix: ${prefix} success`);
    } catch (error) {
      logger.error(
        `MetadataService| MetadataService.deleteCloudPrefix | N/A | Delete prefix: ${prefix} error: ${error}`,
      );
    }
  }

  /**
   * Use to remove project related virtual collection
   * @param projectId {string} id of the project
   * @returns
   */
  async removeProjectRelatedVirtualCollection(projectId: string, teamId: string) {
    let projectOid = new ObjectId(projectId);
    let virtualCollectionId: string = '';

    let virtualCollection = await this.metaDataRepository.findOne({
      where: {
        studioProjectId: projectOid,
      },
      fields: {
        id: true,
      },
    });

    if (virtualCollection) {
      virtualCollectionId = virtualCollection.id as string;

      return await this.deleteVirtualCollection(virtualCollectionId, teamId);
    } else {
      logger.error(
        `Virtual Collection | StudioInterfaceService.deleteVirtualCollection | ${teamId} | Cannot find virtual collection with project id: ${projectId}`,
      );
    }
  }

  /**
   * Use to delete virtual collection
   * @param vCollectionId virtual collection id
   * @param teamId team id
   * @returns response from delete virtual collection
   */
  async deleteVirtualCollection(vCollectionId: string, teamId: string) {
    let vCollectionObjectId = new ObjectId(vCollectionId);

    let params = {vCollectionIdList: vCollectionObjectId};

    // untagged virtual collection id from all metadata
    const response = await this.metaDataRepository.updateManyRemoveFromList(params, params, []);

    // mark stat pending flags true
    await this.metaDataRepository.markStatPendingFlagsTrue(params);

    // delete virtual collection head
    await this.metaDataRepository.deleteById(vCollectionId);

    // delete all QueryOption realated to collection id
    let deletedCount = await this.queryOptionRepository.deleteAll({
      collectionId: vCollectionId,
      teamId: teamId,
    });

    logger.debug(
      `${FLOWS.VIRTUAL_COLLECTION} | StudioInterfaceService.deleteVirtualCollection | ${teamId} | ${deletedCount.count} Deleted QueryOption for virtual collection`,
    );
    logger.info(
      `${FLOWS.VIRTUAL_COLLECTION} | StudioInterfaceService.deleteVirtualCollection | ${teamId} | Deleted virtual collection ${vCollectionId}`,
    );

    return response;
  }

  /**
   * Use to create virtual collection name for a project
   * @param projectName {string}
   * @param teamId {string}
   * @returns created virtual collection name related to the project name
   */
  async createVirtualCollectionNameForProject(projectName: string, teamId: string) {
    let vCollectionName = '';
    let isExist = false;
    let count = 0;

    //create new virtual collection
    if (!projectName) {
      logger.error(
        `Create virtual collection name | StudioInterfaceService.createVirtualCollectionNameForProject | ${teamId} | projectName is not defined`,
      );
      throw new HttpErrors.NotAcceptable(`projectName is not defined`);
    }

    do {
      vCollectionName = `${projectName}_project${count ? `_${count}` : ''}`;
      let isExistObj = await this.metaDataRepository.findOne({
        where: {
          name: vCollectionName,
          objectType: ContentType.IMAGE_COLLECTION,
          teamId: teamId,
        },
      });

      if (isExistObj) {
        isExist = true;
      } else {
        isExist = false;
      }

      count += 1;
    } while (isExist);

    return vCollectionName;
  }

  /**
   * Use to get item details from metadata
   * @param uniqueFileName unique file name {string}
   * @param requiredFields user required fields {object}
   * @param teamId team id {string}
   * @returns item details {object}
   */
  async getItemDetails(
    uniqueFileName: string,
    requiredFields: {[k: string]: boolean},
    currentUserProfile: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile.teamId;
    logger.info(
      `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | Item details of ${uniqueFileName} getting started`,
    );

    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let metaObj = await this.metaDataRepository.findOne({
        where: {
          objectKey: uniqueFileName,
          teamId: new ObjectId(teamId),
        },
      });
      let allowedUserIdList = metaObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    let returnObj: {
      isSuccess: boolean;
      message?: string;
      itemDetails: {[k: string]: any};
    } = {
      isSuccess: false,
      message: '',
      itemDetails: {},
    };

    let defaultFields: {[k: string]: boolean} = {
      id: true,
      name: true,
      objectKey: true,
      url: true,
    };

    if (!uniqueFileName) {
      logger.error(
        `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | uniqueFileName is not defined`,
      );
      returnObj['message'] = 'uniqueFileName is not defined';
      return returnObj;
    }

    let optionalFields: string[] = [
      'id',
      'name',
      'namedEntities',
      'objectKey',
      'url',
      'fileSize',
      'frameCount',
      'frameRate',
      'resolution',
      'storagePath',
      'Tags',
      'createdAt',
      'updatedAt',
      'bucketName',
      'fileTitle',
    ];

    // user required fields formatted to project fields
    let fields = await this.formatFieldsToProjectFields(requiredFields, defaultFields, optionalFields);

    let aggregateQuery = [
      {
        $match: {
          objectKey: uniqueFileName,
          teamId: new ObjectId(teamId),
        },
      },
      {
        $project: fields,
      },
    ];

    logger.debug(
      `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | aggregateQuery: ${JSON.stringify(
        aggregateQuery,
      )}`,
    );

    try {
      let itemDetails = await this.metaDataRepository.aggregate(aggregateQuery);

      logger.debug(
        `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | itemDetails: ${JSON.stringify(
          itemDetails,
        )}`,
      );

      if (Array.isArray(itemDetails) && itemDetails.length > 0) {
        returnObj = await this.formatItemDetailsToUserFields(itemDetails[0], returnObj);
      } else {
        logger.error(
          `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | No item found for aggregate query: ${JSON.stringify(
            aggregateQuery,
          )}`,
        );
        returnObj['message'] = 'No item found';
      }
    } catch (err) {
      logger.error(
        `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | Failed to get item details with error: ${err}`,
      );
      returnObj['message'] = err.message;
    }

    logger.debug(
      `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | returnObj: ${JSON.stringify(
        returnObj,
      )}`,
    );
    logger.info(
      `get item details from metadata | MetaDataService.getItemDetails | ${teamId} | Item details of ${uniqueFileName} returned`,
    );
    return returnObj;
  }

  /**
   * Use to get collection details from metadata
   * @param collectionId collection id {string}
   * @param requiredFields user required fields {object}
   * @param teamId team id {string}
   * @returns collection details {object}
   */
  async getCollectionDetails(
    collectionId: string,
    requiredFields: {[k: string]: boolean},
    currentUserProfile: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile.teamId;
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let metaObj = await this.metaDataRepository.findById(collectionId);
      let allowedUserIdList = metaObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }
    logger.info(
      `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | collection details of ${collectionId} getting started`,
    );

    let returnObj: {
      isSuccess: boolean;
      message?: string;
      itemDetails: {[k: string]: any};
    } = {
      isSuccess: false,
      message: '',
      itemDetails: {},
    };

    let defaultFields: {[k: string]: boolean} = {
      id: true,
      name: true,
      objectType: true,
    };

    if (!collectionId) {
      logger.error(
        `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | collectionId is not defined`,
      );
      returnObj['message'] = 'collectionId is not defined';
      return returnObj;
    }

    let optionalFields: string[] = [
      'id',
      'name',
      'fileSize',
      'frameCount',
      'videoCount',
      'otherCount',
      'frameRate',
      'storagePath',
      'Tags',
      'createdAt',
      'updatedAt',
      'objectKey',
    ];

    // user required fields formatted to project fields
    let fields = await this.formatFieldsToProjectFields(requiredFields, defaultFields, optionalFields);

    let aggregateQuery = [
      {
        $match: {
          _id: new ObjectId(collectionId),
          teamId: new ObjectId(teamId),
        },
      },
      {
        $project: fields,
      },
    ];

    logger.debug(
      `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | aggregateQuery: ${JSON.stringify(
        aggregateQuery,
      )}`,
    );

    try {
      let collectionDetails = await this.metaDataRepository.aggregate(aggregateQuery);

      logger.debug(
        `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | collectionDetails: ${JSON.stringify(
          collectionDetails,
        )}`,
      );

      if (Array.isArray(collectionDetails) && collectionDetails.length > 0) {
        returnObj = await this.formatItemDetailsToUserFields(collectionDetails[0], returnObj);
      } else {
        logger.error(
          `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | No collection found for aggregate query: ${JSON.stringify(
            aggregateQuery,
          )}`,
        );
        returnObj['message'] = 'No collection found';
      }
    } catch (err) {
      logger.error(
        `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | Failed to get collection details with error: ${err}`,
      );
      returnObj['message'] = err.message;
    }

    logger.debug(
      `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | returnObj: ${JSON.stringify(
        returnObj,
      )}`,
    );
    logger.info(
      `get collection details from metadata | MetaDataService.getCollectionDetails | ${teamId} | Collection details of ${collectionId} returned`,
    );
    return returnObj;
  }

  /**
   * Use to format item details to user required fields
   * @param itemDetails retrieved item details from metadata
   * @param returnObj return object
   * @returns formatted item details
   */
  async formatItemDetailsToUserFields(
    itemDetails: {[k: string]: any},
    returnObj: {
      isSuccess: boolean;
      message?: string;
      itemDetails: {[k: string]: any};
    },
  ) {
    let userFields: {[k: string]: any} = {};

    // if itemDetails have field as customMeta, that object should be merged with itemDetails
    if (itemDetails.customMeta) {
      userFields = {...itemDetails, ...itemDetails.customMeta};
      delete userFields.customMeta;
    } else {
      userFields = {...itemDetails};
    }

    // if itemDetails have field as objectKey, it should be changed as uniqueName
    if (itemDetails.objectKey) {
      userFields.uniqueName = itemDetails.objectKey;
      delete userFields.objectKey;
    }

    if (itemDetails['_id']) {
      userFields.id = itemDetails['_id'];
      delete userFields['_id'];
    }

    if (itemDetails['objectType'] == ContentType.IMAGE_COLLECTION) {
      userFields.contentType = 'image';
      delete userFields['objectType'];
    } else if (itemDetails['objectType'] == ContentType.VIDEO_COLLECTION) {
      userFields.contentType = 'video';
      delete userFields['objectType'];
    } else if (itemDetails['objectType'] == ContentType.OTHER_COLLECTION) {
      userFields.contentType = 'other';
      delete userFields['objectType'];
    } else if (itemDetails['objectType'] == ContentType.DATASET) {
      userFields.contentType = 'image';
      delete userFields['objectType'];
    }

    returnObj['isSuccess'] = true;
    returnObj['message'] = 'Success';
    returnObj['itemDetails'] = userFields;

    return returnObj;
  }

  /**
   * Use to format user required fields to project fields
   * @param requiredFields required fields from user
   * @param defaultFields default fields
   * @param optionalFields optional fields
   * @returns formatted fields
   */
  async formatFieldsToProjectFields(
    requiredFields: {[k: string]: boolean},
    defaultFields: {[k: string]: boolean},
    optionalFields: string[],
  ) {
    // if required fields have field as uniqueName, it should be changed as objectKey
    if (requiredFields.uniqueName) {
      requiredFields.objectKey = true;
      delete requiredFields.uniqueName;
    }

    requiredFields = Object.keys(requiredFields).reduce((obj, key) => {
      if (requiredFields[key]) {
        // if key represent in optional fields
        if (optionalFields.includes(key)) {
          obj[key] = requiredFields[key];
        } else {
          obj[`customMeta.${key}`] = requiredFields[key];
        }
      } else {
        delete requiredFields[key];
        if (key == 'uniqueName') {
          delete defaultFields['objectKey'];
        } else if (key == 'id') {
          delete defaultFields['_id'];
          obj['_id'] = false;
        } else {
          delete defaultFields[key];
        }
      }
      return obj;
    }, {} as {[k: string]: boolean});

    let fields = {...defaultFields, ...requiredFields};

    return fields;
  }

  /**
   * use to rename collection
   * @param collectionId collection id {string}
   * @param renameObject {newName: string; collectionType: ContentType}
   * @param teamId team id {string}
   * @param userId user id {string}
   * @returns isSuccess: true if success, else error
   */
  async renameCollection(
    collectionId: string,
    renameObject: {newName: string; collectionType: ContentType},
    teamId: string,
    userId: string,
  ) {
    let collection = await this.metaDataRepository.findById(collectionId);
    if (!collection) {
      logger.error(
        `Rename Collection | MetaDataService.renameCollection | ${teamId} | Collection not found for id: ${collectionId}`,
      );
      throw new HttpErrors.NotFound(DatalakeUserMessages.COLLECTION_NOT_EXIST);
    }

    if (collection.name == renameObject.newName) {
      logger.error(
        `Rename Collection | MetaDataService.renameCollection | ${teamId} | Collection name is same as new name: ${renameObject.newName}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_NAME_SAME);
    }

    if (
      ![
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.OTHER_COLLECTION,
        ContentType.DATASET,
      ].includes(renameObject.collectionType)
    ) {
      logger.error(
        `Rename Collection | MetaDataService.renameCollection | ${teamId} | Invalid collection type: ${renameObject.collectionType}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    let isExist = await this.metaDataRepository.findOne({
      where: {
        name: renameObject.newName,
        teamId: new ObjectId(teamId),
        objectType: renameObject.collectionType,
      },
    });

    if (isExist) {
      logger.error(
        `Rename Collection | MetaDataService.renameCollection | ${teamId} | Collection name already exist: ${renameObject.newName}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_NAME_EXIST);
    }

    let updateObj = {
      name: renameObject.newName,
      updatedAt: new Date(),
    };

    let MetadataHistoryObj = {
      metadataId: new ObjectId(collectionId),
      userId: new ObjectId(userId),
      date: new Date(),
      activityType: ACTIVITY_TYPE.MetaObjectRename,
      info: {
        previousName: collection.name,
        newName: renameObject.newName,
      },
    };

    try {
      await this.metaDataRepository.updateById(collectionId, updateObj);
      await this.metaDataHistoryRepository.create(MetadataHistoryObj);
    } catch (err) {
      logger.error(
        `Rename Collection | MetaDataService.renameCollection | ${teamId} | Failed to rename collection: ${collectionId},
        error: ${err}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FAILED_TO_UPDATE_COLLECTION);
    }

    logger.info(
      `Rename Collection | MetaDataService.renameCollection | ${teamId} | Collection renamed: ${collectionId}`,
    );

    return {isSuccess: true};
  }

  async createCollectionHead(
    collectionName: string,
    objectType: ContentType,
    currentUserProfile: UserProfileDetailed,
    metadataObject?: {[k: string]: any},
  ) {
    let returnObj: Partial<MetaData> = {};
    let teamId = currentUserProfile.teamId;

    let allowedUserId =
      metadataObject && metadataObject['allowedUserIdList'] && metadataObject['allowedUserIdList'][0]
        ? metadataObject['allowedUserIdList'][0]
        : null;

    if (currentUserProfile.userType && currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
      allowedUserId = currentUserProfile.id;
    }

    let collectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
      objectType,
      new ObjectId(teamId),
      collectionName,
      metadataObject,
      undefined,
      allowedUserId,
    );

    if (collectionId) {
      returnObj = await this.metaDataRepository.findById(collectionId, {
        fields: {
          name: true,
          id: true,
          objectType: true,
          storagePrefixPath: true,
          storagePath: true,
        },
      });
    }
    return returnObj;
  }

  /**
   * Retrieve Db Unstructured Records
   * @param categories {string[]} Category names relevant to each section of data being considered for locating records.
   * @param filterKeyValues {[key: string]: any} Key value pairs derived from the question to locate matching records.
   * @param pageIndex {number} Index for the batch required.
   * @returns [{"keyInfo": {"key_1": <value_1>, "key_2": <value_2>},"data": ["data_1", "data_2", ....]}]
   */
  async retrieveDbUnstructuredRecords(categories: string[], filterKeyValues: FilterKeyValues, pageIndex: number) {
    // check page index is valid
    if (pageIndex < 0) {
      throw new HttpErrors.BadRequest(DatalakeUserMessages.INVALID_PAGE_INDEX);
    }

    // use category list to get collection id list
    // categories are equivalent to collection names
    let collectionList: {_id: string}[] = await this.metaDataRepository.aggregate([
      {
        $match: {
          name: {$in: categories},
          objectType: ContentType.OTHER_COLLECTION,
        },
      },
    ]);
    let collectionIdList = collectionList.map(obj => new ObjectId(obj._id));

    // build match query
    // use or for multiple keys
    let matchOrQuery: {$or: any[]} = {$or: []};
    for (const [key, value] of Object.entries(filterKeyValues)) {
      let tempKey = `keyInfo.${key}`;
      // Check if the value is an array
      if (Array.isArray(value)) {
        matchOrQuery.$or.push({[tempKey]: {$in: value}}); // Use $in for arrays
      } else {
        matchOrQuery.$or.push({[tempKey]: value}); // Direct match for strings and numbers
      }
    }

    let matchQuery = {vCollectionIdList: {$in: collectionIdList}};

    // add or query if there are query exists
    if (matchOrQuery.$or.length > 0) {
      matchQuery = {...matchQuery, ...matchOrQuery};
    }
    logger.debug(
      `unstructured data query | MetaDataService.retrieveDbUnstructuredRecords | N/A | matchQuery: ${JSON.stringify(
        matchQuery,
      )} `,
    );
    let response = await this.metaDataRepository.aggregate([
      {$match: matchQuery},
      {$sort: {_id: 1}},
      {$project: {key_info: '$keyInfo', data: 1, _id: 0}},
      {$skip: pageIndex * 25},
      {$limit: 25},
    ]);

    let formatted_response: {key_info: any; data: any}[] = [];

    if (Array.isArray(response)) {
      formatted_response = response.map(obj => {
        if (Array.isArray(obj.data)) {
          obj.data = obj.data.map((item: any, index: number) => `(${index + 1}) ${item}`).join('\n');
        }
        return {
          key_info: obj.key_info,
          data: obj.data,
        };
      });
    }

    return formatted_response;
  }
}

export const META_DATA_SERVICE = BindingKey.create<MetaDataService>('service.metaDataService');

export interface FilterKeyValues {
  [key: string]: string | number | Array<string | number>;
}
