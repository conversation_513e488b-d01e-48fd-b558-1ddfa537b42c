/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * StorageProviderService is the parent class of the sub classes such as StorageProviderService
 */

/**
 * @class StorageProviderService
 * purpose of StorageProviderService service to use as a parent class
 * @description StorageProviderService is the parent class of the sub classes such as StorageProviderService
 * <AUTHOR> channa
 */

import { /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ObjectId} from 'bson';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {CrawlingStatus} from '../models/data-crawl.model';
import {ContentType, MetaData, OBJECT_STATUS} from '../models/meta-data.model';
import {StorageConnectionStatus} from '../models/system-data.model';
import {MetaDataRepository} from '../repositories';
import {STORAGE_SYSTEM_FOLDER} from '../settings/constants';
import {getContentType} from '../settings/tools';
import {AWS_FILE_URL_EXPIRE_DURATION, AWS_UPLOAD_FILE_URL_VALID_DURATION} from './aws-s3-storage.service';
import {StorageOperationHandlerService} from './storage-operation-handler.service';
import {json} from 'express';

interface FolderObjs {
  [key: string]: string; // Use 'string' as the type for the videoCollectionId, you can change it to the actual type if needed.
}

const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

@injectable({scope: BindingScope.TRANSIENT})
export class StorageProviderService {
  storageOperationHandlerService: StorageOperationHandlerService;
  constructor(
    @repository('MetaDataRepository')
    public metaDataRepository: MetaDataRepository,
  ) {}

  /**
   * abstract method
   * generate presigned s3 read file url
   */
  async generateObjectUrl(key: string, expires = AWS_FILE_URL_EXPIRE_DURATION, bucket?: string) {
    return '';
  }

  /**
   * abstract method
   * generate presigned s3 write file url for file upload
   */
  async generateWriteFileUrl(key: string, expires = AWS_UPLOAD_FILE_URL_VALID_DURATION, bucket?: string) {
    return '';
  }

  /**
   * abstract method
   * Use for handle intialize uploading multipart files to s3 from frontend
   */
  async initializeMultipartUpload(key: string, expires = AWS_UPLOAD_FILE_URL_VALID_DURATION, bucket?: string) {
    //isExisting is false if file is not previously uploaded to storage, true if file is already uploaded to storage
    //This is used by the clients to determine whether to proceed with multipart upload or not
    return {fileId: '', fileKey: '', isExisting: false};
  }

  /**
   * abstract method
   * Use for handle chunk list urls for uploading multipart files to s3 from frontend
   * @param isDisableMultipart {boolean} If this flag is true, then upload will function as full write instead of multi-part or resumable upload
   * Added temporarily to handle failure to upload resumable in case of Google Cloud Storage from frontend
   */
  async generateMultipartPreSignedUrls(
    key: string,
    fileId: number,
    parts: number,
    bucket?: string,
    contentType?: string,
    isDisableMultipart?: boolean,
  ) {
    return {
      parts: [
        {
          signedUrl: '',
          PartNumber: 0,
        },
      ],
    };
  }

  /**
   * abstract method
   * Use for handle chunk list urls for uploading multipart files to s3 from frontend
   */
  async generateV4UploadSignedUrl(key: string, fileId: number, parts: number, bucket?: string, contentType?: string) {
    return {
      parts: [
        {
          signedUrl: '',
          PartNumber: 0,
        },
      ],
    };
  }

  /**
   * abstract method
   * Use for finalize multipart file upload to s3 from frontend
   */
  async finalizeMultipartUpload(
    key: string,
    fileId: string,
    parts: any,
    bucket?: string,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    currentUserProfile?: UserProfileDetailed,
  ) {
    return {isSuccess: true};
  }

  /**
   * abstract method
   * Use to find if object key exists
   */
  async checkObjectKey(key: string, bucket?: string): Promise<any> {
    return;
  }

  /**
   * Use to get summary of cloud storage via shell script (EX: in aws s3, it is taken using s3cmd tool)
   * @param dataCrawlId id of relavent DataCrawl record
   * @returns none, DataCrawl DB record will be updated
   */
  async getCloudStorageSummary(dataCrawlId: string, bucket?: string) {}

  /**
   * Use to update file urls
   * @returns Object url with expire settings
   */
  async fileUrlRenew() {}

  /**
   * Use to get file size of an object from provider
   * @param key Storage object key
   * @param bucket bucket name
   */
  async getFileSize(key: string, bucket?: string): Promise<number> {
    return 0;
  }

  async uploadFileFromBuffer(buffer: any, key: string, bucket?: string) {}

  /**
   * Use for deleting object from s3 bucket using objectKey
   * @param key {string} key of the aws s3 media file
   * @returns delete status
   */
  async deleteObject(key: string, bucket?: string) {
    return {isSuccess: false, message: ''};
  }

  /**
   * fetch list of all objects in storage bucket, and call insert files in to db method
   * @param imageCollectionId crawling images belongs to this image collection
   * @param dataCrawlId id of DataCrawl record which relavent to the crawling
   * @returns {boolean} success status of crawling
   */
  async fetchFilesFromStorageAndPopulateMetaData(
    imageCollectionId: string,
    videoCollectionId: string,
    otherCollectionId: string,
    dataCrawlId: string,
    teamId: ObjectId,
    isSubsequentCrawl?: boolean,
    bucketName?: string,
    changedDirectory?: string, //Varible to call partial crawl
    updatedAt?:any //Variable to get the updated files in partial crawl
  ) {

    /**
     * fetch all file objects from storage (AWS S3 / Google / Azure), page by page and insert them into db
     */

    let isDefaultBucket = false;

    logger.info(
      `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | isSubsequentCrawl:${
        isSubsequentCrawl ? 'Yes' : 'No'
      } | started `,
    );

    if (!bucketName) {
      logger.error(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | N/A | not found - AWS bucket name `,
      );
      return {success: false};
    }

    if (!dataCrawlId) {
      logger.error(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | N/A | not found - dataCrawlId: ${dataCrawlId} `,
      );
      return {success: false};
    }

    if (bucketName == defaultBucketName) {
      isDefaultBucket = true;
    }

    let crawledFileSize = 0;
    let crawledFileCount = 0;
    let unpopulatedCount = 0; // used to track not db inserted objects. EX: aws folder objects
    let unpopulatedSize = 0;
    let newlyAddedFileCount = 0;
    let newlyAddedFileSize = 0;
    let continuationToken = undefined;
    //check crawling record to track & update the status

    let crawlRecord = await this.storageOperationHandlerService.findDataCrawlRecord(dataCrawlId);

    if (crawlRecord) {
      //in case of resume initial crawl
      crawledFileCount = crawlRecord.crawledFileCount;
      crawledFileSize = crawlRecord.crawledFileSize;
      unpopulatedCount = crawlRecord.unpopulatedCount;
      unpopulatedSize = crawlRecord.unpopulatedSize;
      continuationToken = crawlRecord.nextContinuationToken;

      // below check and return added to avoid recrawling from beginning in case of nextContinuationToken is null
      // nextContinuationToken can be null in to cases.
      // 1. at the beginning of crawling
      // 2. at the end of the crawling (We can identify this by checking both continuationToken & nextContinuationToken)
      // If changed directory exists which triggers a partial crawl
      if (crawlRecord.continuationToken && !crawlRecord.nextContinuationToken && !isSubsequentCrawl && !changedDirectory) {
        logger.error(
          `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | N/A | Trying to recrawl which has already all pages crawled initial crawling`,
        );
        return {success: false};
      }
    } else {
      logger.error(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | N/A | not found - data crawl record for datarawlId: ${dataCrawlId}`,
      );
      return {success: false};
    }

    // if storage summary not exist for crawling record, then start summary fetching
    if (!crawlRecord.storageSummaryReceivedAt) {
      this.getCloudStorageSummary(dataCrawlId);
    } else {
      // update crawling record status
      await this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, {
        status: CrawlingStatus.FILES_CRAWLING,
      });
    }

    // listObject params
    let IsTruncated = false;
    var params: FileListParams = {
      Bucket: bucketName || '',
      ContinuationToken: continuationToken,
      NextQueryParam: {},
      Prefix: '',
      changedDirectory: changedDirectory, // Passing the changed directory location 
      updatedAt:updatedAt, //Passing the last updated time
    };

    let imageFolderObjs: FolderObjs = {};
    let videoFolderObjs: FolderObjs = {};
    let otherFolderObjs: FolderObjs = {};
    let pageCounter = 1;

    do {
      //NOTE: remove this after testing
      //await new Promise(resolve => setTimeout(resolve, 1000));

      // call to fetch file list from s3 (page)
      logger.debug(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | starting to get the file list for the params: ${JSON.stringify(params)}`,
      );
      let res = await this.getFileList(params);
      if (res.isSuccess == false) {
        logger.error(
          `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | failed to list objects from storage, statusCode: ${res.errMessage}`,
        );
        logger.error('error: ', res.errMessage);

        //update storage record in system data , NOTE: add teamId filterig
        await this.storageOperationHandlerService.updateStorageRecordInSystemData_Set(
          {},
          {
            'cloudStorages.$[storage].connectionStatus': StorageConnectionStatus.NOT_CONNECTED,
          },
          [{'storage.storageName': bucketName || ''}],
        );
        // update crawling record statics
        await this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, {
          status: CrawlingStatus.FAILED,
          error: '',
        });
        return {success: false};
      }

      params.ContinuationToken = res.nextPageRef.NextContinuationToken;
      IsTruncated = res.IsTruncated;

      logger.debug(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | File count: ${res.fileList.length} | page ${pageCounter} fetched `,
      );

      let fileMetaDataArr: Partial<MetaData>[] = [];

      //variables for system data count update
      let addedImageCount = 0;
      let addedVideoCount = 0;
      let addedOtherCount = 0;
      let addedUnsupportedCount = 0;
      let addedImageSize = 0;
      let addedVideoSize = 0;
      let addedOtherSize = 0;
      let addedUnsupportedSize = 0;
      
      ///Changed done to accomdate partial crawl . IF the change directory exists the collection ids are not taken 
      ///And assumed that files are not placed in the root directory of the bucket
      /// This is temperory fix
      let imageCollectionName = "";
      let videoCollectionName = "";
      let otherCollectionName = "";

      if (!changedDirectory)  
      {
        imageCollectionName = await this.storageOperationHandlerService.getCollectionNameById(imageCollectionId);
        videoCollectionName = await this.storageOperationHandlerService.getCollectionNameById(videoCollectionId);
        otherCollectionName = await this.storageOperationHandlerService.getCollectionNameById(otherCollectionId);

      }
      
      //List to save the created root directories
      let rootFolderList: string[] = [];

      for (let storageFile of res.fileList) {
        //increment for crawledFileCount
        crawledFileCount = crawledFileCount + 1;

        //increment for crawledFileSize
        if (storageFile.fileSize) {
          crawledFileSize = crawledFileSize + storageFile.fileSize;
        }

        if (storageFile.fileKey) {
          //if file
          //logger.warn(storageFile.fileKey);
          if (!storageFile.fileKey?.endsWith('/')) {
            //if file is a thumbnail, then skip it
            if (
              storageFile.fileKey.startsWith('layerx-datalake-thumbnails/') ||
              storageFile.fileKey.startsWith('layerNext/') ||
              storageFile.fileKey.startsWith('layerx-datalake-video-frames/') ||
              storageFile.fileKey.startsWith(STORAGE_SYSTEM_FOLDER)
            ) {
              unpopulatedCount = unpopulatedCount + 1;
              if (storageFile.fileSize) unpopulatedSize = unpopulatedSize + storageFile.fileSize;
              continue;
            }

            let fileStoragePath = storageFile.fileKey;
            let fileObjectKey = fileStoragePath.replace(/_/g, '__').replace(/\//g, '_');
            let splitFileObjectKey = fileStoragePath.split('/');
            let fileObjectName = storageFile.fileName;
            let fileObjectLastModified = storageFile.fileLastModified;
            let fileObjectType = await getContentType(fileObjectName);
            let directory = '/' + splitFileObjectKey.slice(0, -1).join('/');
            let parentDirectory = '/' + directory.split('/')[1];
            let rootFolder = directory.split('/')[1];

            //push file to an array to insert into db
            let initialFileMeta: Partial<MetaData> = {};

            initialFileMeta.storagePath = fileStoragePath;
            initialFileMeta.bucketName = bucketName || '';
            initialFileMeta.createdAt = fileObjectLastModified;
            initialFileMeta.updatedAt = new Date();
            initialFileMeta.fileSize = storageFile.fileSize;
            initialFileMeta.name = fileObjectName;
            initialFileMeta.isMediaProcessingPending = true;
            initialFileMeta.objectStatus = OBJECT_STATUS.MEDIA_PROCESSING_PENDING;

            initialFileMeta.statPending = true;
            initialFileMeta.statPendingAt = new Date();
            initialFileMeta.objectType = fileObjectType;
            initialFileMeta.dataCrawlId = new ObjectId(dataCrawlId);
            initialFileMeta.directory = directory;
            initialFileMeta.isLeaf = true;
            initialFileMeta.teamId = new ObjectId(teamId);

            if (parentDirectory == '/') {
              if (fileObjectType == ContentType.IMAGE) {
                initialFileMeta.parentList = [new ObjectId(imageCollectionId)];
                initialFileMeta.collectionId = new ObjectId(imageCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(imageCollectionId)];
                initialFileMeta.frameCount = 1;
                // s3Meta.frameVerificationStatus = FrameVerificationStatus.RAW
                initialFileMeta.objectKey = imageCollectionName + '_' + fileObjectKey;
              } else if (fileObjectType == ContentType.VIDEO) {
                initialFileMeta.parentList = [new ObjectId(videoCollectionId)];
                initialFileMeta.collectionId = new ObjectId(videoCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(videoCollectionId)];
                // s3Meta.frameVerificationStatus = FrameVerificationStatus.RAW
                initialFileMeta.objectKey = videoCollectionName + '_' + fileObjectKey;
              } else if (fileObjectType == ContentType.OTHER) {
                initialFileMeta.parentList = [new ObjectId(otherCollectionId)];
                initialFileMeta.collectionId = new ObjectId(otherCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(otherCollectionId)];
                initialFileMeta.otherCount = 1;
                initialFileMeta.isMediaProcessingPending = true;
                initialFileMeta.url = await this.generateObjectUrl(fileStoragePath, undefined, bucketName);

                initialFileMeta.isAccessible = true;
                initialFileMeta.objectStatus = OBJECT_STATUS.MEDIA_PROCESSING_PENDING;
                // get collection name for each meta object type
                initialFileMeta.objectKey = otherCollectionName + '_' + fileObjectKey;
              }
            } else {
              let collectionName: string = `${bucketName}_${rootFolder}`;
              let newCollectionId = '';
              let isNotStoragePathRequired: boolean = false;

              //if the collection name has / removing those. This is for the local disk storage
              if (collectionName.includes('/')) {
                collectionName = collectionName.replace(/\//g, '_').replace(/^_/, '');
              }
              if (isDefaultBucket) {
                isNotStoragePathRequired = true;

                // create collection head meta data record for each root folder if it is not exist
                if (!rootFolderList.includes(rootFolder)) {
                  let updateRootFolderList = await this.metaDataRepository.createRecordForStorageMapping(rootFolder, true, teamId.toString(), '');
                  if (updateRootFolderList)
                  {
                    rootFolderList.push(rootFolder);
                  }
                }
              }

              if (fileObjectType == ContentType.IMAGE) {
                if (collectionName in imageFolderObjs) {
                  newCollectionId = imageFolderObjs[collectionName];
                } else {
                  let existingCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
                    ContentType.IMAGE_COLLECTION,
                    new ObjectId(teamId),
                    collectionName,
                    {},
                    isNotStoragePathRequired,
                  );
                  if (existingCollectionId) {
                    newCollectionId = existingCollectionId;
                    imageFolderObjs[collectionName] = newCollectionId;
                  }
                }
                initialFileMeta.parentList = [new ObjectId(newCollectionId)];
                initialFileMeta.collectionId = new ObjectId(newCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(newCollectionId)];
                initialFileMeta.objectKey = collectionName + '_' + fileObjectKey;
              } else if (fileObjectType == ContentType.VIDEO) {
                if (collectionName in videoFolderObjs) {
                  newCollectionId = videoFolderObjs[collectionName];
                } else {
                  let existingCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
                    ContentType.VIDEO_COLLECTION,
                    new ObjectId(teamId),
                    collectionName,
                    {},
                    isNotStoragePathRequired,
                  );
                  if (existingCollectionId) {
                    newCollectionId = existingCollectionId;
                    videoFolderObjs[collectionName] = newCollectionId;
                  }
                }
                initialFileMeta.parentList = [new ObjectId(newCollectionId)];
                initialFileMeta.collectionId = new ObjectId(newCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(newCollectionId)];
                initialFileMeta.objectKey = collectionName + '_' + fileObjectKey;
              } else if (fileObjectType == ContentType.OTHER) {
                if (collectionName in otherFolderObjs) {
                  newCollectionId = otherFolderObjs[collectionName];
                } else {
                  let existingCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
                    ContentType.OTHER_COLLECTION,
                    new ObjectId(teamId),
                    collectionName,
                    {},
                    isNotStoragePathRequired,
                  );
                  if (existingCollectionId) {
                    newCollectionId = existingCollectionId;
                    otherFolderObjs[collectionName] = newCollectionId;
                  }
                }
                initialFileMeta.parentList = [new ObjectId(newCollectionId)];
                initialFileMeta.collectionId = new ObjectId(newCollectionId);
                initialFileMeta.vCollectionIdList = [new ObjectId(newCollectionId)];
                initialFileMeta.otherCount = 1;
                initialFileMeta.isMediaProcessingPending = true;
                initialFileMeta.url = await this.generateObjectUrl(fileStoragePath, undefined, bucketName);

                initialFileMeta.isAccessible = true;
                initialFileMeta.objectStatus = OBJECT_STATUS.MEDIA_PROCESSING_PENDING;

                // get collection name for each meta object type
                initialFileMeta.objectKey = collectionName + '_' + fileObjectKey;
              }
            }

            fileMetaDataArr.push(initialFileMeta);

            // //increment for crawledFileSize
            // if (_s3Obj.Size) {
            //   crawledFileSize = crawledFileSize + _s3Obj.Size
            // }
            //increment count variables for system data update
            if (fileObjectType == ContentType.IMAGE) {
              addedImageCount = addedImageCount + 1;
              if (storageFile.fileSize) {
                addedImageSize = addedImageSize + storageFile.fileSize;
              }
            } else if (fileObjectType == ContentType.VIDEO) {
              addedVideoCount = addedVideoCount + 1;
              if (storageFile.fileSize) {
                addedVideoSize = addedVideoSize + storageFile.fileSize;
              }
            } else if (fileObjectType == ContentType.OTHER) {
              addedOtherCount = addedOtherCount + 1;
              if (storageFile.fileSize) {
                addedOtherSize = addedOtherSize + storageFile.fileSize;
              }
            } else if (fileObjectType == ContentType.UNSUPPORTED) {
              addedUnsupportedCount = addedUnsupportedCount + 1;
              if (storageFile.fileSize) {
                addedUnsupportedSize = addedUnsupportedSize + storageFile.fileSize;
              }
            }
          }
          // if folder object, we will not inserted that into db
          else {
            unpopulatedCount = unpopulatedCount + 1;
          }
        }
      }

      try {
        if (isSubsequentCrawl) {
          // method to filter out existing files and update db with metaData
          let addedFileData = await this.storageOperationHandlerService.filterOutExistingFiles(fileMetaDataArr);
          if (addedFileData) {
            newlyAddedFileCount += addedFileData.newFileLength;
            newlyAddedFileSize += addedFileData.newFileSize;
          }
        } else {
          // method to update db with metaData
          await this.storageOperationHandlerService.insertMetaDataFromStorageToDB(fileMetaDataArr);
          newlyAddedFileCount = crawledFileCount;
          newlyAddedFileSize = crawledFileSize;
        }
      } catch (e: any) {
        logger.error(
          `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | failed to insert fetched object list to db`,
        );
        logger.error('error: ', e);
        // update crawling record status
        await this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, {
          status: CrawlingStatus.FAILED,
          error: e,
        });
        return {success: false};
      }

      // update crawling record statics
      await this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, {
        crawledFileCount: crawledFileCount,
        crawledFileSize: crawledFileSize,
        unpopulatedCount: unpopulatedCount,
        unpopulatedSize: unpopulatedSize,
        newlyAddedFileCount: newlyAddedFileCount,
        newlyAddedFileSize: newlyAddedFileSize,
        continuationToken: res.nextPageRef.ContinuationToken,
        nextContinuationToken: res.nextPageRef.NextContinuationToken,
        updatedAt: new Date(),
      });

      //update storage record in system data , NOTE: add teamId filterig
      await this.storageOperationHandlerService.updateStorageRecordInSystemData_Set(
        {},
        {
          'cloudStorages.$[storage].updatedAt': new Date(),
          'cloudStorages.$[storage].connectionStatus': StorageConnectionStatus.CONNECTED,
        },
        [{'storage.storageName': bucketName}],
      );

      if (!isSubsequentCrawl) {
        await this.storageOperationHandlerService.updateStorageRecordInSystemData_Inc(
          {},
          {
            'objectTypeWiseCounts.videos.count': addedVideoCount,
            'objectTypeWiseCounts.videos.size': addedVideoSize,
            'objectTypeWiseCounts.images.count': addedImageCount,
            'objectTypeWiseCounts.images.size': addedImageSize,
            'objectTypeWiseCounts.other.count': addedOtherCount,
            'objectTypeWiseCounts.other.size': addedOtherSize,
          },
          [],
        );
      }
      logger.debug(
        `Crawl Storage for populate data | StorageProviderService.fetchFilesFromStorageAndPopulateMetaData | N/A | page ${pageCounter} saved `,
      );
      pageCounter++;
    } while (IsTruncated);

    logger.info('Inserting files to db finished');
    // update crawling record statics
    await this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, {
      crawlFinishedAt: new Date(),
    });
    return {success: true};
  }

  /**
   * Abstract method to get list of objects from storage with paging
   * Used when crawling data from storage buckets
   * @param params
   * @returns list of objects
   */
  async getFileList(params: FileListParams) {
    let FileListResponse: FileListResponse = {
      isSuccess: false,
      fileList: [],
      errMessage: '',
      nextPageRef: {},
      IsTruncated: false,
    };
    return FileListResponse;
  }

  // /**
  //  * Create a new s3 buckt for processed media saving
  //  * sub method - check media bucket name
  //  * @returns s3 bucketname
  //  */
  //  async createNewBucket(){}
}

export interface FileListParams {
  Bucket: string;
  ContinuationToken?: string;
  Prefix?: string;
  NextQueryParam?: any;
  changedDirectory?:string;
  updatedAt?: any;
}

export interface FileListResponse {
  isSuccess: boolean;
  fileList: StorageFileHeader[];
  errMessage: string;
  nextPageRef: any;
  IsTruncated: boolean;
}

export interface StorageFileHeader {
  fileKey: string;
  fileName: string;
  fileLastModified: Date;
  fileSize: number;
}
