/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 */

/**
 * @class AnnotationDownloadHandlerService
 * @description Handle download raw annotaions (with images)
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AnnotationObject,
  AnnotationTypeString,
  AttributeValues,
  ContentType,
  OBJECT_STATUS,
  OperationMode,
  OperationType,
} from '../models';
import {JobStatus, JobType} from '../models/job.model';
import {MetaDataUpdateRepository} from '../repositories';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {isValidObjectId} from '../settings/tools';
import {AWS_FILE_URL_EXPIRE_DURATION} from './aws-s3-storage.service';
import {JOB_SERVICE, JobService} from './job.service';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from './system-label.service';

@injectable({scope: BindingScope.TRANSIENT})
export class AnnotationDownloadHandlerService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(SYSTEM_LABEL_SERVICE)
    private systemLabelService: SystemLabelService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @repository(MetaDataUpdateRepository) public metaDataUpdateRepository: MetaDataUpdateRepository,
  ) {}

  /**
   * Download annotations for a given collection
   * @param collectionId
   * @param pageNo
   * @param pageSize
   * @param teamId
   * @param operationId - To filter by model id or project id
   * @returns
   */
  async getAnnotationsOfCollection(
    collectionId: string,
    pageNo: number,
    pageSize: number,
    teamId: string,
    operationIdList?: string[],
    annotationTypeString?: AnnotationTypeString,
    sessionId?: string,
    currentUserProfile?: UserProfileDetailed,
  ) {
    let collection = await this.metaDataRepository.findById(collectionId);

    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let allowedUserIdList = collection.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    if (pageNo == 0) {
      //initiate job for create job for dataset generation
      await this.jobService.createOrUpdateJob(
        `Download-${collection.name}-Annotations`,
        sessionId || new Date().getTime().toString(),
        currentUserProfile?.name || 'Unnamed',
        currentUserProfile?.teamId,
        '',
        JobType.DownloadCollection,
        0,
        JobStatus.queued,
        {
          collectionId: new ObjectId(collectionId),
          operationIdList: operationIdList,
          annotationTypeString: annotationTypeString,
        },
      );
    }
    let pagingSize = (pageSize || 100) * 1;
    let skip = (pageNo || 0) * pagingSize;
    if (!collectionId) {
      logger.warn(
        `Download annotations| AnnotationDownloadHandlerService.getAnnotationsOfCollection | N/A | Invalid collection id / pageNo `,
      );
      return;
    }

    let teamMatch: any[] = [];
    let teamObjId: ObjectId = new ObjectId(teamId);
    if (teamId) {
      let groupFilter = {
        teamId: teamObjId,
      };
      // query to lookup group data and check if current logged in user's team is the same as of the requested dataset
      teamMatch = [{$match: groupFilter}];
    }

    let annotationFilter: any[] = [];

    let customProjections: {[k: string]: any} = {};

    if (operationIdList && Array.isArray(operationIdList) && operationIdList.length > 0) {
      let operationObjectIdList = operationIdList.map(elem => {
        if (isValidObjectId(elem)) {
          return new ObjectId(elem);
        } else {
          return elem;
        }
      });
      annotationFilter = [{$in: ['$operationId', operationObjectIdList]}];

      if (operationIdList.length == 1) {
        customProjections = {
          taskId: '$annotationData.taskId',
          taskFrameId: '$annotationData.taskFrameId',
        };
      }
    } else {
      if (annotationTypeString == AnnotationTypeString.HUMAN) {
        annotationFilter = [{$eq: ['$operationMode', OperationMode.HUMAN]}];
      } else if (annotationTypeString == AnnotationTypeString.AUTO) {
        annotationFilter = [{$eq: ['$operationMode', OperationMode.AUTO]}];
      }
    }

    let collectionObj = await this.metaDataRepository.findById(collectionId);
    // let _matchQuery = await this.metaDataRepository.getMatchObjectsOfCollection(
    //   collectionObj,
    // );
    let _matchQuery = {vCollectionIdList: new ObjectId(collectionId)};
    let collectionName = collectionObj.name;

    return this.getAnnotationDataForSelection(
      collectionName,
      _matchQuery,
      skip,
      pagingSize,
      annotationFilter,
      customProjections,
      teamId,
      await this.metaDataRepository.getChildObjectTypeByObjectType(collectionObj.objectType),
    );
  }

  // /**
  //  * NOTE: DEPRECATED
  //  * Download annotations for a given project
  //  * @param projectId
  //  * @param pageNo {int} page numbers starting from 0
  //  * @param pageSize {int} no of frames in single page (default 1000)
  //  * @param teamId
  //  * @returns
  //  */
  // async getAnnotationsOfProject(
  //   projectId: string,
  //   pageNo: number,
  //   pageSize: number,
  //   teamId?: string,
  // ) {
  //   let pagingSize = pageSize || 1000;
  //   let skip = (pageNo || 0) * pagingSize;
  //   if (!projectId) {
  //     logger.warn(
  //       `Download annotations| AnnotationDownloadHandlerService.getAnnotationsOfProject | ${projectId} | Invalid project id `,
  //     );
  //     return;
  //   }

  //   let teamMatch: any[] = [];
  //   let teamObjId: ObjectId = new ObjectId(teamId);
  //   if (teamId) {
  //     let groupFilter = {
  //       teamId: teamObjId,
  //     };
  //     // query to lookup group data and check if current logged in user's team is the same as of the requested dataset
  //     teamMatch = [{$match: groupFilter}];
  //   }

  //   // Only download human annotations done for the project
  //   let annotationFilter = [
  //     {$eq: ['$operationMode', OperationMode.HUMAN]},
  //     {$eq: ['$operationId', new ObjectID(projectId)]},
  //   ];

  //   let projectTaskInfo = {
  //     taskId: 1,
  //     taskFrameId: 1,
  //   };

  //   let res = await this.metaDataRepository.getMatchObjectsOfProjectFilter(
  //     projectId,
  //   );

  //   if (res) {
  //     return this.getAnnotationDataForSelection(
  //       true,
  //       projectId,
  //       res.match,
  //       skip,
  //       pagingSize,
  //       annotationFilter,
  //       teamId,
  //     );
  //   }
  // }

  /**
   *
   * @param projectId
   * @param taskIdList
   * @param isAnnotatedOnly
   * @param pageNo
   * @param pageSize
   * @param teamId
   */
  async getDataOfAnnotationProject(
    projectIdList: string[],
    taskIdList: string[],
    isAnnotatedOnly: boolean,
    isAllTasksSelected: boolean,
    pageNo: number,
    pageSize: number,
    teamId: string,
  ) {
    let pagingSize = pageSize || 1000;
    let skip = (pageNo || 0) * pagingSize;

    if (!Array.isArray(taskIdList)) {
      taskIdList = [];
    }

    let projectObjectIdList = projectIdList.map(elem => new ObjectId(elem));

    // build selection name
    let selectionName = 'annotations';

    let additionalProjections: {[k: string]: any} = {};

    //if isAnnotatedOnly=true, then retrive data from MetaUpdates by looking up to MetaData collection. Beacause MetaUpdates record only exist for a annotation studio projects only if
    if (isAnnotatedOnly) {
      let matchFilter: {[k: string]: any} = {
        teamId: new ObjectId(teamId),
        operationId: {$in: projectObjectIdList},
        'annotationObjects.0': {$exists: true},
      };

      if (!isAllTasksSelected) {
        let taskObjectIdList = taskIdList.map(elem => new ObjectId(elem));
        matchFilter = {
          ...matchFilter,
          taskId: {$in: taskObjectIdList},
        };
      }

      let isMultipleOperations: boolean = true;

      let sortCriteria: {[k: string]: number} = {
        collectionId: 1,
        objectKey: 1,
      };

      if (projectIdList.length == 1) {
        isMultipleOperations = false;

        sortCriteria = {
          taskId: 1,
          taskFrameId: 1,
          objectKey: 1,
        };

        additionalProjections = {
          taskId: 1,
          taskFrameId: 1,
        };
      }

      return this.getMetaUpdatesWithMetaData(
        matchFilter,
        sortCriteria,
        additionalProjections,
        skip,
        pagingSize,
        isMultipleOperations,
        selectionName,
        teamId,
      );
    }
    //if isAnnotatedOnly=false, then retrive data from MetaData by looking up to MetaUpdates collection
    else {
      let matchFilter: {[k: string]: any} = {
        'annotationProjectList.id': {$in: projectObjectIdList},
      };

      if (!isAllTasksSelected) {
        let taskObjectIdList = taskIdList.map(elem => new ObjectId(elem));
        matchFilter = {
          ...matchFilter,
          taskIdList: {$in: taskObjectIdList},
        };
      }

      // Only download annotations of the project
      let annotationFilter = [{$in: ['$operationId', projectObjectIdList]}];

      if (projectObjectIdList.length == 1) {
        additionalProjections = {
          taskId: '$annotationData.taskId',
          taskFrameId: '$annotationData.taskFrameId',
        };
      }

      return this.getAnnotationDataForSelection(
        selectionName,
        matchFilter,
        skip,
        pagingSize,
        annotationFilter,
        additionalProjections,
        teamId,
      );
    }
  }

  /**
   * Generic method to get data for selected set of frames from Data Lake
   * @param isMultipleCollections {boolean} True if the selected items can be in multiple collections (will add collection prefix for downloading data)
   * @param selectionName {string} - Collection name or other criteria name of selection
   * @param _matchQuery {object} - Filtering object corresponding to the selection
   * @param teamMatch {object} - Team based filtering if needed
   * @param skip {int} - paging parameter - no of items to skip
   * @param pagingSize {int} - paging parameter - items per page
   * @param annotationFilter {object} - Filter criteria from MetaDataUpdates collection
   * @param teamId {string} - team id
   * @param objectType {number} - Content type of the leaf elements (image / other)
   * @returns Filtered objects list
   */
  async getAnnotationDataForSelection(
    selectionName: string,
    _matchQuery: any,
    skip: number,
    pagingSize: number,
    annotationFilter: any[],
    customProjections: {[k: string]: any},
    teamId: string,
    objectType: number = ContentType.IMAGE,
  ) {
    let identifier = `${selectionName}_raw`;
    let rawFileName = `${identifier}.json`;
    let params = [
      {
        $match: {
          ..._matchQuery,
          teamId: new ObjectId(teamId),
          objectType: objectType,
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
        },
      },
      {
        $sort: {
          sourceVideoId: 1,
          videoFrameIndex: 1,
          objectKey: 1,
        },
      },
      {
        $skip: skip,
      },
      {
        $limit: pagingSize,
      },
      {
        $lookup: {
          from: 'MetaDataUpdate',
          let: {objectKey: '$objectKey'},
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {$eq: ['$objectKey', '$$objectKey']},
                    {$eq: ['$operationType', OperationType.ANNOTATION]},
                    ...annotationFilter,
                  ],
                },
              },
            },
            {
              $group: {
                _id: null,
                annotationObjects: {$push: '$annotationObjects'},
                taskId: {$first: '$taskId'},
                taskFrameId: {$first: '$taskFrameId'},
              },
            },
          ],
          as: 'annotationList',
        },
      },
      {
        $addFields: {
          annotationData: {
            $arrayElemAt: ['$annotationList', 0],
          },
        },
      },
      {
        $addFields: {
          'annotationData.annotationObjects': {
            $reduce: {
              input: '$annotationData.annotationObjects',
              initialValue: [],
              in: {
                $concatArrays: ['$$value', '$$this'],
              },
            },
          },
        },
      },
      {
        $project: {
          objectKey: 1,
          storagePath: 1,
          bucketName: 1,
          name: 1,
          collectionId: 1,
          annotations: '$annotationData.annotationObjects',
          ...customProjections,
        },
      },
    ];

    let annoData: AnnoDataFormat[] = await this.metaDataRepository.aggregate(params); // get images and annotations
    //console.log(JSON.stringify(params, null, 2))

    return await this.formatAnnoDataToDownload(annoData, identifier, rawFileName, selectionName, teamId, pagingSize);
  }

  /**
   * Use to get project metadat updates with metadata
   * @param isMultipleOperations {boolean} True if the selected items can be in
   * multiple collections (will add collection prefix for downloading data)
   * @param selectionName {string} - Collection name or other criteria name of selection
   * @param matchQuery {object} - Filtering object corresponding to the selection
   * @param skip {int} - paging parameter - no of items to skip
   * @param pagingSize {int} - paging parameter - items per page
   * @param teamId {string} - team id
   * @returns Filtered objects list
   */
  async getMetaUpdatesWithMetaData(
    matchQuery: {[k: string]: any},
    sortCriteria: {[k: string]: any},
    additionalProjections: {[k: string]: any},
    skip: number,
    pagingSize: number,
    isMultipleOperations: boolean,
    selectionName: string,
    teamId: string,
  ) {
    let identifier = `${selectionName}_raw`;
    let rawFileName = `${identifier}.json`;

    let params: any = [
      {
        $match: matchQuery,
      },
    ];

    if (isMultipleOperations) {
      params = [
        ...params,
        {
          $group: {
            _id: '$objectKey',
            objectKey: {$first: '$objectKey'},
            annotationObjects: {$push: '$annotationObjects'},
          },
        },
        {
          $addFields: {
            annotationObjects: {
              $reduce: {
                input: '$annotationObjects',
                initialValue: [],
                in: {
                  $concatArrays: ['$$value', '$$this'],
                },
              },
            },
          },
        },
      ];
    }

    params = [
      ...params,
      {
        $sort: sortCriteria,
      },
      {
        $skip: skip,
      },
      {
        $limit: pagingSize,
      },
      {
        $lookup: {
          from: 'MetaData',
          foreignField: 'objectKey',
          localField: 'objectKey',
          as: 'metaObjects',
        },
      },
      {
        $addFields: {
          metaObject: {
            $arrayElemAt: ['$metaObjects', 0],
          },
        },
      },
      {
        $project: {
          objectKey: 1,
          name: '$metaObject.name',
          annotations: '$annotationObjects',
          storagePath: '$metaObject.storagePath',
          bucketName: '$metaObject.bucketName',
          ...additionalProjections,
        },
      },
    ];

    //let strParams = JSON.stringify(params, null, 2);
    logger.info(
      `Download annotations| AnnotationDownloadHandlerService.getMetaUpdatesWithMetaData | N/A | mongo aggregate param: `,
      params,
    );
    let annoData: AnnoDataFormat[] = await this.metaDataUpdateRepository.aggregate(params); // get images and annotations

    return await this.formatAnnoDataToDownload(annoData, identifier, rawFileName, selectionName, teamId, pagingSize);
  }

  /**
   *
   */
  private async formatAnnoDataToDownload(
    annoData: AnnoDataFormat[],
    identifier: string,
    rawFileName: string,
    selectionName: string,
    teamId: string,
    pagingSize: number,
  ) {
    let isNextPageAvilable = false;

    let downloadData: any = {
      format: 'RAW',
      identifier: identifier,
      rawFileName: rawFileName,
      groupUniqueName: identifier,
      resourceArray: [],
      creatableDirectories: ['./data', '.'],
      nextPage: isNextPageAvilable,
    };

    if (!annoData) {
      logger.warn(
        `Download annotations| AnnotationDownloadHandlerService.formatAnnoDataToDownload | ${selectionName} | Error fetching metadata`,
      );
      return {
        errorMsg: 'Fetching from DataLake failed',
        data: downloadData,
      };
    } else if (annoData.length == 0) {
      logger.warn(
        `Download annotations| AnnotationDownloadHandlerService.formatAnnoDataToDownload | ${selectionName} | No frames exists`,
      );
      return {
        errorMsg: 'No frames available',
        data: downloadData,
      };
    } else if (annoData.length == pagingSize) {
      isNextPageAvilable = true;
    }

    let labelToLabelTextMap: Record<string, string> = {};
    if (teamId) {
      labelToLabelTextMap = await this.systemLabelService.getLabelValRefToTextMapOfTeam(teamId);
    }
    // console.log(teamId)
    // console.log(JSON.stringify(labelToLabelTextMap, null, 2))

    let resourceArr = await Promise.all(
      annoData.map(async item => await this.formatAnnotationsToResourceArr(item, rawFileName, labelToLabelTextMap)),
    );

    downloadData = {
      format: 'RAW',
      identifier: identifier,
      rawFileName: rawFileName,
      groupUniqueName: identifier,
      resourceArray: resourceArr,
      creatableDirectories: ['./data', '.'],
      nextPage: isNextPageAvilable,
    };

    return {
      errorMsg: '',
      data: downloadData,
    };
  }

  /**
   * Foermat annotation data to be downloadable by sync tool / python sdk
   * @param item anotation object in db
   * @param rawFileName file name to save the annotations when downloaded
   * @param isAddCollectionPrefix If this flag is true, collection name prefix is added in front of file names
   * @returns
   */
  private async formatAnnotationsToResourceArr(
    item: AnnoDataFormat,
    rawFileName: string,
    labelToLabelTextMap: Record<string, string>,
  ) {
    let annotationFormatted: any = [];
    if (item.annotations && item.annotations.length > 0) {
      annotationFormatted = item.annotations.map(annoItem =>
        this.formatAnnotationsToRaw(annoItem, labelToLabelTextMap),
      );
    }
    let rawAnno: RawAnnoFormat = {
      image: item.objectKey,
      fileName: item.name || '',
      annotations: annotationFormatted,
      taskId: item.taskId,
      frameId: item.taskFrameId,
    };

    let fileUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
      item.storagePath,
      AWS_FILE_URL_EXPIRE_DURATION,
      item.bucketName,
    );

    let resource = {
      fileUrl: fileUrl,
      fileName: item.objectKey,
      fileDownloadFolderLocation: './data',
      rawAnnotations: rawAnno,
      rawFileName: rawFileName,
      taskId: item.taskId,
      taskFrameId: item.taskFrameId,
      isMediaFile: true,
    };
    return resource;
  }

  /**
   * Format annotations in db (json) to RAW format
   * @param object annotation object in db
   * @returns
   */
  private formatAnnotationsToRaw(object: AnnotationObject, labelToLabelTextMap: Record<string, string>) {
    let label: string | null = null;
    if (object.label?.label) {
      label = labelToLabelTextMap[object.label?.label] ? labelToLabelTextMap[object.label?.label] : object.label?.label;
    }

    // let attributes: {[k: string]: AttributeValues[]} = object.label?.attributeValues ? object.label?.attributeValues : {};

    let attributes: Record<string, string> = {};
    if (object.label?.attributeValues) {
      attributes = this.formatLabelAttributesToRaw(object.label?.attributeValues, labelToLabelTextMap);
    }

    if (object.type == 'line') {
      let lineObject = {
        id: object.id,
        type: object.type,
        confidence: object.confidence,
        line: object.points,
        label: label,
        metadata: object.metadata ? object.metadata : {},
        attributes: attributes,
      };
      return lineObject;
    } else if (object.type == 'polygon') {
      let polygonObject = {
        id: object.id,
        type: object.type,
        confidence: object.confidence,
        polygon: object.points,
        label: label,
        metadata: object.metadata ? object.metadata : {},
        attributes: attributes,
      };
      return polygonObject;
    } else if (object.type == 'rectangle') {
      let rectangleObject = {
        id: object.id,
        type: object.type,
        confidence: object.confidence,
        bbox: [
          object.boxBoundariesAndDimensions?.x,
          object.boxBoundariesAndDimensions?.y,
          object.boxBoundariesAndDimensions?.w,
          object.boxBoundariesAndDimensions?.h,
        ],
        label: label,
        metadata: object.metadata ? object.metadata : {},
        attributes: attributes,
      };
      return rectangleObject;
    } else if (object.type == 'points') {
      let pointsObject = {
        id: object.id,
        type: object.type,
        confidence: object.confidence,
        points: object.points,
        label: label,
        metadata: object.metadata ? object.metadata : {},
        attributes: attributes,
      };
      return pointsObject;
    } else {
      return {};
    }
  }

  /**
   * Format lable attributes (references) in db (json) to RAW format
   * @param attributeValues
   * @param labelToLabelTextMap
   * @returns
   */
  private formatLabelAttributesToRaw(
    attributeValues: {[k: string]: AttributeValues[]},
    labelToLabelTextMap: Record<string, string>,
  ) {
    let attribute: any = {};
    for (const [key, valueArray] of Object.entries(attributeValues)) {
      let keyLabel = labelToLabelTextMap[key];

      for (const valueObj of valueArray) {
        let valueLabel = labelToLabelTextMap[valueObj.value];
        if (keyLabel && valueLabel) {
          attributeValues[key][valueArray.indexOf(valueObj)].value = valueLabel;
        }
      }
      attribute[keyLabel] = attributeValues[key];

      // let valueLabel = labelToLabelTextMap[value];
      // if (keyLabel && valueLabel) {
      //   metadata[labelToLabelTextMap[key]] = labelToLabelTextMap[value];
      // }
    }
    return attribute;
  }
}
export const ANNO_DOWNLOAD_HANDLER_SERVICE = BindingKey.create<AnnotationDownloadHandlerService>(
  'service.annotationDownloadHandler',
);

export interface AnnoDataFormat {
  objectKey: string;
  storagePath: string;
  bucketName: string;
  name?: string;
  annotations?: AnnotationObject[];
  taskId?: string;
  taskFrameId?: number;
}

export interface RawAnnoFormat {
  image: string;
  fileName: string;
  annotations: AnnotationObject[];
  taskId?: string;
  frameId?: number;
}
