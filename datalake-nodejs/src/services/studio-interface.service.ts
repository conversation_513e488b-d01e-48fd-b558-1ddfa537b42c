import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {default as Axios} from 'axios';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, OBJECT_STATUS, OperationMode} from '../models';
import {DatalakeSelectionRepository} from '../repositories';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {DEFAULT_ITEM_SORT_IN_COLLECTION} from '../settings/global-field-config';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
dotenv.config();
const PYTHON_HOST = process.env.PYTHON_BASE_URL;
@injectable({scope: BindingScope.TRANSIENT})
export class StudioInterfaceService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
  ) {}

  async getChildFilesForSelection(
    selectionId: string,
    limit: number,
    projectId: string,
    currentUserProfile: UserProfileDetailed,
  ) {
    let selectionObj = await this.searchQueryBuilderService.getMatchQueryForSelection(currentUserProfile, selectionId);
    let objectIdList: string[] = [];
    logger.debug(selectionObj?.matchQuery);
    if (selectionObj?.objectType == ContentType.IMAGE || selectionObj?.objectType == ContentType.VIDEO) {
      let params: any[] = [{$match: selectionObj.matchQuery}];

      //add images only if they do not already present in project
      //add video even it present in project
      if (selectionObj?.objectType == ContentType.IMAGE) {
        params.push({
          $match: {
            'annotationProjectList.id': {$ne: new ObjectId(projectId)},
            // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
          },
        });
      }
      // else {
      //   params.push(
      //     {
      //       $match: {
      //         objectStatus: {$ne: OBJECT_STATUS.TRASHED}
      //       }
      //     }
      //   )
      // }
      params.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
      params.push({$limit: limit});
      params.push({$project: {_id: 1}});

      let _objectIdList: {_id: string}[] = await this.metaDataRepository.aggregate(params);

      objectIdList = _objectIdList.map(elem => elem._id);
    } else if (
      selectionObj?.objectType == ContentType.IMAGE_COLLECTION ||
      selectionObj?.objectType == ContentType.VIDEO_COLLECTION ||
      selectionObj?.objectType == ContentType.DATASET
    ) {
      let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
        {
          $match: selectionObj.matchQuery,
        },
        {
          $project: {_id: 1},
        },
      ]);
      let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));
      let matchQuery = {
        vCollectionIdList: {$in: collectionObjectIdList},
        objectStatus: OBJECT_STATUS.ACTIVE,
      };

      let params: any[] = [{$match: matchQuery}];

      //add images only if they do not already present in project
      //add video even it present in project
      if (selectionObj?.objectType == ContentType.IMAGE_COLLECTION || selectionObj?.objectType == ContentType.DATASET) {
        params.push({$match: {'annotationProjectList.id': {$ne: new ObjectId(projectId)}}});
      }
      params.push({$sort: DEFAULT_ITEM_SORT_IN_COLLECTION});
      params.push({$limit: limit});
      params.push({$project: {_id: 1}});

      let _objectIdList: {_id: string}[] = await this.metaDataRepository.aggregate(params);

      objectIdList = _objectIdList.map(elem => elem._id);
    }
    // else if (selectionObj?.objectType == ContentType.DATASET) {
    //   let _datasetIds: {_id: string}[] = await this.metaDataRepository.aggregate([
    //     {
    //       $match: selectionObj.matchQuery,
    //     },
    //     {
    //       $project: {_id: 1},
    //     },
    //   ]);

    //   let datasetObjectIdList = _datasetIds.map(elem => new ObjectId(elem._id));
    //   let matchQuery = {
    //     'datasetVersionList.datasetMetaId': {$in: datasetObjectIdList},
    //     objectType: ContentType.IMAGE,
    //   };

    //   let _objectIdList: {_id: string}[] = await this.metaDataRepository.aggregate([
    //     {$match: matchQuery},
    //     {$match: {'annotationProjectList.id': {$ne: new ObjectId(projectId)}}},
    //     {$sort: DEFAULT_ITEM_SORT_IN_COLLECTION},
    //     //{$skip: skip}, // removed skip after adding {$match: {"annotationProjectList.id": {$ne: new ObjectId(projectId)}}} stage
    //     {$limit: limit},
    //     {$project: {_id: 1}},
    //   ]);

    //   objectIdList = _objectIdList.map(elem => elem._id);
    // }
    let childObjectType = selectionObj?.objectType || ContentType.UNSUPPORTED;
    if (selectionObj?.objectType == ContentType.IMAGE_COLLECTION || selectionObj?.objectType == ContentType.DATASET) {
      childObjectType = ContentType.IMAGE;
    } else if (selectionObj?.objectType == ContentType.VIDEO_COLLECTION) {
      childObjectType = ContentType.VIDEO;
    }

    return {
      objectType: childObjectType,
      objectIdList: objectIdList,
    };
  }

  async getDataListWithChildFilesCount(
    selectionId: string,
    currentUserProfile: UserProfileDetailed,
    projectId?: string,
  ) {
    let selectionObj = await this.searchQueryBuilderService.getMatchQueryForSelection(currentUserProfile, selectionId);
    let objectCount: [{count: number}] = [{count: 0}];
    logger.debug(selectionObj?.matchQuery);
    if (
      selectionObj?.objectType == ContentType.IMAGE ||
      selectionObj?.objectType == ContentType.VIDEO ||
      selectionObj?.objectType == ContentType.OTHER
    ) {
      let aggregatePipeline: any = [
        {$match: selectionObj.matchQuery},
        {
          $match: {
            //replace flag with enum
            objectStatus: OBJECT_STATUS.ACTIVE,
            // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
          },
        },
        {$project: {_id: 1}},
        {$count: 'count'},
      ];
      if (projectId) {
        aggregatePipeline = [{$match: selectionObj.matchQuery}];
        if (selectionObj?.objectType == ContentType.IMAGE) {
          aggregatePipeline.push({
            $match: {
              'annotationProjectList.id': {$ne: new ObjectId(projectId)},
              //replace flag with enum
              objectStatus: OBJECT_STATUS.ACTIVE,
              // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
            },
          });
        } else {
          aggregatePipeline.push({
            $match: {
              //replace flag with enum
              objectStatus: OBJECT_STATUS.ACTIVE,
              // objectStatus: {$ne: OBJECT_STATUS.TRASHED}
            },
          });
        }
        aggregatePipeline.push({$project: {_id: 1}});
        aggregatePipeline.push({$count: 'count'});
      }

      let _objectCount: [{count: number}] = await this.metaDataRepository.aggregate(aggregatePipeline);
      //logger.debug(JSON.stringify(aggregatePipeline, null, 2))
      objectCount = _objectCount;
    } else if (
      selectionObj?.objectType == ContentType.IMAGE_COLLECTION ||
      selectionObj?.objectType == ContentType.VIDEO_COLLECTION ||
      selectionObj?.objectType == ContentType.DATASET ||
      selectionObj?.objectType == ContentType.OTHER_COLLECTION
    ) {
      let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
        {$match: selectionObj.matchQuery},
        {$project: {_id: 1}},
      ]);
      let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));
      let matchQuery = {
        vCollectionIdList: {$in: collectionObjectIdList},
        objectStatus: OBJECT_STATUS.ACTIVE,
      };
      let aggregatePipeline: any = [{$match: matchQuery}, {$project: {_id: 1}}, {$count: 'count'}];
      if (projectId) {
        aggregatePipeline = [{$match: matchQuery}];
        if (
          selectionObj?.objectType == ContentType.IMAGE_COLLECTION ||
          selectionObj?.objectType == ContentType.DATASET
        ) {
          aggregatePipeline.push({$match: {'annotationProjectList.id': {$ne: new ObjectId(projectId)}}});
        }
        aggregatePipeline.push({$project: {_id: 1}});
        aggregatePipeline.push({$count: 'count'});
      }

      let _objectCount: [{count: number}] = await this.metaDataRepository.aggregate(aggregatePipeline);
      //logger.debug(JSON.stringify(aggregatePipeline, null, 2))
      objectCount = _objectCount;
    }

    return objectCount;
  }

  /**
   * use to find metaUpdates operation list of the selected frames for a particular project
   * @param projectId id of the project
   * @param selectionId id of the selection object
   * @param removedCollectionIdList string[], ids of collections which are removed from the project
   * @param addedCollectionIdList string[], ids of collection which are added to the project
   * @returns
   */
  async findOperationList(
    currentUserProfile: UserProfileDetailed,
    projectId?: string,
    selectionId?: string,
    removedCollectionIdList?: string[],
    addedCollectionIdList?: string[],
  ) {
    let matchFilter = await this.searchQueryBuilderService.createMatchQuery(
      currentUserProfile,
      selectionId,
      removedCollectionIdList,
      addedCollectionIdList,
      undefined,
      projectId,
    );

    let operationList: {
      operationId: string | ObjectId;
      operationName: string;
    }[] = [];

    if (!matchFilter) return operationList;

    let params: {[k: string]: any}[] = [
      {
        $match: matchFilter,
      },
      {
        $unwind: '$operationList',
      },
      {
        $match: {'operationList.operationMode': OperationMode.AUTO},
      },
      {
        $group: {
          _id: null,
          operations: {
            $addToSet: {
              operationId: '$operationList.operationId',
              operationName: '$operationList.operationName',
            },
          },
        },
      },
    ];

    let operations: [
      {
        _id: null;
        operations: {
          operationId: string | ObjectId;
          operationName: string;
        };
      },
    ] = await this.metaDataRepository.aggregate(params);

    if (
      Array.isArray(operations) &&
      operations.length > 0 &&
      Array.isArray(operations[0].operations) &&
      operations[0].operations.length > 0
    ) {
      operationList = operations[0].operations;
    }

    return operationList;
  }

  /**
   * use to get auto annotation
   * @param requestBody
   * @returns annotation data
   */
  async getAutoAnnotation(requestBody: any) {
    try {
      let url = `${PYTHON_HOST}/internal/annotation/dataIn_prompt_infer`;

      let response = await Axios({
        url,
        method: 'POST',
        data: requestBody,
      });

      let responseData = response.data;

      return responseData;
    } catch (err) {
      logger.error(
        `Auto annotation generation| StudioInterfaceService.getAutoAnnotation | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }
}

export const STUDIO_INTERFACE_SERVICE = BindingKey.create<StudioInterfaceService>('service.studioInterface');
