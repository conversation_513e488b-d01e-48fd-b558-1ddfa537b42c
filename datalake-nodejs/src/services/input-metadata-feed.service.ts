/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Use to process metadata input. Queue bulk metadata inputs and dequeue batchwise
 */

/**
 * @class InputMetadataFeedService
 * Use to process metadata input. Queue bulk metadata inputs and dequeue batchwise
 * @description  Queue bulk metadata inputs and dequeue batchwise
 * <AUTHOR>
 */

import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  ContentType,
  MetaData,
  MetaDataCollectionInputObjectFormat,
  MetaDataKeyInputListFormat,
  MetaDataKeyInputType,
  MetaDataSuggestionFormat,
  MetaFieldFormatFileUpload,
  OBJECT_STATUS,
  SearchQueryRootGroup,
  metaFieldInput,
} from '../models';
import {MetaFieldRepository, MetaTagRepository} from '../repositories';
import {FileUploadProgressRepository} from '../repositories/file-upload-progress.repository';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {QueryOptionRepository} from '../repositories/query-option.repository';
import {SystemDataRepository} from '../repositories/system-data.repository';
import {UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DefaultMetaDataKeys} from '../settings/global-field-config';
import {getContentType} from '../settings/tools';
import {DATALAKE_TRASH_SERVICE, DatalakeTrashService} from './datalake-trash.service';
import {JOB_SERVICE, JobService} from './job.service';
import {META_DATA_SERVICE, MetaDataService} from './meta-data.service';
import {SYSTEM_META_SERVICE, SystemMetaService} from './system-meta.service';

dotenv.config();

const storageType = process.env.STORAGE_TYPE;

@injectable({scope: BindingScope.TRANSIENT})
export class InputMetadataFeedService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(MetaDataRepository) private metaDataRepository: MetaDataRepository,
    @repository(FileUploadProgressRepository) private fileUploadProgressRepository: FileUploadProgressRepository,
    @repository(QueryOptionRepository) private queryOptionRepository: QueryOptionRepository,
    @repository(SystemDataRepository) private systemDataRepository: SystemDataRepository,
    @repository(MetaFieldRepository)
    private metaFieldRepository: MetaFieldRepository,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
    @inject(SYSTEM_META_SERVICE) private systemMetaService: SystemMetaService,
    @inject(DATALAKE_TRASH_SERVICE) private datalakeTrashService: DatalakeTrashService,
    @inject(JOB_SERVICE) private jobService: JobService,
    @repository(MetaTagRepository)
    private metaTagRepository: MetaTagRepository,
  ) {}

  /**
   * Method to handle metadata collection upload start
   * Create collection
   * Create fileUpload (to track progressa)
   */
  async handleMetaDataCollectionInputStart(
    metaDataInputCollection: MetaDataCollectionInputObjectFormat,
    currentUserProfile: UserProfileDetailed,
    teamId?: string,
    userId?: string,
    userName?: string,
  ) {
    logger.debug(`metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Input start received for
        collectionType:${metaDataInputCollection.collectionType}
        collectionName:${metaDataInputCollection.collectionName}
        collectionId:${metaDataInputCollection.collectionId}
        storagePrefixPath:${metaDataInputCollection.storagePrefixPath}
        isOverrideMetaData:${metaDataInputCollection.isOverrideMetaData}
        metaDataObject:${JSON.stringify(metaDataInputCollection.metaDataObject, null, 2)}
        fieldConfig:${JSON.stringify(metaDataInputCollection.fieldConfig, null, 2)}
    `);

    // logger.debug("sdk meta object", JSON.stringify(metaDataInputCollection.metaDataObject, null, 2))
    // recieve tags
    let recievedTags: string[] = [];

    if (!teamId) {
      logger.error(
        `InputMetadataFeedService | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | teamId is not provided`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    //in case of "Tags" field received as not an array, then transform it to an array
    if (
      metaDataInputCollection &&
      metaDataInputCollection.metaDataObject &&
      metaDataInputCollection.metaDataObject.Tags
    ) {
      if (!Array.isArray(metaDataInputCollection.metaDataObject.Tags)) {
        metaDataInputCollection.metaDataObject.Tags = [metaDataInputCollection.metaDataObject.Tags];
      }
    }

    // let receivedFields: string[] = [];
    //transform all custom fields to type of string
    for (const [key, value] of Object.entries(metaDataInputCollection.metaDataObject)) {
      // update query option for custom metadata (custome matadata wil be available in inputMetaDataFeedGroup.metaDataArray)
      if (!MetaData.definition.properties.hasOwnProperty(key)) {
        metaDataInputCollection.metaDataObject[key] = value.toString();
        // receivedFields.push(key);
      }

      if (key == 'Tags') {
        recievedTags = value;
      }
    }

    // let currentUserProfile: Partial<UserProfileDetailed> = {
    //   name: userName,
    //   teamId: teamId,
    // };

    // validate and create tags in the system
    for (let tag of recievedTags) {
      await this.systemMetaService.validateAndCreateTag(tag, currentUserProfile);
    }

    // validate and create metaFields in the system

    // await this.objectMetaUpdaterService.sdkMetaFieldsValidate(
    //   receivedFields,
    //   currentUserProfile?.teamId || '',
    //   currentUserProfile,
    // );

    // check collection - have to use a seperate collection creation logic for frontend (different to exisiting one for upload client)
    let collectionId: string | undefined = undefined;

    // format metaDataUpdates to be inserted to the collection
    let customMetaDataTagOfInput = this.metaDataRepository.filterAndGetCustomMetaTagObject(
      metaDataInputCollection.metaDataObject,
      true,
      true,
    );

    let customMetaDataOfInput = this.metaDataRepository.filterAndGetCustomMetaObject(
      metaDataInputCollection.metaDataObject,
    );

    // initialize metaField Object
    let metaFieldsObject: MetaFieldFormatFileUpload = {
      metaFieldList: [],
      metaObject: {},
    };

    // if fieldConfig is given, then use it to format metaFields
    if (metaDataInputCollection.fieldConfig) {
      // format metaFields to be inserted to the collection
      metaFieldsObject = await this.getMetaFieldList(
        metaDataInputCollection.metaDataObject,
        metaDataInputCollection.fieldConfig,
      );
      // customMetaDataOfInput.metaFieldList = {$each: metaFieldList}
    }

    let allowedUserId = '';
    if (metaDataInputCollection.metaDataObject.allowedUserIdList) {
      allowedUserId = String(metaDataInputCollection.metaDataObject.allowedUserIdList[0]);
      logger.debug(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | allowedUserId: ${allowedUserId}`,
      );
    }

    if (
      // currentUserProfile.userType == UserType.USER_TYPE_AUDITOR ||
      currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR
    ) {
      allowedUserId = currentUserProfile.id || '';
      metaDataInputCollection.metaDataObject.allowedUserIdList = [new ObjectId(allowedUserId)];
    }

    if (metaDataInputCollection.collectionId) {
      logger.debug(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | collectionId:${metaDataInputCollection.collectionId} | collectionId given`,
      );
      // use collectionId
      collectionId = metaDataInputCollection.collectionId;
    } else if (metaDataInputCollection.collectionType) {
      let customMeta = {};
      if (metaDataInputCollection.storagePrefixPath) {
        customMeta = {
          storagePrefixPath: metaDataInputCollection.storagePrefixPath,
        };
      }

      // create/find a new collection and use collectionId
      collectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
        metaDataInputCollection.collectionType,
        new ObjectId(teamId),
        metaDataInputCollection.collectionName,
        customMeta,
        undefined,
        allowedUserId,
      );
    } else {
      // no collectionId nor collection name - invalid
      logger.error(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Invalid or missing collection Id or name`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_COLLECTION_NAME);
    }

    //validate collection for upload
    let collectionObj = await this.metaDataRepository.findById(collectionId);

    if (
      // currentUserProfile.userType == UserType.USER_TYPE_AUDITOR ||
      currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR
    ) {
      let allowedUserIdList = collectionObj.allowedUserIdList?.map(id => String(id)) || [];
      if (currentUserProfile.id && !allowedUserIdList?.includes(currentUserProfile?.id)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    await this.validateCollectionToFileUpload(
      collectionObj,
      metaDataInputCollection.collectionType,
      metaDataInputCollection.storagePrefixPath,
    );

    if (!collectionObj.storagePath) {
      let storagePath = await this.metaDataRepository.getUniqueNameForCollection(collectionObj.name, teamId);
      await this.metaDataRepository.updateById(collectionObj.id, {storagePath: storagePath});
    }

    // isFeatureGraphPending flag update as true, to generate graph coordinates for image collection and dataset
    if (
      collectionObj.objectType == ContentType.IMAGE_COLLECTION &&
      (collectionObj.isFeatureGraphPending == false || !collectionObj.isFeatureGraphPending)
    ) {
      await this.metaDataRepository.updateById(collectionId, {isFeatureGraphPending: true});
    }

    // if collection is trashed, then handle it (Restore it)
    if (
      // collectionObj.collectionType == CollectionType.AUGMENTED_UPLOAD &&
      // metaDataInputCollection.applicationCode == APP_CODE.SDK_VIA_DATASET &&
      collectionObj.objectStatus == OBJECT_STATUS.TRASHED
    ) {
      await this.handleUploadToTrashedCollection(collectionObj, teamId, userId);
    }

    // check if file list available
    let fileList: string[] = [];
    let imageCollectionId: string | undefined;
    let videoCollectionId: string | undefined;
    let collectionList: ObjectId[] = [new ObjectId(collectionId)];

    if (metaDataInputCollection.objectKeyList && metaDataInputCollection.objectKeyList.length > 0) {
      fileList = metaDataInputCollection.objectKeyList;

      //if upload is for a other collection, then check file list contains images and videos also
      if (metaDataInputCollection.collectionType == ContentType.OTHER_COLLECTION) {
        let isVideoExist = fileList.some(file => getContentType(file) == ContentType.VIDEO);
        let isImageExist = fileList.some(file => getContentType(file) == ContentType.IMAGE);

        if (isImageExist) {
          imageCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
            ContentType.IMAGE_COLLECTION,
            new ObjectId(teamId),
            metaDataInputCollection.collectionName,
            {},
            undefined,
            allowedUserId,
          );
          collectionList.push(new ObjectId(imageCollectionId));
          // update upload pending in the collection
          await this.metaDataRepository.updateById(imageCollectionId, {
            uploadInProgress: true,
            uploadingUserId: userId,
          });
          // update updatedAt
          await this.metaDataRepository.updateUpdatedAt(imageCollectionId);
        }

        if (isVideoExist) {
          videoCollectionId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
            ContentType.VIDEO_COLLECTION,
            new ObjectId(teamId),
            metaDataInputCollection.collectionName,
            {},
            undefined,
            allowedUserId,
          );
          collectionList.push(new ObjectId(videoCollectionId));
          // update upload pending in the collection
          await this.metaDataRepository.updateById(videoCollectionId, {
            uploadInProgress: true,
            uploadingUserId: userId,
          });
          // update updatedAt
          await this.metaDataRepository.updateUpdatedAt(videoCollectionId);
        }
      }
    }

    let collectionListToBeUpdated: string[] = [];
    if (collectionId) collectionListToBeUpdated.push(collectionId);
    if (imageCollectionId) collectionListToBeUpdated.push(imageCollectionId);
    if (videoCollectionId) collectionListToBeUpdated.push(videoCollectionId);

    // update collection's updatedAt, uploadInProgress, uploadingUserId, metaFieldList, customMetaData
    for (let colId of collectionListToBeUpdated) {
      // update upload pending in the collection
      await this.metaDataRepository.updateById(colId, {
        uploadInProgress: true,
        uploadingUserId: userId,
      });
      // update updatedAt
      await this.metaDataRepository.updateUpdatedAt(colId);

      // if fieldConfig is given, then use it to format metaFields
      if (metaDataInputCollection.fieldConfig) {
        let metaFieldList = metaFieldsObject.metaFieldList;

        await this.metaDataRepository.updateManySet({_id: new ObjectId(colId)}, {metaFieldList: metaFieldList}, []);
      }

      // update collection by populating custom metaData
      await this.metaDataRepository.updateManyAddToSetToList({_id: new ObjectId(colId)}, customMetaDataTagOfInput, []);
      await this.metaDataRepository.updateManySet({_id: new ObjectId(colId)}, customMetaDataOfInput, []);
    }

    // create a fileUploadProgress document
    let uploadId = await this.fileUploadProgressRepository.createRecordOnFIleUploadStart(
      collectionId!,
      fileList,
      Object.values(metaFieldsObject.metaObject).length == 0 && metaFieldsObject.metaObject.constructor == Object
        ? metaDataInputCollection.metaDataObject
        : metaFieldsObject.metaObject,
      metaDataInputCollection.collectionType,
      teamId,
      metaDataInputCollection.isOverrideMetaData ? metaDataInputCollection.isOverrideMetaData : false,
      imageCollectionId,
      videoCollectionId,
    );

    let jobId;
    //create job entry
    if (uploadId && collectionId) {
      let job = await this.jobService.updateUploadJobEntry(
        uploadId,
        collectionId,
        userId,
        fileList,
        metaDataInputCollection.collectionType,
        userName,
        teamId,
        collectionList,
      );
      jobId = job?._id;
    }

    // to generate recieved keys array
    let recievedKeyArr: string[] = [];

    // update added metadata keys in query builder
    for (const [key, value] of Object.entries(metaDataInputCollection.metaDataObject)) {
      // update query option for custom metadata (custome matadata wil be available in inputMetaDataFeedGroup.metaDataArray)
      if (!MetaData.definition.properties.hasOwnProperty(key) || key == 'Tags') {
        // create a object from key,value pair
        let tempObj: {[k: string]: any} = {};
        tempObj[key] = value;
        // call query update function
        await this.queryOptionRepository.updateQueryOption(
          SearchQueryRootGroup.METADATA,
          tempObj,
          true,
          teamId,
          collectionId,
        );
      }
      // format data to save
      recievedKeyArr.push(key);
    }

    // get key names of default keys
    let defaultKeyStrings = DefaultMetaDataKeys.map(keyObject => keyObject.fieldKey);

    // update system record by adding latest key list and tags ----------

    // filter out default keys from recieved
    let recentKeyList = recievedKeyArr.filter(key => !defaultKeyStrings.includes(key));

    if (uploadId) {
      // collection created
      logger.debug(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | uploadId:${uploadId} | Created upload record`,
      );
      // update isMetaFieldsPropagationRequired flag in collection
      this.metaDataRepository.updateById(collectionId, {isMetaFieldsPropagationRequired: true});
      return {
        isSuccess: true,
        collectionId: collectionId,
        uploadId: uploadId,
        storageType: storageType,
        jobId: jobId,
      };
    } else {
      logger.error(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Failed to create upload record`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.UPLOAD_INITIATION_FAILED);
    }
  }

  /**
   * to handle trashed collection upload
   * @param collectionObj collection object to be restored
   * @param teamId teamId
   * @param userId userId
   * @returns if error occurs, throws error else restores the collection
   */
  async handleUploadToTrashedCollection(collectionObj: MetaData, teamId?: string, userId?: string) {
    if (!collectionObj || !collectionObj.id) {
      logger.error(
        `InputMetadataFeedService | InputMetadataFeedService.handleUploadToTrashedCollection | N/A | Invalid collection object`,
      );
      throw new HttpErrors.NotAcceptable('Invalid collection object');
    }

    if (!teamId) {
      logger.error(
        `InputMetadataFeedService | InputMetadataFeedService.handleUploadToTrashedCollection | N/A | teamId is not provided`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let restoreInfo = {
      objectStatus: OBJECT_STATUS.ACTIVE,
      statPending: true,
      statPendingAt: new Date(),
      showInTrash: false,
      updatedAt: new Date(),
      restoredAt: new Date(),
      restoredBy: userId ? new ObjectId(userId) : undefined,
    };

    try {
      // showInTrash flag update for all trashed child objects
      await this.metaDataRepository.updateManySet(
        {
          vCollectionIdList: new ObjectId(collectionObj.id),
          objectStatus: OBJECT_STATUS.TRASHED,
        },
        {
          showInTrash: true,
          updatedAt: new Date(),
        },
        [],
      );

      // Restore collection object (head)
      await this.metaDataRepository.updateManySet({_id: new ObjectId(collectionObj.id)}, restoreInfo, []);
    } catch (error) {
      logger.error(
        `InputMetadataFeedService | InputMetadataFeedService.handleTrashedAugmentedUploadCollection | ${collectionObj.id} | ${error}`,
      );
      throw new HttpErrors.NotAcceptable('Failed to restore collection');
    }

    // regenerate query option for collection
    this.queryOptionRepository.regenerateQueryOptionForCollection(collectionObj.id, teamId);
  }

  /**
   * validate meta data object and return metaFieldList
   * @param metaObject custom Meta data object
   * @param fieldConfig MetaDataSuggestionFormat array
   * @returns metaFieldList
   */
  async getMetaFieldList(
    updatedCustomMeta: {[k: string]: any},
    fieldConfig: MetaDataSuggestionFormat[],
    existingCustomMeta?: {[k: string]: any},
    deleteFields?: string[],
  ) {
    // create a list of metaFieldId
    let metaFieldList: metaFieldInput[] = [];
    let validatedMetaObjectUpdateInfo: {[k: string]: any} = {};

    // manage custom meta data
    for (let field of fieldConfig) {
      if (field.isSelected) {
        let systemField = await this.metaFieldRepository.findById(field.id);

        metaFieldList.push({
          metaFieldId: new ObjectId(field.id),
          isMandatory: field.isMandatory,
        });

        if (field.isMandatory) {
          // check if field is in existing deleted meta data field list
          if (deleteFields && deleteFields.includes(systemField.fieldName)) {
            logger.error(
              `metadata upload on file upload start | InputMetadataFeedService.getMetaFieldList | N/A | Field ${systemField.fieldName} is mandatory, cannot be deleted`,
            );
            throw new HttpErrors.NotAcceptable(`Field ${systemField.fieldName} is mandatory, cannot be deleted`);
          }

          // check if field is in in updated(add/edit) CustomMeta
          if (updatedCustomMeta.hasOwnProperty(systemField.fieldName) && updatedCustomMeta[systemField.fieldName]) {
            validatedMetaObjectUpdateInfo[systemField.fieldName] = updatedCustomMeta[systemField.fieldName];
          } else {
            // check if field is in existing CustomMeta
            if (existingCustomMeta) {
              if (
                !existingCustomMeta.hasOwnProperty(systemField.fieldName) ||
                !existingCustomMeta[systemField.fieldName]
              ) {
                logger.error(
                  `metadata upload on file upload start | InputMetadataFeedService.getMetaFieldList | N/A | Field ${systemField.fieldName} is mandatory & not available in existing meta data`,
                );
                throw new HttpErrors.NotAcceptable(
                  `Field ${systemField.fieldName} is mandatory & not available in existing meta data`,
                );
              }
            } else {
              logger.error(
                `metadata upload on file upload start | InputMetadataFeedService.getMetaFieldList | N/A | Field ${systemField.fieldName} is mandatory & not available in existing/updating meta data`,
              );
              throw new HttpErrors.NotAcceptable(
                `Field ${systemField.fieldName} is mandatory & not available in existing/updating meta data`,
              );
            }
          }
        } else {
          if (updatedCustomMeta.hasOwnProperty(systemField.fieldName) && updatedCustomMeta[systemField.fieldName]) {
            validatedMetaObjectUpdateInfo[systemField.fieldName] = updatedCustomMeta[systemField.fieldName];
          }
        }
      }
    }

    //manage Tags
    if (updatedCustomMeta.hasOwnProperty('Tags')) {
      validatedMetaObjectUpdateInfo['Tags'] = updatedCustomMeta['Tags'];
    }

    return {metaFieldList: metaFieldList, metaObject: validatedMetaObjectUpdateInfo};
  }

  /**
   * to validate collection when file upload starts
   * @param collectionName collection name
   */
  async validateCollectionToFileUpload(
    collectionObj: MetaData,
    uploadingCollectionType?: ContentType,
    prefixPath?: string,
  ) {
    // if (collectionObj.objectStatus == OBJECT_STATUS.TRASHED) {
    //   // if collection is trashed and collection type is augmented upload then allow to upload
    //   if (
    //     collectionObj.collectionType == CollectionType.AUGMENTED_UPLOAD &&
    //     applicationCode == APP_CODE.SDK_VIA_DATASET
    //   ) {
    //     logger.debug(
    //       `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | collection:${collectionObj.id} | collection already trashed`,
    //     );
    //     return;
    //   }

    //   logger.error(
    //     `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | collection:${collectionObj.id} | collection already trashed`,
    //   );
    //   throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FILE_UPLOAD_NOT_ALLOWED_TO_TRASHED_COLLECTIONS);
    // }

    // if (collectionObj.isFromVideo) {
    //   if (prefixPath) {
    //     logger.debug(
    //       `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | collection is from video and prefix path is available`,
    //     );
    //     return;
    //   }
    //   logger.error(
    //     `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Couldn't upload files to a derived collection - frame collection`,
    //   );
    //   throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FILE_UPLOAD_NOT_ALLOWED_TO_DERIVED_COLLECTIONS);
    // }

    // if (
    //   collectionObj.collectionType == CollectionType.AUGMENTED_UPLOAD
    // && applicationCode != APP_CODE.SDK_VIA_DATASET
    // ) {
    //   logger.error(
    //     `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Couldn't upload files to a derived collection - augmented`,
    //   );
    //   throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FILE_UPLOAD_NOT_ALLOWED_TO_DERIVED_COLLECTIONS);
    // }

    if (collectionObj.objectType == ContentType.DATASET) {
      logger.error(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | Couldn't upload files to a dataset`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FILE_UPLOAD_NOT_ALLOWED_TO_DATASET_COLLECTIONS);
    }

    if (collectionObj.objectType != uploadingCollectionType) {
      logger.error(
        `metadata upload on file upload start | InputMetadataFeedService.handleMetaDataCollectionInputStart | N/A | file type mismatched with collection type - when uploading to collection ${collectionObj.id}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.FILE_TYPE_COLLECTION_TYPE_MISMATCH);
    }
  }

  /**
   * Method to generate metadata field suggestions for input
   * @param contentType - to filter out collection names
   * @param teamId
   */
  // async generateMetaDataFields(contentType: ContentType, teamId?: string) {
  //   // get system data file for the team
  //   let systemData: SystemData | null = null;
  //   let allTags: string[] = [];
  //   let recentTags: string[] = [];
  //   if (teamId) {
  //     systemData = await this.systemDataRepository.findOne({where: {teamId: teamId}});
  //     allTags = (await this.metaTagRepository.distinct('tag', {})) || [];
  //     recentTags = (await this.metaTagRepository.distinct('tag', {isRecent: true})) || [];
  //   }

  //   // get default key list
  //   let defaultKeys: MetaDataKeyInputListFormat[] = JSON.parse(JSON.stringify(DefaultMetaDataKeys));

  //   let defaultKeyStrings: string[] = [];

  //   // generate dynamic key fields ---------------------
  //   if (defaultKeys) {
  //     for (let i = 0; i < defaultKeys.length; i++) {
  //       if (defaultKeys[i] && defaultKeys[i].fieldKey == 'Collection Name') {
  //         // generate collection names list
  //         defaultKeys[i].suggestions = await this.metaDataService.getExistingCollectionListByType(contentType, teamId);
  //       } else if (defaultKeys[i] && defaultKeys[i].fieldKey == 'Tags') {
  //         // generate tag lists
  //         defaultKeys[i].suggestions = allTags;
  //         defaultKeys[i].values = recentTags;
  //       }
  //       let fieldKeyName = defaultKeys[i].fieldKey;
  //       if (fieldKeyName) defaultKeyStrings.push(fieldKeyName);
  //     }
  //   }
  //   // ------------------------------

  //   // append keys
  //   let inputFormFields = [...defaultKeys];

  //   // get and append custom metaData field keys -------------------

  //   let recentValidAndDefaultFieldStrings: string[] = [...defaultKeyStrings];
  //   if (systemData) {
  //     // get recently added fields list from system data
  //     let recentMetaDataFields = systemData.recentMetaDataKeys || [];
  //     // filter out default fields
  //     let recentValidFields = recentMetaDataFields.filter(field => !defaultKeyStrings.includes(field));
  //     recentValidAndDefaultFieldStrings = [...recentValidAndDefaultFieldStrings, ...recentValidFields];

  //     // format keys to inputFormFields format
  //     let formattedRecentFileds = await this.createInputFormMetaDataFields(recentValidFields);

  //     // append keys
  //     inputFormFields = [...inputFormFields, ...formattedRecentFileds];
  //   }

  //   // get added custom fields from queryBuilder ?????????????? --------------  add if adding all the fields is required

  //   // ---------------------------

  //   let responseObj = {
  //     isFormValuesEditable: true,
  //     fields: inputFormFields,
  //   };

  //   return responseObj;
  // }

  /**
   * Sub Method - to generate metaData input form fields from fields array
   * @param fieldsList : string[] - array of field names
   * @returns fields : MetaDataKeyInputListFormat[] - formatted to input form
   */
  async createInputFormMetaDataFields(fieldsList: string[]) {
    let formattedFields = fieldsList.map(key => {
      let fieldObj: MetaDataKeyInputListFormat = {
        fieldName: key,
        fieldKey: key,
        type: MetaDataKeyInputType.TEXT_LINE,
        isDeletable: true,
        isEditable: true,
      };
      return fieldObj;
    });
    return formattedFields;
  }

  /**
   * Sub Method - to generate metaData input form fields from existing fields array
   * @param fieldsList : {key: string, value: any}[] - array of fields
   * @returns fields : MetaDataKeyInputListFormat[] - formatted to input form
   */
  async createInputFormMetaDataFieldsFromExisting(
    fieldsList: {key: string; value: any; suggestions?: any}[],
    isEditable?: boolean,
    isDeletable?: boolean,
    metaFieldList?: metaFieldInput[],
  ) {
    let checkedField: boolean[] = Array(metaFieldList ? metaFieldList.length : 0).fill(false);

    let formattedFields = fieldsList.map(async field => {
      let uniqueName = field.key
        .toLowerCase()
        .trim()
        .replace(/[^0-9A-Z]+/gi, '');
      let isSelected = false;
      let isMandatory = false;

      let metaFieldObj = await this.metaFieldRepository.findOne({where: {uniqueName: uniqueName}});

      // check if field is selected and mandatory from metaFieldList
      if (metaFieldList && metaFieldList.length > 0 && metaFieldObj) {
        // let metaField = metaFieldList.find(field => field.metaFieldId.toString() == metaFieldObj!._id)
        // if (metaField) {
        //   isSelected = true
        //   isMandatory = metaField.isMandatory
        // }
        for (let i = 0; i < metaFieldList.length; i++) {
          if (metaFieldList[i].metaFieldId.toString() == metaFieldObj!._id) {
            checkedField[i] = true;
            isSelected = true;
            isMandatory = metaFieldList[i].isMandatory;
          }
        }
      }

      let fieldObj: MetaDataKeyInputListFormat = {
        fieldId: metaFieldObj ? metaFieldObj._id : undefined,
        fieldName: field.key,
        fieldType: metaFieldObj ? metaFieldObj.fieldType : undefined,
        options: metaFieldObj ? metaFieldObj.options : [],
        fieldKey: field.key,
        values: field.value,
        suggestions: field.suggestions,
        type: MetaDataKeyInputType.TEXT_LINE,
        isSelected: isSelected,
        isMandatory: isMandatory,
        isEditable: isEditable ? true : false,
        isDeletable: isDeletable ? true : false,
      };

      return fieldObj;
    });

    let promiseFormattedFields = await Promise.all(formattedFields);

    // when fieldConfiguration field is not present in customMeta that field append to the end of the list with isEditable and isDeletable true and empty values
    let indicateFieldIndex = checkedField.flatMap((bool, index) => (!bool ? [index] : []));

    for (const index of indicateFieldIndex) {
      if (metaFieldList && metaFieldList[index]) {
        let systemField = await this.metaFieldRepository.findById(metaFieldList[index].metaFieldId.toString());

        let fieldObj: MetaDataKeyInputListFormat = {
          fieldId: metaFieldList[index].metaFieldId.toString(),
          fieldName: systemField ? systemField.fieldName : '',
          fieldType: systemField ? systemField.fieldType : undefined,
          options: systemField ? systemField.options : [],
          fieldKey: systemField ? systemField.fieldName : '',
          values: '',
          suggestions: [],
          type: MetaDataKeyInputType.TEXT_LINE,
          isSelected: true,
          isMandatory: metaFieldList[index].isMandatory,
          isEditable: true,
          isDeletable: true,
        };

        promiseFormattedFields.push(fieldObj);
      }
    }

    return promiseFormattedFields;
  }
}
