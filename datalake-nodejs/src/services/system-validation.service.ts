/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * SystemValidationService is the class use for validate the API key and secret
 */

/**
 * @class SystemValidationService
 * purpose of SystemValidationService service to validate the API key and secret
 * @description SystemValidationService is the class use for validate the API key and secret
 * <AUTHOR>
 */

import {bind, BindingKey, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ApiKeyRepository, MetaDataRepository, MetaDataUpdateRepository} from '../repositories';
import {COMPATIBLE_PYTHON_SDK_VERSIONS} from '../settings/global-field-config';

const className = 'SystemValidationService';

@bind({scope: BindingScope.TRANSIENT})
export class SystemValidationService {

  constructor(
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
    @repository(ApiKeyRepository)
    public apiKeyRepository: ApiKeyRepository,
    @repository(MetaDataUpdateRepository)
    public metaDataUpdateRepository: MetaDataUpdateRepository,
  ) {
  }

  /**
   * Use to valdiate the api key for api calls
   * @param apiKey {string} api key of the request
   * @returns check the validity
   */
  async validateApiKey(apiKey?: string) {
    let key = await this.apiKeyRepository.findOne({where: {key: apiKey}});
    if (!key) {
      throw new HttpErrors.Unauthorized(
        `Error verifying Api Key : 'Api Key' Not Found`,
      );
    }
    return key
  }

  /**
   * check compatibility with python sdk
   * @param sdkVersion sdk version of the request
   * @returns { isCompatible: boolean, message: string}
   */
  async checkCompatibilityWithPythonSdk(sdkVersion: string) {

    let layerNextVersion = COMPATIBLE_PYTHON_SDK_VERSIONS.layerNextVersion;

    let sdkVersionArray = sdkVersion.split('.').map(Number)

    let sdkVersionRange = COMPATIBLE_PYTHON_SDK_VERSIONS.sdkVersionRange
    let sdkVersionRangeFromArray = sdkVersionRange.FROM.split('.').map(Number)
    let sdkVersionRangeToArray = sdkVersionRange.TO.split('.').map(Number)

    if (
      (sdkVersionArray[0] >= sdkVersionRangeFromArray[0] && sdkVersionArray[0] <= sdkVersionRangeToArray[0]) &&
      (sdkVersionArray[1] >= sdkVersionRangeFromArray[1] && sdkVersionArray[1] <= sdkVersionRangeToArray[1]) //&&
      // (sdkVersionArray[2] >= sdkVersionRangeFromArray[2] && sdkVersionArray[2] <= sdkVersionRangeToArray[2])
    ) {
      return {
        isCompatible: true,
        message: 'Compatible'
      }
    } else {

      let compatibleVersionMessage: string = (sdkVersionRange.FROM == sdkVersionRange.TO) ?
        `Compatible SDK version is ${sdkVersionRange.FROM}` :
        `Compatible SDK versions are from ${sdkVersionRange.FROM} to ${sdkVersionRange.TO}`

      return {
        isCompatible: false,
        message: `LayerNext version ${layerNextVersion} is incompatible with the SDK version ${sdkVersion}. ${compatibleVersionMessage}`
      }
    }

  }




}
export const SYSTEM_VALIDATION_SERVICE = BindingKey.create<SystemValidationService>(
  'service.systemValidationService',
);
