/*
 * Copyright (c) 2024 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle common selection for datalake objects
 */

/**
 * @class DatalakeSelectionService
 * @description Handle common selection for datalake objects
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, DatalakeSelectionRequest, MetaData, OptionListType} from '../models';
import {DatalakeSelectionRepository} from '../repositories';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {EMBEDDING_COLLECTION, FLOWS, SIMILARITY_SCORE_THRESHOLD, UserType} from '../settings/constants';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {STUDIO_INTERFACE_SERVICE, StudioInterfaceService} from './studio-interface.service';

@injectable({scope: BindingScope.TRANSIENT})
export class DatalakeSelectionService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @inject(STUDIO_INTERFACE_SERVICE) private studioInterfaceService: StudioInterfaceService,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
  ) {}

  /**
   * Use to tag a selection for project creation from datalake
   */
  async createProjectSelectionTag(selection: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let teamId = currentUserProfile.teamId;
    if (selection.referenceImage) {
      (selection.modelName = EMBEDDING_COLLECTION), (selection.scoreThreshold = SIMILARITY_SCORE_THRESHOLD);
    }
    let objectType: ContentType | undefined = undefined;

    let contentType = selection.contentType;

    objectType = await this.metaDataRepository.getObjectType(
      selection.isAllSelected ? selection.isAllSelected : false,
      contentType,
      selection.collectionId,
      selection.objectIdList,
    );

    let selectionTag = await this.datalakeSelectionRepository.create({
      teamId: teamId,
      selectionRequest: selection,
      createdAt: new Date(),
      objectType: objectType,
    });

    let warningMsg: string | undefined = undefined;
    let totalCount = 0;
    if (selectionTag.id) {
      let countObj: [{count: number}] = await this.studioInterfaceService.getDataListWithChildFilesCount(
        selectionTag.id,
        currentUserProfile,
      );

      if (countObj && Array.isArray(countObj) && countObj.length > 0) {
        totalCount = countObj[0].count;
      }
    }
    logger.info(
      `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionService.createProjectSelectionTag | ${teamId} | selectionTag.id: ${selectionTag.id} | totalCount: ${totalCount}`,
    );
    if (totalCount == 0) {
      return {
        selectionTag: selectionTag.id,
        warningMsg: 'Could not find any object',
        isSuccess: false,
      };
    } else {
      return {
        selectionTag: selectionTag.id,
        warningMsg: warningMsg,
        isSuccess: true,
      };
    }
  }

  /**
   * Get human annotation object count of dataset
   * @param selectionId {string} selection id
   * @returns count of the human annotated objects
   */
  async isAnyHumanAnnotatedExists(selectionId: string) {
    logger.debug('start count');

    let isExists = await this.metaDataRepository.findOne({
      where: {'verificationStatusCount.verified': 1, selectionIdList: selectionId},
    });
    logger.debug('end count');
    if (isExists) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * to get the option list of selections
   * @param selection DatalakeSelectionRequest object
   * @param teamId logged in user team id
   * @returns option list of selections
   */
  async getOptionListOfSelections(selection: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    // initialize the variables
    let optionsResponse: OptionListType[] = [];
    let objectIdList: Partial<MetaData>[] = [];
    let contentType = selection.contentType;
    let teamId = currentUserProfile.teamId;
    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionService.getOptionListOfSelections | N/A | Invalid teamId, teamId= ${teamId}`,
      );
      return optionsResponse;
    }

    if (
      currentUserProfile &&
      currentUserProfile.userType &&
      [UserType.USER_TYPE_AUDITOR, UserType.USER_TYPE_COLLABORATOR, UserType.USER_TYPE_TEAM_ADMIN, UserType.USER_TYPE_SUPER_ADMIN].includes(
        currentUserProfile.userType,
      ) &&
      contentType &&
      [
        ContentType.VIDEO_COLLECTION,
        ContentType.IMAGE_COLLECTION,
        ContentType.OTHER_COLLECTION,
        ContentType.DATASET,
      ].includes(contentType)
    ) {
      optionsResponse.push(OptionListType.SHARE);
    }

    let collection: Partial<MetaData> | undefined = selection.collectionId
      ? await this.metaDataRepository.findById(selection.collectionId)
      : undefined;
    let removeEnabled: boolean = this.searchQueryBuilderService.checkIfObjectRemoveEnabled(
      selection.contentType,
      collection,
    );
    if (removeEnabled) optionsResponse.push(OptionListType.REMOVE);
    let objectType = await this.metaDataRepository.getObjectType(true, contentType, selection.collectionId);

    // if collection id is present then check if it is logical collection, if yes then return remove option
    if (selection.collectionId) {
      let collectionObj = await this.metaDataRepository.findById(selection.collectionId);

      if (collectionObj.objectType === ContentType.DATASET) return optionsResponse;
    }

    // if object type is dataset then return empty option list
    if (objectType == ContentType.DATASET) {
      return optionsResponse;
    } else if (
      [ContentType.VIDEO, ContentType.VIDEO_COLLECTION, ContentType.OTHER, ContentType.OTHER_COLLECTION].includes(
        objectType,
      )
    ) {
      // if object type is video, video collection, other or other collection then return Trash option
      optionsResponse.push(OptionListType.TRASH);
      return optionsResponse;
    } else {
      // get selection tag object
      let selectionObj = await this.createProjectSelectionTag(selection, currentUserProfile);

      if (!selectionObj.isSuccess) {
        logger.error(
          `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionService.getOptionListOfSelections | N/A | Couldn't get option list of selections, selection= ${selection}`,
        );
        return optionsResponse;
      }

      // if selection tag is present then get the object id list
      if (selectionObj.selectionTag) {
        let matchQueryObj = await this.searchQueryBuilderService.getMatchQueryForSelection(
          currentUserProfile,
          selectionObj.selectionTag,
        );

        if (!matchQueryObj) {
          logger.error(
            `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionService.getOptionListOfSelections | N/A | Couldn't get option list of selections, selection= ${selection}`,
          );
          return optionsResponse;
        }

        // if annotationProjectList,curationProjectList,datasetVersionList is empty then trash option will be shown
        let metaObj = await this.metaDataRepository.aggregate([
          {
            $match: matchQueryObj.matchQuery,
          },
          {
            $match: {
              'annotationProjectList.0': {$exists: false},
              'curationProjectList.0': {$exists: false},
              'datasetVersionList.0': {$exists: false},
            },
          },
          {
            $limit: 1,
          },
        ]);

        if (Array.isArray(metaObj) && metaObj.length > 0) optionsResponse.push(OptionListType.TRASH);
      }
    }

    return optionsResponse;
  }
}

export const DATALAKE_SELECTION_SERVICE = BindingKey.create<DatalakeSelectionService>('service.datalakeSelection');
