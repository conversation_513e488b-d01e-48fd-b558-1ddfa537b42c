/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */

/**
 * @class AzureBlobStorageService
 *
 * @description Class which extends Storage Provider Service that handles specific functions for Azure blob storage
 */
import {BindingScope, injectable} from '@loopback/core';
import dotenv from 'dotenv';
import {StorageOperationHandlerService} from './storage-operation-handler.service';
import {FileListParams, FileListResponse, StorageFileHeader, StorageProviderService} from './storage-provider.service';

//Import BlobItem
import {BlobSASPermissions, BlobServiceClient, BlockBlobClient, StorageSharedKeyCredential} from '@azure/storage-blob';
import {HttpErrors} from '@loopback/rest';
import {logger} from '../config';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {repository} from '@loopback/repository';
import {MetaDataRepository} from '../repositories';
import {UserProfileDetailed} from '../authServices/custom-token.service';

dotenv.config();

const AZURE_FILE_URL_EXPIRE_DURATION = 3 * 24 * 60 * 60;

@injectable({scope: BindingScope.TRANSIENT})
export class AzureBlobStorageService extends StorageProviderService {
  azureStorage: BlobServiceClient;

  //constructor
  constructor(
    storageOperationHandlerService: StorageOperationHandlerService,
    @repository('MetaDataRepository') public metaDataRepository: MetaDataRepository,
  ) {
    super(metaDataRepository);
    const account = process.env.AZURE_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
    if (!account || !accountKey) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.STORAGE_ACCOUNT_NOT_DEFINED);

    const sharedKeyCredential = new StorageSharedKeyCredential(account, accountKey);
    const blobServiceClient = new BlobServiceClient(`https://${account}.blob.core.windows.net`, sharedKeyCredential);

    this.azureStorage = blobServiceClient;
    this.storageOperationHandlerService = storageOperationHandlerService;
  }

  async generateWriteFileUrl(key: string, expires = 60, bucket?: string) {
    const containerName = bucket || process.env.DEFAULT_BUCKET_NAME;
    if (!containerName) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BUCKET_NOT_DEFINED);

    const containerClient = this.azureStorage.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(key);

    const url = await blockBlobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse('w'),
      expiresOn: new Date(Date.now() + expires * 1000),
    });
    return url;
  }

  async uploadFileFromBuffer(buffer: Buffer, blobName: string, bucket: string) {
    try {
      const containerName = bucket || process.env.DEFAULT_BUCKET_NAME;
      if (!containerName) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BUCKET_NOT_DEFINED);

      const containerClient = this.azureStorage.getContainerClient(containerName);

      const blockBlobClient = containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.uploadData(buffer);
    } catch (e) {
      logger.error('Failed to upload file buffer to Azure Blob storage', e);
    }
  }

  async generateObjectUrl(key: string, expires = AZURE_FILE_URL_EXPIRE_DURATION, bucket?: string) {
    const containerName = bucket || process.env.DEFAULT_BUCKET_NAME;
    if (!containerName) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BUCKET_NOT_DEFINED);

    const containerClient = this.azureStorage.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(key);

    const url = await blockBlobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse('r'),
      expiresOn: new Date(Date.now() + expires * 1000),
    });
    return url;
  }

  async generateMultipartPreSignedUrls(key: string, fileId: number, parts: number, bucket?: string) {
    const containerName = bucket || process.env.DEFAULT_BUCKET_NAME;
    if (!containerName) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BUCKET_NOT_DEFINED);

    const containerClient = this.azureStorage.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(key);

    //Generate SAS URLs for each part
    const partUrls = [];
    const partUrl = await blockBlobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse('rwc'),
      expiresOn: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
    });
    for (let i = 0; i < parts; i++) {
      partUrls.push({
        PartNumber: i + 1,
        signedUrl: partUrl,
      });
    }
    return {parts: partUrls};
  }

  async initializeMultipartUpload(key: string, expires = 60, bucket?: string) {
    return {fileId: key, fileKey: key, isExisting: false};
  }

  async finalizeMultipartUpload(
    key: string,
    fileId: string,
    parts: any,
    bucket?: string | undefined,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    currentUserProfile?: UserProfileDetailed,
  ): Promise<{isSuccess: boolean}> {
    if (!finalizeUrl) return {isSuccess: false};

    const blockBlobClient = new BlockBlobClient(finalizeUrl);

    try {
      const listBlocksResponse = await blockBlobClient.getBlockList('uncommitted');
      console.log(listBlocksResponse.uncommittedBlocks);
      const blockIds = listBlocksResponse.uncommittedBlocks
        ? listBlocksResponse.uncommittedBlocks.map((block: {name: any}) => block.name)
        : [];
      await blockBlobClient.commitBlockList(blockIds);
      logger.info(
        `AzureBlobStorageService.finalizeMultipartUpload | file upload | Successfully finalized multipart upload to Azure Blob storage for finalizeUrl: ${finalizeUrl}`,
      );
    } catch (e) {
      logger.error(
        `AzureBlobStorageService.finalizeMultipartUpload | file upload | Failed to finalize multipart upload to Azure Blob storage for finalizeUrl: ${finalizeUrl}, error: `,
        e,
      );
      return {isSuccess: false};
    }

    return {isSuccess: true};
  }

  //delete file from azure blob
  async deleteObject(key: string, bucket?: string) {
    const containerName = bucket || process.env.DEFAULT_BUCKET_NAME;
    if (!containerName) return {isSuccess: false, message: 'Azure container name is not defined'};

    const containerClient = this.azureStorage.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(key);
    try {
      // Delete blob
      await blockBlobClient.delete();
      return {isSuccess: true, message: ''};
    } catch (error) {
      return {isSuccess: false, message: error.message};
    }
  }

  /**
   * Get next list of files from Azure blob
   * @param params
   */
  async getFileList(params: FileListParams): Promise<FileListResponse> {
    const containerName = params.Bucket || process.env.DEFAULT_BUCKET_NAME;
    if (!containerName) throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BUCKET_NOT_DEFINED);

    const containerClient = this.azureStorage.getContainerClient(containerName);

    const pageSettings: PageSettings = {
      continuationToken: params.ContinuationToken,
      maxPageSize: 1000,
    };

    let iterator = containerClient.listBlobsFlat({prefix: params.Prefix}).byPage(pageSettings);
    let response = await iterator.next();

    logger.info(
      `AzureBlobStorageService | AzureBlobStorageService.getFileList | N/A | ${response.value.segment.blobItems.length} file crawled`,
    );

    let fileList: StorageFileHeader[] = [];

    //iterate over blob list of response
    const blobList = response.value.segment.blobItems;
    blobList.forEach((blob: any) => {
      fileList.push({
        fileKey: blob.name,
        fileName: blob.name.split('/').pop(),
        fileLastModified: blob.properties.lastModified,
        fileSize: blob.properties.contentLength || 0,
      });
    });

    return {
      isSuccess: true,
      errMessage: '',
      fileList: fileList,
      nextPageRef: {
        NextContinuationToken: response.value.continuationToken,
        ContinuationToken: params.ContinuationToken,
      },
      IsTruncated: response?.value?.continuationToken ? true : false,
    };
  }
}

interface PageSettings {
  continuationToken?: string;
  maxPageSize?: number;
}
