/*
 * copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle the request related to the quickbooks api
 */
/**
 * @class QuickbooksApiService
 * @description This QuickbooksApiService use for Handle the request related to the quickbooks api
 * <AUTHOR>
 */

import {BindingScope, injectable} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {logger} from '../config';
import OAuthClient = require('intuit-oauth');
const QuickBooksClient = require('node-quickbooks');

@injectable({scope: BindingScope.TRANSIENT})
export class QuickBooksApiService {
  private clientId: string;
  private clientSecret: string;
  private qb_oauthClient: OAuthClient;
  private qb_client: typeof QuickBooksClient;
  /* This class facilitates the authorization and calling of QB APIs. */
  constructor() {
    this.clientId = process.env.QB_CLIENT_ID || '';
    this.clientSecret = process.env.QB_CLIENT_SECRET || '';
    this.setupOAuth();
  }

  async setupOAuth() {
    // Load the keys from .env
    const redirectUri = process.env.QB_REDIRECT_URI || '';
    const environment = process.env.QB_ENVIRONMENT || '';
    if (!this.clientId || !this.clientSecret || !redirectUri || !environment) {
      throw new HttpErrors.Unauthorized('Quickbooks credentials not found');
    }
    this.qb_oauthClient = new OAuthClient({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      environment,
      redirectUri,
    });
  }
  async connectQuickBooks() {
    try {
      const scope = process.env.QB_SCOPES || OAuthClient.scopes.Accounting;
      const authUri = this.qb_oauthClient.authorizeUri({
        scope: [scope],
        state: 'csrf-' + Math.random().toString(36).slice(2), // store/verify in session in real apps
      });
      logger.info(`QuickBooksApiService | connectQuickBooks success | authUri: ${authUri}`);
      return authUri;
    } catch (error) {
      logger.error(`QuickBooksApiService | connectQuickBooks failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks connection failed');
    }
  }

  async onOAuthRedirect(code: string) {
    try {
      const tokenResponse = await this.qb_oauthClient.createToken(code);
      const accessToken = tokenResponse.getAccessToken();
      const refreshToken = tokenResponse.getRefreshToken();
      const realmId = tokenResponse.getRealmId();
      this.qb_client = new QuickBooksClient(
        this.clientId,
        this.clientSecret,
        accessToken,
        false,
        realmId,
        true,
        true,
        null,
        '2.0',
        refreshToken,
      );
      logger.info(`QuickBooksApiService | QuickBooks client created | accessToken: ${accessToken}`);
      //Test to see if the connection is working
      const accounts = await this.qb_client.findAccounts();
      console.log(accounts);
    } catch (error) {
      logger.error(`QuickBooksApiService | onOAuthRedirect failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks token creation failed');
    }
  }
}
