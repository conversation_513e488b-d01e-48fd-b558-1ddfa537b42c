/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perform the system meta tags - fields related logics
 */

/**
 * @class SystemMetaService
 * purpose of this service is to manage system meta tags - fields
 * @description Perform the system meta related logics
 * <AUTHOR>
 */

import {bind, BindingKey, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, SelectionOptionsObject} from '../models';
import {MetaDataRepository, QueryOptionRepository, SystemDataRepository} from '../repositories';
import {MetaTagRepository} from '../repositories/meta-tag.repository';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@bind({scope: BindingScope.TRANSIENT})
export class SystemMetaService {
  constructor(
    @repository(MetaTagRepository)
    private metaTagRepository: MetaTagRepository,
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository('SystemDataRepository')
    private systemDataRepository: SystemDataRepository,
    @repository(QueryOptionRepository)
    public queryOptionRepository: QueryOptionRepository,
  ) {}

  /**
   * Use to get Meta tag list
   * @param metaTagFilter {object} tag filter
   * @returns {success: boolean}
   */
  async getMetaTagList(metaTagFilter: {searchKey: string; pageIndex: number; pageSize: number}) {
    logger.info(
      `SystemMetaService | SystemMetaService.getMetaTagList | N/A | get meta tag list search filter: ${metaTagFilter}`,
    );

    //match query build
    let matchQuery: any = {};
    if (metaTagFilter.searchKey) {
      matchQuery['tag'] = {$regex: metaTagFilter.searchKey, $options: 'i'};
    }

    //aggregate query build
    let params = [
      {$match: matchQuery},
      {$sort: {lastModifiedAt: -1}},
      {$skip: metaTagFilter.pageIndex * metaTagFilter.pageSize},
      {$limit: metaTagFilter.pageSize},
    ];

    let metaTagList = await this.metaTagRepository.aggregate(params);

    return metaTagList;
  }

  /**
   * Use to get Meta tag count
   * @param metaTagFilter {object} tag filter
   * @returns {success: boolean}
   */
  async getMetaTagCount(metaTagFilter: {searchKey: string}) {
    logger.info(
      `SystemMetaService | SystemMetaService.getMetaTagList | N/A | get meta tag list search filter: ${metaTagFilter}`,
    );

    //match query build
    let matchQuery: any = {};
    if (metaTagFilter.searchKey) {
      matchQuery['tag'] = {$regex: metaTagFilter.searchKey, $options: 'i'};
    }

    //aggregate query build
    let params = [{$match: matchQuery}, {$count: 'count'}];

    let metaTagList: [{count: number}] = await this.metaTagRepository.aggregate(params);
    let count = 0;
    if (metaTagList && Array.isArray(metaTagList) && metaTagList.length > 0) {
      count = metaTagList[0].count;
    }

    return {count: count};
  }

  /**
   * Use to create Meta tag
   * @param tagName {string} new tag name
   * @param currentUserProfile {UserProfileDetailed} executing user details
   * @returns {success: boolean}
   */
  async createMetaTag(tagName: string, currentUserProfile: UserProfileDetailed) {
    logger.info(
      `SystemMetaService | SystemMetaService.createMetaTag | ${currentUserProfile.teamId} | start create meta tag name: ${tagName}`,
    );

    if (!currentUserProfile.teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    // validate tag name
    let trimmedTagName = tagName.trim();
    let uniqueTagName = await this.validateTagName(tagName, currentUserProfile.teamId);

    try {
      //create new tag
      await this.metaTagRepository.create({
        tag: trimmedTagName,
        uniqueTagName: uniqueTagName,
        teamId: currentUserProfile.teamId,
        lastModifiedAt: new Date(),
        createdAt: new Date(),
        modifiedBy: currentUserProfile.name,
      });

      logger.info(
        `SystemMetaService | SystemMetaService.createMetaTag | ${currentUserProfile.teamId} | create success: ${tagName}`,
      );
      return {success: true};
    } catch (error) {
      logger.error(
        `SystemMetaService | SystemMetaService.createMetaTag | ${currentUserProfile.teamId} | create failed: ${tagName}`,
      );
      return {success: false};
    }
  }

  /**
   * use to validate tag name and return unique tag name
   * @param tagName tag name
   * @param teamId team id
   * @returns unique tag name
   */
  async validateTagName(tagName: string, teamId: string, tagId?: string) {
    let uniqueTagName = await this.getUniqueName(tagName);

    let aggregateQuery: any = [{$match: {uniqueTagName: uniqueTagName, teamId: new ObjectId(teamId.toString())}}];

    if (tagId) {
      aggregateQuery.push({$match: {_id: {$ne: new ObjectId(tagId)}}});
    }

    aggregateQuery.push({$limit: 1});

    //check tag name already exists before change name
    let existingTag = await this.metaTagRepository.aggregate(aggregateQuery);

    //if exists throw error
    if (existingTag && Array.isArray(existingTag) && existingTag.length > 0) {
      logger.error(`SystemMetaService | SystemMetaService.validateTagName | tag name already exists: ${tagName}`);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.TAG_ALREADY_EXISTS);
    }

    return uniqueTagName;
  }

  /**
   * Use to edit Meta tag
   * @param tagId {string} id of the ta
   * @param tagName {string} new tag name
   * @param currentUserProfile {UserProfileDetailed} executing user details
   * @returns {success: boolean}
   */
  async editMetaTag(tagId: string, tagName: string, currentUserProfile: UserProfileDetailed) {
    logger.info(
      `SystemMetaService | SystemMetaService.editMetaTag | user: ${currentUserProfile} | edit meta tag name: ${tagName} of tagId: ${tagId}`,
    );
    if (!currentUserProfile.teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let uniqueTagName = await this.validateTagName(tagName, currentUserProfile.teamId, tagId);

    try {
      //find tag before edit
      let tagDetails = await this.metaTagRepository.findById(tagId);

      //update new tag name
      await this.metaTagRepository.updateById(tagId, {
        tag: tagName,
        uniqueTagName: uniqueTagName,
        lastModifiedAt: new Date(),
        modifiedBy: currentUserProfile.name,
      });

      //if name doesn't change if same name and return
      if (tagName == tagDetails.tag) {
        return {success: true};
      }

      //insert new Tag to Tags list in metaData
      await this.metaDataRepository.updateManyPushToList(
        {
          Tags: tagDetails.tag,
        },
        {
          Tags: tagName,
        },
        [],
      );

      //insert new Tag to collectionHeadOnlyMeta.Tags list in metaData
      // await this.metaDataRepository.updateManyPushToList(
      //   {
      //     'collectionHeadOnlyMeta.Tags': tagDetails.tag,
      //   },
      //   {
      //     'collectionHeadOnlyMeta.Tags': tagName,
      //   },
      //   [],
      // );

      //pull old tag from both Tags and list in metaDat
      await this.metaDataRepository.updateManyRemoveFromList(
        {
          Tags: tagDetails.tag,
        },
        {
          Tags: tagDetails.tag,
          //'collectionHeadOnlyMeta.Tags': tagDetails.tag,
        },
        [],
      );

      //update query option with tag name
      await this.queryOptionRepository.updateQueryKeyValue(
        'metadata.Tags',
        tagName,
        tagDetails.tag,
        currentUserProfile.teamId,
      );

      logger.info(
        `SystemMetaService | SystemMetaService.editMetaTag | user: ${currentUserProfile} | edit success: ${tagDetails.tag}`,
      );
      return {success: true};
    } catch (error) {
      logger.error(
        `SystemMetaService | SystemMetaService.editMetaTag | user: ${currentUserProfile} | edit meta tag of tagId: ${tagId}`,
      );
      return {success: false};
    }
  }

  /**
   * Use to delete Meta tag
   * @param tagId {string} id of the ta
   * @param currentUserProfile {UserProfileDetailed} executing user details
   * @returns {success: boolean}
   */
  async deleteMetaTag(tagId: string, currentUserProfile: UserProfileDetailed) {
    logger.info(
      `SystemMetaService | SystemMetaService.deleteMetaTag | user: ${currentUserProfile} | delete meta tag of tagId: ${tagId}`,
    );

    if (!currentUserProfile.teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    try {
      //find tag before remove
      let tagDetails = await this.metaTagRepository.findById(tagId);

      //pull tag from both Tags list in metaData
      await this.metaDataRepository.updateManyRemoveFromList(
        {
          Tags: tagDetails.tag,
        },
        {
          Tags: tagDetails.tag,
          //'collectionHeadOnlyMeta.Tags': tagDetails.tag,
        },
        [],
      );

      //delete tag
      await this.metaTagRepository.deleteById(tagId);

      //delete query option tag
      await this.queryOptionRepository.deleteQueryOption('metadata.Tags', tagDetails.tag, currentUserProfile.teamId);

      logger.info(
        `SystemMetaService | SystemMetaService.deleteMetaTag | user: ${currentUserProfile} | remove success: ${tagDetails.tag}`,
      );
      return {success: true};
    } catch (error) {
      logger.error(
        `SystemMetaService | SystemMetaService.deleteMetaTag | user: ${currentUserProfile} | delete meta tag of tagId: ${tagId}`,
      );
      return {success: false};
    }
  }

  /**
   * Use to create unique name
   * @param name {string} name to create unique name
   * @returns unique name
   */
  async getUniqueName(name: string) {
    let uniqueName = name
      .toLowerCase()
      .trim()
      .replace(/[^0-9A-Z]+/gi, '');
    return uniqueName;
  }

  /**
   * Use to validate tag name and create tag if not exists
   * @param tagName tag name to validate
   * @param currentUserProfile current user profile
   */
  async validateAndCreateTag(tagName: string, currentUserProfile: UserProfileDetailed, isReturnList?: boolean) {
    //check tag name already exists before change name
    let uniqueTagName = await this.getUniqueName(tagName);
    let existingTag = await this.metaTagRepository.findOne({where: {uniqueTagName: uniqueTagName}});
    if (!existingTag) {
      await this.createMetaTag(tagName, currentUserProfile);
    } else {
      let sameTag = await this.metaTagRepository.findOne({where: {tag: tagName}});
      if (!sameTag) {
        logger.error(
          `Tag Validation | SystemMetaService.validateAndCreateTag | ${currentUserProfile.teamId} | tag name already exists: ${existingTag.tag}`,
        );
        if (isReturnList) {
          return {
            newTag: tagName,
            oldTag: existingTag.tag,
          };
        } else {
          throw new HttpErrors.NotAcceptable(`Tag Already Exists: ${existingTag.tag}.
          Please use the existing ${existingTag.tag} tag instead of ${tagName}.`);
        }
      }
    }
  }

  async updateManySet(params: any, data: any, arrayFilter: any) {
    return await this.metaTagRepository.updateManySet(params, data, arrayFilter);
  }

  /**
   * Use to get system metadata options list for given content type
   * @param teamId {string}
   * @param contentType contentType
   * @returns list of metadata options
   */
  async getSystemMetadataOptionsList(teamId: string, contentType: ContentType) {
    let metadataOptionsList: SelectionOptionsObject[] = [];

    let query = teamId ? {where: {teamId: teamId}} : {};
    let statsObject = await this.systemDataRepository.findOne(query); //NOTE: add teamId filter

    switch (contentType) {
      case ContentType.IMAGE:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.images) {
            for (let metadataObj of statsObject.metaDataFieldSummary.images) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.VIDEO:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.videos) {
            for (let metadataObj of statsObject.metaDataFieldSummary.videos) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.OTHER:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.other) {
            for (let metadataObj of statsObject.metaDataFieldSummary.other) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.IMAGE_COLLECTION:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.imageCollections) {
            for (let metadataObj of statsObject.metaDataFieldSummary.imageCollections) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.VIDEO_COLLECTION:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.videoCollections) {
            for (let metadataObj of statsObject.metaDataFieldSummary.videoCollections) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.OTHER_COLLECTION:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.otherCollections) {
            for (let metadataObj of statsObject.metaDataFieldSummary.otherCollections) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      case ContentType.DATASET:
        if (statsObject) {
          if (statsObject.metaDataFieldSummary?.datasets) {
            for (let metadataObj of statsObject.metaDataFieldSummary.datasets) {
              if (metadataObj._id != 'Tags') {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj._id,
                  selected: false,
                  filterOptions: metadataObj.values.map(valueObj => {
                    return {name: valueObj.value, selected: false};
                  }),
                };
                metadataOptionsList.push(metadataObject);
              } else {
                let metadataObject: SelectionOptionsObject = {
                  name: metadataObj.values[0].value,
                  selected: false,
                };
                metadataOptionsList.push(metadataObject);
              }
            }
          }
        }
        break;
      default:
        break;
    }

    return metadataOptionsList;
  }

  /**
   * use to call getBusinessOverview function in systemDataRepository
   */
  async getBusinessOverview() {
    return await this.systemDataRepository.getBusinessOverview();
  }

  /**
   * use to call getDataSourcesOverview function in systemDataRepository
   */
  async getDataSourcesOverview() {
    return await this.systemDataRepository.getDataSourcesOverview();
  }

  /**
   * use to call getDataDictionaryMapping function in systemDataRepository
   */
  async getDataDictionaryMapping() {
    return await this.systemDataRepository.getDataDictionaryMapping();
  }

  /**
   * use to call getLatestModifiedDate function in systemDataRepository
   */
  async getDataSourcesAndBusinessOverviewsLatestModified() {
    return await this.systemDataRepository.getLatestModifiedDate();
  }

  /**
   * use to call getDataDictMappingLatestModifiedDate function in systemDataRepository
   */
  async getDataDictionaryMappingLatestModified() {
    return await this.systemDataRepository.getDataDictMappingLatestModifiedDate();
  }

  /**
   * use to call getMetaDataLastUpdatedAt function in systemDataRepository
   */
  async getMetaDataLastUpdatedAt() {
    logger.info(
      `SystemMetaService | SystemMetaService.getMetaDataLastUpdatedAt | N/A | Getting meta data last updated at from system data`,
    );
    const systemData = await this.systemDataRepository.findOne();
    if (!systemData) {
      return new Date(0);
    }

    if (!systemData.metaDataLastUpdatedAt) {
      return new Date(0);
    }

    return systemData.metaDataLastUpdatedAt;
  }

  /**
   * Calls the getSuggestedQuestions function in the SystemDataRepository.
   */
  async getSuggestedQuestions() {
    return await this.systemDataRepository.getSuggestedQuestions();
  }
}
export const SYSTEM_META_SERVICE = BindingKey.create<SystemMetaService>('service.systemMetaService');
