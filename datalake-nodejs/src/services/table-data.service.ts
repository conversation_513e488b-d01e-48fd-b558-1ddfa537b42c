import {BindingKey, /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import Axios from 'axios';
import {ObjectId} from 'bson';
import _ from 'lodash';
import {logger} from '../config';
import {DataSourceMetaDataResponse, TableData, TableDataRelations, TableDataResponse, TableField} from '../models';
import {DataSourceConnectionType, DataStructureCrawlStatus} from '../models/connection.model';
import {ConnectionSourceType} from '../models/source.model';
import {DataModellingCacheRepository, SystemDataRepository, TableDataRepository} from '../repositories';
import {ConnectionRepository} from '../repositories/connection.repository';
import {FLOWS} from '../settings/constants';

const PYTHON_HOST = process.env.PYTHON_BASE_URL;
const objectiveBasedFilteringRequired =
  process.env.OBJECTIVE_BASED_FILTERING_REQUIRED?.toLowerCase() === 'true' || false;
@injectable({scope: BindingScope.TRANSIENT})
export class TableDataService {
  constructor(
    @repository(TableDataRepository)
    private tableDataRepository: TableDataRepository,
    @repository(ConnectionRepository)
    private connectionDataRepository: ConnectionRepository,
    @repository(ConnectionRepository)
    private connectionRepository: ConnectionRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @repository(DataModellingCacheRepository)
    private dataModellingCacheRepository: DataModellingCacheRepository,
  ) {}

  /**
   * Get tables for a data source with pagination and optional search key
   * @param connectionId - The ID of the connection
   * @param pageIndex - The page index for pagination
   * @param pageSize - The number of items per page
   * @param searchKey - Optional search key for filtering table names
   * @returns A promise that resolves to the paginated and formatted table data
   */
  async getTablesForDataSource(
    connectionId: string,
    pageIndex: number,
    pageSize: number,
    searchKey?: string,
  ): Promise<TableDataResponse> {
    const MAX_PAGE_SIZE = 20;
    pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
    const connectionObject = await this.connectionDataRepository.findOneByConnectionId(connectionId);
    if (!connectionObject) {
      throw new Error(`Connection with id ${connectionId} not found`);
    }

    if (
      connectionObject.isDataStructureCrawled &&
      connectionObject?.isDataStructureCrawled === DataStructureCrawlStatus.COMPLETED
    ) {
      return this.formatTableData(connectionId, pageIndex, pageSize, searchKey);
    } else if (
      connectionObject.isDataStructureCrawled &&
      connectionObject?.isDataStructureCrawled === DataStructureCrawlStatus.NOT_INITIATED
    ) {
      // this.triggerDataStructureCrawling(connectionObject); //remove crawling here since we are using separate function to invoke it
      return {isSuccess: false, message: 'Data structure crawling initiated. Please try again later.'};
    } else {
      return {isSuccess: false, message: 'Data structure crawling not completed. Please try again later.'};
    }
  }

  /**
   * Trigger data structure crawling for a connection
   * @param connectionObject - The connection object containing details needed for crawling
   */
  async triggerDataStructureCrawling(connectionObject: any) {
    logger.info(
      `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Triggering data structure crawling for connectionId: ${connectionObject.connectionId}`,
    );
    try {
      const url = `${PYTHON_HOST}/internal/connection/generate/tablestructure`;
      const resultCrawling = await Axios({
        url,
        method: 'POST',
        data: {
          dataSourceId: connectionObject.connectionId,
          type: connectionObject.type,
          credentials: connectionObject.connectionCredentials,
        },
      });

      if (resultCrawling.status === 200 && resultCrawling.data.isSuccess) {
        logger.info(
          `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Data structure crawling finished successfully for connectionId: ${connectionObject.connectionId}`,
        );
      } else {
        logger.error(
          `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Failed the data structure crawling for connectionId: ${connectionObject.connectionId}`,
        );
        return {isSuccess: false, message: 'Failed the data structure crawling'};
      }

      //re-generate data sources overview
      try {
        logger.debug(
          `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Starting to re-generate data sources overview after data structure crawling for connectionId: ${connectionObject.connectionId}`,
        );
        await this.generateLayernextDataDictionary(connectionObject.connectionId, false); // If false does not trigger the layernext data dictionary regeneration
        logger.debug(
          `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Completed re-generating data sources overview after data structure crawling for connectionId: ${connectionObject.connectionId}`,
        );
        return {isSuccess: true, message: 'Data structure crawling triggered successfully'};
      } catch (error) {
        logger.error(
          `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Failed to re-generate data sources overview after data structure crawling for connectionId: ${connectionObject.connectionId} ERROR: ${error}`,
        );
        return {isSuccess: false, message: 'Failed to re-generate data sources overview'};
      }
    } catch (err) {
      logger.error(
        `${FLOWS.DB_CRAWLING} | TableDataService.triggerDataStructureCrawling | N/A | Failed to trigger data structure crawling for connectionId: ${connectionObject.connectionId}: ${err}`,
      );
      return {isSuccess: false, message: 'Failed to trigger data structure crawling'};
    }
  }

  // async triggerTableDescriptionReGeneration(connectionObject:any){

  // }

  /**
   * Format table data with pagination and optional search key
   * @param connectionId - The ID of the connection
   * @param pageIndex - The page index for pagination
   * @param pageSize - The number of items per page
   * @param searchKey - Optional search key for filtering table names
   * @returns A promise that resolves to the paginated and formatted table data
   */
  private async formatTableData(connectionId: string, pageIndex: number, pageSize: number, searchKey?: string) {
    const {data, total} = await this.tableDataRepository.findByConnectionIdWithPagination(
      connectionId,
      pageIndex,
      pageSize,
      searchKey,
    );
    const formatted = data.map(record => {
      const tableName = record.tableName;
      const mappedName = record.mappedName || tableName;
      const createdAt = record.createdAt;
      const modifiedDate = record.modifiedDate;
      const modifiedBy = record.modifiedBy;
      const fieldsCount = record.fieldsCount;
      const isVisible = !!record?.isVisibleToAI;
      const isVerified = record.isVerified;

      return {
        _id: record._id ? record._id.toString() : '',
        tableName,
        mappedName,
        createdAt,
        modifiedBy,
        lastModified: modifiedDate,
        fieldsCount,
        visibility: isVisible,
        verified: isVerified,
      };
    });

    return {
      isSuccess: true,
      data: formatted,
      total,
    };
  }

  async getFields(
    tableId: string,
    pageIndex: number,
    pageSize: number,
    searchKey?: string,
  ): Promise<{data: TableField[]; total: number}> {
    return this.tableDataRepository.findFieldsByTableId(tableId, pageIndex, pageSize, searchKey);
  }

  /**
   * Updates the description of a specific field in a table.
   * @param id {string} - The table ID.
   * @param fieldName {string} - The name of the field.
   * @param description {string} - The new description for the field.
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   */
  async updateFieldDescription(id: string, fieldName: string, description: string) {
    await this.tableDataRepository.updateFieldDescription(id, fieldName, description);
  }

  /**
   * Updates the visibility of a specific field in the table and triggers an external service to sync the change.
   * This method first updates the field's visibility in the database and then asynchronously calls an external service to reflect this change.
   *
   * @param id {string} - The unique identifier of the table data.
   * @param fieldName {string} - The name of the field whose visibility is being updated.
   * @param isVisible {boolean} - The new visibility state of the field.
   * @returns {Promise<{isSuccess: boolean, message: string}>} - A promise that resolves with a success message if the database update is successful.
   * If the field is not found, it returns an error message indicating failure.
   *
   * @throws {Error} - Any errors during the external service call are logged, but do not affect the main operation's success.
   */
  async updateFieldVisibility(id: string, fieldName: string, isVisible: boolean) {
    const result = await this.tableDataRepository.updateFieldVisibility(id, fieldName, isVisible);

    if (!result.isSuccess) {
      return {isSuccess: false, message: result.message};
    }
    const response = {message: `isVisible in tableInfo field "${fieldName}" updated successfully`};

    const connection = await this.connectionRepository.findOneByConnectionId(result.data!.connectionId);

    if (!connection) {
      logger.warn(`Connection with ID ${result.data!.connectionId} not found`);
      return {isSuccess: false, message: `Connection with ID ${result.data!.connectionId} not found`};
    }

    if (
      [ConnectionSourceType.MYSQL_DB, ConnectionSourceType.MSSQL_DB, ConnectionSourceType.POSTGRESQL].includes(
        connection.type as ConnectionSourceType,
      )
    ) {
      this.callExternalServiceToUpdateVisibility(result.data!).catch(error => {
        logger.error(`Failed to change field visibility for connection ${result?.data?.connectionId}: ${error}`);
      });
    } else {
      logger.info(`Skipping external service call for connection type: ${connection.type}`);
    }
    return {isSuccess: true, message: response.message};
  }

  async updateTableVisibilityToAI(
    connectionId: string,
    tableName: string,
    isVisibleToAI: boolean,
    currentUserName: string,
    requestId: string,
  ) {
    const tableObj = await this.tableDataRepository.findOne({
      where: {
        connectionId: connectionId,
        tableName: tableName,
      },
    });

    if (!tableObj) {
      logger.warn(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | No table object found for ${tableName} in connection ${connectionId}`,
      );
      return {isSuccess: false, message: 'No table object found'};
    }

    const connection = await this.connectionRepository.findOneByConnectionId(connectionId);
    if (!connection) {
      logger.warn(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | No connection object found for ${connectionId}`,
      );
      return {isSuccess: false, message: 'No connection object found'};
    }

    if (!connection.layerNextConnectionId) {
      logger.warn(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Connection with ID ${connectionId} has no layerNext ConnectionId`,
      );
      return {isSuccess: false, message: `Connection with ID ${connectionId} has no layerNext ConnectionId`};
    }

    const fieldNameList = tableObj.fields.map(field => field.name);

    //check existing visibility is same as the new visibility
    if (tableObj.isVisibleToAI === isVisibleToAI) {
      logger.warn(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Visibility is already set to the same value`,
      );
      return {isSuccess: false, message: 'Visibility is already set to the same value'};
    }

    if (objectiveBasedFilteringRequired) {
      return this.updateVisibilityToAI(tableObj, fieldNameList, isVisibleToAI, currentUserName, requestId);
    } else {
      if (isVisibleToAI) {
        logger.info(
          `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Adding table ${tableObj.tableName} to layernext data dictionary`,
        );
        //check if table is already in layernext data dictionary
        const layernextTable = await this.tableDataRepository.findOne({
          where: {
            connectionId: connection.layerNextConnectionId,
            tableName: tableObj.tableName,
          },
        });
        if (layernextTable) {
          logger.warn(
            `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Table ${tableObj.tableName} is already in layernext data dictionary`,
          );
          return {isSuccess: false, message: `Table ${tableObj.tableName} is already in layernext data dictionary`};
        }

        //add table to layernext data dictionary

        //create copy of raw table object and add it to layernext data dictionary
        let layernextTableObj = _.cloneDeep(tableObj);
        //remove _id from layernextTableObj if it exists, add layerNextConnectionId as connectionId
        delete layernextTableObj._id;
        layernextTableObj.connectionId = connection.layerNextConnectionId;

        //add table to layernext data dictionary
        let createdLayernextTable = await this.tableDataRepository.create(layernextTableObj);
        if (!createdLayernextTable) {
          logger.error(
            `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Failed to add table ${tableObj.tableName} to layernext data dictionary`,
          );
          return {isSuccess: false, message: 'Failed to add table to layernext data dictionary'};
        }

        logger.info(
          `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Added table ${tableObj.tableName} to layernext data dictionary`,
        );
      } else {
        logger.info(
          `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Removing table ${tableObj.tableName} from layernext data dictionary`,
        );
        //remove table from layernext data dictionary
        let deletedLayernextTable = await this.tableDataRepository.deleteOne({
          connectionId: connection.layerNextConnectionId,
          tableName: tableObj.tableName,
        });

        logger.info(
          `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Removed table ${tableObj.tableName} from layernext data dictionary. Result: ${deletedLayernextTable}`,
        );
      }

      //update raw table visibility and  visibility of all fields of the table
      let updatedRawTable = await this.tableDataRepository.updateOneSet(
        {_id: new ObjectId(tableObj._id)},
        {
          isVisibleToAI: isVisibleToAI,
          'fields.$[].isVisibleToAI': isVisibleToAI,
          'fields.$[].lastModifiedTime': new Date(),
          modifiedBy: currentUserName,
          modifiedDate: new Date(),
        },
        [],
      );

      logger.info(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}. Result: ${updatedRawTable}`,
      );

      //update modifiedBy and modifiedDate of the layernext table
      let updatedLayernextTableChangedBy = await this.tableDataRepository.updateOneSet(
        {
          connectionId: connection.layerNextConnectionId,
          tableName: tableObj.tableName,
        },
        {modifiedBy: currentUserName, modifiedDate: new Date()},
        [],
      );

      logger.info(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Updated layernext table changedBy and modifiedDate for ${tableObj.tableName}. Result: ${updatedLayernextTableChangedBy}`,
      );

      //update field count of the layernext table
      if (isVisibleToAI) {
        this.updateFieldCountOfTable(connection.layerNextConnectionId, tableObj.tableName).catch(error => {
          logger.error(
            `Update field count of table | TableDataService.updateTableVisibilityToAI | ${requestId} | Failed to update field count for table ${tableObj.tableName}: ${error}`,
          );
        });
      }

      logger.info(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}`,
      );

      //call python service to update data sources overview
      this.updateDataSourcesDescription()
        .then(() => {
          logger.info(
            `Data Sources Overview | TableDataService.updateTableVisibilityToAI | ${requestId} | Successfully updated data sources description`,
          );
        })
        .catch(error => {
          logger.error(
            `Data Sources Overview | TableDataService.updateTableVisibilityToAI | ${requestId} | Failed to update data sources description: ${error}`,
          );
        });

      logger.info(
        `Change visibility | TableDataService.updateTableVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}`,
      );

      return {isSuccess: true, message: 'Visibility updated successfully'};
    }
  }

  async updateFieldVisibilityToAI(
    id: string,
    fieldName: string,
    isVisibleToAI: boolean,
    currentUserName: string,
    requestId: string,
  ) {
    logger.info(
      `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | id: ${id}, fieldName: ${fieldName}, isVisibleToAI: ${isVisibleToAI}`,
    );
    const tableObj = await this.tableDataRepository.findById(id);

    if (!tableObj) {
      logger.warn(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | No table object found for ${id}`,
      );
      return {isSuccess: false, message: 'No table object found'};
    }

    const connection = await this.connectionRepository.findOneByConnectionId(tableObj.connectionId);
    if (!connection) {
      logger.warn(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | No connection object found for ${tableObj.connectionId}`,
      );
      return {isSuccess: false, message: 'No connection object found'};
    }

    const field = tableObj.fields.find(f => f.name === fieldName);
    if (!field) {
      logger.warn(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | No field object found for ${fieldName} in table ${tableObj.tableName}`,
      );
      return {isSuccess: false, message: 'No field object found'};
    }

    //check existing visibility is same as the new visibility
    if (field.isVisibleToAI === isVisibleToAI) {
      logger.warn(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Visibility is already set to the same value`,
      );
      return {isSuccess: false, message: 'Visibility is already set to the same value'};
    }

    if (objectiveBasedFilteringRequired) {
      return this.updateVisibilityToAI(tableObj, [fieldName], isVisibleToAI, currentUserName, requestId);
    } else {
      if (isVisibleToAI) {
        //add field to layernext data dictionary
        logger.info(
          `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Adding field ${fieldName} to layernext data dictionary`,
        );

        //check table is already in layernext data dictionary
        const layernextTable = await this.tableDataRepository.findOne({
          where: {
            connectionId: connection.layerNextConnectionId,
            tableName: tableObj.tableName,
          },
        });

        if (layernextTable) {
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Table ${tableObj.tableName} is in layernext data dictionary, adding field ${fieldName}`,
          );

          //check if field is already in fields array
          const fieldIndex = layernextTable.fields.findIndex(f => f.name === fieldName);
          if (fieldIndex === -1) {
            logger.info(
              `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Field ${fieldName} is not in layernext data dictionary, adding it`,
            );
            let updatedLayernextTable = await this.tableDataRepository.updateOne(
              {_id: new ObjectId(layernextTable._id)},
              {$push: {fields: field}},
              [],
            );
            logger.info(
              `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated layernext data dictionary. Result: ${updatedLayernextTable}`,
            );
          } else {
            logger.warn(
              `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Field ${fieldName} is already in layernext data dictionary`,
            );
          }
        } else {
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Table ${tableObj.tableName} is not in layernext data dictionary, adding it`,
          );
          //create copy of raw table object and add it to layernext data dictionary
          let layernextTableObj = _.cloneDeep(tableObj);
          //remove _id from layernextTableObj if it exists, add layerNextConnectionId as connectionId
          delete layernextTableObj._id;
          layernextTableObj.connectionId = connection.layerNextConnectionId;
          layernextTableObj.fields = [field];

          //add table to layernext data dictionary
          let createdLayernextTable = await this.tableDataRepository.create(layernextTableObj);
          if (!createdLayernextTable) {
            logger.error(
              `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Failed to add table ${tableObj.tableName} to layernext data dictionary`,
            );
            return {isSuccess: false, message: 'Failed to add table to layernext data dictionary'};
          }

          //make table isVisibleToAI true in raw table
          let updatedRawTable = await this.tableDataRepository.updateOneSet(
            {_id: new ObjectId(tableObj._id)},
            {isVisibleToAI: true},
            [],
          );
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}. Result: ${updatedRawTable}`,
          );

          //call python service to update data sources overview
          this.updateDataSourcesDescription()
            .then(() => {
              logger.info(
                `Data Sources Overview | TableDataService.updateFieldVisibilityToAI | ${requestId} | Successfully updated data sources description`,
              );
            })
            .catch(error => {
              logger.error(
                `Data Sources Overview | TableDataService.updateFieldVisibilityToAI | ${requestId} | Failed to update data sources description: ${error}`,
              );
            });
        }
      } else {
        //remove field from layernext data dictionary
        logger.info(
          `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Removing field ${fieldName} from layernext data dictionary`,
        );

        //check all fields are false except this one
        const allFieldsExceptThisOne = tableObj.fields.filter(f => f.name !== fieldName);
        const allFieldsExceptThisOneAreFalse = allFieldsExceptThisOne.every(f => f.isVisibleToAI === false);
        if (allFieldsExceptThisOneAreFalse) {
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | All fields except this one are false, removing table from layernext data dictionary`,
          );
          //remove table from layernext data dictionary
          let deletedLayernextTable = await this.tableDataRepository.deleteOne({
            connectionId: connection.layerNextConnectionId,
            tableName: tableObj.tableName,
          });

          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Removed table ${tableObj.tableName} from layernext data dictionary. Result: ${deletedLayernextTable}`,
          );

          //make raw table isVisibleToAI false
          let updatedRawTable = await this.tableDataRepository.updateOneSet(
            {_id: new ObjectId(tableObj._id)},
            {isVisibleToAI: false},
            [],
          );

          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}. Result: ${updatedRawTable}`,
          );

          //call python service to update data sources overview
          this.updateDataSourcesDescription()
            .then(() => {
              logger.info(
                `Data Sources Overview | TableDataService.updateFieldVisibilityToAI | ${requestId} | Successfully updated data sources description`,
              );
            })
            .catch(error => {
              logger.error(
                `Data Sources Overview | TableDataService.updateFieldVisibilityToAI | ${requestId} | Failed to update data sources description: ${error}`,
              );
            });
        } else {
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Field ${fieldName} is in layernext data dictionary, removing it`,
          );
          //remove field from layernext data dictionary
          let updatedLayernextTable = await this.tableDataRepository.updateOne(
            {
              connectionId: connection.layerNextConnectionId,
              tableName: tableObj.tableName,
            },
            {$pull: {fields: {name: fieldName}}},
            [],
          );
          logger.info(
            `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated layernext data dictionary. Result: ${updatedLayernextTable}`,
          );
        }
      }

      // Update the visibility of the specific field in the raw table and set lastModifiedAt
      let updatedRawTable = await this.tableDataRepository.updateOne(
        {_id: new ObjectId(tableObj._id)},
        {
          $set: {
            'fields.$[field].isVisibleToAI': isVisibleToAI,
            'fields.$[field].lastModifiedAt': new Date(),
            modifiedBy: currentUserName,
            modifiedDate: new Date(),
          },
        },
        [
          {
            'field.name': fieldName,
          },
        ],
      );

      logger.info(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated raw table visibility for ${tableObj.tableName}. Result: ${updatedRawTable}`,
      );

      this.updateFieldCountOfTable(connection.layerNextConnectionId, tableObj.tableName).catch(error => {
        logger.error(
          `Update field count of table | TableDataService.updateFieldVisibilityToAI | ${requestId} | Failed to update field count for table ${tableObj.tableName}: ${error}`,
        );
      });

      //update modifiedBy and modifiedDate of the layernext table
      let updatedLayernextTableChangedBy = await this.tableDataRepository.updateOneSet(
        {
          connectionId: connection.layerNextConnectionId,
          tableName: tableObj.tableName,
        },
        {modifiedBy: currentUserName, modifiedDate: new Date()},
        [],
      );

      logger.info(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated layernext table changedBy and modifiedDate for ${tableObj.tableName}. Result: ${updatedLayernextTableChangedBy}`,
      );

      logger.info(
        `Change field visibility | TableDataService.updateFieldVisibilityToAI | ${requestId} | Updated field visibility for ${fieldName} in table ${tableObj.tableName}`,
      );

      return {isSuccess: true, message: 'Field visibility updated successfully'};
    }
  }

  async updateFieldCountOfTable(connectionId: string, tableName: string) {
    const tableObj = await this.tableDataRepository.findOne({
      where: {
        connectionId,
        tableName,
      },
    });
    if (!tableObj) {
      logger.warn(
        `Update field count of table | TableDataService.updateFieldCountOfTable | No table object found for ${connectionId} and ${tableName}`,
      );
      return;
    }

    const fields = tableObj.fields || [];
    const fieldCount = fields.length;

    await this.tableDataRepository.updateOneSet({_id: new ObjectId(tableObj._id)}, {fieldsCount: fieldCount}, []);

    logger.info(
      `Update field count of table | TableDataService.updateFieldCountOfTable | Successfully updated field count for table ${connectionId} and ${tableName}`,
    );
  }

  async updateVisibilityToAI(
    tableObj: TableData & TableDataRelations,
    fieldNameList: string[],
    isVisibleToAI: boolean,
    currentUserName: string,
    requestId: string,
  ) {
    logger.info(
      `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | id: ${tableObj._id}, fieldNameList: ${fieldNameList}, isVisibleToAI: ${isVisibleToAI}`,
    );
    const fields = tableObj.fields.filter(f => fieldNameList.includes(f.name));

    if (fields.length === 0) {
      logger.warn(
        `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | No fields found for the given field names`,
      );
      return {isSuccess: false, message: 'No fields found'};
    }

    const isExistingVisibilitySame = fields.every(
      f => (f.isVisibleToAI === undefined ? false : f.isVisibleToAI) === isVisibleToAI,
    );

    if (isExistingVisibilitySame) {
      logger.warn(
        `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Visibility is already set to the same value`,
      );
      return {isSuccess: false, message: 'Visibility is already set to the same value'};
    }

    //update fields
    await this.tableDataRepository.updateManySet(
      {_id: new ObjectId(tableObj._id)},
      {
        'fields.$[field].isVisibleToAI': isVisibleToAI,
        'fields.$[field].lastModifiedAt': new Date(),
      },
      [
        {
          'field.name': {$in: fieldNameList},
        },
      ],
    );

    const connection = await this.connectionRepository.findOneByConnectionId(tableObj.connectionId);

    if (!connection) {
      logger.warn(
        `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Connection with ID ${tableObj.connectionId} not found`,
      );
      return {isSuccess: false, message: `Connection with ID ${tableObj.connectionId} not found`};
    }

    if (!connection.layerNextConnectionId) {
      logger.warn(
        `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Connection with ID ${tableObj.connectionId} has no layerNextConnectionId`,
      );
      return {isSuccess: false, message: `Connection with ID ${tableObj.connectionId} has no layerNext ConnectionId`};
    }

    let skipSectionRefine = false;

    //update table
    if (isVisibleToAI) {
      await this.tableDataRepository.updateOneSet(
        {_id: new ObjectId(tableObj._id)},
        {
          isVisibleToAI: isVisibleToAI,
          modifiedBy: currentUserName,
          modifiedDate: new Date(),
        },
        [],
      );
    } else {
      //check if all fields are false except this one
      const allFields = tableObj.fields.filter(f => !fieldNameList.includes(f.name));
      const allFieldsAreFalse = allFields.every(
        f => (f.isVisibleToAI === undefined ? false : f.isVisibleToAI) === false,
      );
      if (allFieldsAreFalse) {
        skipSectionRefine = true;

        //make raw table as invisible
        await this.tableDataRepository.updateOneSet(
          {_id: new ObjectId(tableObj._id)},
          {
            isVisibleToAI: isVisibleToAI,
            modifiedBy: currentUserName,
            modifiedDate: new Date(),
          },
          [],
        );

        //remove table from layernext data dictionary
        await this.tableDataRepository.deleteOne({
          connectionId: connection.layerNextConnectionId,
          tableName: tableObj.tableName,
        });
      }
    }

    logger.info(
      `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Starting python service callExternalServiceToUpdateVisibilityToAI`,
    );

    this.callPythonServiceToUpdateVisibilityToAI(
      tableObj.connectionId,
      connection.layerNextConnectionId,
      tableObj.tableName,
      fieldNameList,
      isVisibleToAI,
      skipSectionRefine,
      requestId,
    )
      .catch(error => {
        logger.error(
          `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Failed to change visibility for connection ${tableObj.connectionId}: ${error}`,
        );
      })
      .then(() => {
        logger.info(
          `Change visibility | TableDataService.updateVisibilityToAI | ${requestId} | Completed successfully`,
        );
      });

    return {isSuccess: true, message: 'Visibility updated successfully'};
  }

  /**
   * Makes an asynchronous call to an external Python service to update the visibility of a field.
   * This method does not block the main thread and is intended to run after the main update logic.
   *
   * @param dataObject {TableData} - The updated table data object containing the connection ID and table information.
   * @returns {Promise<void>} - A promise that resolves when the external service call is complete.
   *
   * @throws {Error} - If the Axios request fails, the error will be logged and handled by the caller.
   */
  private async callExternalServiceToUpdateVisibility(dataObject: TableData) {
    const url = `${PYTHON_HOST}/internal/connection/generate/change_field_visibility`;
    await Axios({
      url,
      method: 'POST',
      data: {
        dataSourceId: dataObject.connectionId,
        tableName: dataObject.tableName,
      },
    });
  }

  private async callPythonServiceToUpdateVisibilityToAI(
    connectionId: string,
    layerNextConnectionId: string,
    tableName: string,
    fieldNameList: string[],
    isVisibleToAI: boolean,
    skipSectionRefine: boolean,
    requestId: string,
  ) {
    logger.info(
      `Change visibility | TableDataService.callPythonServiceToUpdateVisibilityToAI | ${requestId} | Starting python service callPythonServiceToUpdateVisibilityToAI`,
    );
    const url = `${PYTHON_HOST}/internal/connection/generate/change_field_visibility_to_ai`;
    try {
      const response = await Axios({
        url,
        method: 'POST',
        data: {
          dataSourceId: connectionId,
          layerNextConnectionId,
          tableName,
          fieldNameList,
          isVisibleToAI,
          skipSectionRefine,
          requestId,
        },
      });

      if (response.data && response.data.isSuccess) {
        logger.info(
          `Change visibility | TableDataService.callPythonServiceToUpdateVisibilityToAI | ${requestId} | Successfully updated visibility to AI`,
        );
        return response.data;
      } else {
        logger.warn(
          `Change visibility | TableDataService.callPythonServiceToUpdateVisibilityToAI | ${requestId} | Failed to update visibility to AI: ${
            response.data?.message || 'Unknown error'
          }`,
        );
        return {isSuccess: false, message: response.data?.message || 'Failed to update visibility to AI'};
      }
    } catch (error) {
      logger.error(
        `Change visibility | TableDataService.callPythonServiceToUpdateVisibilityToAI | ${requestId} | Error updating visibility to AI: ${error.message}`,
      );
      return {isSuccess: false, message: 'Failed to update visibility to AI. Error: ' + error.message};
    }
  }

  /**
   * Updates the description of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param description {string} - The new description for the table.
   * @param currentUserName {string} - name of the modifier
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   */
  async updateTableDescription(tableId: string, description: string, currentUserName = '') {
    await this.tableDataRepository.updateTableDescription(tableId, description, currentUserName);
  }

  /**
   * Updates the overview of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param overview {string} - The new overview for the table.
   * @param currentUserName {string} - The name of the modifier.
   * @returns {Promise<void>} - A promise that resolves when the update is complete.
   */
  async updateTableOverview(tableId: string, overview: string, currentUserName = ''): Promise<void> {
    const sanitizedOverview = overview.replace(/<\/?[^>]+(>|$)/g, '');
    let tableDataDoc;
    try {
      tableDataDoc = await this.tableDataRepository.find({where: {_id: tableId}});
      await this.tableDataRepository.updateTableOverview(tableId, sanitizedOverview, currentUserName);
    } catch (error) {
      logger.error(
        `Update table overview update| TableDataService.updateTableOverview| Error updating table overview for tableId ${tableId}: ${error.message}`,
      );
    }
    try {
      if (tableDataDoc && tableDataDoc[0] && tableDataDoc[0].connectionId) {
        await this.generateLayernextDataDictionary(tableDataDoc[0].connectionId, false); // If false does not trigger the layernext data dictionary regeneration
      } else {
        logger.error(
          `Regenerate data source overview after table overview update|TableDataService.updateTableOverview| Error regenerating data sources overview for tableId ${tableId}: connectionId not found`,
        );
      }
    } catch (error) {
      logger.error(
        `Regenerate data source overview after table overview update|TableDataService.updateTableOverview| Error regenerating data sources overview for tableId ${tableId}: ${error.message}`,
      );
    }
  }

  /**
   * Retrieves navigation data for tables based on connection ID and current table ID.
   * @param connectionId {string} - The connection ID.
   * @param currentTableId {string} - The current table ID.
   * @returns {Promise<any>} - A promise that resolves to an object containing the navigation data for the tables.
   */

  async getNavigationData(connectionId: string, currentTableId: string): Promise<any> {
    return this.tableDataRepository.getNavigationData(connectionId, currentTableId);
  }

  /**
   * Updates the visibility status of a specific table.
   * @param connectionId {string} - The connection ID.
   * @param tableName {string} - The name of the table.
   * @param isVisible {boolean} - The visibility status.
   * @returns {Promise<{isSuccess: boolean}>} - A promise that resolves to an object indicating the success of the update.
   */
  async updateVisibility(
    connectionId: string,
    tableName: string,
    isVisible: boolean,
    currentUserName: string,
  ): Promise<{isSuccess: boolean}> {
    const res = await this.tableDataRepository.updateVisibility(connectionId, tableName, isVisible, currentUserName);

    //re-generate data sources overview
    try {
      logger.debug(
        `Data Sources Overview | TableDataService.updateVisibility | N/A | Starting to re-generate data sources overview after updating visibility for table ${tableName} in connection ${connectionId} to value ${isVisible}`,
      );
      await this.generateLayernextDataDictionary(connectionId, false); // If false does not trigger the layernext data dictionary regeneration
      logger.debug(
        `Data Sources Overview | TableDataService.updateVisibility | N/A | Completed re-generating data sources overview after updating visibility for table ${tableName} in connection ${connectionId} to value ${isVisible}`,
      );
    } catch (error) {
      logger.error(
        `Data Sources Overview | TableDataService.updateVisibility | N/A | Failed to re-generate data sources overview after updating visibility for table ${tableName} in connection ${connectionId} to value ${isVisible} Error is: ${error}`,
      );
    }
    return res;
  }

  /**
   * Makes an asynchronous call to an external Python service to update the data dictionary of a data source.
   * This method does not block the main thread and is intended to run after the main update logic.
   *
   * @param dataObject {ConnectionId} - The updated data source id.
   * @returns {Promise<void>} - A promise that resolves when the external service call is complete.
   *
   * @throws {Error} - If the Axios request fails, the error will be logged and handled by the caller.
   */
  private async callExternalServiceToUpdateDataDictionary(connectionId: String) {
    const url = `${PYTHON_HOST}/internal/connection/generate/generate_layernext_data_source`;
    await Axios({
      url,
      method: 'POST',
      data: {
        dataSourceId: connectionId,
      },
    });
  }

  /**
   * Makes an asynchronous call to an external Python service to update the data description of the overall system.
   * This method does not block the main thread and is intended to run after the main update logic.
   *
   * @returns {Promise<void>} - A promise that resolves when the external service call is complete.
   *
   * @throws {Error} - If the Axios request fails, the error will be logged and handled by the caller.
   */
  private async callExternalServiceToUpdateSystemDataDescription() {
    logger.info(
      `Data Sources Overview | TableDataService.callExternalServiceToUpdateSystemDataDescription | N/A | Starting to call external service to update data sources description`,
    );
    const url = `${PYTHON_HOST}/internal/connection/update/update_data_source_description`;
    try {
      const response = await Axios({
        url,
        method: 'POST',
        data: {},
      });
      logger.info(
        `Data Sources Overview | TableDataService.callExternalServiceToUpdateSystemDataDescription | N/A | Response received from external service`,
      );
      if (response.data && response.data.isSuccess) {
        logger.info(
          `Data Sources Overview | TableDataService.callExternalServiceToUpdateSystemDataDescription | N/A | Successfully updated data sources description`,
        );
        return {
          isSuccess: true,
          message: 'Data sources description updated successfully',
        };
      } else {
        logger.error(
          `Data Sources Overview | TableDataService.callExternalServiceToUpdateSystemDataDescription | N/A | Failed to update data sources description: ${
            response.data?.message || 'Unknown error'
          }`,
        );
        return {
          isSuccess: false,
          message: response.data?.message || 'Failed to update data sources description',
        };
      }
    } catch (error) {
      logger.error(
        `Data Sources Overview | TableDataService.callExternalServiceToUpdateSystemDataDescription | N/A | Failed to update data sources description: ${error.message}`,
      );
      return {
        isSuccess: false,
        message: 'Failed to update data sources description. Error: ' + error.message,
      };
    }
  }

  /**
   * Triggers an external API to regenerate the Layernext data dictionary for a given data source connection.
   * This operation is asynchronous and does not block the main thread.
   *
   * @param connectionId {String} - The ID of the data source connection to regenerate the data dictionary for.
   * @param shouldRegenerateDataDictionary {boolean} - Whether to regenerate the data dictionary from scratch.
   * @returns {Promise<void>} - A promise that resolves when the external API call is complete.
   *
   * @throws {Error} - If the Axios request fails, the error will be logged and handled by the caller.
   */
  async generateLayernextDataDictionary(connectionId: string, shouldRegenerateDataDictionary = false) {
    if (!connectionId) {
      logger.warn(
        `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Connection ID not provided`,
      );
      return;
    }

    const connectionObject = await this.connectionRepository.findOneByConnectionId(connectionId);

    if (!connectionObject) {
      logger.warn(
        `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Connection with ID ${connectionId} not found`,
      );
      return;
    }

    if (shouldRegenerateDataDictionary) {
      logger.debug(
        `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Starting callExternalServiceToUpdateDataDictionary ${connectionId}`,
      );
      await this.callExternalServiceToUpdateDataDictionary(connectionId).catch(error => {
        logger.error(`Failed to regenerate data sources overview for connection ${connectionId}: ${error}`);
      });
    } else {
      logger.debug(
        `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Skipping data dictionary regeneration for connection ${connectionId} as data dictionary is not required`,
      );
      if (connectionObject.connectionType == DataSourceConnectionType.LAYERNEXT_DATABASE) {
        logger.debug(
          `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Starting callExternalServiceToUpdateSystemDataDescription`,
        );
        this.updateDataSourcesDescription().catch(error => {
          logger.error(
            `Data Sources Overview | TableDataService.generateLayernextDataDictionary | N/A | Failed to update data sources description: ${error}`,
          );
        });
      }
    }
  }

  /**
   * Updates the data sources description in system data collection
   *
   * Makes an asynchronous call to an external Python service to regenerate the data sources overview.
   *
   * @returns {Promise<void>} - A promise that resolves when the external service call is complete.
   *
   * @throws {Error} - If the Axios request fails, the error will be logged and handled by the caller.
   */

  async updateDataSourcesDescription() {
    return this.callExternalServiceToUpdateSystemDataDescription();
  }

  /**
   * Updates the verification status for both tableInfo and a specific field if provided.
   * @param connectionId {string} - The connection ID.
   * @param tableName {string} - The name of the table.
   * @param isVerified {boolean} - The verification status.
   * @param fieldName {string} - (Optional) The name of the field.
   * @returns {Promise<{isSuccess: boolean}>} - A promise that resolves to an object indicating the success of the update.
   */

  async updateVerification(
    connectionId: string,
    tableName: string,
    isVerified: boolean,
    fieldName?: string,
  ): Promise<{isSuccess: boolean}> {
    return this.tableDataRepository.updateVerification(connectionId, tableName, isVerified, fieldName);
  }

  /**
   * Retrieves the table schema information as a JSON object for a given connection ID.
   *
   * This method fetches the table schema details for the provided connection ID by calling
   * the `getTableSchemaAsJson` method from the `tableDataRepository`.
   *
   * @param {string} connectionId - The connection ID for which to retrieve table schema information.
   * @returns {Promise<any>} - A promise that resolves to a JSON object containing the table schema information.
   */
  async getTableDataByConnectionID(connectionId: string) {
    return this.tableDataRepository.getTableSchemaAsJson(connectionId);
  }

  /**
   * Retrieves the latest modified date among all fields in the tables for a given connection ID.
   *
   * This method fetches the latest modified date for the table data associated with the provided
   * connection ID by calling the `getLatestModifiedFieldDate` method from the `tableDataRepository`.
   *
   * @param {string} connectionId - The connection ID for which to retrieve the latest modified date of table data.
   * @returns {Promise<Date>} - A promise that resolves to the latest modified date among all fields.
   */
  async getTableDataUpdatedAtByConnectionID(connectionId: string) {
    return this.tableDataRepository.getLatestModifiedFieldDate(connectionId);
  }

  /**
   * Retrieves the count of configured and unconfigured tables for a given connection ID,
   * with error handling for null values, exceptions, and undefined table information.
   *
   * This method fetches all table data entries associated with the provided connection ID
   * by calling the `findByConnectionId` method from the `tableDataRepository`. It then iterates
   * through the table data entries to calculate the number of configured and unconfigured tables.
   *
   * Configured tables are those where `tableInfo.isVisible` is set to `true`, and unconfigured
   * tables are those where `tableInfo.isVisible` is `false`. If `tableInfo` is missing or
   * `isVisible` is undefined, those entries are skipped.
   *
   * @param {string} connectionId - The connection ID for which to retrieve table counts.
   * @returns {Promise<{configuredTables: number, unconfiguredTables: number}>} - A promise that resolves to an object
   * containing the number of configured and unconfigured tables.
   * @throws {Error} - Throws an error if there is an issue fetching the table data.
   */
  async getTableCountsByConnectionID(
    connectionId: string,
  ): Promise<{configuredTables: number; unconfiguredTables: number}> {
    try {
      const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);

      let configuredTableCount = 0;
      let unconfiguredTableCount = 0;

      for (const tableData of tableDataEntries) {
        if (tableData && typeof tableData.isVisible === 'boolean') {
          if (tableData.isVisible) {
            configuredTableCount++;
          } else {
            unconfiguredTableCount++;
          }
        } else {
          logger.warn(
            `SrcDestConnectionService| getTableCountsByConnectionID | N/A |Skipping table entry due to missing or invalid tableInfo for connectionId: ${connectionId}`,
          );
        }
      }
      return {
        configuredTables: configuredTableCount,
        unconfiguredTables: unconfiguredTableCount,
      };
    } catch (error) {
      logger.error(
        `SrcDestConnectionService| getTableCountsByConnectionID | N/A |Error fetching table counts for connectionId: ${connectionId}: ${error}`,
      );
      throw new Error(
        `SrcDestConnectionService| getTableCountsByConnectionID | N/A  |Failed to retrieve table counts for connectionId: ${connectionId}`,
      );
    }
  }

  /**
   * Retrieves the schema for a specific table from the data source.
   *
   * This method fetches the schema of the specified table from a data source based on the provided source name
   * and table name. It returns the table name, description, and a list of visible fields, including their name,
   * data type, and description.
   *
   * @param sourceName The name of the data source from which the table schema will be retrieved.
   * @param tableName The name of the table whose schema is to be retrieved.
   *
   * @returns {Promise<{
   *   table_name: string;
   *   table_description: string;
   *   fields: { name: string; dataType: string; description: string; }[]
   * } | { error: string }>}
   *          A promise that resolves with the table schema, including the table name, description, and visible fields.
   *          If an error occurs, it returns an error message indicating the failure reason.
   */
  async getTableSchema(
    sourceName: string,
    tableName: string,
  ): Promise<
    | {
        table_name: string;
        mapped_name: string;
        table_description: string;
        fields: {name: string; dataType: string; description: string}[];
      }
    | {error: string}
  > {
    try {
      const sourceConnectionObj = await this.connectionRepository.findOne({
        where: {
          sourceName: sourceName,
        },
      });
      if (!sourceConnectionObj || !sourceConnectionObj.connectionId) {
        throw new Error(`No connection found for sourceName: ${sourceName}`);
      }

      const connectionId = sourceConnectionObj?.connectionId;
      if (!connectionId) {
        throw new Error(`No connection found for sourceName: ${sourceName}`);
      }

      const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);
      if (!tableDataEntries || tableDataEntries.length === 0) {
        throw new Error(`No table data found for connectionId: ${connectionId}`);
      }

      const tableDataEntry = tableDataEntries.find(entry => entry?.tableName === tableName);
      if (!tableDataEntry) {
        throw new Error(`No table found with tableName: ${tableName} for sourceName: ${sourceName}`);
      }

      logger.debug(
        `Table Schema Retrieval | TableDataService.getTableSchema | N/A | Successfully retrieved table data for table ${tableName} in connection ${connectionId}`,
      );

      const fields = Array.isArray(tableDataEntry.fields) ? tableDataEntry.fields : [];
      const visibleFields = fields
        .filter(field => field?.isVisible === true)
        .map(field => ({
          name: field?.name || 'N/A',
          dataType: field?.dataType || 'N/A',
          description: field?.description || 'N/A',
        }));
      logger.debug(
        `Table Schema Retrieval | TableDataService.getTableSchema | N/A | Successfully filtered visible fields for table ${tableName} in connection ${connectionId}`,
      );
      return {
        table_name: tableDataEntry.tableName || 'N/A',
        mapped_name: tableDataEntry.mappedName || tableDataEntry.tableName || 'N/A',
        table_description: tableDataEntry.description || 'N/A',
        fields: visibleFields,
      };
    } catch (error) {
      logger.error(
        `Table Schema Retrieval | TableDataService.getTableSchema | N/A | Failed to retrieve schema for table ${tableName} in source ${sourceName}. Error: ${error.message}`,
      );
      return {
        error: `Failed to fetch the table schema. Please check the sourceName and tableName.Error: ${error.message}`,
      };
    }
  }

  async getSectionList(connectionId: string): Promise<string[]> {
    try {
      const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);
      if (!tableDataEntries || tableDataEntries.length === 0) {
        throw new Error(`No table data found for connectionId: ${connectionId}`);
      }

      const sections = tableDataEntries.map(entry => entry.mappedName || entry.tableName);
      const uniqueSections = [...new Set(sections)];

      logger.debug(
        `Section List Retrieval | TableDataService.get_section_list | N/A | Successfully retrieved section list for connection ${connectionId}`,
      );

      return uniqueSections;
    } catch (error) {
      logger.error(
        `Section List Retrieval | TableDataService.get_section_list | N/A | Failed to retrieve section list for connection ${connectionId}. Error: ${error.message}`,
      );
      return [];
    }
  }

  async getSectionOverviewsForDataSource(connectionId: string): Promise<string> {
    try {
      const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);
      if (!tableDataEntries || tableDataEntries.length === 0) {
        return '<p>No tables found for the specified connection ID.</p>';
      }

      const dataSourceName = await this.connectionDataRepository.getDataSourceName(connectionId);

      let htmlString = `<p>Data source name: ${dataSourceName}</p><p>Sections:</p>`;

      for (const table of tableDataEntries) {
        htmlString += `<p>${table.tableName}</p><p>${table.table_overview}</p><p></p>`;
      }

      return htmlString;
    } catch (error) {
      logger.error(
        `Section List Retrieval | TableDataService.get_section_overviews_for_data_source | N/A | Failed to retrieve section list for connection ${connectionId}. Error: ${error.message}`,
      );
      return '';
    }
  }

  /**
   * Retrieves the field mapping for a given data source connection ID.
   * If mappedName is present in the field, it is used as the key in the mapping added to the map.
   *
   * @param {string} connectionId - The connection ID for which to retrieve the field mapping.
   * @returns {Promise<Map<string, string>>} - A promise that resolves to a Map containing the column mapping.
   */
  async getTableAndFieldMapping(connectionId: string): Promise<Map<string, string>> {
    const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);
    const fieldMapping = new Map<string, string>();

    for (const entry of tableDataEntries) {
      // Add table mapping if available
      if (entry.mappedName) {
        fieldMapping.set(entry.mappedName, entry.tableName);
      }

      // Existing field mapping code
      for (const field of entry.fields) {
        if (field.mappedName) {
          fieldMapping.set(field.mappedName, field.name);
        }
      }
    }

    return fieldMapping;
  }

  /**
   * Retrieves the total count of tables for a given connection ID.
   *
   * This method fetches all table data entries associated with the provided connection ID
   * by calling the `findByConnectionId` method from the `tableDataRepository`.
   * It returns the number of tables found. If no tables are found, it returns 0.
   *
   * @param {string} connectionId - The connection ID for which to retrieve the table count.
   * @returns {Promise<number>} - A promise that resolves to the total count of tables.
   */
  async getTableCount(connectionId: string): Promise<number> {
    const tableDataEntries = await this.tableDataRepository.findByConnectionId(connectionId);

    if (!tableDataEntries || tableDataEntries.length === 0) {
      return 0;
    }

    return tableDataEntries.length;
  }

  async getDataSourceMetaData(connectionId: string): Promise<DataSourceMetaDataResponse> {
    const tableDataEntries = await this.tableDataRepository.find({where: {connectionId: connectionId}});
    if (!tableDataEntries || tableDataEntries.length === 0) {
      logger.error(
        `fetch connection meta | TableDataService.getDataSourceMetaData | N/A | No data found for connectionId: ${connectionId}`,
      );
      return {
        isSuccess: false,
        message: `No data found for connectionId: ${connectionId}`,
      };
    }

    const dataSourceMetaData = tableDataEntries.map(entry => ({
      tableName: entry.tableName,
      tableDescription: entry.description,
      tableOverview: entry.table_overview,
      fields: entry.fields.map(field => ({
        name: field.name,
        dataType: field.dataType,
        description: field.description,
        ...(field.fieldStats?.value_distribution && {availableValues: field.fieldStats.value_distribution}),
        ...(field.fieldStats?.distinct_count && {distinctValuesCount: field.fieldStats.distinct_count}),
      })),
      ...(entry.relationships && {relationships: entry.relationships}),
      ...(entry.unique_fields_combinations && {uniqueFieldsCombinations: entry.unique_fields_combinations}),
    }));

    return {
      isSuccess: true,
      data: dataSourceMetaData,
    };
  }

  /**
   * Deletes table data for a given connection ID and table names.
   *
   * This method deletes the table data for the specified connection ID and table names.
   * It returns a success message if the tables are deleted successfully.
   *
   * @param {string} connectionId - The connection ID for which to delete the table data.
   * @param {string[]} tableNames - The names of the tables to delete.
   * @returns {Promise<{isSuccess: boolean, message: string}>} - A promise that resolves to an object indicating the success of the deletion.
   */
  async deleteTableData(connectionId: string, tableNames: string[], layerNextConnectionId?: string) {
    if (!tableNames || tableNames.length === 0) {
      logger.error(
        `delete table data | TableDataService.deleteTableData | N/A | No table names provided for connectionId: ${connectionId}`,
      );
      return {
        isSuccess: false,
        message: `No table names provided for connectionId: ${connectionId}`,
      };
    }
    //remove tables from raw connection
    logger.info(
      `delete table data | TableDataService.deleteTableData | N/A | Deleting tables: ${tableNames} for connectionId: ${connectionId}`,
    );
    const response = await this.tableDataRepository.deleteMany({
      connectionId: connectionId,
      tableName: {$in: tableNames},
    });

    if (response.deletedCount === 0) {
      logger.error(
        `delete table data | TableDataService.deleteTableData | N/A | No tables deleted for connectionId: ${connectionId} for tableNames: ${tableNames}`,
      );
      return {
        isSuccess: false,
        message: `No tables deleted for connectionId: ${connectionId}`,
      };
    }

    //remove relationships from raw connection
    logger.info(
      `delete table data | TableDataService.deleteTableData | N/A | Deleting relationships for tables: ${tableNames} for connectionId: ${connectionId}`,
    );
    await this.tableDataRepository.updateManyPull(
      {connectionId: connectionId},
      {relationships: {parent_table: {$in: tableNames}}},
      [],
    );

    //remove data modelling cache
    logger.info(
      `delete table data | TableDataService.deleteTableData | N/A | Deleting data modelling cache for tables: ${tableNames} for connectionId: ${connectionId}`,
    );

    //delete if table is in tableName or parentTable or childTable and connectionId is matching
    await this.dataModellingCacheRepository.deleteMany({
      $or: [{tableName: {$in: tableNames}}, {parentTable: {$in: tableNames}}, {childTable: {$in: tableNames}}],
      connectionId: connectionId,
    });

    if (layerNextConnectionId) {
      logger.info(
        `delete table data | TableDataService.deleteTableData | N/A | Deleting tables: ${tableNames} for layerNextConnectionId: ${layerNextConnectionId}`,
      );
      //remove tables from layernext connection
      const layerNextResponse = await this.tableDataRepository.deleteMany({
        connectionId: layerNextConnectionId,
        tableName: {$in: tableNames},
      });
      if (layerNextResponse.deletedCount === 0) {
        logger.error(
          `delete table data | TableDataService.deleteTableData | N/A | No tables deleted for layerNextConnectionId: ${layerNextConnectionId} for tableNames: ${tableNames}`,
        );
        return {
          isSuccess: false,
          message: `No tables deleted for layerNextConnectionId: ${layerNextConnectionId}`,
        };
      }

      //remove relationships from layernext connection
      logger.info(
        `delete table data | TableDataService.deleteTableData | N/A | Deleting relationships for tables: ${tableNames} for layerNextConnectionId: ${layerNextConnectionId}`,
      );
      await this.tableDataRepository.updateManyPull(
        {connectionId: layerNextConnectionId},
        {relationships: {parent_table: {$in: tableNames}}},
        [],
      );
    }

    return {
      isSuccess: true,
      message: `Successfully deleted ${response.deletedCount} tables for connectionId: ${connectionId}`,
    };
  }
}

export const TABLE_DATA_SERVICE = BindingKey.create<TableDataService>('service.tableDataService');
