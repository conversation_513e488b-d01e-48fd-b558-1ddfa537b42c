/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * StorageOperationHandlerService is the parent class of the sub classes such as AwsS3StorageService
 */

/**
 * @class StorageOperationHandlerService
 * purpose of StorageOperationHandlerService service to save crawled data to db
 * @description StorageOperationHandlerService service use to save crawled data to db
 * <AUTHOR> channa
 */

import {BindingKey, /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {logger} from '../config';
import {DataCrawl, MetaData, SearchQueryRootGroup} from '../models';
import {DataCrawlRepository, MetaDataRepository, QueryOptionRepository, SystemDataRepository} from '../repositories';
import {BulkUpdateOneMetaDataFormat} from './object-meta-updater.service';

@injectable({scope: BindingScope.TRANSIENT})
export class StorageOperationHandlerService {
  constructor(/* Add @inject to inject parameters */
    @repository('QueryOptionRepository') private queryOptionRepository: QueryOptionRepository,
    @repository('MetaDataRepository') private metaDataRepository: MetaDataRepository,
    @repository('DataCrawlRepository') private dataCrawlRepository: DataCrawlRepository,
    @repository('SystemDataRepository') private systemDataRepository: SystemDataRepository,
  ) { }

  /**
    * Method to save metadata initially (populating from an existing storage)
    * called from relavent storage crawler
  **/
  async populateMetaDataFromStorage(metaDataList: Partial<MetaData>[]) {
    let updatesArr: BulkUpdateOneMetaDataFormat[] = []

    for (let metaDataItem of metaDataList) {
      if (metaDataItem.objectKey) {
        let tempUpdateQuery: BulkUpdateOneMetaDataFormat = {
          updateOne: {
            filter: {objectKey: metaDataItem.objectKey},
            update: {$set: metaDataItem},
            upsert: true
          }
        }
        updatesArr.push(tempUpdateQuery);
      }
    }

    await this.metaDataRepository.bulkWrite(updatesArr);

    //await this.metaDataRepository.bulkWrite(updatesArr.slice(0, 1)); // temporarily only update first 3 - testing
    //console.log(JSON.stringify(updatesArr.slice(0, 1), null, 2))
    return
  }

  /**
    * Method to save metadata initially (populating from an existing storage)
    * called from relavent storage crawler
  **/
  async insertMetaDataFromStorageToDB(metaDataList: Partial<MetaData>[]) {
    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.insertMetaDataFromStorageToDB | metaDataList length:${metaDataList.length} |inserting to db `)
    let updatesArr: {
      insertOne: {
        document: Partial<MetaData>;
      };
    }[] = []

    for (let metaDataItem of metaDataList) {
      if (metaDataItem.objectKey) {
        let tempUpdateQuery = {
          insertOne: {
            document: metaDataItem
          }
        }
        updatesArr.push(tempUpdateQuery);
      }

      //update query options
      let addedQueryKeysWithKeyGroup: {[k: string]: any[]} = {} //use to temparally keep added queryOptions to reduce DB calls for current loop
      let teamId: string | undefined = undefined
      let collectionId: string | undefined = undefined
      if (metaDataItem.teamId) teamId = metaDataItem.teamId as unknown as string
      if (metaDataItem.collectionId) collectionId = metaDataItem.collectionId as unknown as string
      // create a object from key,value pair
      let tempObj: {[k: string]: any} = {}
      tempObj.directory = metaDataItem.directory
      // call query update function
      await this.queryOptionRepository.updateQueryOption(SearchQueryRootGroup.METADATA, tempObj, true, teamId, collectionId, addedQueryKeysWithKeyGroup)
    }

    let response = await this.metaDataRepository.bulkWrite(updatesArr);
    //console.log("The write respones is:",response);
    //await this.metaDataRepository.bulkWrite(updatesArr.slice(0, 1)); // temporarily only update first 3 - testing
    //console.log(JSON.stringify(updatesArr.slice(0, 1), null, 2))
    return;
  }

  /**
    * Method to filter out already existing data
    * To insert only newly added items from subsequent crawl
  **/
  async filterOutExistingFiles(metaDataList: Partial<MetaData>[]) {
    if (metaDataList && metaDataList.length > 0) { /* then ok*/}
    else {
      logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | N/A | invalid or empty metaDataList `)
      return
    }
    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | metaDataList length:${metaDataList.length} | filter out existing started `)

    // get all ids
    let objectKeyArr = metaDataList.map(item => item.objectKey)

    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | objectKeyArr length:${objectKeyArr.length} | map object key arrays `)


    // find relevent objects from db
    let existingObjects = await this.metaDataRepository.find({
      where: {objectKey: {inq: objectKeyArr}},
      fields: {
        objectKey: true,
      }
    })
    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | existingObjects length:${existingObjects.length} | get existing objects `)

    let existingKeys = existingObjects.map(item => item.objectKey)

    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | existingKeys length:${existingKeys.length} | map existing key objectkey to array `)


    // console.log(`${objectKeyArr.length}`)
    console.log(`existing keys count = ${existingKeys.length}`)

    // filterOutExistingObjects
    let newFileList = metaDataList.filter(object => (!existingKeys.includes(object.objectKey)))

    logger.debug(`Crawl S3 for populate data (subsequent) | StorageOperationHandlerService.filterOutExistingFiles | filtered metaDataList length:${newFileList.length} | filter out existing done `)

    let newFileLength = newFileList.length
    let newFileSize = 0

    for (let file of newFileList) {
      newFileSize += file.fileSize || 0
    }

    // call insert method to insert only new files to db as metaData updates
    if (newFileLength > 0) await this.insertMetaDataFromStorageToDB(newFileList)


    return {newFileLength, newFileSize}
  }

  /**
   * use to retrive dataCrawl record from db by id
   * @param dataCrawlId dataCrawlId
   * @returns dataCrawl Record
   */
  async findDataCrawlRecord(dataCrawlId: string) {
    return await this.dataCrawlRepository.findById(dataCrawlId)
  }

  /**
   * user to update dataCrawk record by id
   * @param dataCrawlId id of dataCrawl Record
   * @param data Partial<DataCrawl>
   */
  async updateDataCrawlRecord(dataCrawlId: string, data: Partial<DataCrawl>) {
    await this.dataCrawlRepository.updateById(dataCrawlId, data)
  }

  /**
   * use to call updateOneSetData function in systemDataRepository
   */
  async updateStorageRecordInSystemData_Set(params: any, data: any, arrayFilter: any) {
    await this.systemDataRepository.updateOneSetData(params, data, arrayFilter)
  }

  /**
   * use to call updateOneIncData function in systemDataRepository
   */
  async updateStorageRecordInSystemData_Inc(params: any, data: any, arrayFilter: any) {
    await this.systemDataRepository.updateOneIncData(params, data, arrayFilter)
  }

  /**
   * to get collection name by collectionId
   * @param collectionId collectionId of the collection
   * @returns collection name
   */
  async getCollectionNameById(collectionId: string) {
    let collectionObj = await this.metaDataRepository.findById(collectionId)
    
    return collectionObj.name
  }

}
export const STORAGE_OPERATION_HANDLER_SERVICE =
  BindingKey.create<StorageOperationHandlerService>('service.storageOperationHandler');

