/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * AwsS3Storage service handle the request to the AWS cloud s3 bucket, a subclass of the StorageProviderService
 */

/**
 * @class AwsS3Storage service
 * purpose of AwsS3Storage service for handle AWS s3 bucket services
 * @description AWSCloud service handle the request to the AWS cloud s3 bucket, a subclass of the StorageProviderService
 * <AUTHOR> channa
 */

import {BindingKey, /* inject, */ BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import AWS from 'aws-sdk';
import {spawn} from 'child_process';
import _ from 'lodash';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {CrawlingStatus, DataCrawl} from '../models';
import {MetaDataRepository} from '../repositories';
import {AwsConfiguration} from '../settings/aws.configuration';
import {StorageOperationHandlerService} from './storage-operation-handler.service';
import {FileListParams, StorageFileHeader, StorageProviderService} from './storage-provider.service';

const AWS_ACCESS_KEY = AwsConfiguration.AWS_ACCESS_KEY;
const AWS_SECRET_KEY = AwsConfiguration.AWS_SECRET_KEY;
const AWS_REGION = AwsConfiguration.AWS_REGION;
var AWS_BUCKET_NAME = AwsConfiguration.AWS_BUCKET_NAME;
const AWS_BUCKET_NAME_PREFIX_KEY = AwsConfiguration.AWS_BUCKET_NAME_PREFIX_KEY;
const AWS_DATALAKE_MEDIA_BUCKET_NAME = AwsConfiguration.AWS_DATALAKE_MEDIA_BUCKET_NAME;

export const AWS_FILE_URL_EXPIRE_DURATION = 3 * 24 * 60 * 60; // in seconds
export const AWS_FILE_URL_RENEW_BEFORE_DURATION = 1 * 24 * 60 * 60; // in seconds
export const AWS_CHUNK_FILE_URL_RENEW_BEFORE_DURATION = 1 * 24 * 60 * 60;

export const AWS_UPLOAD_FILE_URL_VALID_DURATION = 12 * 60 * 60; // in seconds

@injectable({scope: BindingScope.TRANSIENT})
export class AwsS3StorageService extends StorageProviderService {
  constructor(
    storageOperationHandlerService: StorageOperationHandlerService,
    @repository('MetaDataRepository') public metaDataRepository: MetaDataRepository,
  ) {
    super(metaDataRepository);
    this.storageOperationHandlerService = storageOperationHandlerService;
  }

  /**
   * Use for Initialize the s3Bucket
   * @returns initialized s3Bucket
   */
  async initAWS() {
    const s3Bucket = new AWS.S3({
      accessKeyId: AWS_ACCESS_KEY,
      secretAccessKey: AWS_SECRET_KEY,
      signatureVersion: 'v4',
      region: AWS_REGION,
    });
    return s3Bucket;
  }

  /**
   * Use to upload single file from the local storage to AWS S3 bucket
   * @param filePath {string} path of the file
   * @param key {string} AWS S3 key
   */
  async uploadFileFromBuffer(buffer: any, key: string, bucket: string) {
    let s3Bucket = await this.initAWS();

    //logger.debug(filePath)
    const params = {
      Bucket: bucket ? bucket : 'testbucketdatalake', // pass your bucket name
      Key: key, // file will be saved as key
      Body: buffer,
    };
    await s3Bucket.upload(params).promise();
  }

  // /**
  //  * Find the file type by extention of its name
  //  * @param fileName {string} name of file with extention
  //  * @returns file type
  //  */
  // async getContentTypeForS3Object(fileName: string) {

  //   let fileExtention = fileName.split('.').pop()
  //   fileExtention = fileExtention?.toLowerCase()
  //   if (fileExtention == 'jpeg' || fileExtention == 'png' || fileExtention == 'jpg') {
  //     return ContentType.IMAGE
  //   }
  //   else if (fileExtention == 'mp4' || fileExtention == 'avi') {
  //     return ContentType.VIDEO
  //   }
  //   else {
  //     return ContentType.UNSUPPORTED
  //   }
  // }

  /**
   * get metaData from s3 for given object
   * @params objectData
   * @returns parent directory list
   */
  async getMetaDataAttribsOfObject(s3Bucket: AWS.S3, objectData: S3MetaDataFormat) {
    // console.log(`ObjectData: ${JSON.stringify(objectData, null, 2)}`)

    let params: AWS.S3.GetObjectAttributesRequest = {
      Bucket: AWS_BUCKET_NAME || '',
      Key: objectData.key,
      ObjectAttributes: ['StorageClass', 'ObjectParts', 'ObjectSize'],
    };
    let res = s3Bucket.getObjectAttributes(params).promise();
    res.then((res: any) => {
      // console.log(`attributes`,JSON.stringify(res, null, 2))
      // console.log(`----------------------------------------------------------------------------------------------------------------------`)
    });
  }

  /**
   * Use for generate Object url for access annotation video
   * @param key {string} key of the amazon media file
   * @param expires expire time in seconds
   * @returns Object url with expire settings
   */
  async generateObjectUrl(key: string, expires = AWS_FILE_URL_EXPIRE_DURATION, bucket?: string) {
    const s3Bucket = this.initAWS();
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME,
      Expires: expires,
      Key: key,
      ResponseContentDisposition: 'attachment',
    };
    let preSigneUrl = (await s3Bucket)
      .getSignedUrlPromise('getObject', params)
      .then((res: any) => res)
      .catch((err: any) => err);
    return preSigneUrl;
  }

  /**
   * Use for generate Object url for upload media to s3
   * @param key {string} key of the amazon media file to be created
   * @param expires expire time in seconds
   * @returns Object url with expire settings
   */
  async generateWriteFileUrl(key: string, expires = AWS_UPLOAD_FILE_URL_VALID_DURATION, bucket?: string) {
    const s3Bucket = this.initAWS();
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME,
      Expires: expires,
      Key: key,
    };
    let preSignedUrl = (await s3Bucket)
      .getSignedUrlPromise('putObject', params)
      .then((res: any) => res)
      .catch((err: any) => err);
    return preSignedUrl;
  }

  /**
   * Use for handle intialize uploading multipart files to s3 from frontend
   */
  async initializeMultipartUpload(key: string, expires = AWS_UPLOAD_FILE_URL_VALID_DURATION, bucket?: string) {
    const s3Bucket = this.initAWS();
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME || 'layerx-datalake-dev',
      // Expires: expires,
      Key: key,
    };
    let multipartUpload = await (
      await s3Bucket
    )
      .createMultipartUpload(params)
      .promise()
      .then((res: any) => res);
    return {fileId: multipartUpload.UploadId, fileKey: multipartUpload.Key, isExisting: false};
  }

  /**
   * Use for handle chunk list urls for uploading multipart files to s3 from frontend
   */
  async generateMultipartPreSignedUrls(
    key: string,
    fileId: number,
    parts: number,
    bucket?: string,
    contentType?: string,
    isDisableMultipart?: boolean,
  ) {
    const s3Bucket = this.initAWS();
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME || 'layerx-datalake-dev',
      Expires: AWS_CHUNK_FILE_URL_RENEW_BEFORE_DURATION,
      Key: key,
      UploadId: fileId,
    };

    let promises = [];
    for (let index = 0; index < parts; index++) {
      promises.push(
        (await s3Bucket).getSignedUrlPromise('uploadPart', {
          ...params,
          PartNumber: index + 1,
        }),
      );
    }
    let signedUrls = await Promise.all(promises);

    // assign to each URL the index of the part to which it corresponds
    let partSignedUrlList = await signedUrls.map((signedUrl, index) => {
      return {
        signedUrl: signedUrl,
        PartNumber: index + 1,
      };
    });

    // let multipartUpload = await (await s3Bucket)
    //   .createMultipartUpload(params).promise()
    //   .then((res: any) => res)

    return {parts: partSignedUrlList};
  }

  /**
   * Use for finalize multipart file upload to s3 from frontend
   */
  async finalizeMultipartUpload(
    key: string,
    fileId: string,
    parts: any,
    bucket?: string,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    currentUserProfile?: UserProfileDetailed,
  ) {
    logger.debug(`finalizeMultipartUpload | ${fileId} | ${JSON.stringify(parts)} | ${bucket} | ${finalizeUrl}`);
    let bucketName = bucket ? bucket : AWS_BUCKET_NAME || 'layerx-datalake-dev';
    const s3Bucket = this.initAWS();
    const params = {
      Bucket: bucketName,
      // Expires: expires,
      Key: key,
      UploadId: fileId,
      MultipartUpload: {
        // ordering the parts to make sure they are in the right order
        Parts: _.orderBy(parts, ['PartNumber'], ['asc']),
      },
    };
    logger.debug(`finalizeMultipartUpload | ${bucketName}`);
    try {
      let multipartUpload = await (
        await s3Bucket
      )
        .completeMultipartUpload(params)
        .promise()
        .then((res: any) => res);

      // completeMultipartUploadOutput.Location represents the URL to the resource just uploaded to the cloud storage
      logger.info(
        `File chunk upload generate urls finalize upload | AwsS3StorageService.finalizeMultipartUpload | fileKey:${multipartUpload.Location} | upload finalized`,
      );
      return {isSuccess: true};
    } catch (error) {
      logger.error(
        `File chunk upload generate urls finalize upload Failed | AwsS3StorageService.finalizeMultipartUpload | key: ${key} | upload failde: ${error}`,
      );
      return {isSuccess: false};
    }
  }

  /**
   * Use to get summary of aws s3 bucket via shell script usimg s3cmd tool
   * @param dataCrawlId id of relavent DataCrawl record
   * @returns none, DataCrawl DB record will be updated
   */
  async getCloudStorageSummary(dataCrawlId: string, bucket?: string) {
    logger.debug(`Crawl storage for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | started`);

    let bucketName = bucket ? bucket : AWS_BUCKET_NAME;
    let s3BucketPath = '';
    if (!bucketName) {
      logger.error(
        `Crawl S3 for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | not found - bucket name `,
      );
      return;
    } else {
      s3BucketPath = s3BucketPath + bucketName;
      if (AWS_BUCKET_NAME_PREFIX_KEY) {
        s3BucketPath = s3BucketPath + '/' + AWS_BUCKET_NAME_PREFIX_KEY;
      }
    }
    const awsCliProcess = spawn('sh', ['scripts/aws-s3-summary.sh', bucketName || '']);

    awsCliProcess.stdout.on('data', async data => {
      logger.debug(`Crawl storage for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | stdout`);
      logger.info(Buffer.from(data).toString());
      let output = Buffer.from(data).toString();
      let words = output.split(' ');

      let storageSummary: Partial<DataCrawl> = {
        totalFileCount: undefined,
        totalFileSize: undefined,
        status: CrawlingStatus.FILES_CRAWLING,
        storageSummaryReceivedAt: new Date(),
      };

      if (Number(words[0]) > 0) {
        storageSummary.totalFileSize = Number(words[0]);
      }
      if (Number(words[1]) > 0) {
        storageSummary.totalFileCount = Number(words[1]);
      }
      // update crawling record statics
      let dataCrawlRecord = await this.storageOperationHandlerService.findDataCrawlRecord(dataCrawlId);
      if (dataCrawlRecord) {
        if (dataCrawlRecord.status) {
          if (dataCrawlRecord.status > CrawlingStatus.FILES_CRAWLING) {
            storageSummary.status = dataCrawlRecord.status;
          }
        }
      }
      this.storageOperationHandlerService.updateDataCrawlRecord(dataCrawlId, storageSummary);
    });

    awsCliProcess.stderr.on('data', data => {
      logger.error(`Crawl storage for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | stderr`);
      logger.debug(Buffer.from(data).toString());
    });

    awsCliProcess.on('error', error => {
      logger.error(
        `Crawl storage for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | error: ${error.message}`,
      );
    });

    awsCliProcess.on('close', code => {
      logger.debug(
        `Crawl storage for populate data | AwsS3StorageService.getCloudStorageSummary | N/A | aws cli child process exited with code ${code}`,
      );
    });
  }

  /**
   * Check if the object key exists
   * @param {string} key object key
   * @param {string} bucket bucket name
   * @returns
   */
  async checkObjectKey(key: string, bucket?: string) {
    const s3Bucket = await this.initAWS();
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME || 'layerx-datalake-dev',
      Key: key,
    };

    try {
      await s3Bucket.headObject(params).promise();

      return {
        isExist: true,
      };
    } catch (error) {
      if (error.name === 'NotFound') {
        logger.error('object key not found');

        return {
          isExist: false,
          errorOccur: false,
        };
      } else {
        logger.error('Error occur in check', error);
        return {
          isExist: false,
          errorOccur: true,
        };
      }
    }
  }

  /**
   * Get file size in bytes from S3 bucket for a given object
   */
  async getFileSize(key: string, bucket?: string): Promise<number> {
    const s3Bucket = await this.initAWS();
    //Get the size of the object in bytes from S3 bucket without downloading data
    const params = {
      Bucket: bucket ? bucket : AWS_BUCKET_NAME || 'layerx-datalake-dev',
      Key: key,
    };

    try {
      //Get the size of the object in bytes from S3 bucket without downloading data
      const {ContentLength} = await s3Bucket.headObject(params).promise();
      let dataBytes = ContentLength || 0;
      return dataBytes;
    } catch (error) {
      if (error.name === 'NotFound') {
        logger.error('object key not found');
        return -1;
      } else {
        logger.error('Error occur in check', error);
        return -1;
      }
    }
  }

  /**
   * Use for deleting object from s3 bucket using objectKey
   * @param key {string} key of the aws s3 media file
   * @returns delete status
   */
  async deleteObject(key: string, bucket?: string) {
    const s3Bucket = await this.initAWS();

    let bucketName = bucket ? bucket : AWS_BUCKET_NAME;

    if (!bucketName) {
      logger.error(
        `File delete failed | AwsS3StorageService.deleteObject | key: ${key} | delete failed: Bucket name missing`,
      );
      return {
        isSuccess: false,
        message: 'Bucket name missing',
      };
    }

    try {
      await s3Bucket.deleteObject({Bucket: bucketName, Key: key}).promise();
      return {
        isSuccess: true,
        message: '',
      };
    } catch (error) {
      logger.error(`File delete failed | AwsS3StorageService.deleteObject | key: ${key} | delete failed: ${error}`);
      return {
        isSuccess: false,
        message: error?.message ? error.message : error,
      };
    }
  }

  /**
   * Implementation of StorageProvider's getFileList method for AWS S3
   * @param params {FileListParams} parameters for listing files for the next page
   * @returns list of information objects for each file in the bucket
   */
  async getFileList(params: FileListParams) {
    let s3Bucket = await this.initAWS();
    let res;
    let returnData = {
      isSuccess: false,
      errMessage: 'File list failed',
      fileList: <StorageFileHeader[]>[],
      nextPageRef: {
        NextContinuationToken: '',
        ContinuationToken: '',
      },
      IsTruncated: false,
    };

    try {
      let awsQueryParams = {
        Bucket: params.Bucket ? params.Bucket : AWS_BUCKET_NAME || '',
        ContinuationToken: params.ContinuationToken,
        Prefix: params.Prefix,
      };
      res = await s3Bucket.listObjectsV2(awsQueryParams).promise();
      let s3ObjList: AWS.S3.ObjectList | undefined = res.Contents;
      if (s3ObjList == undefined) {
        return returnData;
      }
      let fileList: StorageFileHeader[] = s3ObjList.map((item: any) => {
        let splitFileObjectKey = item.Key.split('/');
        return {
          fileKey: item.Key,
          fileName: splitFileObjectKey[splitFileObjectKey.length - 1],
          fileLastModified: item.LastModified,
          fileSize: item.Size,
        };
      });

      return {
        isSuccess: true,
        errMessage: '',
        fileList: fileList,
        nextPageRef: {
          NextContinuationToken: res.NextContinuationToken || '',
          ContinuationToken: res.ContinuationToken || '',
        },
        IsTruncated: res.IsTruncated || false,
      };
    } catch (error) {
      logger.error(`File list failed | AwsS3StorageService.getFileList | list failed: ${error}`);
      returnData.errMessage = error?.message ? error.message : 'File list failed';
      return returnData;
    }
  }
}
export const AWS_S3_STORAGE_SERVICE = BindingKey.create<AwsS3StorageService>('service.awsS3Storage');

export interface S3MetaDataFormat {
  //  teamId: string,
  key: string;
  parentKey: string;
  name: string;
  objectContentType: number;
  lastModified: any;
  size: any;
}
