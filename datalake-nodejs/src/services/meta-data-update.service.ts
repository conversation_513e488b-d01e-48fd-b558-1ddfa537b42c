/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the MetaData insert, modify related logics
 */

/**
 * @class MetaDataUpdateService
 * purpose of this service is to perfome the MetaData update analytics calculation with python script
 * @description MetaData analytics calculation with python script
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import Axios from 'axios';
import {ObjectId} from 'bson';
import {spawn} from 'child_process';
import dotenv from 'dotenv';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {OperationMode, OperationType} from '../models';
import {JobStatus} from '../models/job.model';
import {MetaDataRepository, MetaDataUpdateRepository, RemovedMetaDataUpdateRepository} from '../repositories';
import {UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {JOB_SERVICE, JobService} from './job.service';
dotenv.config();

@injectable({scope: BindingScope.TRANSIENT})
export class MetaDataUpdateService {
  constructor(
    @repository(MetaDataUpdateRepository) public metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @repository(RemovedMetaDataUpdateRepository)
    public removedMetaDataUpdateRepository: RemovedMetaDataUpdateRepository,
    @inject(JOB_SERVICE) private jobService: JobService,
  ) {}

  async calcMetaDataUpdateAnalytics() {
    // let params = [
    //   {$match: {analyticsPending: true}},
    //   {$group: {_id: "$operationId"}}
    // ]
    // let operationIdList: {_id: string}[] = await this.metaDataUpdateRepository.aggregate(params);
    // logger.debug(operationIdList);
    //for (let operation of operationIdList) {
    const pythonProcess = spawn('python3', ['../datalake-python-processor/analytics.py'], {env: {...process.env}});

    pythonProcess.stdout.on('data', data => {
      logger.debug(
        `Process for calculate analytics for operation | MetaDataUpdateService.calcMetaDataUpdateAnalytics | N/A | data recieved from python media processor`,
      );
      logger.debug(Buffer.from(data).toString());
    });

    pythonProcess.stderr.on('data', data => {
      logger.warn(
        `Process for calculate analytics for operation | MetaDataUpdateService.calcMetaDataUpdateAnalytics | N/A | error - python media processor`,
      );
      logger.debug(Buffer.from(data).toString());
    });
    //}
  }

  /**
   * Use to remove annotations of relevent collection and operationId
   * @param collectionId {string} id of the collection
   * @param operationId {string} operationId
   */
  async deleteCollectionAnnotation(
    collectionId: string,
    operationId: string,
    sessionId?: string,
    currentUserProfile?: UserProfileDetailed,
  ) {
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let collectionObj = await this.metaDataRepository.findById(collectionId);
      let allowedUserIdList = collectionObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    logger.debug(
      `Process for delete annotation of collection & operation started| MetaDataUpdateService.deleteCollectionAnnotation | N/A | delete annotation of collection: ${collectionId} & operationId: ${operationId}`,
    );

    if (sessionId)
      await this.jobService.updateAnnotationRemoveJobEntry(
        sessionId,
        collectionId,
        operationId,
        JobStatus.queued,
        OperationType.ANNOTATION,
        undefined,
        0,
        currentUserProfile?.teamId,
        currentUserProfile?.id,
        currentUserProfile?.name,
      );

    try {
      //use for copy annoation documents to RemovedMetaDataUpdate collection
      let param = [
        {$match: {collectionId: new ObjectId(collectionId)}},
        {
          $addFields: {
            operationIdString: {$toString: '$operationId'},
          },
        },
        {$match: {operationIdString: operationId}},
        {$merge: 'RemovedMetaDataUpdate'},
      ];
      await this.metaDataUpdateRepository.aggregate(param);

      //use to remove model run annotations
      await this.metaDataUpdateRepository.directRemove({
        collectionId: new ObjectId(collectionId),
        operationId: operationId,
      });

      //use to remove ground truth annotations
      if (ObjectId.isValid(operationId)) {
        await this.metaDataUpdateRepository.directRemove({
          collectionId: new ObjectId(collectionId),
          operationId: new ObjectId(operationId),
        });
        this.updateStudioTaskProjectStats(operationId);
        if (sessionId)
          await this.jobService.updateAnnotationRemoveJobEntry(
            sessionId,
            collectionId,
            operationId,
            JobStatus.inProgress,
            OperationType.ANNOTATION,
            OperationMode.HUMAN,
            0,
            currentUserProfile?.teamId,
            currentUserProfile?.id,
            currentUserProfile?.name,
          );
      } else {
        if (sessionId)
          await this.jobService.updateAnnotationRemoveJobEntry(
            sessionId,
            collectionId,
            operationId,
            JobStatus.inProgress,
            OperationType.ANNOTATION,
            OperationMode.HUMAN,
            0,
            currentUserProfile?.teamId,
            currentUserProfile?.id,
            currentUserProfile?.name,
          );
      }

      //use to update flags to correct the new data of metadata documents
      this.metaDataRepository.updateManySet(
        {collectionId: new ObjectId(collectionId)},
        {
          statPending: true,
          statPendingAt: new Date(),
          annotationStatPending: true,
          datasetStatPending: true,
          isVerificationStatusPending: true,
          updatedAt: new Date(),
        },
        [],
      );
      if (sessionId)
        await this.jobService.updateAnnotationRemoveJobEntry(
          sessionId,
          collectionId,
          operationId,
          JobStatus.completed,
          OperationType.ANNOTATION,
          OperationMode.HUMAN,
          100,
          currentUserProfile?.teamId,
          currentUserProfile?.id,
          currentUserProfile?.name,
        );
      return {isSuccess: true};
    } catch (error) {
      logger.error(
        `Process for delete annotation of collection & operation | MetaDataUpdateService.deleteCollectionAnnotation | N/A | error occured: ${collectionId} & operationId: ${operationId} error: ${error}`,
      );
      if (sessionId)
        this.jobService.updateAnnotationRemoveJobEntry(
          sessionId,
          collectionId,
          operationId,
          JobStatus.failed,
          OperationType.ANNOTATION,
          OperationMode.HUMAN,
          100,
          currentUserProfile?.teamId,
          currentUserProfile?.id,
          currentUserProfile?.name,
        );
      return {isSuccess: false};
    }
  }

  async updateStudioTaskProjectStats(projectId: string) {
    try {
      let url = `${process.env.ANNO_INTERNAL_SERVER}/internal/project/statUpdate`;

      await Axios({
        url,
        method: 'POST',
        data: {
          projectId: projectId,
        },
      });

      logger.info(
        `MetaDataUpdateService | SearchQueryBuilderService.getAnnotationProjectInfoList | N/A | Update project and task stats after annotation delete success`,
      );
    } catch (err) {
      logger.error(
        `MetaDataUpdateService | SearchQueryBuilderService.getAnnotationProjectInfoList | N/A | Update project and task stats after annotation delete failed`,
      );
    }
  }
}

export const META_DATA_UPDATE_SERVICE = BindingKey.create<MetaDataUpdateService>('service.metaDataUpdateService');
