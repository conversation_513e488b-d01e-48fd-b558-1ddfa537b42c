/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Service class describes the business logic for the KnowledgeSource model
 */

/**
 * @class KnowledgeSourceService
 * @description Service class describes the business logic for the KnowledgeSource model
 * <AUTHOR>
 */

import {BindingKey, BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {default as Axios} from 'axios';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {KnowledgeSourceRefreshStatus, KnowledgeSourceStatus, KnowledgeSourceType} from '../models';
import {BusinessRuleRepository} from '../repositories/business-rule.repository';
import {KnowledgeSourceRepository} from '../repositories/knowledge-source.repository';
import {KnowledgeRepository} from '../repositories/knowledge.repository';
import {FLOWS} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

const PYTHON_HOST = process.env.PYTHON_BASE_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class KnowledgeSourceService {
  constructor(
    @repository(KnowledgeSourceRepository)
    private knowledgeSourceRepository: KnowledgeSourceRepository,
    @repository(BusinessRuleRepository)
    private businessRuleRepository: BusinessRuleRepository,
    @repository(KnowledgeRepository)
    private knowledgeRepository: KnowledgeRepository,
  ) {}

  /**
   * Create a new knowledge source
   * @param name - The name of the knowledge source
   * @param type - The type of the knowledge source
   * @param credentials - The credentials of the knowledge source
   * @param currentUserProfile - The current user profile
   */
  async createKnowledgeSource(
    name: string,
    type: KnowledgeSourceType,
    credentials: object,
    currentUserProfile: UserProfileDetailed,
  ) {
    try {
      let data = {
        name: name,
        type: type,
        status: KnowledgeSourceStatus.INITIALIZING,
        refreshStatus: KnowledgeSourceRefreshStatus.UNABLE_TO_REFRESH,
        createdAt: new Date(),
        credentials: credentials,
        addedById: currentUserProfile.id,
        addedBy: currentUserProfile.name,
        updatedAt: new Date(),
        updatedBy: currentUserProfile.name,
        updatedById: currentUserProfile.id,
      };

      let knowledgeSource = await this.knowledgeSourceRepository.create(data);
      if (knowledgeSource && knowledgeSource._id) {
        //trigger refreshing
        await this.refreshKnowledgeSource(knowledgeSource._id);

        return {
          isSuccess: true,
          data: {
            id: knowledgeSource._id,
            name: knowledgeSource.name,
            type: knowledgeSource.type,
            status: knowledgeSource.status,
            refreshStatus: knowledgeSource.refreshStatus,
            createdAt: knowledgeSource.createdAt,
            addedBy: knowledgeSource.addedBy,
            businessRuleCount: knowledgeSource.businessRuleCount,
          },
        };
      } else {
        return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_CREATE_FAILED}`};
      }
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.createKnowledgeSource | N/A | Error creating knowledge source: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_CREATE_FAILED}`};
    }
  }

  /**
   * Check the connection of the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  private async checkKnowledgeSourceConnection(knowledgeSourceId: string) {
    //call python service to check connection
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.checkKnowledgeSourceConnection | N/A | Checking knowledge source connection for knowledge source id: ${knowledgeSourceId}`,
    );

    await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
      status: KnowledgeSourceStatus.INITIALIZING,
    });

    try {
      const url = `${PYTHON_HOST}/internal/knowledge-process/connect`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {knowledgeSourceId: knowledgeSourceId},
      });

      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.checkKnowledgeSourceConnection | N/A | Python service called successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          response.data,
        )}`,
      );

      let res = response.data;

      if (res.isSuccess) {
        await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
          status: KnowledgeSourceStatus.CONNECTED,
        });
        logger.info(
          `${
            FLOWS.KNOWLEDGE_SOURCE
          } | KnowledgeSourceService.checkKnowledgeSourceConnection | N/A | Knowledge source connection checked successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
            res.data,
          )}`,
        );
      } else {
        logger.error(
          `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.checkKnowledgeSourceConnection | N/A | Failed to check knowledge source connection for knowledge source id: ${knowledgeSourceId}, error: ${res.message}`,
        );
        await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
          status: KnowledgeSourceStatus.FAILED,
        });
      }

      return res;
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.checkKnowledgeSourceConnection | N/A | Error checking knowledge source connection for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
      );
      await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
        status: KnowledgeSourceStatus.FAILED,
      });
      return {isSuccess: false, message: error};
    }
  }

  /**
   * Crawl the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  private async crawlKnowledgeSource(knowledgeSourceId: string) {
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.crawlKnowledgeSource | N/A | Crawling knowledge source for knowledge source id: ${knowledgeSourceId}`,
    );

    try {
      const url = `${PYTHON_HOST}/internal/knowledge-process/retrieve_and_save_rules`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {knowledgeSourceId: knowledgeSourceId},
      });

      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.crawlKnowledgeSource | N/A | Crawling knowledge source for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          response.data,
        )}`,
      );

      return response.data;
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.crawlKnowledgeSource | N/A | Error crawling knowledge source for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
      );
      return {isSuccess: false, message: error};
    }
  }

  /**
   * Get the list of knowledge sources
   */
  async getKnowledgeSourceList() {
    let knowledgeSourceListResponse: {
      id?: string;
      name: string;
      type: string;
      status: number;
      refreshStatus: number;
      createdAt: Date;
      addedBy: string;
      businessRuleCount?: number;
    }[] = [];

    try {
      let knowledgeSources = await this.knowledgeSourceRepository.find({order: ['updatedAt DESC', '_id DESC']});

      knowledgeSourceListResponse = knowledgeSources.map(knowledgeSource => {
        return {
          id: knowledgeSource._id,
          name: knowledgeSource.name,
          type: knowledgeSource.type,
          status: knowledgeSource.status,
          refreshStatus: knowledgeSource.refreshStatus,
          createdAt: knowledgeSource.updatedAt,
          addedBy: knowledgeSource.updatedBy,
          businessRuleCount: knowledgeSource.businessRuleCount,
        };
      });

      return {isSuccess: true, data: knowledgeSourceListResponse};
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.getKnowledgeSourceList | N/A |Error fetching knowledge source list: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_LIST_FAILED}`};
    }
  }

  /**
   * Get the details of the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  async getKnowledgeSourceDetails(knowledgeSourceId: string) {
    try {
      let knowledgeSource = await this.knowledgeSourceRepository.findById(knowledgeSourceId);
      return {
        isSuccess: true,
        data: {
          sourceName: knowledgeSource.name,
          type: knowledgeSource.type,
          credentials: knowledgeSource.credentials,
        },
      };
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.getKnowledgeSourceDetails | N/A | Error fetching knowledge source details for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_DETAILS_FAILED}`};
    }
  }

  /**
   * Refresh the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  async refreshKnowledgeSource(knowledgeSourceId: string) {
    try {
      let source = await this.knowledgeSourceRepository.findById(knowledgeSourceId);

      await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
        refreshStatus: KnowledgeSourceRefreshStatus.REFRESHING,
      });

      this.refreshKnowledgeSourceInBackground(knowledgeSourceId)
        .then(res => {
          logger.info(
            `${
              FLOWS.KNOWLEDGE_SOURCE
            } | KnowledgeSourceService.refreshKnowledgeSource | N/A | Knowledge source refreshed successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
              res,
            )}`,
          );
        })
        .catch(error => {
          logger.error(
            `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSource | N/A | Failed to refresh knowledge source in background for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
          );
        })
        .finally(() => {
          //update knowledge source refresh status
          this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
            refreshStatus: KnowledgeSourceRefreshStatus.REFRESHED,
          });
        });

      return {
        isSuccess: true,
        message: 'Knowledge source refreshing started successfully',
        data: {message: 'Knowledge source refreshing started successfully'},
      };
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSource | N/A |Error refreshing knowledge source: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_REFRESH_FAILED}`};
    }
  }

  /**
   * Refresh the knowledge source in background
   * @param knowledgeSourceId - The id of the knowledge source
   */
  private async refreshKnowledgeSourceInBackground(knowledgeSourceId: string) {
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Refreshing knowledge source in background for knowledge source id: ${knowledgeSourceId}`,
    );

    //call python service to check connection

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Checking knowledge source connection for knowledge source id: ${knowledgeSourceId}`,
    );
    let connectionCheckRes = await this.checkKnowledgeSourceConnection(knowledgeSourceId);

    if (!connectionCheckRes.isSuccess) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Connection check failed for knowledge source id: ${knowledgeSourceId}, error: ${connectionCheckRes.message}`,
      );
      return {isSuccess: false, message: connectionCheckRes.message};
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Connection check passed for knowledge source id: ${knowledgeSourceId}`,
    );

    //start crawling and update records in Knowledge Collection
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Starting crawling for knowledge source id: ${knowledgeSourceId}`,
    );

    let crawlRes = await this.crawlKnowledgeSource(knowledgeSourceId);

    if (!crawlRes.isSuccess) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Crawling failed for knowledge source id: ${knowledgeSourceId}, error: ${crawlRes.message}`,
      );
      return {isSuccess: false, message: crawlRes.message};
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Crawling passed for knowledge source id: ${knowledgeSourceId}`,
    );

    //todo: update metadata from Knowledge records

    //update business rules from Knowledge records
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Updating business rules for knowledge source id: ${knowledgeSourceId}`,
    );
    let updateBusinessRulesRes = await this.updateBusinessRulesFromKnowledgeRecords(knowledgeSourceId);

    if (!updateBusinessRulesRes.isSuccess) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Failed to update business rules for knowledge source id: ${knowledgeSourceId}, error: ${updateBusinessRulesRes.message}`,
      );
      return {isSuccess: false, message: updateBusinessRulesRes.message};
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.refreshKnowledgeSourceInBackground | N/A | Business rules updated successfully for knowledge source id: ${knowledgeSourceId}`,
    );

    return {isSuccess: true, message: 'Knowledge source refreshed successfully'};
  }

  /**
   * Update the business rules from the knowledge records
   * @param knowledgeSourceId - The id of the knowledge source
   */
  private async updateBusinessRulesFromKnowledgeRecords(knowledgeSourceId: string) {
    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateBusinessRulesFromKnowledgeRecords | N/A | Calling python service to update business rules for knowledge source id: ${knowledgeSourceId}`,
    );
    try {
      const url = `${PYTHON_HOST}/internal/knowledge-process/business-rules`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          knowledgeSourceId: knowledgeSourceId,
        },
      });

      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.updateBusinessRulesFromKnowledgeRecords | N/A | Python service called successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          response.data,
        )}`,
      );

      return response.data;
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateBusinessRulesFromKnowledgeRecords | N/A | Error calling python service to update business rules for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
      );
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateBusinessRulesFromKnowledgeRecords | N/A | source id: ${knowledgeSourceId}, Error code: ${error?.code}`,
      );
      return {isSuccess: false, message: error};
    }
  }

  /**
   * Delete the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  async deleteKnowledgeSource(knowledgeSourceId: string) {
    try {
      //remove source from knowledge source

      let res = await this.knowledgeSourceRepository.deleteById(knowledgeSourceId);
      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.deleteKnowledgeSource | N/A | Knowledge source deleted successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          res,
        )}`,
      );

      //remove records from knowledge collection
      let res1 = await this.knowledgeRepository.deleteAll({
        knowledgeSourceId: knowledgeSourceId,
      });
      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.deleteKnowledgeSource | N/A | Knowledge records deleted successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          res1,
        )}`,
      );

      //remove records from business rules collection
      let res2 = await this.businessRuleRepository.deleteAll({
        knowledge_source_id: knowledgeSourceId,
      });
      logger.info(
        `${
          FLOWS.KNOWLEDGE_SOURCE
        } | KnowledgeSourceService.deleteKnowledgeSource | N/A | Business rules deleted successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
          res2,
        )}`,
      );

      logger.info(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.deleteKnowledgeSource | N/A | Knowledge source deleted successfully for knowledge source id: ${knowledgeSourceId}`,
      );

      return {
        isSuccess: true,
        message: 'Knowledge source deleted successfully',
      };
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.deleteKnowledgeSource | N/A | Error deleting knowledge source: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_DELETE_FAILED}`};
    }
  }

  /**
   * Update the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   * @param name - The name of the knowledge source
   * @param type - The type of the knowledge source
   * @param credentials - The credentials of the knowledge source
   * @param currentUserProfile - The current user profile
   */
  async updateKnowledgeSource(
    knowledgeSourceId: string,
    name: string,
    type: KnowledgeSourceType,
    credentials: object,
    currentUserProfile: UserProfileDetailed,
  ) {
    try {
      let knowledgeSource = await this.knowledgeSourceRepository.findById(knowledgeSourceId);

      await this.knowledgeSourceRepository.updateById(knowledgeSourceId, {
        name: name,
        type: type,
        status: KnowledgeSourceStatus.INITIALIZING,
        refreshStatus: KnowledgeSourceRefreshStatus.UNABLE_TO_REFRESH,
        credentials: credentials,
        updatedAt: new Date(),
        updatedBy: currentUserProfile.name,
        updatedById: currentUserProfile.id,
      });

      //call python service to check connection
      this.checkKnowledgeSourceConnection(knowledgeSourceId)
        .then(res => {
          logger.info(
            `${
              FLOWS.KNOWLEDGE_SOURCE
            } | KnowledgeSourceService.updateKnowledgeSource | N/A | Knowledge source connection checked successfully for knowledge source id: ${knowledgeSourceId}, response: ${JSON.stringify(
              res,
            )}`,
          );
        })
        .catch(error => {
          logger.error(
            `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateKnowledgeSource | N/A | Failed to check knowledge source connection for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
          );
        })
        .finally(() => {
          //update knowledge source refresh status
          this.knowledgeSourceRepository
            .updateById(knowledgeSourceId, {
              refreshStatus: KnowledgeSourceRefreshStatus.REFRESHED,
            })
            .catch(error => {
              logger.error(
                `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateKnowledgeSource | N/A | Failed to update knowledge source refresh status for knowledge source id: ${knowledgeSourceId}, error: ${error}`,
              );
            });
        });

      return {isSuccess: true, message: 'Knowledge source updated successfully'};
    } catch (error) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceService.updateKnowledgeSource | N/A | Error updating knowledge source: ${error}`,
      );
      return {isSuccess: false, message: `${DatalakeUserMessages.KNOWLEDGE_SOURCE_UPDATE_FAILED}`};
    }
  }
}

export const KNOWLEDGE_SOURCE_SERVICE = BindingKey.create<KnowledgeSourceService>('service.knowledgeSourceService');
