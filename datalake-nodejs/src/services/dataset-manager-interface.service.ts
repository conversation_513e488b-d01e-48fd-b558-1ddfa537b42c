/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 */

/**
 * @class DatasetManagerInterfaceService
 * @description Handle processes requested by dataset manager
 */

import {BindingKey, BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AugmentedInfoWithMetaList,
  CollectionType,
  ContentType,
  DatasetLabelAggregate,
  DatasetSplitCount,
  DatasetSplitType,
  DatasetUpdateFormat,
  MetaData,
  OBJECT_STATUS,
  ObjectCount,
  SearchQueryRootGroup,
  SearchQuerySubGroup,
} from '../models';
import {QueryOptionRepository} from '../repositories';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {FLOWS, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';

@injectable({scope: BindingScope.TRANSIENT})
export class DatasetManagerInterfaceService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @repository(QueryOptionRepository) private queryOptionRepository: QueryOptionRepository,
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
  ) {}

  /**
   * Use to create a metadata record for a dataset group
   * Handle creating a new dataset
   * @param datasetData : DatasetUpdateFormat - selection tag an version details
   * @returns
   */
  async createNewDataset(datasetData: DatasetUpdateFormat, currentUserProfile: UserProfileDetailed) {
    // create metadata for new dataset (type:dataset)
    let datasetMetadata: Partial<MetaData> = {
      datasetGroupId: datasetData.datasetGroupId,
      datasetVersionList: [
        {
          datasetGroupName: datasetData.datasetName,
          datasetGroupId: datasetData.datasetGroupId,
        },
      ],
    };
    let allowedUserId: string | undefined = undefined;
    if (
      currentUserProfile?.userType == UserType.USER_TYPE_COLLABORATOR
      // || currentUserProfile?.userType == UserType.USER_TYPE_AUDITOR
    ) {
      allowedUserId = currentUserProfile.id;
    }

    // let datasetMetaId = await this.createDataset(datasetMetadata);
    let datasetMetaId = await this.metaDataRepository.createOrUpdateCollectionMetaByName(
      ContentType.DATASET,
      new ObjectId(datasetData.teamId),
      datasetData.datasetName,
      datasetMetadata,
      undefined,
      allowedUserId,
    );
    if (!datasetMetaId) {
      logger.error(
        `create dataset metadata | DatasetManagerInterfaceService.createNewDataset | N/A | Creating dataset failed`,
      );
      return {success: false};
    }

    //update query option for add project name suggestion
    // create a object from key,value pair
    let tempObj: {[k: string]: any} = {};
    tempObj[SearchQuerySubGroup.DATASET_PROJECT] = datasetData.datasetName;
    // call query update function
    await this.queryOptionRepository.updateQueryOption(
      SearchQueryRootGroup.DATASET,
      tempObj,
      true,
      datasetData.teamId,
      datasetMetaId,
    );

    return {success: true};
  }

  /**
   * create a virtual meta entry entry for dataset
   * @param metaData
   * @returns
   */
  async createDataset(metaData: Partial<MetaData>) {
    let dataset = await this.metaDataRepository.create({
      ...metaData,
      objectType: ContentType.DATASET,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return dataset.id;
  }

  /**
   * Fetch meta data and annotations of a dataset version
   * @returns images and annotations list
   */
  async getDatasetVersionFiles(
    versionId: string,
    isTextFileRquired: boolean,
    exportFormat: string,
    pageSize: number,
    skip: number,
  ) {
    // query metadata
    let versionMetaList = await this.queryMetaDataOfVersion(versionId, isTextFileRquired, exportFormat, pageSize, skip);

    return versionMetaList;
    //----------------------------------------
  }

  /**
   * Fetch meta data and annotations of a dataset version
   * @returns images and annotations list
   */
  async queryMetaDataOfVersion(
    versionId: string,
    isTextFileRquired: boolean,
    exportFormat: string,
    pageSize: number,
    skip: number,
  ) {
    let params: any = [
      {$match: {'datasetVersionList.datasetVersionId': new ObjectId(versionId), objectType: ContentType.IMAGE}},
      {
        $skip: skip,
      },
      {
        $limit: pageSize,
      },
    ];

    let projectParams: Record<string, any> = {
      _id: 1,
      objectKey: 1,
      name: 1,
      url: 1,
    };
    projectParams.exportData = `$datasetVersionData.fileDownloadData.${exportFormat}`;
    projectParams.splitType = `$datasetVersionData.datasetSplitType`;

    // if (isTextFileRquired) {

    params = [
      ...params,
      {
        $addFields: {
          datasetVersionDataArr: {
            $filter: {
              input: '$datasetVersionList',
              as: 'datasetVersionDataArray',
              cond: {$eq: ['$$datasetVersionDataArray.datasetVersionId', new ObjectId(versionId)]},
              // limit: 1
            },
          },
        },
      },
      {
        $addFields: {
          datasetVersionData: {
            $arrayElemAt: ['$datasetVersionDataArr', 0],
          },
        },
      },
    ];
    // }

    params = [
      ...params,
      {
        $project: projectParams,
      },
    ];
    //--------------------------------------------------

    let versionMetaList = await this.metaDataRepository.aggregate(params);
    return versionMetaList;
  }

  /**
   * Use to tag the frames of a datasetVersion randomly into train, validation, test sets
   * @param datasetVersionId dataset version id
   * @param splitData splitting counts
   */
  async randomSplitDatasetVersion(datasetVersionId: string, splitData: DatasetSplitCount) {
    logger.info(
      `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.randomSplitDatasetVersion | N/A | datasetVersionId= ${datasetVersionId}, splitData: `,
      splitData,
    );

    let totalCountToBeSplitted = 0;
    for (let _split of splitData.split) {
      totalCountToBeSplitted = totalCountToBeSplitted + _split.splitCount;
    }

    // remove augmentedFrames from splitting frame count
    // we are not including augmented frames when splitting dataset
    // augmented images should only include in train dataset
    // let totalDatasetVersionFrameCount: [{count: number}] = await this.metaDataRepository.count({
    //   'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId), 'isAugmentedImage': {$ne: true},
    //   'objectType': ContentType.IMAGE,
    //   objectStatus: {neq: OBJECT_STATUS.TRASHED}
    // })
    let totalDatasetVersionFrameCount: [{count: number}] = await this.metaDataRepository.aggregate([
      {
        $match: {
          objectType: ContentType.IMAGE,
          //'isAugmentedImage': {$ne: true},
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
          datasetVersionList: {
            $elemMatch: {
              datasetVersionId: new ObjectId(datasetVersionId),
              isAugmentedImage: {$ne: true},
            },
          },
        },
      },
      {$count: 'count'},
    ]);
    let count = 0;
    if (Array.isArray(totalDatasetVersionFrameCount) && totalDatasetVersionFrameCount.length > 0) {
      count = totalDatasetVersionFrameCount[0].count;
    }

    if (totalCountToBeSplitted != count) {
      logger.info(
        `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.randomSplitDatasetVersion | N/A | mismatch between total count= ${totalDatasetVersionFrameCount[0].count} & to be splited count= ${totalCountToBeSplitted}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATASET_MANAGER_REQUEST_FAILED);
    }

    //reset existing split
    // remove augmentedFrames from splitting frame count
    // we are not including augmented frames when splitting dataset
    // augmented images should only include in train dataset
    await this.metaDataRepository.updateManyUnSet(
      {'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId)},
      {'datasetVersionList.$[elem].datasetSplitType': ''},
      [{'elem.datasetVersionId': new ObjectId(datasetVersionId), 'elem.isAugmentedImage': {$ne: true}}],
    );

    let batchSize = 250000;

    //randomly choosing objects to each split type
    let response: DatasetSplitCount = {
      split: [],
    };

    for (let _splitType of splitData.split) {
      let remainingCount = _splitType.splitCount;
      let existingCount = [{count: 0}];

      while (remainingCount > 0) {
        let randomIdList: {_id: string}[] = await this.metaDataRepository.aggregate([
          {
            $match: {
              objectType: ContentType.IMAGE,
              datasetVersionList: {
                $elemMatch: {
                  datasetVersionId: new ObjectId(datasetVersionId),
                  datasetSplitType: {$exists: false},
                },
              },
            },
          },
          {$sample: {size: Math.min(batchSize, remainingCount)}},
          {$project: {_id: 1}},
        ]);

        let randomObjectIdList = randomIdList.map(obj => new ObjectId(obj._id));
        await this.metaDataRepository.updateManySet(
          {_id: {$in: randomObjectIdList}},
          {'datasetVersionList.$[elem].datasetSplitType': _splitType.splitType},
          [{'elem.datasetVersionId': new ObjectId(datasetVersionId)}],
        );

        //since $sample can be return duplicated documents, it is better to check the count
        existingCount = await this.metaDataRepository.aggregate([
          {
            $match: {
              datasetVersionList: {
                $elemMatch: {
                  datasetVersionId: new ObjectId(datasetVersionId),
                  datasetSplitType: _splitType.splitType,
                },
              },
            },
          },
          {$count: 'count'},
        ]);

        remainingCount = _splitType.splitCount - existingCount[0].count;
      }
      response.split.push({
        splitType: _splitType.splitType,
        splitCount: existingCount[0].count,
      });
    }

    return response;
  }

  /**
   * Use to add or remove the dataset, datasetversion tags from meta frames
   * @param datasetGroupId string, Dataset Manager side id of the dataset group
   * @param datasetName string, Dataset Manager side name of the dataset group
   * @param datasetVersionId datasetVersionId: string, Dataset Manager side id of the dataset version
   * @param selectionId selectionId?: string, datalake side id for a particular selection to add frames to dataset version
   * @returns
   */
  async addOrRemoveFramesToDatasetVersion(
    datasetGroupId: string,
    datasetName: string,
    datasetVersionId: string,
    createNewVersion: boolean,
    baseVersionId: string | undefined,
    currentUserProfile: UserProfileDetailed,
    selectionId?: string,
    removedCollectionIdList?: string[],
    addedCollectionIdList?: string[],
  ) {
    let datasetMetaObj = await this.metaDataRepository.findOne({
      where: {
        objectType: ContentType.DATASET,
        datasetGroupId: datasetGroupId,
      },
    });
    //logger.info(`????????????????????????? ${datasetMetaObj?._id}`)
    if (datasetMetaObj) {
      await this.metaDataRepository.updateById(datasetMetaObj.id, {updatedAt: new Date()});
    }

    if (!datasetMetaObj) {
      logger.warn(
        `${FLOWS.DATALAKE_SELECTION} | DatasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion | N/A | couldn't find datasetMetaObj datasetVersionId= ${datasetVersionId} datasetGroupId= ${datasetGroupId}`,
      );
      return;
    }

    //detaset version info object, used to tag the meta objects
    let datasetVersionInfo = {
      datasetVersionId: new ObjectId(datasetVersionId),
      datasetMetaId: new ObjectId(datasetMetaObj.id),
      datasetGroupId: new ObjectId(datasetGroupId),
      datasetGroupName: datasetName,
      isNew: true,
    };

    //set isNew: false of existing frames of the dataset version before tag new frames
    await this.metaDataRepository.updateManySet(
      {
        objectType: ContentType.IMAGE,
        'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
      },
      {'datasetVersionList.$[elem].isNew': false},
      [{'elem.datasetVersionId': new ObjectId(datasetVersionId)}],
    );

    // if selectionId exist, at first, add the version and the dataset to the frames
    if (selectionId) {
      let selectionMatchQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
        currentUserProfile,
        selectionId,
      );
      let matchQuery: any = {};

      if (selectionMatchQuery?.objectType == ContentType.IMAGE) {
        matchQuery = selectionMatchQuery.matchQuery;
      } else if (
        selectionMatchQuery?.objectType == ContentType.IMAGE_COLLECTION ||
        selectionMatchQuery?.objectType == ContentType.DATASET
      ) {
        let _collectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
          {
            $match: selectionMatchQuery.matchQuery,
          },
          {
            $project: {_id: 1},
          },
        ]);
        let collectionObjectIdList = _collectionIds.map(elem => new ObjectId(elem._id));

        matchQuery = {
          vCollectionIdList: {$in: collectionObjectIdList},
          // $or: [{collectionId: {$in: collectionObjectIdList}}, {vCollectionIdList: {$in: collectionObjectIdList}}],
          //replace flag with enum
          objectStatus: OBJECT_STATUS.ACTIVE,
          objectType: ContentType.IMAGE,
          // isError: {$ne: true},
          // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
          // isMediaProcessingPending: {$ne: true},
          // isAccessible: true,
        };
      }
      // else if (selectionMatchQuery?.objectType == ContentType.DATASET) {
      //   let _datasetCollectionIds: {_id: string}[] = await this.metaDataRepository.aggregate([
      //     {
      //       $match: selectionMatchQuery.matchQuery,
      //     },
      //     {
      //       $project: {_id: 1},
      //     },
      //   ]);
      //   let datasetCollectionObjectIdList = _datasetCollectionIds.map(elem => new ObjectId(elem._id));
      //   matchQuery = {
      //     // 'datasetVersionList.datasetMetaId': {$in: datasetCollectionObjectIdList},
      //     vCollectionIdList: {$in: datasetCollectionObjectIdList},
      //     objectType: ContentType.IMAGE,
      //   };
      // }

      logger.info(
        `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion | N/A | versionId: ${datasetVersionInfo}`,
      );
      //add dataset version related metadata
      await this.metaDataRepository.updateManyPushToList(
        {
          ...matchQuery,
          'datasetVersionList.datasetVersionId': {$ne: new ObjectId(datasetVersionId)},
        },
        {
          datasetVersionList: datasetVersionInfo,
        },
        [],
      );

      //set updatedAt
      await this.metaDataRepository.updateManySet(
        {
          ...matchQuery,
          'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
        },
        {updatedAt: new Date()},
        [],
      );
    }

    //if new version is created based on a version
    if (createNewVersion && baseVersionId) {
      logger.info(
        `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion | N/A | createNewVersion= ${createNewVersion}, baseVersionId= ${baseVersionId}`,
      );

      await this.metaDataRepository.updateManyPushToList(
        {
          $and: [
            {
              datasetVersionList: {
                $elemMatch: {
                  datasetVersionId: new ObjectId(baseVersionId),
                  isAugmentedImage: {$ne: true},
                },
              },
            },
            {'datasetVersionList.datasetVersionId': {$ne: new ObjectId(datasetVersionId)}},
          ],
        },
        {
          datasetVersionList: datasetVersionInfo,
        },
        [],
      );

      //set updatedAt
      await this.metaDataRepository.updateManySet(
        {'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId)},
        {updatedAt: new Date()},
        [],
      );
    }

    if (removedCollectionIdList && removedCollectionIdList.length > 0) {
      let removedCollectionObjectIdList = removedCollectionIdList.map(_elem => new ObjectId(_elem));
      //remove dataset version related metadata from frames
      await this.metaDataRepository.updateManyRemoveFromList(
        {
          vCollectionIdList: {$in: removedCollectionObjectIdList},
          'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
        },
        {
          datasetVersionList: {
            datasetVersionId: new ObjectId(datasetVersionId),
          },
        },
        [],
      );

      // remove datasetMetaId from vCollectionIdList if not used by any other dataset version
      await this.metaDataRepository.updateManyRemoveFromList(
        {
          vCollectionIdList: {$in: removedCollectionObjectIdList},
          'datasetVersionList.datasetGroupId': {$ne: new ObjectId(datasetGroupId)},
          objectType: ContentType.IMAGE,
        },
        {
          vCollectionIdList: datasetVersionInfo.datasetMetaId,
        },
        [],
      );

      //remove dataset version related metadata from prarent objects
      let parentIds: [{_id: null; uniqueParents: string[]}] = await this.metaDataRepository.aggregate([
        {
          $match: {_id: {$in: removedCollectionObjectIdList}},
        },
        {
          $unwind: '$parentList',
        },
        {
          $group: {_id: null, uniqueParents: {$addToSet: '$parentList'}},
        },
      ]);
      let parentIdList: string[] = [];
      if (parentIds && Array.isArray(parentIds) && parentIds.length > 0) {
        parentIdList = parentIds[0].uniqueParents;
      }

      await this.removeDatasetVersionFromParentsIfRequired(
        [...removedCollectionIdList, ...parentIdList],
        datasetVersionId,
      );
      // await this.metaDataRepository.updateManyRemoveFromList(
      //   {
      //     '_id': {$in: [...removedCollectionObjectIdList, ...parentObjectIdList]},
      //     'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId)
      //   },
      //   {
      //     'datasetVersionList': {
      //       datasetVersionId: new ObjectId(datasetVersionId)
      //     }
      //   },
      //   []
      // )
    }

    if (addedCollectionIdList && addedCollectionIdList.length > 0) {
      let addedCollectionObjectIdList = addedCollectionIdList.map(_elem => new ObjectId(_elem));
      //add dataset version related metadata
      await this.metaDataRepository.updateManyPushToList(
        {
          vCollectionIdList: {$in: addedCollectionObjectIdList},
          'datasetVersionList.datasetVersionId': {$ne: new ObjectId(datasetVersionId)},
        },
        {
          datasetVersionList: datasetVersionInfo,
        },
        [],
      );

      //set updatedAt
      await this.metaDataRepository.updateManySet(
        {collectionId: {$in: addedCollectionObjectIdList}},
        {updatedAt: new Date()},
        [],
      );
    }

    await this.metaDataRepository.updateManyAddToSetToList(
      {
        'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
        vCollectionIdList: {$ne: datasetVersionInfo.datasetMetaId},
        objectType: ContentType.IMAGE,
      },
      {
        vCollectionIdList: datasetVersionInfo.datasetMetaId,
      },
      [],
    );

    //set thumbnail for dataset meta head
    if (!datasetMetaObj.thumbnailKey) {
      logger.info(
        `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion | N/A | setting thumbnail for datasetMetaObj id: ${datasetMetaObj.id}}`,
      );
      let _obj = await this.metaDataRepository.findOne({
        where: {
          'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
          objectType: ContentType.IMAGE,
        },
      });
      if (_obj && _obj.thumbnailKey) {
        this.metaDataRepository.updateById(datasetMetaObj.id, {
          thumbnailKey: _obj.thumbnailKey,
          thumbnailUrl: _obj.thumbnailUrl,
          urlExpiredAt: _obj.urlExpiredAt,
          updatedAt: new Date(),
        });
      } else {
        logger.info(
          `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion | N/A | failed to set thumbnail for datasetMetaObj id: ${datasetMetaObj.id}} from meta frame obj of id: ${_obj?.id}`,
        );
      }
    }

    // call propagate dataset version to parents
    this.propagateDatasetVersionToParentsOnFrameUpdate(datasetVersionInfo);
  }

  /**
   * Propagate dataset version to parents of the frames
   * @param datasetVersionInfo
   */
  async propagateDatasetVersionToParentsOnFrameUpdate(datasetVersionInfo: {
    datasetVersionId: ObjectId;
    datasetMetaId: ObjectId;
    datasetGroupId: ObjectId;
    datasetGroupName: string;
  }) {
    // find the parents of the images with dataset id
    let parentObjectIdList: ObjectId[] = [];
    let _parentIds: [{uniqueParents: string[]}] = await this.metaDataRepository.aggregate([
      {
        $match: {
          'datasetVersionList.datasetVersionId': datasetVersionInfo.datasetVersionId,
          objectType: ContentType.IMAGE,
        },
      },
      {
        $project: {_id: 0, parentList: 1},
      },
      {$unwind: '$parentList'},
      {$group: {_id: null, uniqueParents: {$addToSet: '$parentList'}}},
      {$project: {_id: 0, uniqueParents: 1}},
    ]);

    if (_parentIds && Array.isArray(_parentIds) && _parentIds.length > 0) {
      let parentIdList = _parentIds[0].uniqueParents;
      if (parentIdList && Array.isArray(parentIdList) && parentIdList.length > 0) {
        parentObjectIdList = parentIdList.map(elem => new ObjectId(elem));
      }
    }

    let _vCollectionIds: [{uniqueVCollectionIds: string[]}] = await this.metaDataRepository.aggregate([
      {
        $match: {
          $or: [
            {
              _id: {$in: parentObjectIdList},
              objectType: ContentType.VIDEO,
            },
            {
              'datasetVersionList.datasetVersionId': datasetVersionInfo.datasetVersionId,
              objectType: ContentType.IMAGE,
            },
          ],
        },
      },
      {
        $project: {_id: 0, vCollectionIdList: 1},
      },
      {$unwind: '$vCollectionIdList'},
      {$group: {_id: null, uniqueVCollectionIds: {$addToSet: '$vCollectionIdList'}}},
      {$project: {_id: 0, uniqueVCollectionIds: 1}},
    ]);

    if (_vCollectionIds && Array.isArray(_vCollectionIds) && _vCollectionIds.length > 0) {
      let vCollectionIdList = _vCollectionIds[0].uniqueVCollectionIds;
      if (vCollectionIdList && Array.isArray(vCollectionIdList) && vCollectionIdList.length > 0) {
        let vCollectionObjectIdList = vCollectionIdList.map(elem => new ObjectId(elem));
        // vCollectionObjectIdList iterate and elem not in parentObjectIdList then push to parentObjectIdList
        for (let elem of vCollectionObjectIdList) {
          if (!parentObjectIdList.includes(elem)) {
            parentObjectIdList.push(elem);
          }
        }
      }
    }

    // update parents - add dataset version info to parents
    await this.metaDataRepository.updateManyPushToList(
      {
        _id: {$in: parentObjectIdList}, // update the parents
        'datasetVersionList.datasetVersionId': {$ne: datasetVersionInfo.datasetVersionId},
      },
      {
        datasetVersionList: datasetVersionInfo,
      },
      [],
    );

    //set updatedAt
    await this.metaDataRepository.updateManySet({_id: {$in: parentObjectIdList}}, {updatedAt: new Date()}, []);
  }

  /**
   *
   * @param parentIdList list of parent ids and collection ids in vCollectionIdList
   * @param datasetVersionId
   * @returns
   */
  async removeDatasetVersionFromParentsIfRequired(parentIdList: string[], datasetVersionId: string) {
    let parentObjectIdList: ObjectId[] = [];
    if (parentIdList && Array.isArray(parentIdList) && parentIdList.length > 0) {
      parentIdList.forEach(_elem => {
        parentObjectIdList.push(new ObjectId(_elem));
      });
    } else {
      return;
    }

    //sort parents to ensure always dependant nodes are updated before parent nodes
    let sortedParentObjectIdList: [{uniqueParents: ObjectId[]}] = await this.metaDataRepository.aggregate([
      {$match: {_id: {$in: parentObjectIdList}}},
      {$addFields: {parentListSize: {$size: {$ifNull: ['$parentList', []]}}}},
      {$sort: {parentListSize: -1}},
      {$group: {_id: null, uniqueParents: {$push: '$_id'}}},
      {$project: {_id: 0, uniqueParents: 1}},
    ]);

    if (
      sortedParentObjectIdList &&
      Array.isArray(sortedParentObjectIdList) &&
      sortedParentObjectIdList.length > 0 &&
      sortedParentObjectIdList[0].uniqueParents &&
      Array.isArray(sortedParentObjectIdList[0].uniqueParents) &&
      sortedParentObjectIdList[0].uniqueParents.length > 0
    ) {
      for (let parentId of sortedParentObjectIdList[0].uniqueParents) {
        // check at least one child has the dataset version
        let _obj = await this.metaDataRepository.findOne({
          where: {
            and: [
              {or: [{parentList: parentId}, {vCollectionIdList: parentId}]},
              {'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId)},
            ],
          },
        });

        if (!_obj) {
          await this.metaDataRepository.updateManyRemoveFromList(
            {
              _id: parentId,
              'datasetVersionList.datasetVersionId': new ObjectId(datasetVersionId),
            },
            {
              datasetVersionList: {
                datasetVersionId: new ObjectId(datasetVersionId),
              },
            },
            [],
          );
        }
      }
    }
  }

  /**
   * Use to create new dataset version based on existing dataset version only when no files changed and not split changed
   * @param datasetGroupId string, Dataset Manager side id of the dataset group
   * @param baseDatasetVersionId  string, Dataset Manager side id of the base dataset version
   * @param newDatasetVersionId  string, Dataset Manager side id of the new dataset version
   * @returns
   */
  async createNewDatasetVersion(
    datasetGroupId: string,
    baseDatasetVersionId: string,
    newDatasetVersionId: string,
    datasetGroupName: string,
  ) {
    logger.info(
      `${FLOWS.DATASET_MANAGER_REQ}  | DatasetManagerInterfaceService.createNewDatasetVersion | N/A | datasetGroupId= ${datasetGroupId}, baseDatasetVersionId= ${baseDatasetVersionId}, newDatasetVersionId= ${newDatasetVersionId}, datasetGroupName= ${datasetGroupName}`,
    );

    let datasetMetaObj = await this.metaDataRepository.findOne({
      where: {
        objectType: ContentType.DATASET,
        datasetGroupId: datasetGroupId,
      },
    });

    if (!datasetMetaObj) {
      logger.warn(
        `${FLOWS.DATASET_MANAGER_REQ} | DatasetManagerInterfaceService.createNewDatasetVersion | N/A | couldn't find datasetMetaObj baseDatasetVersionId= ${baseDatasetVersionId} datasetGroupId= ${datasetGroupId}`,
      );
      return;
    }

    let splitTypes = [DatasetSplitType.TRAINING, DatasetSplitType.VALIDATION, DatasetSplitType.TESTING];
    //let isUsedForAugmentationList = [true, false]
    //let promiseArray: any[] = []
    // for (let type of splitTypes) {
    //   for (let isUsedForAugmentation of isUsedForAugmentationList) {
    //     promiseArray.push(
    //       await this.metaDataRepository.updateManyPushToList(
    //         {
    //           'datasetVersionList': {
    //             $elemMatch: {
    //               datasetVersionId: new ObjectId(baseDatasetVersionId),
    //               datasetSplitType: type,
    //               isUsedForAugmentation: isUsedForAugmentation
    //             }
    //           }
    //         },
    //         {
    //           'datasetVersionList': {
    //             datasetVersionId: new ObjectId(newDatasetVersionId),
    //             datasetMetaId: datasetMetaObj?._id,
    //             datasetGroupId: datasetGroupId,
    //             datasetGroupName: datasetGroupName,
    //             datasetSplitType: type,
    //             isNew: false,
    //             isUsedForAugmentation: isUsedForAugmentation
    //           }
    //         },
    //         []
    //       )
    //     )
    //   }

    // }

    let promiseArray = splitTypes.map(async splitType => {
      await this.metaDataRepository.updateManyPushToList(
        {
          datasetVersionList: {
            $elemMatch: {
              datasetVersionId: new ObjectId(baseDatasetVersionId),
              datasetSplitType: splitType,
              isAugmentedImage: {$ne: true},
            },
          },
        },
        {
          datasetVersionList: {
            datasetVersionId: new ObjectId(newDatasetVersionId),
            datasetMetaId: datasetMetaObj?._id,
            datasetGroupId: datasetGroupId,
            datasetGroupName: datasetGroupName,
            datasetSplitType: splitType,
            isNew: false,
          },
        },
        [],
      );
    });

    promiseArray.push(
      await this.metaDataRepository.updateManyPushToList(
        {
          datasetVersionList: {
            $elemMatch: {
              datasetVersionId: new ObjectId(baseDatasetVersionId),
              datasetSplitType: DatasetSplitType.TRAINING,
              isAugmentedImage: true,
            },
          },
        },
        {
          datasetVersionList: {
            datasetVersionId: new ObjectId(newDatasetVersionId),
            datasetMetaId: datasetMetaObj?._id,
            datasetGroupId: datasetGroupId,
            datasetGroupName: datasetGroupName,
            datasetSplitType: DatasetSplitType.TRAINING,
            isAugmentedImage: true,
            isNew: false,
          },
        },
        [],
      ),
    );

    await Promise.all(promiseArray);

    return {success: true};
  }

  /**
   *  Aggregate dataset stats
   * @param {string} datasetGroupId dataset group id
   * @returns count and label stats
   */
  async aggregateDatasetStats(datasetGroupId: string): Promise<{
    labelStats: DatasetLabelAggregate[];
    frameCount: ObjectCount[];
  }> {
    const matchFilter: any = {
      'datasetVersionList.datasetGroupId': new ObjectId(datasetGroupId),
      objectType: ContentType.IMAGE,
    };

    const group: any = {
      _id: '$labelList.label',
      count: {$sum: '$labelList.count'},
    };

    const lookup: any = {
      from: 'SystemLabel',
      let: {label: '$_id'},
      pipeline: [{$match: {$expr: {$eq: ['$$label', '$label']}}}, {$project: {labelText: 1, _id: 0, type: 1}}],
      as: 'labels',
    };

    const project: any = {
      _id: 0,
      count: 1,
      mainLabel: '$_id',
      labels: 1,
    };

    const param: any = [
      {$match: matchFilter},
      {$unwind: '$labelList'},
      {$group: group},
      {$lookup: lookup},
      {$project: project},
    ];

    const frameParam: any = [
      {$match: matchFilter},
      {
        $group: {
          _id: null,
          count: {$sum: 1},
          size: {$sum: '$fileSize'},
          rawSum: {$sum: '$verificationStatusCount.raw'},
          machineAnnotatedSum: {$sum: '$verificationStatusCount.machineAnnotated'},
          verifiedSum: {$sum: '$verificationStatusCount.verified'},
        },
      },
    ];

    // logger.debug(JSON.stringify(frameParam, null, 2))

    let labelAggregate: DatasetLabelAggregate[] = await this.metaDataRepository.aggregate(param);
    let countAggregate: ObjectCount[] = await this.metaDataRepository.aggregate(frameParam);

    return {
      labelStats: labelAggregate,
      frameCount: countAggregate,
    };
  }

  /**
   * Rebuild search query options for a dataset
   * @param datasetIdList meta ids of datasets
   */
  async rebuildSearchQueryOfDataset(datasetIdList: string[]) {
    for (let datasetMetaId of datasetIdList) {
      logger.warn(
        `Rebuild Search Queries | DatasetManagerInterfaceService.rebuildSearchQueryOfDataset | N/A | datasetMetaId= ${datasetMetaId}`,
      );
      let datasetObj = await this.metaDataRepository.findById(datasetMetaId);

      //update query option for add project name suggestion
      // create a object from key,value pair
      let tempObj: {[k: string]: any} = {};
      tempObj[SearchQuerySubGroup.DATASET_PROJECT] = datasetObj.name;
      // call query update function
      await this.queryOptionRepository.updateQueryOption(
        SearchQueryRootGroup.DATASET,
        tempObj,
        true,
        datasetObj.teamId as unknown as string,
        datasetMetaId,
      );

      //for tags
      if (datasetObj.datasetGroupId)
        await this.queryOptionRepository.rebuildDatasetQueryOptionForTags(datasetObj.datasetGroupId);
    }
  }

  /**
   * delete dataset version details from metadata
   * @param versionId {string} id of the version
   * @returns {isSuccess: boolean}
   */
  async deleteDatasetVersion(versionId: string, deleteDatasetGroup: boolean, datasetGroupId: string) {
    try {
      let datasetMetaObj = await this.metaDataRepository.findOne({
        where: {
          objectType: ContentType.DATASET,
          datasetGroupId: datasetGroupId,
        },
      });

      if (!datasetMetaObj) {
        logger.warn(
          `${FLOWS.DATALAKE_SELECTION} | DatasetManagerInterfaceService.removeAugmentationFromDatasetVersion | N/A | couldn't find datasetMetaObj datasetVersionId= ${versionId} datasetGroupId= ${datasetGroupId}`,
        );
        return;
      }

      await this.metaDataRepository.updateManyRemoveFromList(
        {
          'datasetVersionList.datasetVersionId': new ObjectId(versionId),
        },
        {
          datasetVersionList: {
            datasetVersionId: new ObjectId(versionId),
          },
        },
        [],
      );

      await this.metaDataRepository.updateManyRemoveFromList(
        {
          vCollectionIdList: new ObjectId(datasetMetaObj.id),
          'datasetVersionList.datasetGroupId': {$ne: new ObjectId(datasetGroupId)},
          objectType: ContentType.IMAGE,
        },
        {
          vCollectionIdList: new ObjectId(datasetMetaObj.id),
        },
        [],
      );

      if (deleteDatasetGroup) {
        // let datasetGroup = await this.metaDataRepository.findOne({where: {datasetGroupId: datasetGroupId}});
        if (datasetMetaObj) {
          this.metaDataRepository.deleteById(datasetMetaObj.id);
          this.queryOptionRepository.deleteAll({
            keyGroup: 'dataset.project',
            key: datasetMetaObj.name,
          });
        }
      }

      logger.info(
        `delete dataset version details from metadata | DatasetManagerInterfaceService.deleteDatasetVersion | N/A | dataset version id success= ${versionId}`,
      );
      return {isSuccess: true};
    } catch (err) {
      logger.error(
        `delete dataset version details from metadata | DatasetManagerInterfaceService.deleteDatasetVersion | N/A | dataset version id failed= ${versionId}`,
      );
      return {isSuccess: false, error: err};
    }
  }

  /**
   * use to find metaupdates operation list of the selected frames for a particular dataset version
   * @param datasetVersionId id of the dataset version
   * @param selectionId id of the selection object
   * @param removedCollectionIdList string[], ids of collections which are removed from the dataset version
   * @param addedCollectionIdList string[], ids of collection which are added to the dataset version
   * @returns
   */
  async findOperationList(
    currentUserProfile: UserProfileDetailed,
    datasetVersionId?: string,
    selectionId?: string,
    removedCollectionIdList?: string[],
    addedCollectionIdList?: string[],
  ) {
    let matchFilter = await this.searchQueryBuilderService.createMatchQuery(
      currentUserProfile,
      selectionId,
      removedCollectionIdList,
      addedCollectionIdList,
      datasetVersionId,
    );

    let operationList: {
      operationId: string | ObjectId;
      operationName: string;
    }[] = [];

    if (!matchFilter) return operationList;

    let params: {[k: string]: any}[] = [
      {
        $match: matchFilter,
      },
      {
        $unwind: '$operationList',
      },
      {
        $group: {
          _id: null,
          operations: {
            $addToSet: {
              operationId: '$operationList.operationId',
              operationName: '$operationList.operationName',
            },
          },
        },
      },
    ];

    let operations: [
      {
        _id: null;
        operations: {
          operationId: string | ObjectId;
          operationName: string;
        };
      },
    ] = await this.metaDataRepository.aggregate(params);

    if (
      Array.isArray(operations) &&
      operations.length > 0 &&
      Array.isArray(operations[0].operations) &&
      operations[0].operations.length > 0
    ) {
      operationList = operations[0].operations;
    }

    return operationList;
  }

  /**
   * use to check if new frame is added to the dataset version
   * @param datasetVersionId id of the dataset version
   * @returns object  {isNewFrameAdded: boolean}
   */
  async isNewFrameAddedToDatasetVersion(datasetVersionId: string) {
    let obj = await this.metaDataRepository.findOne({
      where: {
        datasetVersionList: {
          elemMatch: {
            datasetVersionId: new ObjectId(datasetVersionId),
            isNew: true,
          },
        },
      },
    });

    if (obj) {
      return {isNewFrameAdded: true};
    } else {
      return {isNewFrameAdded: false};
    }
  }

  /**
   * call when augmentation is removed from dataset version
   * Use to remove augmentation from dataset version
   * @param augmentationId augmentation id
   * @param augmentationPropertyId augmentation property id
   * @param datasetVersionId dataset version id
   */
  async removeAugmentationFromDatasetVersion(
    augmentationId: string,
    augmentationPropertyId: string,
    datasetVersionId: string,
    datasetGroupId: string,
  ) {
    logger.info(`${FLOWS.MANAGE_AUGMENTATIONS} | DatasetManagerInterfaceService.removeAugmentationFromDatasetVersion | N/A | removing augmentation from dataset version
      augmentationId: ${augmentationId},
      augmentationPropertyId: ${augmentationPropertyId},
      datasetVersionId: ${datasetVersionId}
    `);

    let datasetMetaObj = await this.metaDataRepository.findOne({
      where: {
        objectType: ContentType.DATASET,
        datasetGroupId: datasetGroupId,
      },
    });

    if (!datasetMetaObj) {
      logger.warn(
        `${FLOWS.DATALAKE_SELECTION} | DatasetManagerInterfaceService.removeAugmentationFromDatasetVersion | N/A | couldn't find datasetMetaObj datasetVersionId= ${datasetVersionId} datasetGroupId= ${datasetGroupId}`,
      );
      return;
    }

    let matchingCriteria = {
      datasetVersionList: {
        $elemMatch: {
          datasetVersionId: new ObjectId(datasetVersionId),
          isAugmentedImage: true,
        },
      },
      objectType: ContentType.IMAGE,
      isAugmentedImage: true,
      'augmentationType.id': augmentationId,
      'augmentationType.property.id': augmentationPropertyId,
    };

    //retrieve affected parentId list before remove tag from frames
    let parentIdList: string[] = [];
    let _parentIds: [{uniqueParents: string[]}] = await this.metaDataRepository.aggregate([
      {
        $match: matchingCriteria,
      },
      {
        $project: {_id: 0, parentList: 1},
      },
      {$unwind: '$parentList'},
      {$group: {_id: null, uniqueParents: {$addToSet: '$parentList'}}},
      {$project: {_id: 0, uniqueParents: 1}},
    ]);

    if (_parentIds && Array.isArray(_parentIds) && _parentIds.length > 0) {
      parentIdList = _parentIds[0].uniqueParents;
    }

    let _vCollectionIds: [{uniqueVCollectionIds: string[]}] = await this.metaDataRepository.aggregate([
      {
        $match: matchingCriteria,
      },
      {
        $project: {_id: 0, vCollectionIdList: 1},
      },
      {$unwind: '$vCollectionIdList'},
      {$group: {_id: null, uniqueVCollectionIds: {$addToSet: '$vCollectionIdList'}}},
      {$project: {_id: 0, uniqueVCollectionIds: 1}},
    ]);

    if (_vCollectionIds && Array.isArray(_vCollectionIds) && _vCollectionIds.length > 0) {
      let vCollectionIdList = _vCollectionIds[0].uniqueVCollectionIds;
      if (vCollectionIdList && Array.isArray(vCollectionIdList) && vCollectionIdList.length > 0) {
        let vCollectionStringIdList = vCollectionIdList.map(elem => elem.toString());
        // vCollectionObjectIdList iterate and elem not in parentObjectIdList then push to parentObjectIdList
        for (let elem of vCollectionStringIdList) {
          if (!parentIdList.includes(elem)) {
            parentIdList.push(elem);
          }
        }
      }
    }

    //remove tag from frames
    await this.metaDataRepository.updateManyRemoveFromList(
      matchingCriteria,
      {
        datasetVersionList: {
          datasetVersionId: new ObjectId(datasetVersionId),
        },
      },
      [],
    );

    await this.metaDataRepository.updateManyRemoveFromList(
      {
        vCollectionIdList: new ObjectId(datasetMetaObj.id),
        'datasetVersionList.datasetGroupId': {$ne: new ObjectId(datasetGroupId)},
        objectType: ContentType.IMAGE,
      },
      {
        vCollectionIdList: new ObjectId(datasetMetaObj.id),
      },
      [],
    );

    //remove tag from affected parents if required
    await this.removeDatasetVersionFromParentsIfRequired(parentIdList, datasetVersionId);
  }

  /**
   * Call when add/edit augmentation type to a dataset version
   * Use to set metadata for augmented images
   * add augmentation info to metadata
   * tag augmented images for dataset version
   * @param augmentedInfoWithMetaList
   */
  async addAugmentationToDatasetVersion(augmentedInfoWithMetaList: AugmentedInfoWithMetaList) {
    logger.info(`${FLOWS.MANAGE_AUGMENTATIONS} | DatasetManagerInterfaceService.addAugmentationToDatasetVersion | N/A | adding augmentation to dataset version,
      augmentedFrameDataId: ${augmentedInfoWithMetaList.augmentedFrameDataId},
      augmentationType: ${augmentedInfoWithMetaList.augmentationType},
      datasetVersionId: ${augmentedInfoWithMetaList.datasetVersionId},
      datasetGroupId: ${augmentedInfoWithMetaList.datasetGroupId},
      datasetGroupName: ${augmentedInfoWithMetaList.datasetGroupName},
      images count: ${augmentedInfoWithMetaList.images.length}
    `);

    // updated meta objects with augmentation info
    let bulkWriteAugmentedInfo = augmentedInfoWithMetaList.images.map(image => {
      return {
        updateOne: {
          filter: {
            objectKey: image.objectKey,
          },
          update: {
            $set: {
              isAugmentedImage: true,
              sourceObjectKey: image.sourceObjectKey,
              augmentationType: augmentedInfoWithMetaList.augmentationType,
              augmentationSettings: image.augmentationSettings,
            },
          },
        },
      };
    });
    await this.metaDataRepository.bulkWrite(bulkWriteAugmentedInfo);

    let keyList = augmentedInfoWithMetaList.images.map(image => image.objectKey);
    // let sourceKeyList = augmentedInfoWithMetaList.images.map(image => image.sourceObjectKey)
    let datasetMetaObj = await this.metaDataRepository.findOne({
      where: {
        datasetGroupId: augmentedInfoWithMetaList.datasetGroupId,
      },
    });
    if (!datasetMetaObj) {
      logger.warn(
        `${FLOWS.MANAGE_AUGMENTATIONS} | DatasetManagerInterfaceService.addAugmentationToDatasetVersion | N/A | couldn't find datasetMetaObj for augmentedFrameDataId= ${augmentedInfoWithMetaList.augmentedFrameDataId}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATSET_META_HEAD_NOT_EXIST);
    }
    //tag images to dataset version
    await this.metaDataRepository.updateManyPushToList(
      {
        objectKey: {$in: keyList},
        'datasetVersionList.datasetVersionId': {$ne: new ObjectId(augmentedInfoWithMetaList.datasetVersionId)},
      },
      {
        datasetVersionList: {
          datasetVersionId: new ObjectId(augmentedInfoWithMetaList.datasetVersionId),
          datasetSplitType: DatasetSplitType.TRAINING,
          datasetMetaId: new ObjectId(datasetMetaObj.id),
          datasetGroupId: new ObjectId(augmentedInfoWithMetaList.datasetGroupId),
          datasetGroupName: augmentedInfoWithMetaList.datasetGroupName,
          isAugmentedImage: true,
          isNew: false,
        },
      },
      [],
    );

    await this.metaDataRepository.updateManyAddToSetToList(
      {
        objectKey: {$in: keyList},
        'datasetVersionList.datasetVersionId': new ObjectId(augmentedInfoWithMetaList.datasetVersionId),
        vCollectionIdList: {$ne: new ObjectId(datasetMetaObj.id)},
        objectType: ContentType.IMAGE,
      },
      {
        vCollectionIdList: new ObjectId(datasetMetaObj.id),
      },
      [],
    );

    // await this.metaDataRepository.updateManyPushToList(
    //   {
    //     'objectKey': {$in: sourceKeyList},
    //     'datasetVersionList.datasetVersionId': {$ne: new ObjectId(augmentedInfoWithMetaList.datasetVersionId)}
    //   },
    //   {
    //     "datasetVersionList.isUsedForAugmentation": true
    //   },
    //   []
    // )

    // if matching criteria(objectKey and trashed object) is matched then restore that object
    await this.metaDataRepository.updateManySet(
      {
        objectKey: {$in: keyList},
        objectStatus: OBJECT_STATUS.TRASHED,
      },
      {
        objectStatus: OBJECT_STATUS.ACTIVE,
        statPending: true,
        statPendingAt: new Date(),
        showInTrash: false,
        updatedAt: new Date(),
        restoredAt: new Date(),
        restoredBy: undefined,
      },
      [],
    );

    // call propagate dataset version to parents
    this.propagateDatasetVersionToParentsOnFrameUpdate({
      datasetVersionId: new ObjectId(augmentedInfoWithMetaList.datasetVersionId),
      datasetMetaId: new ObjectId(datasetMetaObj.id),
      datasetGroupId: new ObjectId(augmentedInfoWithMetaList.datasetGroupId),
      datasetGroupName: augmentedInfoWithMetaList.datasetGroupName,
    });

    //mark collection as augmented image collection
    this.markAsAugmentedImageCollection(keyList);
  }

  /**
   * use to set collectionType for augmented image collections
   * @param objectKeyList
   */
  async markAsAugmentedImageCollection(objectKeyList: string[]) {
    if (objectKeyList && Array.isArray(objectKeyList) && objectKeyList.length > 0) {
      let uniqueCollections = await this.metaDataRepository.distinct('collectionId', {objectKey: {$in: objectKeyList}});
      if (uniqueCollections && Array.isArray(uniqueCollections) && uniqueCollections.length > 0) {
        let uniqueCollectionsObjectIds = uniqueCollections.map(collectionId => new ObjectId(collectionId));
        await this.metaDataRepository.updateManySet(
          {_id: {$in: uniqueCollectionsObjectIds}},
          {collectionType: CollectionType.AUGMENTED_UPLOAD},
          [],
        );
      }
    }
  }
}

export const DATASET_MANAGER_INTERFACE_SERVICE = BindingKey.create<DatasetManagerInterfaceService>(
  'service.datasetManagerInterface',
);
