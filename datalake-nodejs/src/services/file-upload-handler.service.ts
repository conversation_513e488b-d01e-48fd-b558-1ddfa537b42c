/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Use to track file upload progress & to keep custom metadata for a particular upload
 */

/**
 * @class FileUploadHandlerService
 * Use to track file upload progress & to keep custom metadata for a particular upload
 * to show file upload progress of files uploaded from the frontend
 * @description frontend file uploads related logics
 * <AUTHOR> vinura
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, Request} from '@loopback/rest';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, MetaData, OBJECT_STATUS,
  MetaDataCollectionInputObjectFormat} from '../models';
import {FileUploadProgress, FileUploadProgressStatus} from '../models/file-upload-progress.model';
import {InputMetaDataFeed, InputMetaDataFeedGroup, MetaDataInputFeedObject} from '../models/input-meta-data-feed.model';
import {JobStatus} from '../models/job.model';
import {FileUploadProgressRepository} from '../repositories/file-upload-progress.repository';
import {InputMetaDataFeedRepository} from '../repositories/input-meta-data-feed.repository';
import {JobRepository} from '../repositories/job.repository';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {getContentType} from '../settings/tools';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
import * as multiparty from 'multiparty';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
dotenv.config();

const storageType = process.env.STORAGE_TYPE;
const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

@injectable({scope: BindingScope.TRANSIENT})
export class FileUploadHandlerService {
  constructor(
    /* Add @inject to inject parameters */
    @repository(FileUploadProgressRepository)
    private fileUploadProgressRepository: FileUploadProgressRepository,
    @repository(InputMetaDataFeedRepository)
    public inputMetaDataFeedRepository: InputMetaDataFeedRepository,
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository(JobRepository)
    private jobRepository: JobRepository,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
  ) {}

  /*
   * Add service methods here
   */

  /**
   * Use for handle uploading files to s3 from frontend
   * Generate a presigned upload url
   * @param filePath {string} path to save the amazon media file
   * @returns Presigned upload url
   */
  async getFileUploadUrl(filePath: string) {
    logger.info(
      `File upload generate presigned url | FileUploadHandlerService.getFileUploadUrl | filePath:${filePath} | generating`,
    );
    let preSigneUrl = await this.storageCrawlerService.storageServiceProvider.generateWriteFileUrl(filePath);
    return {
      url: preSigneUrl,
    };
  }

  /**
   * Use for handle intialize uploading multipart files to s3 from frontend
   */
  async initializeMultipartUpload(
    fileName: string,
    collectionName: string,
    uploadId: string,
    apiKey?: string,
    currentUserProfile?: UserProfileDetailed,
  ) {
    let uploadProgressData = await this.fileUploadProgressRepository.findById(uploadId);

    let collectionObj = await this.metaDataRepository.findById(uploadProgressData.collectionId);

    let uniqueName = collectionObj.storagePath ? collectionObj.storagePath : collectionObj.name; // #########################

    let filePath = `${uniqueName}/${fileName}`;
    let objectKey = `${uniqueName}_${fileName}`;

    // if (applicationCode) {
    //   if (applicationCode == APP_CODE.SDK_VIA_DATASET) {
    //     filePath = STORAGE_SYSTEM_SUB_FOLDER_AUGMENTATIONS + '/' + filePath
    //   }
    // }

    logger.info(
      `Initialize Multipart Upload | FileUploadHandlerService.initializeMultipartUpload | filePath:${filePath} | generating`,
    );
    // First check if this file is already uploaded and present in metadata collection
    let metaDataObj = await this.metaDataRepository.findOne({where: {objectKey: objectKey}});

    let queuedMetaDataObj = await this.inputMetaDataFeedRepository.findOne({
      where: {objectKey: objectKey, isActive: true},
    });

    if (metaDataObj || queuedMetaDataObj) {
      //Skip uploading and return
      if (metaDataObj) {
        logger.info(
          `FileUploadHandlerService | Initialize Multipart Upload | File ${filePath} already uploaded as ${metaDataObj.id}`,
        );
      }
      if (queuedMetaDataObj) {
        logger.info(
          `FileUploadHandlerService | Initialize Multipart Upload | File ${filePath} already in queue as ${queuedMetaDataObj.id}`,
        );
      }

      let job = await this.jobRepository.findOne({where: {'jobSpecificDetails.sessionId': `upload_${uploadId}`}});
      if (
        metaDataObj &&
        (!metaDataObj.collectionId ||
          (metaDataObj.collectionId &&
            uploadProgressData.collectionId.toString() != metaDataObj.collectionId.toString()))
      ) {
        let reason: string = `${fileName} was previously removed from this collection has already been attached to another collection(s)`;
        let jobUpdate: Record<string, any>[] = [
          {
            updateOne: {
              filter: {_id: new ObjectId(job?._id)},
              update: {
                $inc: {'subJobs.fileUploading.uploadRemovedCount': 1},
                $addToSet: {
                  'jobSpecificDetails.uploadRemovedFileList': fileName,
                  'jobSpecificDetails.jobPartFailedReasonList': reason,
                },
              },
            },
          },
        ];
        await this.jobRepository.bulkWrite(jobUpdate);
        return {
          fileId: '', //Setting empty because multipart upload is not done
          fileKey: metaDataObj.storagePath,
          objectKey: objectKey,
          isExisting: true,
          uploadRemovedFile: true,
        };
      }
      if (uploadId && apiKey) {
        // update job skipped file list
        let jobUploadedFileAddToSet = {
          'jobSpecificDetails.skippedFileList': objectKey,
        };
        await this.jobRepository.updateOneAddToSetToList({_id: new ObjectId(job?._id)}, jobUploadedFileAddToSet, []);

        // insert file metaData to db for skipped file
        let uploadProgressData = await this.fileUploadProgressRepository.findById(uploadId);

        if (uploadProgressData.isOverrideMetaData) {
          uploadProgressData.collectionObjectType;
          let fileNameArr = filePath.split('/');
          // console.log(fileNameArr)
          let fileName = fileNameArr.pop();
          // console.log(fileName)
          let fileObjectType = await getContentType(fileName!, uploadProgressData.collectionObjectType);

          let metaDataUpdates = {
            ...uploadProgressData.metaDataUpdates,
            // isMediaProcessingPending: false, //--> this is commented because isMediaProcessingPending is should marked as false only from media processing side after processing complete
            fileUploadId: new ObjectId(uploadId),
            storagePath: filePath,
            objectKey: objectKey,
            objectType: fileObjectType,
            name: fileName,
          };

          let metaDataPushList = {
            fileUploadIdList: [new ObjectId(uploadId)],
          };

          let metaDataInput: MetaDataInputFeedObject = {
            apiKey: apiKey, //?????????????????--------------------
            collectionId: uploadProgressData.collectionId,
            metaDataList: [
              {
                objectKey: objectKey,
                metaDataObject: metaDataUpdates,
                metaDataPushList: metaDataPushList,
              },
            ],
          };
          await this.inputMetaDataFeedRepository.onInputMetaData(metaDataInput, currentUserProfile);
        }
      }

      return {
        fileId: '', //Setting empty because multipart upload is not done
        fileKey: metaDataObj ? metaDataObj.storagePath : queuedMetaDataObj?.metaDataObject.storagePath,
        objectKey: objectKey,
        isExisting: true,
      };
    }

    // add storage prefix path to upload file into different folder if needed
    if (collectionObj.storagePrefixPath) {
      filePath = collectionObj.storagePrefixPath + '/' + filePath;
    }

    let uploadData = await this.storageCrawlerService.storageServiceProvider.initializeMultipartUpload(filePath);

    let response = {
      ...uploadData,
      objectKey: objectKey,
    };
    return response;
  }

  async createObjectKeyByS3Key(s3Key: string) {
    let storagePath = s3Key.replace(/\//g, '_');

    return storagePath;
  }

  /**
   * Use for handle chunk list urls for uploading multipart files to s3 from frontend
   */
  async generateMultipartPreSignedUrls(
    filePath: string,
    fileId: number,
    parts: number,
    contentType?: string,
    isDisableMultipart?: boolean,
  ) {
    logger.info(
      `File chunk upload generate presigned urls | FileUploadHandlerService.generateMultipartPreSignedUrls | filePath:${filePath} | generating`,
    );
 
    let uploadData = await this.storageCrawlerService.storageServiceProvider.generateMultipartPreSignedUrls(
      filePath,
      fileId,
      parts,
      '',
      contentType,
      isDisableMultipart,
    );
    return uploadData;
  }

  /**
   * Use for finalize multipart file upload to s3 from frontend
   */
  async finalizeMultipartUpload(
    filePath: string,
    fileId: string,
    parts: any,
    uploadId: string,
    apiKey: string,
    finalizeUrl?: string,
    isDisableMultipart?: boolean,
    isSkipInputMetadataQueue?: boolean,
    currentUserProfile?: UserProfileDetailed,
    objectMetaUpdaterService?: any,
  ) {
    logger.info(
      `File chunk upload finalize | FileUploadHandlerService.finalizeMultipartUpload | filePath:${filePath} | finalizing`,
    );

    logger.info(
      'handleSkipInputMetadataQueue | uploadId:',
      uploadId,
      'isSkipInputMetadataQueue:',
      isSkipInputMetadataQueue,
    );

    // finalize from s3
    let uploadData = await this.storageCrawlerService.storageServiceProvider.finalizeMultipartUpload(
      filePath,
      fileId,
      parts,
      undefined,
      finalizeUrl,
      isDisableMultipart,
      currentUserProfile,
    );

    if (!uploadId) {
      logger.error(
        `File chunk upload finalize | FileUploadHandlerService.finalizeMultipartUpload | filePath:${filePath} | no upload Id`,
      );
      return uploadData;
    }

    // insert file metaData to db
    let uploadProgressData = await this.fileUploadProgressRepository.findById(uploadId);

    let fileNameArr = filePath.split('/');
    // console.log(fileNameArr)
    let fileName = fileNameArr.pop();
    // console.log(fileName)

    let collectionObj = await this.metaDataRepository.findById(uploadProgressData.collectionId);
    let objectKey = `${collectionObj.storagePath}_${fileName}`; // #########################

    let fileObjectType: ContentType;
    if (uploadProgressData.collectionObjectType == ContentType.OTHER_COLLECTION) {
      fileObjectType = getContentType(fileName!);
    } else {
      fileObjectType = getContentType(fileName!, uploadProgressData.collectionObjectType);
    }

    // let objectStatus:OBJECT_STATUS = (fileObjectType == ContentType.IMAGE || fileObjectType == ContentType.VIDEO)?
    // OBJECT_STATUS.MEDIA_PROCESSING_PENDING : OBJECT_STATUS.ACTIVE;

    let metaDataUpdates: Partial<Record<keyof MetaData, any>> = {
      ...uploadProgressData.metaDataUpdates,
      isMediaProcessingPending:
        fileObjectType == ContentType.IMAGE ||
        fileObjectType == ContentType.VIDEO ||
        fileObjectType == ContentType.OTHER,
      // objectStatus:objectStatus,
      fileUploadId: new ObjectId(uploadId),
      objectKey: objectKey,
      storagePath: filePath,
      objectType: fileObjectType,
      name: fileName,
      nameInLowerCase: typeof fileName == 'string' && fileName.length != 0 ? fileName.toLowerCase() : '',
    };

    if (
      collectionObj.allowedUserIdList &&
      Array.isArray(collectionObj.allowedUserIdList) &&
      collectionObj.allowedUserIdList.length > 0
    ) {
      for (let oId of collectionObj.allowedUserIdList) {
        if (
          metaDataUpdates.allowedUserIdList &&
          Array.isArray(metaDataUpdates.allowedUserIdList) &&
          metaDataUpdates.allowedUserIdList.length > 0
        ) {
          let allowedUserStringIdList = metaDataUpdates.allowedUserIdList.map(id => id.toString());
          if (!allowedUserStringIdList.includes(oId.toString())) {
            metaDataUpdates.allowedUserIdList.push(new ObjectId(oId));
          }
        } else {
          metaDataUpdates.allowedUserIdList = collectionObj.allowedUserIdList;
        }
      }
    }

    if (
      fileObjectType == ContentType.IMAGE ||
      fileObjectType == ContentType.VIDEO ||
      fileObjectType == ContentType.OTHER
    )
      metaDataUpdates.objectStatus = OBJECT_STATUS.MEDIA_PROCESSING_PENDING;

    //For OTHER files, size calculation done at this stage because media processing not done for OTHER files
    if (fileObjectType == ContentType.OTHER) {
      metaDataUpdates.fileSize = await this.storageCrawlerService.storageServiceProvider.getFileSize(filePath);
    }
    let collectionId = uploadProgressData.collectionId;
    let metaDataPushList = {
      fileUploadIdList: [new ObjectId(uploadId)],
    };

    // handle the other file upload with image and video object type
    if (uploadProgressData.collectionObjectType == ContentType.OTHER_COLLECTION) {
      if (fileObjectType == ContentType.IMAGE && uploadProgressData.imageCollectionId) {
        collectionId = uploadProgressData.imageCollectionId;
      } else if (fileObjectType == ContentType.VIDEO && uploadProgressData.videoCollectionId) {
        collectionId = uploadProgressData.videoCollectionId;
      }
    }

    if (metaDataUpdates.vCollectionIdList) {
      metaDataUpdates.vCollectionIdList.push(new ObjectId(collectionId.toString()));
    } else {
      metaDataUpdates.vCollectionIdList = [new ObjectId(collectionId.toString())];
    }

    let metaDataInput: MetaDataInputFeedObject = {
      apiKey: apiKey,
      collectionId: collectionId,
      metaDataList: [
        {
          objectKey: objectKey,
          metaDataObject: metaDataUpdates,
          metaDataPushList: metaDataPushList,
        },
      ],
    };

    if (!isSkipInputMetadataQueue) {
      await this.inputMetaDataFeedRepository.onInputMetaData(metaDataInput, currentUserProfile);
    }

    // invoke media processor to start generating thumbnails

    // mark uploaded file in fileUploadProgress record
    let uploadedFileAddToSet = {
      uploadedFileList: objectKey,
    };
    await this.fileUploadProgressRepository.updateOneAddToSetToList(
      {_id: new ObjectId(uploadId)},
      uploadedFileAddToSet,
      [],
    );
    // -----

    if (!uploadData.isSuccess) {
      let uploadedFailedFileAddToSet = {
        uploadedFailedFileList: objectKey,
      };
      await this.fileUploadProgressRepository.updateOneAddToSetToList(
        {_id: new ObjectId(uploadId)},
        uploadedFailedFileAddToSet,
        [],
      );
    }
    let allFileList = uploadProgressData.allFileList ? uploadProgressData.allFileList : [];
    let uploadedFileList = uploadProgressData.uploadedFileList ? uploadProgressData.uploadedFileList : [];
    uploadedFileList.push(objectKey);

    let job = await this.jobRepository.findOne({where: {'jobSpecificDetails.sessionId': `upload_${uploadId}`}});
    //if (job?.jobSpecificDetails) {
    // let uploadedFileList = job?.jobSpecificDetails.uploadedFileList ? job.jobSpecificDetails.uploadedFileList : []
    // if (fileName && !uploadedFileList.includes(fileName)) {
    //   uploadedFileList.push(fileName)
    // }
    let jobUploadedFileAddToSet = {
      'jobSpecificDetails.uploadedFileList': fileName,
    };
    await this.jobRepository.updateOneAddToSetToList({_id: new ObjectId(job?._id)}, jobUploadedFileAddToSet, []);
    let allFileCount = allFileList.length;
    let uploadedFileCount = uploadedFileList.length;
    let fileUploadingProgress = 0;
    if (allFileCount > 0) {
      fileUploadingProgress = (uploadedFileCount / allFileCount) * 100;
    }

    let progress =
      ((job?.subJobs?.inputMetaDataFeedDequeue?.progress || 0) +
        (job?.subJobs?.mediaProcessing?.progress || 0) +
        fileUploadingProgress) /
      3;

    await this.jobRepository.updateManySet(
      {_id: new ObjectId(job?._id)},
      {
        status: JobStatus.inProgress,
        progress: progress,
        'subJobs.fileUploading.progress': fileUploadingProgress,
        'subJobs.fileUploading.completedCount': uploadedFileCount,
        //"jobSpecificDetails.uploadedFileList": uploadedFileList,
        updatedAt: new Date(),
      },
      [],
    );
    await this.fileUploadProgressRepository.updateById(uploadId, {progress: fileUploadingProgress});

    // set 'uploadInProgress' to true when receiving upload. (because collection can be marked as uploadInProgress: false when detecting timeout jobs)
    await this.metaDataRepository.updateById(uploadProgressData.collectionId, {
      uploadInProgress: true,
      updatedAt: new Date(),
    });

    if (isSkipInputMetadataQueue) {
      logger.info(
        'handleSkipInputMetadataQueue | uploadId:',
        uploadId,
        'isSkipInputMetadataQueue:',
        isSkipInputMetadataQueue,
      );
      await this.handleSkipInputMetadataQueue(metaDataInput, objectMetaUpdaterService, currentUserProfile);
    }

    return uploadData;
  }

  async handleSkipInputMetadataQueue(
    metaDataListObject: MetaDataInputFeedObject,
    objectMetaUpdaterService: any,
    currentUserProfile?: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile?.teamId;

    if (!teamId) {
      logger.error(
        `handle skip input metadata queue | handleSkipInputMetadataQueue | `,
        'Team doesnt exists on api key',
      );
      throw new HttpErrors.Unauthorized(`Team doesnt exists on api key`);
    }

    let inputMetaDataFeedGroup: InputMetaDataFeedGroup[] = [];
    //_id: string;
    // metaDataArray: InputMetaDataFeed[];
    // metaDataObjectForUpdate?: MetaData[];
    if (metaDataListObject.metaDataList) {
      if (metaDataListObject.collectionName) {
        if (!metaDataListObject.collectionType) {
          // cannot create collection without type
          return;
        }
        let collectionMetaData: Partial<MetaData> = {};
        let newCollectionObj = await this.metaDataRepository.handleCreateCollection(
          metaDataListObject.collectionName,
          metaDataListObject.collectionType,
          collectionMetaData,
          currentUserProfile,
        );

        let newCollectionId = newCollectionObj.collectionId;
        metaDataListObject.collectionId = newCollectionId;
        //metaDataListObject.parentList = [new ObjectId(newCollectionId)]
      } else if (metaDataListObject.collectionId) {
        metaDataListObject.collectionId = new ObjectId(metaDataListObject.collectionId) as any;
        //metaDataListObject.parentList = [new ObjectId(metaDataListObject.collectionId)]
      }

      for (let metaDataItem of metaDataListObject.metaDataList) {
        metaDataItem.isActive = true;
        metaDataItem.apiKey = metaDataListObject.apiKey;
        metaDataItem.metaDataObject.teamId = new ObjectId(teamId);

        metaDataItem.metaDataObject.allowedUserIdList =
          metaDataItem.metaDataObject.allowedUserIdList &&
          Array.isArray(metaDataItem.metaDataObject.allowedUserIdList) &&
          metaDataItem.metaDataObject.allowedUserIdList.length > 0
            ? metaDataItem.metaDataObject.allowedUserIdList
            : [];
        let allowedUserStringIdList = metaDataItem.metaDataObject.allowedUserIdList.map(id => id.toString());
        if (
          currentUserProfile &&
          currentUserProfile.id &&
          currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR &&
          !allowedUserStringIdList.includes(currentUserProfile.id.toString())
        ) {
          metaDataItem.metaDataObject.allowedUserIdList.push(new ObjectId(currentUserProfile.id));
        }

        if (metaDataListObject.collectionId) {
          metaDataItem.metaDataObject.collectionId = new ObjectId(metaDataListObject.collectionId);
          if (metaDataItem.metaDataObject.objectType != ContentType.VIDEO) {
            metaDataItem.metaDataObject.videoLength = 0;
          }

          if (metaDataItem.parentList) {
            metaDataItem.parentList.push(metaDataListObject.collectionId);
          } else {
            metaDataItem.parentList = [metaDataListObject.collectionId];
          }
        }

        metaDataItem.metaDataObject.bucketName = defaultBucketName;
        let metadata = await this.metaDataRepository.findOne({
          where: {objectKey: metaDataItem.metaDataObject.objectKey},
        });
        let parentList = metaDataItem.parentList?.map(id => new ObjectId(id));

        const metaDataFeed = new InputMetaDataFeed({
          metaDataObject: metaDataItem.metaDataObject,
          metaDataPushList: metaDataItem.metaDataPushList,
          parentList: parentList,
          isActive: metaDataItem.isActive,
          apiKey: metaDataItem.apiKey,
          collectionName: metaDataListObject.collectionName,
          collectionId: metaDataListObject.collectionId,
          collectionType: metaDataListObject.collectionType,
        });

        let tempMetaDataItem: InputMetaDataFeed[] = [metaDataFeed];

        let metaDataObjectForUpdate = metadata ? [metadata] : [];

        inputMetaDataFeedGroup.push({
          _id: metaDataItem?.metaDataObject.objectKey || '',
          metaDataArray: tempMetaDataItem,
          metaDataObjectForUpdate: metaDataObjectForUpdate,
        });
      }
    } else {
      logger.debug(`no metaDataList`);
    }

    await objectMetaUpdaterService.updateMetaDataFromSkipQueue(inputMetaDataFeedGroup);
  }

  /**
   * Mark uploading status of the collection to completed
   */
  async markCollectionUploadComplete(uploadId: string, isReturnedUniqueName?: boolean) {
    // new Promise(resolve => setTimeout(resolve, 60* 1000)) // wait 1 min // ?????
    if (!uploadId) {
      logger.error(
        `Mark collection uploading completed | FileUploadHandlerService.markCOllectionUploadComplete | uploadId:${uploadId} | no uploadId`,
      );
    }
    //get upload data
    let uploadData = await this.fileUploadProgressRepository.findById(uploadId);

    // update upload status
    let progressUpdate: Partial<FileUploadProgress> = {
      status: FileUploadProgressStatus.COMPLETED,
      updatedAt: new Date(),
      uploadFinishedAt: new Date(),
    };
    await this.fileUploadProgressRepository.updateById(uploadId, progressUpdate);

    let job = await this.jobRepository.findOne({where: {'jobSpecificDetails.sessionId': `upload_${uploadId}`}});
    let uploadRemovedCount: number = job?.subJobs?.fileUploading?.uploadRemovedCount ?? 0;
    let totalFileUploadCount: number = job?.subJobs?.fileUploading?.totalCount ?? 0;

    let completedCount = job?.subJobs?.fileUploading?.completedCount ? job?.subJobs?.fileUploading?.completedCount : 0;

    let fileUploadProgress: number =
      uploadRemovedCount > 0 && totalFileUploadCount > 0 ? (completedCount / totalFileUploadCount) * 100 : 100;
    let skippedCount = job?.jobSpecificDetails?.skippedFileList?.length
      ? job?.jobSpecificDetails?.skippedFileList?.length
      : 0;

    let totalFileCountReceivedToInputMetaDataFeed = uploadData.isOverrideMetaData
      ? completedCount + skippedCount
      : completedCount;
    let totalFileCountReceivedToMediaProcess = completedCount;

    if (totalFileCountReceivedToInputMetaDataFeed && totalFileCountReceivedToInputMetaDataFeed > 0) {
      let progress =
        ((job?.subJobs?.inputMetaDataFeedDequeue?.progress || 0) +
          (job?.subJobs?.mediaProcessing?.progress || 0) +
          fileUploadProgress) /
        3;
      this.jobRepository.updateManySet(
        {_id: new ObjectId(job?._id)},
        {
          progress: progress,
          'subJobs.fileUploading.progress': fileUploadProgress,
          'subJobs.inputMetaDataFeedDequeue.totalCount': totalFileCountReceivedToInputMetaDataFeed,
          'subJobs.mediaProcessing.totalCount': totalFileCountReceivedToMediaProcess,
          status: JobStatus.inProgress,
          updatedAt: new Date(),
        },
        [],
      );
    } else {
      this.jobRepository.updateManySet(
        {_id: new ObjectId(job?._id)},
        {
          progress: fileUploadProgress,
          'subJobs.fileUploading.progress': fileUploadProgress,
          'subJobs.inputMetaDataFeedDequeue.progress': fileUploadProgress,
          'subJobs.mediaProcessing.progress': fileUploadProgress,
          'subJobs.inputMetaDataFeedDequeue.totalCount': totalFileCountReceivedToInputMetaDataFeed,
          'subJobs.mediaProcessing.totalCount': totalFileCountReceivedToMediaProcess,
          status: fileUploadProgress == 100 ? JobStatus.completed : JobStatus.failed,
          updatedAt: new Date(),
          'jobSpecificDetails.finishedAt': new Date(),
        },
        [],
      );
    }

    let collectionId = uploadData.collectionId;
    let teamId = uploadData.teamId;
    //Kelum 02/12/2022: Removed the logic to check previous ongoing uploads - to allow us to view collection when at least one upload is complete
    /*
    //check if other uploads ongoing for the collection
    let ongoingUploads = await this.fileUploadProgressRepository.findOne({
      where: {
        collectionId: collectionId,
        status: FileUploadProgressStatus.UPLOADING,
        teamId: teamId
      }
    })
    if (ongoingUploads) {
      // then there are other uploads ongoing to the collection - do nothing
      logger.info(`Mark collection uploading completed | FileUploadHandlerService.markCOllectionUploadComplete | collectionId:${collectionId} | another upload is ongoing for the collection`)
    }
    else {*/

    // update status in collection since complete
    await this.metaDataRepository.updateById(collectionId, {
      uploadInProgress: false,
      updatedAt: new Date(),
    });
    if (uploadData.collectionObjectType == ContentType.OTHER_COLLECTION) {
      // update upload pending false in the collection
      if (uploadData.imageCollectionId) {
        await this.metaDataRepository.updateById(uploadData.imageCollectionId, {
          uploadInProgress: false,
          updatedAt: new Date(),
        });
      }

      // update upload pending false in the collection
      if (uploadData.videoCollectionId) {
        await this.metaDataRepository.updateById(uploadData.videoCollectionId, {
          uploadInProgress: false,
          updatedAt: new Date(),
        });
      }
    }

    if (isReturnedUniqueName) {
      return {
        uploadedFileList: uploadData.uploadedFileList
          ? uploadData.uploadedFileList
          : job?.jobSpecificDetails?.skippedFileList
          ? job?.jobSpecificDetails?.skippedFileList
          : [],
      };
    }
    return;
  }

  /**
   * Get collection data of uploads
   * Used for thumbnail generation invoking
   */
  async getCollectionDataOfUpload(uploadId: string) {
    let uploadData = await this.fileUploadProgressRepository.findById(uploadId);

    let imageCollectionId: string | null = null;
    let videoCollectionId: string | null = null;
    let otherCollectionId: string | null = null;

    if (uploadData.collectionObjectType == ContentType.IMAGE_COLLECTION) {
      imageCollectionId = uploadData.collectionId;
    } else if (uploadData.collectionObjectType == ContentType.VIDEO_COLLECTION) {
      videoCollectionId = uploadData.collectionId;
    } else if (uploadData.collectionObjectType == ContentType.OTHER_COLLECTION) {
      otherCollectionId = uploadData.collectionId;
    }

    return {imageCollectionId, videoCollectionId, otherCollectionId};
  }

  /**
   * Check if object key exists
   * @param {string} key object key
   * @returns whether exists
   */
  async checkObjectKey(key: string) {
    return this.storageCrawlerService.storageServiceProvider.checkObjectKey(key);
  }

  /**
   * use for get annotation and image upload progress
   * @param collectionName {string} name of the collection
   * @returns progress details
   */
  async getUploadStatus(collectionName: string, currentUserProfile: UserProfileDetailed) {
    let collectionObj = await this.metaDataRepository.findOne({
      where: {
        name: collectionName,
        objectType: ContentType.IMAGE_COLLECTION,
      },
    });
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      let allowedUserIdList = collectionObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    let aggregateCollection: [{fileSize: number; fileCount: number}] = await this.metaDataRepository.aggregate([
      {$match: {collectionId: new ObjectId(collectionObj?.id)}},
      {$group: {_id: null, fileSize: {$sum: '$fileSize'}, fileCount: {$sum: '$frameCount'}}},
    ]);

    let aggregateAnnotationCounts = await this.metaDataRepository.aggregate([
      {$match: {collectionId: new ObjectId(collectionObj?.id)}},
      {
        $lookup: {
          from: 'MetaDataUpdate',
          foreignField: 'objectKey',
          localField: 'objectKey',
          as: 'annotations',
        },
      },
      {$unwind: '$annotations'},
      {$group: {_id: '$annotations.operationId', count: {$sum: 1}}},
      {$project: {operation_unique_id: '$_id', _id: 0, updated_frameCount: '$count'}},
    ]);

    let fileCount = 0;
    let fileSize = 0;
    let statPending = collectionObj?.statPending;
    let annotationStatPending = collectionObj?.annotationStatPending;
    let uploadInProgress = collectionObj?.uploadInProgress;
    let rawCount = collectionObj?.verificationStatusCount?.raw;
    let machineVerifiedCount = collectionObj?.verificationStatusCount?.machineAnnotated;
    let groundTruthCount = collectionObj?.verificationStatusCount?.verified;
    if (aggregateCollection && aggregateCollection.length > 0) {
      fileCount = aggregateCollection[0].fileCount;
      fileSize = aggregateCollection[0].fileSize;
    }

    //logger.debug(collectionObj)

    let responseObject = {
      file_size: fileSize,
      file_count: fileCount,
      is_stat_pending: statPending,
      is_annotation_stat_pending: annotationStatPending,
      is_finished_file_uploading: !uploadInProgress,
      raw_count: rawCount,
      machine_verified_count: machineVerifiedCount,
      ground_truth_count: groundTruthCount,
      annotation_counts: aggregateAnnotationCounts,
    };

    return responseObject;
  }

  /**
   * Used to save the uploaded files
   * @param basePath {string} Base path of bucket location
   * @param bucket {string} path to the local storage folder which acts as the bucket
   * @param keyValue {string} path to the desired location to store the file
   * @param request {Request} data from the request
   * @returns success status of the upload
   */
  async saveUploadedFilesToDiskStorage(
    basePath: string,
    bucket: string,
    keyValue: string,
    request: Request,
  ){
    // Ensure the directory exists before attempting to save the file
    try {
      const uploadDir = path.join(basePath + bucket, path.dirname(keyValue));
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, {recursive: true});
      }
      logger.info(`saveUploadedFilesToDiskStorage: saving file ${keyValue} to ${uploadDir}`)

      // Parse the multipart request
      const form = new multiparty.Form();

      // Parse form data
      const formData: { fields: any, files: { file: multiparty.File[] } } = await new Promise((resolve, reject) => {
        form.parse(request, (err, fields, files) => {
          if (err) {
            reject(err);
          } else {
            resolve({fields, files});
          }
        });
      });

      // Read the file content as a binary buffer
      const fileBuffer = fs.readFileSync(formData.files.file[0].path);

      // Save the file content to disk
      const filePath = path.join(basePath + bucket, keyValue);
      fs.writeFileSync(filePath, fileBuffer);
      logger.info(`saveUploadedFilesToDiskStorage: Successfully uploaded file to ${filePath}`)
      return true;
    }
    catch (error)
    {
      logger.warn(`saveUploadedFilesToDiskStorage: Failed to save uploaded file ${keyValue}`);
      logger.warn(error);
      return false;
    }
  }

}

export const FILE_UPLOAD_HANDLER = BindingKey.create<FileUploadHandlerService>('service.fileUploadHandler');
