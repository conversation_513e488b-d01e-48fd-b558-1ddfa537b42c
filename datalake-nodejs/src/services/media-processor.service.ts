/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Process media files using python scripts (geneate thumbnails, find frameCount, frameRate, etc)
 */

/**
 * @class MediaProcessorService
 * Process media files using python scripts
 * @description  geneate thumbnails, find frameCount, frameRate, etc
 * <AUTHOR>
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {default as Axios} from 'axios';
import dotenv from 'dotenv';
import {logger} from '../config';
import {FileUploadProgressStatus} from '../models/file-upload-progress.model';
import {FileUploadProgressRepository} from '../repositories/file-upload-progress.repository';
import {STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS} from '../settings/constants';
import {FILE_UPLOAD_HANDLER, FileUploadHandlerService} from './file-upload-handler.service';
import {JOB_SERVICE, JobService} from './job.service';
dotenv.config();
const PYTHON_HOST = process.env.PYTHON_BASE_URL;

const defaultBucketName = process.env.DEFAULT_BUCKET_NAME;

@injectable({scope: BindingScope.TRANSIENT})
export class MediaProcessorService {
  constructor(
    /* Add @inject to inject parameters */
    @inject(FILE_UPLOAD_HANDLER) private fileUploadHandlerService: FileUploadHandlerService,
    @inject(JOB_SERVICE) private jobService: JobService,
    @repository(FileUploadProgressRepository) private fileUploadProgressRepository: FileUploadProgressRepository,
  ) {}

  /**
   * Handle invoking thumbnail generator from an uploadId list
   * @param uploadIdList : string[]
   */
  async handleThumbnailGenerationInvokingOnUploadIdList(uploadIdList: string[]) {
    for (let uploadId of uploadIdList) {
      // get collectionIds of uploadIds
      let uploadInfo = await this.fileUploadHandlerService.getCollectionDataOfUpload(uploadId);
      let uploadIdData = {
        uploadId: uploadId,
        imageCollectionId: uploadInfo.imageCollectionId,
        videoCollectionId: uploadInfo.videoCollectionId,
        otherCollectionId: uploadInfo.otherCollectionId,
      };
      // uploadIdDataList.push(uploadIdData)

      // check ongoing uploads and invoke media processor for each uploadId
      this.handleOngoingMediaProcessCheckAndStartMediaProcessor(uploadIdData);
    }
  }

  /**
   * Upload Job Status Update Invoking On UploadIdList
   * @param uploadIdList : string[]
   * @param isMediaProcessTodo : boolean True if media processing is pending
   */
  async handleUploadJobStatusUpdateInvokingOnUploadIdList(uploadIdList: string[], isMediaProcessTodo: boolean) {
    for (let uploadId of uploadIdList) {
      // update dequeue status in upload job
      await this.jobService.uploadJobDequeueProgressUpdate(uploadId, isMediaProcessTodo == false);
    }
  }

  /**
   * Handle check ongoing media_processeors to the relavent upload invoking thumbnail generator from metadata bulk inserts list
   * @param uploadData
   */
  async handleOngoingMediaProcessCheckAndStartMediaProcessor(uploadData: {
    uploadId: string;
    imageCollectionId: string | null;
    videoCollectionId: string | null;
    otherCollectionId: string | null;
  }) {
    // check if uploadId has ongoing uploads
    let uploadObject = await this.fileUploadProgressRepository.findById(uploadData.uploadId);

    if (uploadObject && uploadObject.isThumbnailProcessingOngoing) {
      //  there is an ongoing thumbnail processor for the upload
      return;
    } else {
      // mark thumbnail processing ongoing
      await this.fileUploadProgressRepository.updateById(uploadData.uploadId, {isThumbnailProcessingOngoing: true});
      // invoke media processor for each uploadId
      await this.startMediaProcessByUploadId(uploadData);
    }
  }

  /**
   * Use to start media processing python service
   * @param uploadData: {uploadId: string, imageCollectionId: string|null, videoCollectionId: string|null}
   * used to invoke media processor from input feed
   */
  async startMediaProcessByUploadId(uploadData: {
    uploadId: string;
    imageCollectionId: string | null;
    videoCollectionId: string | null;
    otherCollectionId: string | null;
  }) {
    const thumbnail_prefix = STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS.toString();
    logger.info(
      `Process thumbnail from input feed | MediaProcessorService.startMediaProcessByUploadId | uploadId:${uploadData.uploadId} | starting python media processor`,
    );

    try {
      let url = `${PYTHON_HOST}/internal/media/process`;

      Axios({
        url,
        method: 'POST',
        data: {
          dataInputType: DataInputType.UPLOAD.toString(),
          bucketName: defaultBucketName || '',
          systemSubFolderThumbnails: thumbnail_prefix,
          dataId: uploadData.uploadId.toString(),
          imageCollectionId: uploadData.imageCollectionId || '',
          videoCollectionId: uploadData.videoCollectionId || '',
          otherCollectionId: uploadData.otherCollectionId || '',
        },
      });
    } catch (err) {
      logger.error(
        `Process thumbnail from input feed | MediaProcessorService.startMediaProcessByUploadId | N/A | failed to post request to python host, err = ${err}`,
      );
    }

    logger.debug(
      `Process thumbnail from input feed | MediaProcessorService.startMediaProcessByUploadId | N/A | starting python media processor`,
    );
  }

  /**
   * Method to restart interupted media processing (thumbnail generation of upload Ids)
   * Triggered once at server start
   **/
  async restartOngoingMediaProcessesOnAppStart() {
    logger.debug(
      `restart media processes | MediaProcessorService.restartOngoingMediaProcessesOnAppStart | N/A | Check for failed media processes of uploads - at start `,
    );
    //  find ongoing mediaprocesses in uploads
    let ongoingMediaProcessesUploads = await this.fileUploadProgressRepository.find({
      where: {
        status: FileUploadProgressStatus.UPLOADING,
        isThumbnailProcessingOngoing: true,
      },
    });
    // mark their status as failed
    if (ongoingMediaProcessesUploads && ongoingMediaProcessesUploads.length > 0) {
      logger.warn(
        `Failed media processes | MediaProcessorService.restartOngoingMediaProcessesOnAppStart | failed media process count : ${ongoingMediaProcessesUploads.length} | Failed media processes found `,
      );
      // mark all uploads with uploading status to failed on server start
      await this.fileUploadProgressRepository.updateAll(
        {/*status: FileUploadProgressStatus.FAILED,*/ isThumbnailProcessingOngoing: false},
        {status: FileUploadProgressStatus.UPLOADING, isThumbnailProcessingOngoing: true},
      );

      // call restart method to re process them (do only once)
      let uploadIdset = new Set<string>();
      for (let uploadObj of ongoingMediaProcessesUploads) {
        if (uploadObj && uploadObj.id) {
          // uploadIdArr.push(uploadObj.id)
          uploadIdset.add(uploadObj.id);
        }
      }
      let uploadIdArr: string[] = [...uploadIdset];
      this.handleThumbnailGenerationInvokingOnUploadIdList(uploadIdArr);
    }

    return;
  }
}

export enum DataInputType {
  CRAWL = 1,
  UPLOAD = 2,
}

export const MEDIA_PROCESS_HANDLER = BindingKey.create<MediaProcessorService>('service.MediaProcessor');
