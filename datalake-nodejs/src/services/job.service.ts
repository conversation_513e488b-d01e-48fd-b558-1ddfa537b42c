/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Process media files using python scripts (geneate thumbnails, find frameCount, frameRate, etc)
 */

/**
 * @class JobService
 * Use to handle job related functions
 * @description  job list, job create, job progress, job progress
 * <AUTHOR>
 */

import {BindingKey, BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ObjectId} from 'bson';
import {logger} from '../config';
import {ContentType, MetaDataUpdateObject, OperationMode, OperationType} from '../models';
import {Job, JobFilter, JobListResponse, JobSpecificDetails, JobStatus, JobType, SubJobs} from '../models/job.model';
import {JobRepository} from '../repositories/job.repository';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import {getLocalTime, regExpEscape, roundNumberToGivenDecimalPlaces} from '../settings/tools';

const SHOW_LIST_SIZE = 10;

@injectable({scope: BindingScope.TRANSIENT})
export class JobService {
  constructor(
    @repository(JobRepository) private jobRepository: JobRepository,
    @repository(MetaDataRepository) private metaDataRepository: MetaDataRepository,
  ) {}

  /**
   * create the job for the first time for upload type job
   * @param uploadId {string} upload id from file-upload-progress
   * @param collectionId {string} collection id of the upload related
   * @param userId {string} user id of the upload related
   * @param fileList {string[]} file list
   * @param collectionType {number} collection type eg:- image or video
   * @param userName {string} name of the uploading user
   */
  async updateUploadJobEntry(
    uploadId: string,
    collectionId: string,
    userId?: string,
    fileList?: string[],
    collectionType?: ContentType,
    userName?: string,
    teamId?: string,
    collectionList?: ObjectId[],
  ) {
    logger.info(
      `JobService | JobService.updateUploadJobEntry | N/A | create file upload job entry - uploadId: ${uploadId} collectionId: ${collectionId} userId: ${userId} collectionType: ${collectionType} fileList: ${fileList}`,
    );
    try {
      let collection = await this.metaDataRepository.findById(collectionId);
      let jobOwnerId = userId;
      let jobOwnerName = userName ? userName : 'Unnamed';
      let contentType = ContentType.UNSUPPORTED;

      if (collectionType == ContentType.IMAGE_COLLECTION) {
        contentType = ContentType.IMAGE;
      } else if (collectionType == ContentType.VIDEO_COLLECTION) {
        contentType = ContentType.VIDEO;
      } else if (collectionType == ContentType.OTHER_COLLECTION) {
        contentType = ContentType.OTHER;
      }

      let subJobs: SubJobs = {
        fileUploading: {
          progress: 0,
          completedCount: 0,
          totalCount: fileList ? fileList.length : 0,
        },
        inputMetaDataFeedDequeue: {
          progress: 0,
          completedCount: 0,
          totalCount: fileList ? fileList.length : 0,
        },
        mediaProcessing: {
          progress: 0,
          completedCount: 0,
          totalCount: fileList ? fileList.length : 0,
        },
      };

      let job = await this.jobRepository.create({
        jobName: collection.name,
        jobType: JobType.fileUpload,
        teamId: teamId,
        createdAt: new Date(),
        updatedAt: new Date(),
        jobOwnerId: jobOwnerId,
        jobOwnerName: jobOwnerName,
        subJobs: subJobs,
        jobSpecificDetails: {
          uploadId: new ObjectId(uploadId),
          collectionId: new ObjectId(collectionId),
          sessionId: `upload_${uploadId}`,
          allFileList: fileList,
          uploadedFileList: [],
          startedAt: new Date(),
          contentType: contentType,
          collectionList: collectionList,
        },
        progress: 0,
        status: JobStatus.queued,
      });

      logger.info(`JobService | JobService.updateUploadJobEntry | N/A | create file upload job entry success`);

      return job;
    } catch (error) {
      logger.error(
        `JobService | JobService.updateUploadJobEntry | N/A | create file upload job entry failed with error: ${error}`,
      );
    }
  }

  /**
   * Use to update upload Job Dequeue Progress
   * @param uploadId id of relevant upload record
   * @param isLastStep is this the last step of the upload (i.e. when media processing is not required)
   */
  async uploadJobDequeueProgressUpdate(uploadId: string, isLastStep: boolean) {
    logger.info(
      `JobService | JobService.uploadJobDequeueProgressUpdate | N/A | updating dequeue progress for fileUploadId: ${uploadId}, isLastStep: ${isLastStep}`,
    );

    let job = await this.jobRepository.findOne({where: {'jobSpecificDetails.sessionId': `upload_${uploadId}`}});

    let dequeueCountObject: [{count: number}] = await this.metaDataRepository.aggregate([
      {
        $match: {
          fileUploadIdList: new ObjectId(uploadId),
        },
      },
      {
        $count: 'count',
      },
    ]);
    let dequeueCount = 0;

    if (dequeueCountObject && Array.isArray(dequeueCountObject) && dequeueCountObject.length > 0) {
      dequeueCount = dequeueCountObject[0].count;
    }

    let dequeueProgress = 0;
    if (job && job.subJobs && job.subJobs.inputMetaDataFeedDequeue && job.subJobs.inputMetaDataFeedDequeue.totalCount) {
      dequeueProgress = (dequeueCount / job.subJobs.inputMetaDataFeedDequeue.totalCount) * 100;
    }

    let mediaProcessProgress = isLastStep ? 100 : job?.subJobs?.mediaProcessing?.progress || 0;
    let isDequeueCompleted = dequeueCount == job?.subJobs?.inputMetaDataFeedDequeue?.totalCount;

    let progress = (dequeueProgress + (job?.subJobs?.fileUploading?.progress || 0) + mediaProcessProgress) / 3;

    if (isLastStep && isDequeueCompleted) {
      logger.debug(
        `JobService | JobService.uploadJobDequeueProgressUpdate | N/A | no media processing: upload job is fully completed: ${uploadId}`,
      );
      let removedUploadCount: number = job?.subJobs?.fileUploading?.uploadRemovedCount ?? 0;
      let status: JobStatus = removedUploadCount > 0 ? JobStatus.inProgress : JobStatus.completed;
      //Job is fully completed
      await this.jobRepository.updateManySet(
        {_id: new ObjectId(job?._id)},
        {
          progress: progress,
          'subJobs.inputMetaDataFeedDequeue.progress': dequeueProgress,
          'subJobs.inputMetaDataFeedDequeue.completedCount': dequeueCount,
          status: status,
          updatedAt: new Date(),
          'jobSpecificDetails.finishedAt': new Date(),
        },
        [],
      );
    } else {
      //Job is still in progress
      await this.jobRepository.updateManySet(
        {_id: new ObjectId(job?._id)},
        {
          progress: progress,
          'subJobs.inputMetaDataFeedDequeue.progress': dequeueProgress,
          'subJobs.inputMetaDataFeedDequeue.completedCount': dequeueCount,
          status: isLastStep && isDequeueCompleted ? JobStatus.completed : JobStatus.inProgress,
          updatedAt: new Date(),
        },
        [],
      );
    }
  }

  /**
   * Use to get job list with status, searchKey and filterObj filters
   * @param pageIndex {number} skip the jobs
   * @param pageSize {number} page size for limit output
   * @param status {number} status of the job status
   * @param searchKey {string} search key for search on job name
   * @param filterObj {object} filterBy jobType and dates
   * @returns job list
   */
  async getJobList(
    pageIndex: number,
    pageSize: number,
    status?: number[],
    searchKey?: string,
    filterObj?: JobFilter,
    labelHashList?: {[k: string]: string},
    timeZoneOffset?: number,
  ) {
    logger.info(
      `JobService | JobService.getJobList | N/A | get joblist - pageIndex: ${pageIndex} pageSize: ${pageSize}`,
    );

    //build match query
    let matchQueryAndArray = await this.createMatchQueryList(status, searchKey, filterObj);
    let matchQuery: any;

    if (Array.isArray(matchQueryAndArray) && matchQueryAndArray.length == 0) {
      matchQuery = {};
    } else {
      matchQuery = {$and: matchQueryAndArray};
    }

    logger.info(`JobService | JobService.getJobList | N/A | get joblist - match query: ${matchQuery}`);

    let unprocessedJobList: Job[] = [];

    try {
      unprocessedJobList = await this.jobRepository.aggregate([
        {$match: matchQuery},
        {$sort: {updatedAt: -1}},
        {$skip: pageIndex * pageSize},
        {$limit: pageSize},
        {
          $addFields: {
            'jobSpecificDetails.uploadFailedFileList': {
              $setDifference: ['$jobSpecificDetails.allFileList', '$jobSpecificDetails.uploadedFileList'],
            },
          },
        },
        {
          $addFields: {
            'jobSpecificDetails.uploadFailedFileListCount': {
              $cond: {
                if: {$isArray: '$jobSpecificDetails.uploadFailedFileList'},
                then: {$size: '$jobSpecificDetails.uploadFailedFileList'},
                else: 0,
              },
            },
            'jobSpecificDetails.allFileListCount': {
              $cond: {
                if: {$isArray: '$jobSpecificDetails.allFileList'},
                then: {$size: '$jobSpecificDetails.allFileList'},
                else: 0,
              },
            },
            'jobSpecificDetails.uploadedFileListCount': {
              $cond: {
                if: {$isArray: '$jobSpecificDetails.uploadedFileList'},
                then: {$size: '$jobSpecificDetails.uploadedFileList'},
                else: 0,
              },
            },
            'jobSpecificDetails.annotationUploadTryObjectKeysCount': {
              $cond: {
                if: {$isArray: '$jobSpecificDetails.annotationUploadTryObjectKeys'},
                then: {$size: '$jobSpecificDetails.annotationUploadTryObjectKeys'},
                else: 0,
              },
            },
            'jobSpecificDetails.annotationUploadFailedObjectKeysCount': {
              $cond: {
                if: {$isArray: '$jobSpecificDetails.annotationUploadFailedObjectKeys'},
                then: {$size: '$jobSpecificDetails.annotationUploadFailedObjectKeys'},
                else: 0,
              },
            },
            'jobSpecificDetails.uploadFailedFileList': {
              $slice: ['$jobSpecificDetails.uploadFailedFileList', -1 * SHOW_LIST_SIZE],
            },
            'jobSpecificDetails.annotationUploadFailedObjectKeys': {
              $slice: ['$jobSpecificDetails.annotationUploadFailedObjectKeys', -1 * SHOW_LIST_SIZE],
            },
            'jobSpecificDetails.successFileNameList': {
              $slice: ['$jobSpecificDetails.successFileNameList', -1 * SHOW_LIST_SIZE],
            },
            'jobSpecificDetails.failedFileNameList': {
              $slice: ['$jobSpecificDetails.failedFileNameList', -1 * SHOW_LIST_SIZE],
            },
            'jobSpecificDetails.jobPartFailedReasonList': {
              $slice: ['$jobSpecificDetails.jobPartFailedReasonList', -1 * SHOW_LIST_SIZE],
            },
          },
        },
        {
          $project: {
            'jobSpecificDetails.allFileList': 0,
            'jobSpecificDetails.uploadedFileList': 0,
            'jobSpecificDetails.annotationUploadTryObjectKeys': 0,
          },
        },
      ]);
    } catch (error) {
      logger.info(`JobService | JobService.getJobList | N/A | get job list failed with error : ${error}`);
      throw new HttpErrors.NotAcceptable(`Failed with error: ${error}`);
    }

    let returnJobList = [];
    for (let job of unprocessedJobList) {
      //add job list common main fields
      let tempJob: JobListResponse = {
        jobId: job._id,
        jobName: job.jobName,
        jobType: job.jobType,
        createdDate: job.createdAt,
        jobStatus: {
          status: job.status,
          value: job.progress,
        },
        contentType: job.jobSpecificDetails?.contentType,
        drawerData: [],
      };

      switch (job.status) {
        case JobStatus.inProgress:
          let jobStatusInprogress = {
            status: JobStatus.inProgress,
            value: job.progress,
          };
          tempJob.jobStatus = jobStatusInprogress;
          break;
        case JobStatus.completed:
          let startAt = job.jobSpecificDetails?.startedAt;
          let finishAt = job.jobSpecificDetails?.finishedAt;
          let timeDiff = '';
          if (startAt && finishAt) {
            let delta = (finishAt.getTime() - startAt.getTime()) / 1000;

            // calculate (and subtract) whole days
            // var days = Math.floor(delta / 86400);
            // delta -= days * 86400;

            // calculate (and subtract) whole hours
            var hours: any = Math.floor(delta / 3600) % 24;
            delta -= hours * 3600;

            // calculate (and subtract) whole minutes
            var minutes: any = Math.floor(delta / 60) % 60;
            delta -= minutes * 60;

            // what's left is seconds
            var seconds: any = Math.ceil(delta % 60); // in theory the modulus is not required
            if (hours < 10) hours = `0${hours}`;
            if (minutes < 10) minutes = `0${minutes}`;
            if (seconds < 10) seconds = `0${seconds}`;

            timeDiff = `${hours}:${minutes}:${seconds}`;
          }

          let jobStatusCompleted = {
            status: JobStatus.completed,
            value: timeDiff ? timeDiff : job.progress,
          };
          tempJob.jobStatus = jobStatusCompleted;
          break;
        case JobStatus.queued:
          let jobStatusQueued = {
            status: JobStatus.queued,
            value: 0,
          };
          tempJob.jobStatus = jobStatusQueued;
          break;
        case JobStatus.failed:
          let jobStatusFailed = {
            status: JobStatus.failed,
          };
          tempJob.jobStatus = jobStatusFailed;
          break;
        default:
          let jobStatusDefault = {
            status: job.status,
            value: job.progress,
          };
          tempJob.jobStatus = jobStatusDefault;
          break;
      }

      //add job list common extended drawer fields
      if (job.jobSpecificDetails?.startedAt) {
        let startedAt = new Date(job.jobSpecificDetails?.startedAt);

        startedAt = timeZoneOffset ? getLocalTime(timeZoneOffset, startedAt) : startedAt;

        let dayTimeHlaf = 'AM';
        if (startedAt.getUTCHours() > 11) {
          dayTimeHlaf = 'PM';
        }

        let startYear = startedAt.getUTCFullYear();
        let startDate = startedAt.getUTCDate();
        let startMonth = startedAt.getUTCMonth() + 1;
        let _startHour = startedAt.getUTCHours();
        if (_startHour > 12) {
          _startHour = _startHour - 12;
        }
        let startHour = _startHour > 9 ? _startHour.toString() : `0${_startHour}`;
        let startMinute =
          startedAt.getUTCMinutes() > 9 ? startedAt.getUTCMinutes().toString() : `0${startedAt.getUTCMinutes()}`;
        let startSecond =
          startedAt.getUTCSeconds() > 9 ? startedAt.getUTCSeconds().toString() : `0${startedAt.getUTCSeconds()}`;

        let startedAtString = `${startMonth}/${startDate}/${startYear} ${startHour}:${startMinute}:${startSecond} ${dayTimeHlaf}`;

        tempJob.drawerData?.push({title: 'Started to Create', value: startedAtString, type: 1});
      }
      if (job.jobSpecificDetails?.finishedAt) {
        let finishedAt = new Date(job.jobSpecificDetails?.finishedAt);

        finishedAt = timeZoneOffset ? getLocalTime(timeZoneOffset, finishedAt) : finishedAt;

        let dayTimeHlaf = 'AM';
        if (finishedAt.getUTCHours() > 11) {
          dayTimeHlaf = 'PM';
        }

        let finishYear = finishedAt.getUTCFullYear();
        let finishDate = finishedAt.getUTCDate();
        let finishMonth = finishedAt.getUTCMonth() + 1;
        let _finishHour = finishedAt.getUTCHours();
        if (_finishHour > 12) {
          _finishHour = _finishHour - 12;
        }
        let finishHour = _finishHour > 9 ? _finishHour.toString() : `0${_finishHour}`;
        let finishMinute =
          finishedAt.getUTCMinutes() > 9 ? finishedAt.getUTCMinutes().toString() : `0${finishedAt.getUTCMinutes()}`;
        let finishSecond =
          finishedAt.getUTCSeconds() > 9 ? finishedAt.getUTCSeconds().toString() : `0${finishedAt.getUTCSeconds()}`;

        let finishedAtString = `${finishMonth}/${finishDate}/${finishYear} ${finishHour}:${finishMinute}:${finishSecond} ${dayTimeHlaf}`;
        tempJob.drawerData?.push({title: 'Finished', value: finishedAtString, type: 1});
      }
      if (job.jobOwnerName) tempJob.drawerData?.push({title: 'Job Owner', value: job.jobOwnerName, type: 1});

      if (job.jobSpecificDetails?.warning) {
        tempJob.drawerData?.push({title: 'Warnings', value: job.jobSpecificDetails?.warning, type: 1});
      }

      //add job list fields according to the job type
      switch (job.jobType) {
        case JobType.fileUpload:
          /**
           * JobType - File upload
           */
          tempJob.jobType = job.jobType;

          if (job.jobSpecificDetails) {
            if (job.jobSpecificDetails?.uploadedFileListCount)
              tempJob.drawerData?.push({
                title: 'Uploaded Count',
                value: job.jobSpecificDetails?.uploadedFileListCount,
                type: 1,
              });
            if (job.jobSpecificDetails?.allFileListCount)
              tempJob.drawerData?.push({
                title: 'All File Count',
                value: job.jobSpecificDetails?.allFileListCount,
                type: 1,
              });
            if (
              job?.jobSpecificDetails?.jobPartFailedReasonList &&
              Array.isArray(job.jobSpecificDetails.jobPartFailedReasonList) &&
              job.jobSpecificDetails.jobPartFailedReasonList.length > 0
            ) {
              tempJob.drawerData?.push({
                title: 'Upload fail reason list',
                value: job.jobSpecificDetails.jobPartFailedReasonList,
                type: 2,
              });
            }
            if (
              job.jobSpecificDetails?.uploadFailedFileList &&
              job.jobSpecificDetails?.uploadFailedFileListCount &&
              job.jobSpecificDetails?.uploadFailedFileListCount < SHOW_LIST_SIZE &&
              (job.status == JobStatus.completed || job.status == JobStatus.failed)
            ) {
              tempJob.drawerData?.push({
                title: 'Uploaded Failed List',
                value: job.jobSpecificDetails?.uploadFailedFileList,
                type: 2,
              });
            }
            if (
              job.jobSpecificDetails?.uploadFailedFileListCount &&
              (job.status == JobStatus.completed || job.status == JobStatus.failed)
            ) {
              tempJob.drawerData?.push({
                title: 'Uploaded Failed Count',
                value: job.jobSpecificDetails?.uploadFailedFileListCount,
                type: 1,
              });
            }
            if (job.jobSpecificDetails && job.jobSpecificDetails.contentType) {
              tempJob.drawerData?.push({
                title: 'Content Type',
                value:
                  job.jobSpecificDetails?.contentType == ContentType.IMAGE
                    ? 'Image'
                    : job.jobSpecificDetails?.contentType == ContentType.VIDEO
                    ? 'Video'
                    : job.jobSpecificDetails?.contentType == ContentType.OTHER
                    ? 'Other'
                    : 'Unknown',
                type: 1,
              });
            }
          }

          break;
        case JobType.annotationUpload:
          /**
           * JobType - Annotation upload
           */

          let labelList: string[] = [];
          if (job.jobSpecificDetails?.annotationUploadLabelList && labelHashList) {
            for (let label of job.jobSpecificDetails?.annotationUploadLabelList) {
              labelList.push(labelHashList[label]);
            }
          }

          tempJob.jobType = job.jobType;

          if (job.jobSpecificDetails) {
            if (job.jobSpecificDetails?.annotationUploadAnnotationCount)
              tempJob.drawerData?.push({
                title: 'Upload Annotation Count',
                value: job.jobSpecificDetails?.annotationUploadAnnotationCount,
                type: 1,
              });
            if (job.jobSpecificDetails?.annotationUploadTryObjectKeysCount)
              tempJob.drawerData?.push({
                title: 'Tried Image Count',
                value: job.jobSpecificDetails?.annotationUploadTryObjectKeysCount,
                type: 1,
              });
            if (
              job.jobSpecificDetails.annotationUploadFailedObjectKeysCount &&
              (job.status == JobStatus.completed || job.status == JobStatus.failed)
            )
              tempJob.drawerData?.push({
                title: 'Failed Image Count',
                value: job.jobSpecificDetails.annotationUploadFailedObjectKeysCount,
                type: 1,
              });
            if (
              job.jobSpecificDetails.annotationUploadCollectionList &&
              Array.isArray(job.jobSpecificDetails.annotationUploadCollectionList) &&
              job.jobSpecificDetails.annotationUploadCollectionList.length > 0
            )
              tempJob.drawerData?.push({
                title: 'Collection List',
                value: job.jobSpecificDetails.annotationUploadCollectionList,
                type: 2,
              });
            if (
              job.jobSpecificDetails?.annotationUploadFailedObjectKeys &&
              job.jobSpecificDetails?.annotationUploadFailedObjectKeysCount &&
              job.jobSpecificDetails?.annotationUploadFailedObjectKeysCount < SHOW_LIST_SIZE
            ) {
              tempJob.drawerData?.push({
                title: 'Failed Image List',
                value: job.jobSpecificDetails.annotationUploadFailedObjectKeys,
                type: 2,
              });
            }
            if (
              job.jobSpecificDetails.annotationUploadLabelList &&
              Array.isArray(job.jobSpecificDetails.annotationUploadLabelList) &&
              job.jobSpecificDetails.annotationUploadLabelList.length > 0
            ) {
              tempJob.drawerData?.push({title: 'Annotation Label List', value: labelList, type: 2});
            }
          }
          break;
        case JobType.annotationRemove:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.annotationRemoveOperationMode) {
            let removeType = 'Human';
            if (job.jobSpecificDetails?.annotationRemoveOperationMode == OperationMode.AUTO) {
              removeType = 'Model Run';
            }
            tempJob.drawerData?.push({title: 'Removed Annotation Type', value: removeType, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.operationId) {
            tempJob.drawerData?.push({title: 'Operation Id', value: job.jobSpecificDetails?.operationId, type: 1});
          }
          break;
        case JobType.datasetGeneration:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.datasetGroupId) {
            tempJob.drawerData?.push({title: 'Dataset Id', value: job.jobSpecificDetails?.datasetGroupId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.datasetName) {
            tempJob.drawerData?.push({title: 'Dataset Name', value: job.jobSpecificDetails?.datasetName, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.versionNo) {
            tempJob.drawerData?.push({title: 'Version No', value: job.jobSpecificDetails?.versionNo, type: 1});
          }
          break;
        case JobType.annotationProject:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.projectName) {
            tempJob.drawerData?.push({title: 'Project Name', value: job.jobSpecificDetails?.projectName, type: 1});
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.requiredFps &&
            job.jobSpecificDetails.contentType == ContentType.VIDEO
          ) {
            tempJob.drawerData?.push({title: 'Required Fps', value: job.jobSpecificDetails?.requiredFps, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.selectionId) {
            tempJob.drawerData?.push({title: 'Selection Id', value: job.jobSpecificDetails?.selectionId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.projectId) {
            tempJob.drawerData?.push({title: 'Project Id', value: job.jobSpecificDetails?.projectId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.contentTypeName) {
            tempJob.drawerData?.push({title: 'Content Type', value: job.jobSpecificDetails?.contentTypeName, type: 1});
          }
          break;
        case JobType.Trash:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.totalCount) {
            tempJob.drawerData?.push({
              title: 'Trashed total count',
              value: job.jobSpecificDetails?.totalCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.selectionId) {
            tempJob.drawerData?.push({title: 'Selection Id', value: job.jobSpecificDetails?.selectionId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.successCount) {
            tempJob.drawerData?.push({
              title: 'Trash success count',
              value: job.jobSpecificDetails?.successCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.failCount) {
            tempJob.drawerData?.push({title: 'Trash fail count', value: job.jobSpecificDetails?.failCount, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails.successFileCount) {
            tempJob.drawerData?.push({
              title: 'Trash success file count',
              value: job.jobSpecificDetails?.successFileCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails.failedFileCount) {
            tempJob.drawerData?.push({
              title: 'Trash failed file count',
              value: job.jobSpecificDetails?.failedFileCount,
              type: 1,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.successFileNameList &&
            Array.isArray(job.jobSpecificDetails?.successFileNameList) &&
            job.jobSpecificDetails?.successFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Trash success name list',
              value: job.jobSpecificDetails?.successFileNameList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.failedFileNameList &&
            Array.isArray(job.jobSpecificDetails?.failedFileNameList) &&
            job.jobSpecificDetails?.failedFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Trash fail name list',
              value: job.jobSpecificDetails?.failedFileNameList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.jobPartFailedReasonList &&
            Array.isArray(job.jobSpecificDetails?.jobPartFailedReasonList) &&
            job.jobSpecificDetails?.jobPartFailedReasonList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Trash fail reason list',
              value: job.jobSpecificDetails?.jobPartFailedReasonList,
              type: 2,
            });
          }
          break;
        case JobType.Restore:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.totalCount) {
            tempJob.drawerData?.push({
              title: 'Restored total count',
              value: job.jobSpecificDetails?.totalCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.selectionId) {
            tempJob.drawerData?.push({title: 'Selection Id', value: job.jobSpecificDetails?.selectionId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.successCount) {
            tempJob.drawerData?.push({
              title: 'Restored success count',
              value: job.jobSpecificDetails?.successCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.successFileCount) {
            tempJob.drawerData?.push({
              title: 'Restored success file count',
              value: job.jobSpecificDetails?.successFileCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.failedFileCount) {
            tempJob.drawerData?.push({
              title: 'Restored failed file count',
              value: job.jobSpecificDetails?.failedFileCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.failCount) {
            tempJob.drawerData?.push({title: 'Restored fail count', value: job.jobSpecificDetails?.failCount, type: 1});
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.successFileNameList &&
            Array.isArray(job.jobSpecificDetails?.successFileNameList) &&
            job.jobSpecificDetails?.successFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Restored success name list',
              value: job.jobSpecificDetails?.successFileNameList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.failedFileNameList &&
            Array.isArray(job.jobSpecificDetails?.failedFileNameList) &&
            job.jobSpecificDetails?.failedFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Restored fail name list',
              value: job.jobSpecificDetails?.failedFileNameList,
              type: 2,
            });
          }
          break;
        case JobType.PermanentDelete:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.totalCount) {
            tempJob.drawerData?.push({
              title: 'Permanent delete total count',
              value: job.jobSpecificDetails?.totalCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.successCount) {
            tempJob.drawerData?.push({
              title: 'Permanent delete success count',
              value: job.jobSpecificDetails?.successCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.successFileCount) {
            tempJob.drawerData?.push({
              title: 'Permanent delete success file count',
              value: job.jobSpecificDetails?.successFileCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.failedFileCount) {
            tempJob.drawerData?.push({
              title: 'Permanent delete failed file count',
              value: job.jobSpecificDetails?.failedFileCount,
              type: 1,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.failCount) {
            tempJob.drawerData?.push({
              title: 'Permanent delete fail count',
              value: job.jobSpecificDetails?.failCount,
              type: 1,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.successFileNameList &&
            Array.isArray(job.jobSpecificDetails?.successFileNameList) &&
            job.jobSpecificDetails?.successFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Permanent delete success name list',
              value: job.jobSpecificDetails?.successFileNameList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.failedFileNameList &&
            Array.isArray(job.jobSpecificDetails?.failedFileNameList) &&
            job.jobSpecificDetails?.failedFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Permanent delete fail name list',
              value: job.jobSpecificDetails?.failedFileNameList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.jobPartFailedReasonList &&
            Array.isArray(job.jobSpecificDetails?.jobPartFailedReasonList) &&
            job.jobSpecificDetails?.jobPartFailedReasonList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Permanent delete file fail reason list',
              value: job.jobSpecificDetails?.jobPartFailedReasonList,
              type: 2,
            });
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.jobFailedReasonList &&
            Array.isArray(job.jobSpecificDetails?.jobFailedReasonList) &&
            job.jobSpecificDetails?.jobFailedReasonList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Permanent delete job fail reason list',
              value: job.jobSpecificDetails?.jobFailedReasonList,
              type: 2,
            });
          }
          break;

        case JobType.DownloadDataset:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.datasetGroupId) {
            tempJob.drawerData?.push({title: 'Dataset Id', value: job.jobSpecificDetails?.datasetGroupId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.versionId) {
            tempJob.drawerData?.push({title: 'Dataset Version Id', value: job.jobSpecificDetails?.versionId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.datasetName) {
            tempJob.drawerData?.push({title: 'Dataset Name', value: job.jobSpecificDetails?.datasetName, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.versionNo) {
            tempJob.drawerData?.push({title: 'Version No', value: job.jobSpecificDetails?.versionNo, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.exportFormatType) {
            tempJob.drawerData?.push({
              title: 'Export Format Type',
              value: job.jobSpecificDetails?.exportFormatType,
              type: 1,
            });
          }
          break;
        case JobType.DownloadProject:
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.projectIdList &&
            Array.isArray(job.jobSpecificDetails?.projectIdList) &&
            job.jobSpecificDetails?.projectIdList.length > 0
          ) {
            tempJob.drawerData?.push({title: 'Project Id List', value: job.jobSpecificDetails?.projectIdList, type: 2});
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.statusList &&
            Array.isArray(job.jobSpecificDetails?.statusList) &&
            job.jobSpecificDetails?.statusList.length > 0
          ) {
            tempJob.drawerData?.push({title: 'Task Status List', value: job.jobSpecificDetails?.statusList, type: 2});
          }
          break;
        case JobType.DownloadCollection:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.collectionId) {
            tempJob.drawerData?.push({title: 'Collection Id', value: job.jobSpecificDetails?.collectionId, type: 1});
          }
          if (
            job.jobSpecificDetails &&
            job.jobSpecificDetails?.operationIdList &&
            Array.isArray(job.jobSpecificDetails?.operationIdList) &&
            job.jobSpecificDetails?.operationIdList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Operation List',
              value: job.jobSpecificDetails?.operationIdList,
              type: 2,
            });
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.annotationTypeString) {
            tempJob.drawerData?.push({
              title: 'Annotation Type',
              value: job.jobSpecificDetails?.annotationTypeString,
              type: 1,
            });
          }
          break;
        case JobType.VirtualCollection:
          if (job.jobSpecificDetails && job.jobSpecificDetails?.collectionId) {
            tempJob.drawerData?.push({title: 'Collection Id', value: job.jobSpecificDetails?.collectionId, type: 1});
          }
          if (job.jobSpecificDetails && job.jobSpecificDetails?.error) {
            tempJob.drawerData?.push({title: 'Error', value: job.jobSpecificDetails?.error, type: 1});
          }
          break;

        case JobType.ObjectRemove:
          if (job?.jobSpecificDetails?.totalObjectToBeRemovedCount) {
            tempJob.drawerData?.push({
              title: 'Remove total count',
              value: job.jobSpecificDetails.totalObjectToBeRemovedCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.selectionId) {
            tempJob.drawerData?.push({title: 'Selection Id', value: job.jobSpecificDetails?.selectionId, type: 1});
          }
          if (job?.jobSpecificDetails?.objectRemoveSuccessCount) {
            tempJob.drawerData?.push({
              title: 'Remove success count',
              value: job.jobSpecificDetails?.objectRemoveSuccessCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.objectRemovedFailedCount) {
            tempJob.drawerData?.push({
              title: 'Remove fail count',
              value: job.jobSpecificDetails?.objectRemovedFailedCount,
              type: 1,
            });
          }

          if (
            job?.jobSpecificDetails?.successRemovedFileNameList &&
            Array.isArray(job.jobSpecificDetails.successRemovedFileNameList) &&
            job.jobSpecificDetails.successRemovedFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Remove success name list',
              value: job.jobSpecificDetails.successRemovedFileNameList,
              type: 2,
            });
          }
          if (
            job?.jobSpecificDetails?.failedRemovedFileNameList &&
            Array.isArray(job.jobSpecificDetails.failedRemovedFileNameList) &&
            job.jobSpecificDetails.failedRemovedFileNameList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Remove fail name list',
              value: job.jobSpecificDetails.failedRemovedFileNameList,
              type: 2,
            });
          }
          if (
            job?.jobSpecificDetails?.jobPartFailedReasonList &&
            Array.isArray(job.jobSpecificDetails.jobPartFailedReasonList) &&
            job.jobSpecificDetails.jobPartFailedReasonList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Remove fail reason list',
              value: job.jobSpecificDetails.jobPartFailedReasonList,
              type: 2,
            });
          }
          break;
        case JobType.UploadEmbedding:
          if (job?.jobSpecificDetails?.embeddingUploadTotalCount) {
            tempJob.drawerData?.push({
              title: 'Embedding upload total count',
              value: job.jobSpecificDetails.embeddingUploadTotalCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.modelName) {
            tempJob.drawerData?.push({title: 'Model name', value: job.jobSpecificDetails?.modelName, type: 1});
          }
          if (
            job?.jobSpecificDetails?.embeddingUploadFailedObjectKeys &&
            Array.isArray(job?.jobSpecificDetails?.embeddingUploadFailedObjectKeys) &&
            job?.jobSpecificDetails?.embeddingUploadFailedObjectKeys.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Failed object keys',
              value: job.jobSpecificDetails?.embeddingUploadFailedObjectKeys,
              type: 2,
            });
          }
          if (
            job?.jobSpecificDetails?.embeddingUploadTryObjectKeys &&
            Array.isArray(job?.jobSpecificDetails?.embeddingUploadTryObjectKeys) &&
            job?.jobSpecificDetails?.embeddingUploadTryObjectKeys.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Tried object keys',
              value: job.jobSpecificDetails?.embeddingUploadTryObjectKeys,
              type: 2,
            });
          }
          if (job?.jobSpecificDetails?.embeddingUploadSuccessCount) {
            tempJob.drawerData?.push({
              title: 'Embedding upload success count',
              value: job.jobSpecificDetails?.embeddingUploadSuccessCount,
              type: 1,
            });
          }

          if (
            job?.jobSpecificDetails?.errorList &&
            Array.isArray(job.jobSpecificDetails.errorList) &&
            job.jobSpecificDetails.errorList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Embedding upload failed reasons list',
              value: job.jobSpecificDetails.errorList,
              type: 2,
            });
          }
          break;
        case JobType.GenerateEmbedding:
          if (job?.jobSpecificDetails?.embeddingGenerateTotalCount) {
            tempJob.drawerData?.push({
              title: 'Embedding generate total count',
              value: job.jobSpecificDetails.embeddingGenerateTotalCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.completedCount) {
            tempJob.drawerData?.push({
              title: 'Embedding generate success count',
              value: job.jobSpecificDetails?.completedCount,
              type: 1,
            });
          }
          if (
            job?.jobSpecificDetails?.errorList &&
            Array.isArray(job.jobSpecificDetails.errorList) &&
            job.jobSpecificDetails.errorList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Embedding generate failed reasons list',
              value: job.jobSpecificDetails.errorList,
              type: 2,
            });
          }
          break;
        case JobType.GenerateAutoTagging:
          if (job?.jobSpecificDetails?.autoTaggingGenerateTotalCount) {
            tempJob.drawerData?.push({
              title: 'Auto tagging generate total count',
              value: job.jobSpecificDetails.autoTaggingGenerateTotalCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.completedCount) {
            tempJob.drawerData?.push({
              title: 'Auto tagging generate success count',
              value: job.jobSpecificDetails?.completedCount,
              type: 1,
            });
          }
          if (
            job?.jobSpecificDetails?.errorList &&
            Array.isArray(job.jobSpecificDetails.errorList) &&
            job.jobSpecificDetails.errorList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Auto tagging generate failed reasons list',
              value: job.jobSpecificDetails.errorList,
              type: 2,
            });
          }
          break;
        case JobType.GenerateAutoAnnotation:
          if (job?.jobSpecificDetails?.autoAnnotationGenerateTotalCount) {
            tempJob.drawerData?.push({
              title: 'Auto annotation generate total count',
              value: job.jobSpecificDetails.autoAnnotationGenerateTotalCount,
              type: 1,
            });
          }
          if (job?.jobSpecificDetails?.completedCount) {
            tempJob.drawerData?.push({
              title: 'Auto annotation generate success count',
              value: job.jobSpecificDetails?.completedCount,
              type: 1,
            });
          }
          if (
            job?.jobSpecificDetails?.errorList &&
            Array.isArray(job.jobSpecificDetails.errorList) &&
            job.jobSpecificDetails.errorList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Auto annotation generate failed reasons list',
              value: job.jobSpecificDetails.errorList,
              type: 2,
            });
          }
          break;

        case JobType.CollectionMerge:
          if (job?.jobSpecificDetails?.selectionId) {
            tempJob.drawerData?.push({title: 'Selection Id', value: job.jobSpecificDetails?.selectionId, type: 1});
          }
          if (
            job?.jobSpecificDetails?.jobPartFailedReasonList &&
            Array.isArray(job.jobSpecificDetails.jobPartFailedReasonList) &&
            job.jobSpecificDetails.jobPartFailedReasonList.length > 0
          ) {
            tempJob.drawerData?.push({
              title: 'Merge fail reason list',
              value: job.jobSpecificDetails.jobPartFailedReasonList,
              type: 2,
            });
          }

          break;

        default:
          /**
           * JobType - Unknown
           */

          tempJob.jobType = JobType.UnknownJob;
          break;
      }
      returnJobList.push(tempJob);
    }

    return returnJobList;
  }

  /**
   * Use to get job list count with status, searchKey and filterObj filters
   * @param status {number} status of the job status
   * @param searchKey {string} search key for searh on job name
   * @param filterObj {objcet} filterBy jobType and dates
   * @returns job list count
   */
  async getTotalJobCount(status?: number[], searchKey?: string, filterObj?: JobFilter) {
    //build match query
    let matchQuery: any;
    let matchQueryAndArray = await this.createMatchQueryList(status, searchKey, filterObj);

    if (Array.isArray(matchQueryAndArray) && matchQueryAndArray.length == 0) {
      matchQuery = {};
    } else {
      matchQuery = {$and: matchQueryAndArray};
    }

    logger.info(`JobService | JobService.getTotalJobCount | N/A | match query : ${matchQuery}`);

    try {
      let JobsCount: [{count: number}] = await this.jobRepository.aggregate([{$match: matchQuery}, {$count: 'count'}]);
      let totalCount = 0;
      if (Array.isArray(JobsCount) && JobsCount.length > 0 && JobsCount[0].count) {
        totalCount = JobsCount[0].count;
      }

      return {count: totalCount};
    } catch (error) {
      logger.info(`JobService | JobService.getTotalJobCount | N/A | job list count failed with error : ${error}`);
      throw new HttpErrors.NotAcceptable(`Failed with error: ${error}`);
    }
  }

  /**
   * create the match query for job aggregate filter
   * @param status {number} status of the job status
   * @param searchKey {string} search key for searh on job name
   * @param filterObj {objcet} filterBy jobType and dates
   * @returns match query
   */
  async createMatchQueryList(status?: number[], searchKey?: string, filterObj?: JobFilter) {
    let matchQueryAndArray: any[] = [];

    if (status) {
      matchQueryAndArray.push({status: {$in: status}});
    }
    if (searchKey) {
      const pattern = new RegExp(regExpEscape(searchKey), 'i');
      matchQueryAndArray.push({jobName: pattern});
    }
    if (filterObj) {
      if (Array.isArray(filterObj.filterBy) && filterObj.filterBy.length > 0) {
        matchQueryAndArray.push({jobType: {$in: filterObj.filterBy}});
      }
      try {
        if (filterObj.fromDate || filterObj.toDate) {
          let createdAtFilter: any = {};

          if (filterObj.fromDate) {
            let fromDate = new Date(filterObj.fromDate); //await this.createDateForFiter(filterObj.fromDate, false)

            createdAtFilter['$gte'] = fromDate;
          }
          if (filterObj.toDate) {
            let toDate = new Date(new Date(filterObj.toDate).getTime() + 24 * 3600 * 1000); //await this.createDateForFiter(filterObj.toDate, true)
            createdAtFilter['$lte'] = toDate;
          }
          //logger.debug(createdAtFilter)
          matchQueryAndArray.push({createdAt: createdAtFilter});
        }
      } catch (error) {
        logger.info(
          `JobService | JobService.createMatchQueryList | N/A | create Match Query failed with error : ${error}`,
        );
      }
    }

    return matchQueryAndArray;
  }

  /**
   * update the job for annotation uplaod
   * @param sessionId {string} unique id for identify the same session for job
   * @param totalImageCount {number}
   * @param uploadedImageCount {number}
   * @param MetaDataUpdateObjectArray {MetaDataUpdateObject[]}
   * @param operationId {string}
   * @param operationType {number}
   * @param operationMode {number}
   * @param teamId {string} team id of the job owner
   * @param userId {string} id of the job owner
   * @param userName {string} name of the job owner
   * @param allArray {string} all object Key array of batch
   * @param faildeArray {string[]} failed object Key array of batch
   */
  async updateAnnotationUploadJobEntry(
    sessionId: string,
    totalImageCount: number,
    uploadedImageCount: number,
    MetaDataUpdateObjectArray: MetaDataUpdateObject[],
    operationId: string,
    operationType: OperationType,
    operationMode: OperationMode,
    teamId?: string,
    userId?: string | null,
    userName?: string | null,
    allArray?: string[],
    faildeArray?: string[],
  ) {
    logger.info(`JobService | JobService.createAnnotationUploadJobEntry | N/A |
    create annotation upload job entry sessionId: ${sessionId}
    totalImageCount : ${totalImageCount}
    uploadedImageCount : ${uploadedImageCount}
    MetaDataUpdateObjectArray length : ${MetaDataUpdateObjectArray.length}
    operationId : ${operationId}
    operationType : ${operationType}
    operationMode : ${operationMode}
    `);

    let jobOwnerId = userId;
    let jobOwnerName = userName ? userName : 'Unnamed';
    let contentType = ContentType.IMAGE;

    let existingSession = await this.jobRepository.findOne({where: {'jobSpecificDetails.sessionId': sessionId}});

    let progress = 0;
    let status = JobStatus.inProgress;
    if (totalImageCount && uploadedImageCount) {
      progress = (uploadedImageCount / totalImageCount) * 100;
    }
    if (progress == 100) {
      status = JobStatus.completed;
    }
    if (!faildeArray || !Array.isArray(faildeArray)) faildeArray = [];
    if (!allArray || !Array.isArray(allArray)) allArray = [];

    let annotationUploadAnnotationCount = 0;
    let annotationUploadLabelList: string[] = existingSession?.jobSpecificDetails?.annotationUploadLabelList || [];
    let annotationUploadCollectionList: string[] = [];
    let annotationUploadCollectionIdList: ObjectId[] = [];
    if (existingSession?.jobSpecificDetails?.annotationUploadCollectionList) {
      annotationUploadCollectionList.map(id => id.toString());
    }
    for (let metaData of MetaDataUpdateObjectArray) {
      let uniqueIdentifier = metaData.objectKey ? metaData.objectKey : `${metaData.bucketName}-${metaData.storagePath}`;
      if (!faildeArray.includes(uniqueIdentifier)) {
        annotationUploadAnnotationCount += metaData.data.annotationObjects?.length || 0;
        let collectionIdList = [];
        let collectionId;
        if (metaData.data.collectionId) {
          collectionId = metaData.data.collectionId;
          if (collectionId && !annotationUploadCollectionList.includes(collectionId.toString())) {
            annotationUploadCollectionList.push(collectionId.toString());
          }
        } else {
          let whereObj = null;
          if (metaData.objectKey) {
            whereObj = {where: {objectKey: metaData.objectKey}};
          } else {
            whereObj = {where: {bucketName: metaData.bucketName, storagePath: metaData.storagePath}};
          }
          let metaDataObj = await this.metaDataRepository.findOne(whereObj);
          if (metaDataObj && metaDataObj.vCollectionIdList && metaDataObj.vCollectionIdList.length > 0) {
            collectionIdList = metaDataObj.vCollectionIdList;
            collectionIdList.forEach(oId => {
              if (oId && !annotationUploadCollectionList.includes(oId.toString())) {
                annotationUploadCollectionList.push(oId.toString());
              }
            });
          }
        }

        // if (collectionId && !annotationUploadCollectionList.includes(collectionId.toString())) {
        //   annotationUploadCollectionList.push(collectionId.toString());
        // }

        annotationUploadCollectionIdList = annotationUploadCollectionList.map(id => new ObjectId(id));
        if (!metaData.data.annotationObjects) continue;
        for (let annotation of metaData.data.annotationObjects) {
          if (annotation.label?.label && !annotationUploadLabelList.includes(annotation.label?.label)) {
            annotationUploadLabelList.push(annotation.label?.label);
          }
        }
      } else {
        // if (!annotationUploadCollectionList.includes(new ObjectId(metaData.data.collectionId))) {
        //   annotationUploadCollectionList.push(new ObjectId(metaData.data.collectionId))
        // }
        // if (!metaData.data.annotationObjects) continue
        // for (let annotation of metaData.data.annotationObjects) {
        //   if (annotation.label?.label && !annotationUploadLabelList.includes(annotation.label?.label)) {
        //     annotationUploadLabelList.push(annotation.label?.label)
        //   }
        // }
      }
    }

    let annotationUploadFailedObjectKeys: string[] = [];
    let annotationUploadTryObjectKeys: string[] = [];

    if (!existingSession) {
      if (status != JobStatus.completed) {
        status = JobStatus.queued;
      }
      //create if sessionId not exists on jobs
      await this.jobRepository.create({
        jobName: `Annotation-upload-${operationId}`,
        jobType: JobType.annotationUpload,
        createdAt: new Date(),
        updatedAt: new Date(),
        teamId: teamId,
        jobOwnerId: jobOwnerId,
        jobOwnerName: jobOwnerName,
        jobSpecificDetails: {
          sessionId: sessionId,
          startedAt: new Date(),
          contentType: contentType,
          annotationUploadTotalCount: totalImageCount,
          annotationUploadOperationMode: operationMode,
          annotationUploadOperationType: operationType,
          annotationUploadTryObjectKeys: allArray,
          annotationUploadFailedObjectKeys: faildeArray,
          annotationUploadAnnotationCount: annotationUploadAnnotationCount,
          annotationUploadCollectionList: annotationUploadCollectionIdList,
          annotationUploadLabelList: annotationUploadLabelList,
        },
        progress: progress,
        status: status,
      });
    } else {
      //update if sessionId exists on jobs
      annotationUploadFailedObjectKeys = existingSession.jobSpecificDetails?.annotationUploadFailedObjectKeys || [];
      annotationUploadFailedObjectKeys = [...annotationUploadFailedObjectKeys, ...faildeArray];

      annotationUploadTryObjectKeys = existingSession.jobSpecificDetails?.annotationUploadTryObjectKeys || [];
      annotationUploadTryObjectKeys = [...annotationUploadTryObjectKeys, ...allArray];

      annotationUploadAnnotationCount += existingSession.jobSpecificDetails?.annotationUploadAnnotationCount || 0;

      await this.jobRepository.updateManySet(
        {'jobSpecificDetails.sessionId': sessionId},
        {
          progress: progress,
          status: status,
          'jobSpecificDetails.annotationUploadTryObjectKeys': annotationUploadTryObjectKeys,
          'jobSpecificDetails.annotationUploadFailedObjectKeys': annotationUploadFailedObjectKeys,
          'jobSpecificDetails.annotationUploadAnnotationCount': annotationUploadAnnotationCount,
          'jobSpecificDetails.annotationUploadCollectionList': annotationUploadCollectionList,
          'jobSpecificDetails.annotationUploadLabelList': annotationUploadLabelList,
          updatedAt: new Date(),
        },
        [],
      );
    }

    //update job finish time adter progress become 100
    if (progress == 100) {
      await this.jobRepository.updateManySet(
        {'jobSpecificDetails.sessionId': sessionId},
        {
          'jobSpecificDetails.finishedAt': new Date(),
          updatedAt: new Date(),
        },
        [],
      );
    }
  }

  /**
   * use for set failed job status
   */
  async setFailedJobsStatus() {
    let currentTime: number = new Date().getTime();
    let timeBeforSetTimeInterval = new Date(currentTime - 15 * 60 * 1000);

    let failedJobList: {_id: string; jobType: number; collectionId?: ObjectId}[] = await this.jobRepository.aggregate([
      {
        $match: {
          status: {$in: [JobStatus.inProgress, JobStatus.queued]},
          updatedAt: {$lte: timeBeforSetTimeInterval},
        },
      },
      {$project: {_id: 1, jobType: 1, collectionId: '$jobSpecificDetails.collectionId'}},
    ]);

    let idList: ObjectId[] = failedJobList.map(obj => new ObjectId(obj._id));

    this.jobRepository.updateManySet({_id: {$in: idList}}, {status: JobStatus.failed, updatedAt: new Date()}, []);

    //reset 'uploadInProgress' flag in collection for upload failed jobs
    let uploadTimeOutJobs = failedJobList.filter(_job => _job.jobType == JobType.fileUpload);
    let uploadTimeOutCollections: ObjectId[] = [];
    for (let _upload of uploadTimeOutJobs) {
      if (_upload.collectionId) {
        uploadTimeOutCollections.push(new ObjectId(_upload.collectionId));
      }
    }
    if (uploadTimeOutCollections.length > 0) {
      this.metaDataRepository.updateManySet(
        {_id: {$in: uploadTimeOutCollections}},
        {uploadInProgress: false, updatedAt: new Date()},
        [],
      );
    }
  }

  /**
   * Use to create of update annotation remove job entry
   * @param sessionId {string} job session id
   * @param collectionId {string} annotation collection id
   * @param operationId {string} annotation operation id
   * @param status {number} job status
   * @param operationType {number} operation type of  annotation
   * @param operationMode {number} operation mode of annotations
   * @param progress {number} progress of the job
   * @param teamId {string} team id user
   * @param userId {string} id of the user
   * @param userName {string} name of the user
   */
  async updateAnnotationRemoveJobEntry(
    sessionId: string,
    collectionId: string,
    operationId: string,
    status: JobStatus,
    operationType?: OperationType,
    operationMode?: OperationMode,
    progress?: number,
    teamId?: string,
    userId?: string | null,
    userName?: string | null,
  ) {
    logger.info(`JobService | JobService.createAnnotationUploadJobEntry | N/A |
      create annotation delete job entry collectionID: ${collectionId}
      operationId : ${operationId}
      operationType : ${operationType}
    `);

    let jobOwnerId = userId;
    let jobOwnerName = userName ? userName : 'Unnamed';
    let contentType = ContentType.IMAGE;

    if (progress == 100) {
      status = JobStatus.completed;
    }

    let existingSession = await this.jobRepository.findOne({
      where: {
        'jobSpecificDetails.sessionId': sessionId,
        'jobSpecificDetails.collectionId': new ObjectId(collectionId),
        jobType: JobType.annotationRemove,
      },
    });

    if (!existingSession) {
      //create if sessionId not exists on jobs
      await this.jobRepository.create({
        jobName: `Annotation-Remove-${operationId}`,
        jobType: JobType.annotationRemove,
        createdAt: new Date(),
        updatedAt: new Date(),
        teamId: teamId,
        jobOwnerId: jobOwnerId,
        jobOwnerName: jobOwnerName,
        jobSpecificDetails: {
          operationId: operationId,
          sessionId: sessionId,
          collectionId: new ObjectId(collectionId),
          startedAt: new Date(),
          contentType: contentType,
          annotationRemoveOperationType: operationType,
          annotationRemoveOperationMode: operationMode,
        },
        progress: progress,
        status: JobStatus.queued,
      });
    } else {
      //update if sessionId exists on jobs

      await this.jobRepository.updateManySet(
        {
          'jobSpecificDetails.collectionId': new ObjectId(collectionId),
          'jobSpecificDetails.sessionId': sessionId,
          jobType: JobType.annotationRemove,
        },
        {
          progress: progress,
          status: status,
          'jobSpecificDetails.annotationRemoveOperationType': operationType,
          'jobSpecificDetails.annotationRemoveOperationMode': operationMode,
          updatedAt: new Date(),
        },
        [],
      );
    }

    //update job finish time adter progress become 100
    if (progress == 100 || status == JobStatus.completed) {
      await this.jobRepository.updateManySet(
        {
          'jobSpecificDetails.collectionId': new ObjectId(collectionId),
          jobType: JobType.annotationRemove,
        },
        {
          'jobSpecificDetails.finishedAt': new Date(),
          updatedAt: new Date(),
        },
        [],
      );
    }
  }

  /**
   * Use to create common jon entry or update job entry
   * @param jobName {string} job name
   * @param sessionId {string} job session id
   * @param jobOwnerId {string} job owner id
   * @param teamId {string} job team id
   * @param jobOwnerName {string} job owner name
   * @param jobType {number} job type
   * @param progress {number} job progress
   * @param status {number} job status
   * @param jobSpecificDetails {object} job details
   */
  async createOrUpdateJob(
    jobName: string,
    sessionId: string,
    jobOwnerId?: string,
    teamId?: string,
    jobOwnerName?: string,
    jobType?: number,
    progress?: number,
    status?: number,
    jobSpecificDetails?: JobSpecificDetails,
    subJobs?: SubJobs,
  ) {
    logger.info(`JobService | JobService.createAnnotationUploadJobEntry | N/A |
      create job entry jobName: ${jobName}
      sessionId : ${sessionId}
      jobOwnerId : ${jobOwnerId}
      teamId : ${teamId}
      jobOwnerName : ${jobOwnerName}
      jobType : ${jobType}
      progress : ${progress}
      status : ${status}
      jobSpecificDetails : ${jobSpecificDetails},
      subJobs: ${JSON.stringify(subJobs)}
    `);

    let existingSession = await this.jobRepository.findOne({
      where: {
        'jobSpecificDetails.sessionId': sessionId,
        jobType: jobType,
      },
    });
    let newSessionObj: Partial<Job> = {};

    if (!existingSession) {
      let tempJobSpecificDetails = jobSpecificDetails ? jobSpecificDetails : {};

      tempJobSpecificDetails['sessionId'] = sessionId;
      tempJobSpecificDetails.startedAt = new Date();

      switch (jobType) {
        case JobType.datasetGeneration:
          let datasetMeta = await this.metaDataRepository.findOne({
            where: {
              datasetGroupId: jobSpecificDetails?.datasetGroupId,
            },
          });
          if (jobSpecificDetails) {
            tempJobSpecificDetails.datasetMetaId = datasetMeta?.id;
            tempJobSpecificDetails.datasetName = datasetMeta?.name;
          }
      }

      newSessionObj = await this.jobRepository.insert({
        jobName: jobName,
        sessionId: sessionId,
        jobOwnerId: teamId ? new ObjectId(teamId) : null,
        teamId: teamId ? new ObjectId(teamId) : null,
        jobOwnerName: jobOwnerName,
        createdAt: new Date(),
        updatedAt: new Date(),
        jobType: jobType,
        progress: progress,
        status: status,
        jobSpecificDetails: tempJobSpecificDetails,
        subJobs: subJobs,
      });

      //update job finish time adter progress become 100
      if (progress == 100 || status == JobStatus.completed) {
        await this.jobRepository.updateManySet(
          {
            jobType: jobType,
            'jobSpecificDetails.sessionId': sessionId,
          },
          {
            'jobSpecificDetails.finishedAt': new Date(),
            updatedAt: new Date(),
          },
          [],
        );
      }
    } else {
      if (!jobSpecificDetails) {
        jobSpecificDetails = existingSession.jobSpecificDetails;
      } else {
        jobSpecificDetails = {
          ...existingSession.jobSpecificDetails,
          ...jobSpecificDetails,
        };
        switch (jobType) {
          case JobType.annotationProject:
            let uploadIdList = existingSession.jobSpecificDetails?.uploadIdList || [];
            if (jobSpecificDetails.uploadIdList) {
              for (let uploadId of jobSpecificDetails.uploadIdList) {
                uploadIdList.push(uploadId);
              }
            }
            jobSpecificDetails.uploadIdList = uploadIdList;
            if (existingSession.jobSpecificDetails?.errorList) {
              let errorList = existingSession.jobSpecificDetails?.errorList || [];
              errorList.push(jobSpecificDetails?.errorList);
              jobSpecificDetails.errorList = errorList;
            }
            break;
          case JobType.UploadEmbedding:
            if (existingSession.jobSpecificDetails?.errorList) {
              let errorList = existingSession.jobSpecificDetails?.errorList || [];
              errorList.push(jobSpecificDetails?.errorList);
              jobSpecificDetails.errorList = errorList;
            }
            let successObjectKeys = existingSession.jobSpecificDetails?.embeddingUploadTryObjectKeys || [];
            if (jobSpecificDetails.embeddingUploadTryObjectKeys) {
              for (let objectKey of jobSpecificDetails.embeddingUploadTryObjectKeys) {
                if (!successObjectKeys.includes(objectKey)) successObjectKeys.push(objectKey);
              }
            }
            jobSpecificDetails.embeddingUploadTryObjectKeys = successObjectKeys;
            let tryObjectKeys = existingSession.jobSpecificDetails?.embeddingUploadFailedObjectKeys || [];
            if (jobSpecificDetails.embeddingUploadFailedObjectKeys) {
              for (let tryObjectKey of jobSpecificDetails.embeddingUploadFailedObjectKeys) {
                if (!tryObjectKeys.includes(tryObjectKey)) tryObjectKeys.push(tryObjectKey);
              }
            }
            jobSpecificDetails.embeddingUploadFailedObjectKeys = tryObjectKeys;
            break;
          case JobType.GenerateAutoTagging:
          case JobType.GenerateAutoAnnotation:
          case JobType.GenerateEmbedding:
            if (existingSession.jobSpecificDetails?.errorList) {
              let errorList = existingSession.jobSpecificDetails?.errorList || [];
              if (Array.isArray(jobSpecificDetails.errorList)) {
                for (let errorMessage of jobSpecificDetails.errorList) {
                  if (!errorList.includes(errorMessage)) errorList.push(errorMessage);
                }
              } else {
                errorList.push(jobSpecificDetails?.errorList);
              }
              jobSpecificDetails.errorList = errorList;
            }
            break;
        }

        await this.jobRepository.updateManySet(
          {
            jobType: jobType,
            'jobSpecificDetails.sessionId': sessionId,
          },
          {
            progress: progress,
            status: status,
            updatedAt: new Date(),
            jobSpecificDetails: jobSpecificDetails,
          },
          [],
        );
      }

      if (!subJobs) {
        subJobs = existingSession.subJobs;
      } else {
        subJobs = {
          ...existingSession.subJobs,
          ...subJobs,
        };
      }

      switch (jobType) {
        case JobType.datasetGeneration:
          //if (existingSession.subJobs) {
          let augmentationProgress = existingSession.subJobs?.augmentation?.progress || 0;
          let generationProgress = existingSession.subJobs?.generation?.progress || 0;
          let currentAugmentationProgress = subJobs?.augmentation?.progress || 0;
          let currentGenerationProgress = subJobs?.generation?.progress || 0;
          augmentationProgress =
            currentAugmentationProgress > augmentationProgress ? currentAugmentationProgress : augmentationProgress;
          generationProgress =
            currentGenerationProgress > generationProgress ? currentGenerationProgress : generationProgress;
          progress = (augmentationProgress || 0 + generationProgress || 0) / 2;
        //}
      }

      await this.jobRepository.updateManySet(
        {
          _id: new ObjectId(existingSession._id),
        },
        {
          progress: progress,
          status: status,
          updatedAt: new Date(),
          jobSpecificDetails: jobSpecificDetails,
          subJobs: subJobs,
        },
        [],
      );
    }

    //update job finish time adter progress become 100
    if (progress == 100 || status == JobStatus.completed) {
      await this.jobRepository.updateManySet(
        {
          jobType: jobType,
          'jobSpecificDetails.sessionId': sessionId,
        },
        {
          'jobSpecificDetails.finishedAt': new Date(),
          status: JobStatus.completed,
          progress: 100,
          updatedAt: new Date(),
        },
        [],
      );
    }

    return existingSession ? existingSession : newSessionObj['ops'][0];
  }

  /**
   * Use to the job status
   * @param jobId {string} id of the job
   * @returns status of the job
   */
  async getJobStatus(jobId: string) {
    try {
      let jobDetails = await this.jobRepository.findById(jobId);

      logger.info(`get job status | JobService.getJobStatus | N/A | get status for jobId: ${jobId} success`);
      return {
        status: jobDetails.status,
        progress: roundNumberToGivenDecimalPlaces(2, jobDetails.progress),
        JobType: jobDetails.jobType,
        isSuccess: true,
      };
    } catch (error) {
      logger.error(`get job status | JobService.getJobStatus | N/A | get status for jobId: ${jobId} failed`);
      return {
        isSuccess: false,
      };
    }
  }
}

export const JOB_SERVICE = BindingKey.create<JobService>('service.Job');
