/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the system label related logics
 */

/**
 * @class SystemLabelService
 * purpose of this service is to manage system labels
 * @description Perfome the system label related logics
 * <AUTHOR> channa
 */

import {bind, BindingKey, /* inject, */ BindingScope, inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import Axios from 'axios';
import dotenv from 'dotenv';
import {ObjectId} from 'mongodb';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AnnotationUserType,
  ChangeType,
  ContentType,
  distractorLabels,
  Explore_API_TYPE,
  FileInterface,
  labelImageFilesInfo,
  LabelInfoCreateRequest,
  LabelInfoEditRequest,
  SelectionOptionsObject,
  SystemChangeType,
  SystemLabel,
  systemLabelAttribute,
  SystemLabelListResponse,
  SystemLabelType,
  systemLabelValues,
} from '../models';
import {
  LabelGroupRepository,
  MetaDataRepository,
  MetaDataUpdateRepository,
  QueryOptionRepository,
  SystemChangeRepository,
  SystemDataRepository,
  SystemLabelRepository,
} from '../repositories';
import {FLOWS, STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService} from './storage-crawler.service';
var randomColor = require('randomcolor');
dotenv.config();

const className = 'SystemLabelService';

const SYSTEM_DATA_BUCKET = process.env.DEFAULT_BUCKET_NAME;

const NotificationType = {
  EDIT: 1,
  DELETE: 2,
};

@bind({scope: BindingScope.TRANSIENT})
export class SystemLabelService {
  constructor(
    @repository(SystemLabelRepository)
    private systemLabelRepository: SystemLabelRepository,
    @repository(SystemChangeRepository)
    private systemChangeRepository: SystemChangeRepository,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @repository(MetaDataUpdateRepository)
    private metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository('SystemDataRepository')
    private systemDataRepository: SystemDataRepository,
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @repository(LabelGroupRepository)
    private labelGroupRepo: LabelGroupRepository,
  ) {}

  /**
   * Use to create new system label
   * @param label {LabelInfoCreateRequest} info of new label
   * @param teamId {string} teamId of requested user
   * @returns created label instance
   */
  async createSystemLabel(label: LabelInfoCreateRequest, userName?: string, teamId?: string) {
    //remove additional spaces from before and after
    label.labelText = label.labelText.trim();

    //validate labelText
    let uniqueLabelTextObj = await this.validateLabelText(label.labelText, teamId, []);

    //validate label unique identifier doesn't exist
    let labelIdentifier = uuidV4();
    let isExistLabel = await this.systemLabelRepository.findOne({
      where: {
        label: labelIdentifier,
        isDeleted: {neq: true},
      },
    });
    if (isExistLabel) {
      logger.error(
        `${className}|createSystemLabel| New Lable create request from teamId: ${teamId}, generated UUID already exist : `,
        labelIdentifier,
      );
      throw new HttpErrors.NotAcceptable('Generated UUID already exist');
    }

    //create system label for the team
    try {
      // //below logic added due to a frontend requiremnet. If label with attributes type label doesn't provide any attrubite while createing then FE cannot show defult empty chart. Therefore here added emplty attribute if not exist at leaset one
      // // frontend will send empty attribute, so below is for python SDK label create requests to be matched with fronend request
      // if (label.type == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
      //   if (!label.attributes || label.attributes.length == 0) {
      //     label.attributes = [
      //       {
      //         label: '',
      //         labelText: '',
      //         key: '',
      //         values: [
      //           {
      //             valueName: '',
      //             valueText: '',
      //             description: '',
      //             imgFiles: [],
      //           },
      //         ],
      //       },
      //     ];
      //   }
      // }

      if (!label.attributes) label.attributes = []; //some frontend functionality (edit popup, select label when annotating) will not work if attributes is null, there should be at least an empty array

      let attributeObjectList: systemLabelAttribute[] = [];
      for (let attribute of label.attributes) {
        let tempAttribute: systemLabelAttribute = {};
        let labelAttributeIdentifier = uuidV4();
        tempAttribute.key = labelAttributeIdentifier;
        tempAttribute.label = labelAttributeIdentifier;
        tempAttribute.labelText = attribute.labelText;
        //tempAttribute.isNew = attribute.isNew
        //tempAttribute.errorMsg = attribute.errorMsg

        let valueList: systemLabelValues[] = [];
        for (let index in attribute.values) {
          let tempValue: systemLabelValues = {};
          let labelAttributeValueIdentifier = uuidV4();
          tempValue.valueName = labelAttributeValueIdentifier;
          tempValue.description = attribute.values[index].description;
          tempValue.imgFiles = attribute.values[index].imgFiles;
          tempValue.valueText = attribute.values[index].valueText;
          //tempValue.isNew = attribute.values[index].isNew
          //tempValue.errorMsg = attribute.values[index].errorMsg

          valueList.push(tempValue);
        }
        tempAttribute.values = valueList;
        attributeObjectList.push(tempAttribute);
      }

      let createdLabel = await this.systemLabelRepository.create({
        teamId: teamId,
        type: label.type,
        label: labelIdentifier,
        key: labelIdentifier,
        uniqueLabelText: uniqueLabelTextObj.uniqueLabelText,
        labelText: label.labelText,
        description: label.description,
        annotationType: label.annotationType,
        imgFiles: label.imgFiles,
        distractorLabelList: label.distractorLabelList,
        color: randomColor(),
        attributes: attributeObjectList,
        lastModifiedAt: new Date(),
        lastModifiedBy: userName,
      });

      return createdLabel;
    } catch (e) {
      logger.error(
        `${className}|createSystemLabel| Failed new Lable create request from teamId: ${teamId} for label: `,
        label,
      );
      logger.error('Error: ', e);
      throw new HttpErrors.NotAcceptable('Create System Label Faild');
    }
  }

  /**
   * Use to validate labelText before create/edit system label
   * @param labelText labeText of System label
   * @param teamId teamId of requested User
   * @returns none
   */
  async validateLabelText(labelText: string, teamId: string | undefined, ninArr: string[]) {
    //const pattern = new RegExp(labelText, "i");
    // const pattern = new RegExp('^' + regExpEscape(labelText) + '$', 'i');
    const uniqueLabelText = labelText
      .toLowerCase()
      .trim()
      .replace(/[^0-9A-Z]+/gi, '');
    let likeLabelText = await this.systemLabelRepository.findOne({
      where: {
        and: [
          // {labelText: {regexp: pattern}},
          {uniqueLabelText: uniqueLabelText},
          {teamId: teamId},
          {label: {nin: ninArr}},
          {isDeleted: {neq: true}},
        ],
      },
    });
    if (likeLabelText) {
      logger.error(
        `${className}| System Lable create/edit request from teamId: ${teamId} for likely exist labelText: `,
        labelText,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.SYSTEM_LABEL_LIKELY_EXIST);
    } else {
      return {uniqueLabelText: uniqueLabelText};
    }
  }

  /**
   * Use to edit existing system label
   * @param label {LabelInfoEditRequest} updated info of the label
   * @param teamId {string} teamId of requested user
   * @returns status of completion
   */
  async editSystemLabel(label: LabelInfoEditRequest, userName?: string, teamId?: string) {
    let editedLabel = label;
    let uniqueLabelText = '';
    let oldLabel = await this.systemLabelRepository.findOne({
      where: {
        and: [{label: editedLabel.label}, {teamId: teamId}, {isDeleted: {neq: true}}],
      },
    });
    if (!oldLabel) {
      logger.error(
        `${className}|editSystemLabel| Edit system label request from teamId: ${teamId} for non exist label: `,
        editedLabel.label,
      );
      throw new HttpErrors.NotAcceptable('Non exist label');
    }

    //validate labelText
    if (oldLabel.labelText != editedLabel.labelText) {
      let uniqueLabelTextObj = await this.validateLabelText(label.labelText, teamId, [oldLabel.label]);
      uniqueLabelText = uniqueLabelTextObj.uniqueLabelText;
    }

    // let _details = {teamId: teamId, label: oldLabel};
    // let detailsObj: any = await this.getTaskList(_details);

    // let taskObjIdList = [];
    // if(detailsObj){
    //   let taskIdList = detailsObj.data;
    //   if (taskIdList.length > 0)
    //     taskObjIdList = taskIdList.map((_obj: any) => new ObjectId(_obj._id));
    // }

    try {
      let isAbleDelete = oldLabel.isAbleDelete ? true : false;
      let isLabelModified = false;
      let isLabelAttributeValuesRemoved = false;
      let updatedLabelFields = {};
      let updatedLabelFieldsData = {};
      //Class labelText update
      if (oldLabel.labelText != editedLabel.labelText) {
        logger.info(`${className}|editSystemLabel| updating class label labelText for label: ${editedLabel.label}`);
        isLabelModified = true;
        updatedLabelFields = Object.assign(updatedLabelFields, {
          labelText: editedLabel.labelText,
          uniqueLabelText: uniqueLabelText,
        });
        updatedLabelFieldsData = Object.assign(updatedLabelFieldsData, {
          'labels.$[labelElem].labelText': editedLabel.labelText,
        });
      }
      //Distractor Object label update
      let oldDistractorLabelList = oldLabel.distractorLabelList ? oldLabel.distractorLabelList : [];
      let newDistractorLabelList = editedLabel.distractorLabelList ? editedLabel.distractorLabelList : [];
      let _comparedDistractorLabelLists = await this.objectArrayCompare(
        oldDistractorLabelList,
        newDistractorLabelList,
        'distractorLabel',
      );
      if (_comparedDistractorLabelLists._inFirstOnly.length + _comparedDistractorLabelLists._inSecondOnly.length > 0) {
        logger.info(`${className}|editSystemLabel| updating distractorLabel for label: ${editedLabel.label}`);
        isLabelModified = true;
        updatedLabelFields = Object.assign(updatedLabelFields, {
          distractorLabelList: editedLabel.distractorLabelList,
        });
        updatedLabelFieldsData = Object.assign(updatedLabelFieldsData, {
          'labels.$[labelElem].distractorLabelList': editedLabel.distractorLabelList,
        });
      }

      //Class description update if it is Class only label
      if (editedLabel.type == SystemLabelType.CLASS_ONLY) {
        if (oldLabel.description != editedLabel.description) {
          logger.info(`${className}|editSystemLabel| updating class label description for label: ${editedLabel.label}`);
          isLabelModified = true;
          updatedLabelFields = Object.assign(updatedLabelFields, {
            description: editedLabel.description,
          });
          updatedLabelFieldsData = Object.assign(updatedLabelFieldsData, {
            'labels.$[labelElem].description': editedLabel.description,
          });
        }

        //label images update for Values
        let oldLabelImages = oldLabel.imgFiles ? oldLabel.imgFiles : [];
        let newLabelImages = editedLabel.imgFiles ? editedLabel.imgFiles : [];
        let _changedLabelImages = await this.objectArrayCompare(oldLabelImages, newLabelImages, 'srcUrl');
        let removedLabelImages = _changedLabelImages._inFirstOnly;
        if (removedLabelImages.length > 0) {
          logger.info(
            `${className}|editSystemLabel| ${removedLabelImages.length} number of images has removed for label: ${oldLabel.label}`,
          );
          isLabelModified = true;
          updatedLabelFields = Object.assign(updatedLabelFields, {
            imgFiles: newLabelImages,
          });
          updatedLabelFieldsData = Object.assign(updatedLabelFieldsData, {
            'labels.$[labelElem].imgFiles': newLabelImages,
          });
        }
      }

      let isAttributesModified = false;

      //Class Attributes update if it is Class with Attributes
      if (editedLabel.type == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
        // check attributes
        let oldLabelAttributes = oldLabel.attributes ? oldLabel.attributes : [];
        let editedLabelAttributes = editedLabel.attributes ? editedLabel.attributes : [];
        let _attrs = await this.objectArrayCompare(oldLabelAttributes, editedLabelAttributes, 'label');
        let commonAttrs = _attrs._inBoth;
        let removedAttrs = _attrs._inFirstOnly;
        let addedAttrs = _attrs._inSecondOnly;
        if (addedAttrs.length > 0) {
          isAttributesModified = true;

          //add attribute and value references to the newly added attributes
          for (let newAttribute of addedAttrs) {
            //get index of new attribute
            let index = editedLabelAttributes.findIndex(attribute => attribute.label == newAttribute.label);

            let labelAttributeIdentifier = uuidV4();
            editedLabelAttributes[index].label = labelAttributeIdentifier;
            editedLabelAttributes[index].key = labelAttributeIdentifier;

            //add references to the new values and delete unwanted fields
            for (let j in editedLabelAttributes[index].values) {
              let labelAttributeValueIdentifier = uuidV4();
              editedLabelAttributes[index].values[j].valueName = labelAttributeValueIdentifier;
              // delete editedLabelAttributes[index].values[j].annotatedCount
              // delete editedLabelAttributes[index].values[j].imgFile
              // delete editedLabelAttributes[index].values[j].imgURL
              // delete editedLabelAttributes[index].values[j].isDefault
              // delete editedLabelAttributes[index].values[j].imgData
            }
          }
        }
        if (isAbleDelete && removedAttrs.length > 0) {
          isAttributesModified = true;
          isLabelAttributeValuesRemoved = true;
          for (let removedAttr of removedAttrs) {
            logger.info(
              `start removing data from AnnotationFrame and AnnotationTask for removed attribute: ${removedAttr.label} of label: ${oldLabel.label} of teamId: ${teamId}`,
            );

            //unset removed attribute related data from AnnotationFrame
            let _unsetAttrParams: {[k: string]: any} = {};
            //_unsetAttrParams['taskId'] = {$in: taskObjIdList};
            _unsetAttrParams['annotationObjects.label.label'] = oldLabel.label;
            _unsetAttrParams['annotationObjects.label.attributeValues.' + removedAttr.label] = {$exists: true};

            let _unsetAttrData: {[k: string]: any} = {};
            _unsetAttrData['annotationObjects.$[annotationObject].label.attributeValues.' + removedAttr.label] = '';

            let _arrayFilterUnsetAttrData = [
              {
                'annotationObject.label.label': oldLabel.label,
              },
            ];

            await this.metaDataUpdateRepository.unsetLabelAttibuteInFrame(
              _unsetAttrParams,
              _unsetAttrData,
              _arrayFilterUnsetAttrData,
            );
          }
        } else if (!isAbleDelete && removedAttrs.length > 0) {
          //if user has removed attributes from not deletable label, then add them again
          editedLabel.attributes = [...editedLabel.attributes, ...removedAttrs];
        }

        let hashEditedLabelAttrs = Object.fromEntries(editedLabel.attributes.map(e => [e['label'], e]));
        for (let commonAttrInOldLabel of commonAttrs) {
          let commonAttrInEditedLabel = hashEditedLabelAttrs[commonAttrInOldLabel.label];

          //Attribute label text update
          if (commonAttrInEditedLabel.labelText != commonAttrInOldLabel.labelText) {
            isAttributesModified = true;
          }
          //Attribute's values update
          let commonAttrInOldLabelValues = commonAttrInOldLabel.values ? commonAttrInOldLabel.values : [];
          let commonAttrInEditedLabelValues = commonAttrInEditedLabel.values ? commonAttrInEditedLabel.values : [];
          let _values = await this.objectArrayCompare(
            commonAttrInOldLabelValues,
            commonAttrInEditedLabelValues,
            'valueName',
          );
          let commonVals = _values._inBoth;
          let removedVals = _values._inFirstOnly;
          let addedVals = _values._inSecondOnly;

          if (addedVals.length > 0) {
            isAttributesModified = true;

            //find value edited attribute index
            let indexOfAttribute = editedLabelAttributes.findIndex(
              attribute => attribute.label == commonAttrInOldLabel.label,
            );
            for (let newValue of addedVals) {
              //find index of the edited value and replace with value reference
              let indexOfValue = editedLabelAttributes[indexOfAttribute].values.findIndex(
                value => value.valueName == newValue.valueName,
              );

              let labelAttributeValueIdentifier = uuidV4();

              editedLabelAttributes[indexOfAttribute].values[indexOfValue].valueName = labelAttributeValueIdentifier;
              // delete editedLabelAttributes[indexOfAttribute].values[indexOfValue].annotatedCount
              // delete editedLabelAttributes[indexOfAttribute].values[indexOfValue].imgData
              // delete editedLabelAttributes[indexOfAttribute].values[indexOfValue].imgFile
              // delete editedLabelAttributes[indexOfAttribute].values[indexOfValue].imgURL
              // delete editedLabelAttributes[indexOfAttribute].values[indexOfValue].isDefault
            }
          }
          if (isAbleDelete && removedVals.length > 0) {
            isAttributesModified = true;
            isLabelAttributeValuesRemoved = true;
            let removedValuesOfAttribute: string[] = removedVals.map((_val: any) => _val.valueName);
            if (removedValuesOfAttribute.length > 0) {
              //unset removed value related data from AnnotationFrame
              // let _unsetValuesParams: {[k: string]: any} = {};
              // //_unsetValuesParams['taskId'] = {$in: taskObjIdList};
              // _unsetValuesParams['annotationObjects.label.label'] =
              //   oldLabel.label;
              // _unsetValuesParams[
              //   'annotationObjects.label.attributeValues.' +
              //   commonAttrInOldLabel.label
              // ] = {$in: removedValuesOfAttribute};

              // let _unsetValuesData: {[k: string]: any} = {};
              // _unsetValuesData[
              //   'annotationObjects.$[annotationObject].label.attributeValues.' +
              //   commonAttrInOldLabel.label
              // ] = '';

              // let _arrayFilterUnsetValuesData: {[k: string]: any} = {};
              // _arrayFilterUnsetValuesData[
              //   'annotationObject.label.attributeValues.' +
              //   commonAttrInOldLabel.label
              // ] = {$in: removedValuesOfAttribute};

              // await this.metaDataUpdateRepository.unsetLabelAttibuteInFrame(
              //   _unsetValuesParams,
              //   _unsetValuesData,
              //   [_arrayFilterUnsetValuesData],
              // );

              let _pullValuesParams: {[k: string]: any} = {};
              _pullValuesParams['annotationObjects.label.label'] = oldLabel.label;
              _pullValuesParams['annotationObjects.label.attributeValues.' + commonAttrInOldLabel.label + '.value'] = {
                $in: removedValuesOfAttribute,
              };

              let _pullValuesData: {[k: string]: any} = {};
              _pullValuesData[
                'annotationObjects.$[annotationObject].label.attributeValues.' + commonAttrInOldLabel.label
              ] = {value: {$in: removedValuesOfAttribute}};

              let _arrayFilterPullValuesData: {[k: string]: any} = {};
              _arrayFilterPullValuesData[
                'annotationObject.label.attributeValues.' + commonAttrInOldLabel.label + '.value'
              ] = {$in: removedValuesOfAttribute};

              await this.metaDataUpdateRepository.updateManyRemoveFromList(_pullValuesParams, _pullValuesData, [
                _arrayFilterPullValuesData,
              ]);
            }
          } else if (!isAbleDelete && removedVals.length > 0) {
            //if user has removed values from not deletable label, then add them again
            for (let i in editedLabel.attributes) {
              if (editedLabel.attributes[i].label == commonAttrInEditedLabel.label) {
                editedLabel.attributes[i].values = [...editedLabel.attributes[i].values, ...removedVals];
                break;
              }
            }
          }

          let hashEditedAttrValues = Object.fromEntries(commonAttrInEditedLabel.values.map(e => [e['valueName'], e]));
          for (let commonValInOldAttr of commonVals) {
            let commonValInEditedAttr = hashEditedAttrValues[commonValInOldAttr.valueName];

            //Value text and description update
            if (
              commonValInOldAttr.valueText != commonValInEditedAttr.valueText ||
              commonValInOldAttr.description != commonValInEditedAttr.description
            ) {
              isAttributesModified = true;
            }

            //label images update for Values
            let commonValInOldAttrImgFiles = commonValInOldAttr.imgFiles ? commonValInOldAttr.imgFiles : [];
            let commonValInEditedAttrImgFiles = commonValInEditedAttr.imgFiles ? commonValInEditedAttr.imgFiles : [];
            let _changedImages = await this.objectArrayCompare(
              commonValInOldAttrImgFiles,
              commonValInEditedAttrImgFiles,
              'srcUrl',
            );
            let removedImages = _changedImages._inFirstOnly;
            if (removedImages.length > 0) {
              logger.info(
                `${className}|editSystemLabel| ${removedImages.length} number of images has removed from value:${commonValInOldAttr.valueName} of label: ${oldLabel.label}`,
              );
              isAttributesModified = true;
            }
          }
        }
      }
      if (isAttributesModified) {
        isLabelModified = true;
        updatedLabelFields = Object.assign(updatedLabelFields, {
          attributes: editedLabel.attributes,
        });
        updatedLabelFieldsData = Object.assign(updatedLabelFieldsData, {
          'labels.$[labelElem].attributes': editedLabel.attributes,
        });
      }
      if (isLabelModified) {
        logger.info(
          `start updating attributes data in AnnotationSystemLabel of label ${oldLabel.label} of teamId: ${teamId}`,
        );

        updatedLabelFields = Object.assign(updatedLabelFields, {
          lastModifiedBy: userName,
        });
        updatedLabelFields = Object.assign(updatedLabelFields, {
          lastModifiedAt: new Date(),
        });

        await this.systemChangeRepository.create({
          type: SystemChangeType.LABEL,
          changeType: ChangeType.EDIT,
          changeAt: new Date(),
          change: label,
          beforeChange: oldLabel,
          teamId: teamId,
          changeBy: userName,
        });

        await this.systemLabelRepository.updateById(oldLabel.id, updatedLabelFields);
      }

      //make studio notification call for delete project labels
      let details = {
        oldLabel: oldLabel,
        label: label,
        userName: userName,
        teamId: teamId,
        type: NotificationType.EDIT,
      };

      //Use to send notification for delete or edit labels in project in annotation studio
      await this.makeStudioNotification(details);

      return await this.getFormattedSystemLabel(oldLabel.id, teamId);
    } catch (e) {
      logger.error(`Unecpected error occured while editing system label: ${oldLabel.label} `, e);
      //return {success: false}
      throw new HttpErrors.NotAcceptable('Unecpected error occured while editing system label');
    }
  }

  /**
   * This function can be used to compare objects in an array based in a given property
   * @param list1 any[] : first objects array to compare
   * @param list2 any[] : second objects array to compare
   * @param key any : object property to be used to comapre
   * @returns object of containing 3 arrays
   * {
   *  _inBoth: Array of objects exist in both arrays (return objects are taken from first array)
   *  _inFirstOnly: Array of objects exist only in first array
   *  _inSecondOnly: Array of objects exist only in second array
   * }
   */
  async objectArrayCompare(list1: any[], list2: any[], key: any) {
    const KEY = key;
    // Generic helper function that can be used for the three operations:
    const operation = (list1: any[], list2: any[], isUnion = false) =>
      list1.filter(
        (
          set => (a: any) =>
            isUnion === set.has(a[KEY])
        )(new Set(list2.map(b => b[KEY]))),
      );

    // Following functions are to be used:
    const inBoth = (list1: any[], list2: any[]) => operation(list1, list2, true),
      inFirstOnly = operation,
      inSecondOnly = (list1: any[], list2: any[]) => inFirstOnly(list2, list1);

    return {
      _inBoth: inBoth(list1, list2),
      _inFirstOnly: inFirstOnly(list1, list2),
      _inSecondOnly: inSecondOnly(list1, list2),
    };
  }

  /**
   * Get formatted system label to send to frontend (add flags and distractor objects)
   * @param labelId mogodb id of the system label
   * @param teamId teamId of the requested user
   * @returns formatted system label object
   */
  async getFormattedSystemLabel(labelId?: string, teamId?: string) {
    let systemLabel = await this.systemLabelRepository.findById(labelId);

    let _distractorLabelList = systemLabel.distractorLabelList || [];
    let distactorLabels = _distractorLabelList.map(e => e.distractorLabel);
    let _distractorObjectList = await this.systemLabelRepository.find({
      where: {
        and: [{teamId: teamId}, {label: {inq: distactorLabels}}, {isDeleted: {neq: true}}],
      },
    });

    delete systemLabel.id;

    let _newObj: SystemLabelListResponse = {
      ...systemLabel,
      distractorObjectList: _distractorObjectList,
    };
    _newObj.distractorLabelList = _distractorLabelList;
    // add flag to check if able to edit/delete the label
    let isAbleDelete = systemLabel.isAbleDelete ? true : false;
    _newObj.isAbleDelete = isAbleDelete;
    _newObj.isEditable = true;

    return _newObj;
  }

  /**
   * Use to delete existing system label
   * @param label {string} unique identifier of the label
   * @param teamId {string} teamId of requested user
   * @returns status of completion
   */
  async deleteSystemLabel(label: string, teamId?: string) {
    let existingLabel = await this.systemLabelRepository.findOne({
      where: {
        and: [{label: label}, {teamId: teamId}, {isDeleted: {neq: true}}],
      },
    });
    if (!existingLabel) {
      logger.error(
        `${className}|deleteSystemLabel| Delete system label request from teamId: ${teamId} for non exist label: `,
        label,
      );
      throw new HttpErrors.NotAcceptable('Non exist label');
    }

    let isAbleDelete = existingLabel.isAbleDelete ? true : false;

    if (!isAbleDelete) {
      logger.error(
        `${className}|deleteSystemLabel| Delete system label request from teamId: ${teamId} for non deletable label: `,
        label,
      );
      throw new HttpErrors.NotAcceptable('Non deletable label');
    }

    try {
      await this.systemLabelRepository.updateById(existingLabel.id, {
        isDeleted: true,
      });
      this.labelDeleteAsync(existingLabel, label, teamId);
      return {success: true};
    } catch (err) {
      logger.debug('label delete success');
      return {success: false};
    }
  }

  /**
   * Delete the label take more time
   * So it done as async function without waiting its response
   * @param existingLabel label
   * @param label label name
   * @param teamId team id
   * @returns
   */
  async labelDeleteAsync(existingLabel: SystemLabel, label: string, teamId?: string) {
    // let _details = {teamId: teamId, label: existingLabel};
    // let detailsObj: any = await this.getTaskList(_details);
    // let taskIdList = detailsObj.data;

    // let taskObjIdList = taskIdList.map((_obj: any) => new ObjectId(_obj._id));

    try {
      //remove AnnotationFrame data of deleted label
      logger.info(
        `${className}|deleteSystemLabel| removing AnnotationFrame data of deleted label ${existingLabel.label} for team teamId ${teamId}`,
      );
      let paramAnnotationFrame = {
        // taskId: {
        //   $in: taskObjIdList,
        // },
        'annotationObjects.label.label': existingLabel.label,
      };
      let dataAnnotationFrame = {
        annotationObjects: {
          'label.label': existingLabel.label,
        },
      };
      await this.metaDataUpdateRepository.updateManyRemoveFromList(paramAnnotationFrame, dataAnnotationFrame);

      //remove label reference from distractor label lists
      let paramAnnotationSystemLabelDistractorLabel = {
        teamId: new ObjectId(teamId),
        'distractorLabelList.distractorLabel': existingLabel.label,
      };
      let dataAnnotationSystemLabelDistractorLabel = {
        distractorLabelList: {
          distractorLabel: existingLabel.label,
        },
      };
      await this.systemLabelRepository.updateManyRemoveFromList(
        paramAnnotationSystemLabelDistractorLabel,
        dataAnnotationSystemLabelDistractorLabel,
        [],
      );

      //make studio notification call for delete project labels
      let details = {
        oldLabel: existingLabel,
        label: label,
        teamId: teamId,
        type: NotificationType.DELETE,
      };

      //Use to send notification for delete or edit labels in project in annotation studio
      await this.makeStudioNotification(details);

      // await this.annotationProjectRepository.updateManyRemoveFromList(paramAnnotationProjectDistractorLabel, dataAnnotationProjectDistractorLabel, arrayFilterAnnotationProjectDistractorLabel)
      await this.systemChangeRepository.create({
        type: SystemChangeType.LABEL,
        changeType: ChangeType.DELETE,
        changeAt: new Date(),
        change: null,
        beforeChange: existingLabel,
        teamId: teamId,
        //changeBy: userName
      });

      let metaDataFilter = {'labelList.label': existingLabel.label};

      // flag verification status pending
      //  flag to recalculate verification
      await this.metaDataRepository.markStatPendingFlagsTrue(metaDataFilter);

      //remove label related stats
      //remove stats from MetaData
      await this.metaDataRepository.updateManyRemoveFromList(
        metaDataFilter,
        {
          labelList: {
            label: existingLabel.label,
          },
        },
        [],
      );

      //remove from operationList
      // await this.metaDataRepository.updateManyRemoveFromList(
      //   metaDataFilter,
      //   {
      //     labelList: {
      //       label: existingLabel.label,
      //     },
      //   },
      //   [],
      // );

      //remove stats from SystemData
      let removeLabel: {[k: string]: any} = {};
      removeLabel[`objectTypeWiseCounts.videos.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.images.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.datasets.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.imageCollections.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.videoCollections.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.other.labelList.${existingLabel.label}`] = '';
      removeLabel[`objectTypeWiseCounts.otherCollections.labelList.${existingLabel.label}`] = '';
      await this.systemDataRepository.updateOneUnsetData({}, removeLabel, []);

      //remove stats from SystemData
      // let _unsetData: {[k: string]: string} = {};
      // _unsetData['labelCounts.' + existingLabel.label] = '';
      // //NOTE: add teamId filtering
      // await this.systemDataRepository.updateOneUnsetData({}, _unsetData, []);

      //remove search query options belongs to label
      await this.queryOptionRepository.deleteQueryOption('annotation.label', existingLabel.label, teamId);

      //delete system label
      await this.systemLabelRepository.deleteById(existingLabel.id);

      return {success: true};
    } catch (e) {
      logger.error(`Unecpected error occured while editing system label: ${existingLabel.label} `, e);
      return {success: false};
    }
  }

  /**
   * Use to send notification for delete or edit labels in project
   * @param details {object} request body for delete or edit label
   */
  async makeStudioNotification(details: any) {
    let url = `${process.env.ANNO_INTERNAL_SERVER}/internal/system/label/notification`;

    try {
      await Axios({
        url,
        method: 'POST',
        data: details,
      });
    } catch (err) {
      logger.error('project and task edit failde', err);
    }
  }

  /**
   * Use to send notification for delete or edit labels in project in annotation studio
   * @param details {object} request body for delete or edit label
   */
  async getTaskList(details: {teamId: any; label?: any}) {
    let url = `${process.env.ANNO_INTERNAL_SERVER}/internal/system/task/list`;

    try {
      let response = await Axios({
        url,
        method: 'GET',
        data: details,
      });
      return response;
    } catch (err) {
      logger.error('project and task edit failed', err);
    }
  }

  /**
   * Use for get all system labels of a team
   * @param teamId {string} teamId of requested user
   * @param labelType {number} type of system label to filter
   * @param labelGroupId {string}: Filter by group id (optional)
   * @returns list of system labels
   */
  async getSystemLabelList(
    userType: number,
    teamId: string,
    exportApiType: Explore_API_TYPE,
    pageIndex?: number | undefined,
    pageSize?: number | undefined,
    searchKey?: string,
    labelType?: number,
    isSummaryOnly?: boolean,
    labelGroupId?: string,
  ) {
    let filterArr: any[] = [];
    if (labelType == SystemLabelType.CLASS_ONLY) {
      filterArr = [{teamId: teamId}, {type: SystemLabelType.CLASS_ONLY}, {isDeleted: {neq: true}}];
    } else if (labelType == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
      filterArr = [{teamId: teamId}, {type: SystemLabelType.CLASS_WITH_ATTRIBUTES}, {isDeleted: {neq: true}}];
    } else {
      filterArr = [{teamId: teamId}, {isDeleted: {neq: true}}];
    }

    // if search key is present
    if (searchKey) {
      filterArr.push({labelText: {regexp: `/${searchKey}/i`}});
    }

    //Optionally we can filter from label group id
    if (labelGroupId) {
      let groupObj = await this.labelGroupRepo.findById(labelGroupId);
      filterArr.push({groupIdList: groupObj.id});
    }

    let findObject: any = {
      where: {
        and: filterArr,
      },
      order: 'lastModifiedAt DESC',
    };

    // if index and size is present
    if (pageIndex != undefined && pageSize != undefined) {
      findObject['skip'] = pageIndex * pageSize;
      findObject['limit'] = pageSize;
    }

    if (exportApiType == Explore_API_TYPE.LIST) {
      //find system labels of the teams
      let labelList: SystemLabel[] = [];
      if (isSummaryOnly) {
        findObject['fields'] = ['labelText', 'key', 'description'];
        labelList = await this.systemLabelRepository.find(findObject);

        let returnList = labelList.map(labelObj => {
          return {
            key: labelObj.key,
            name: labelObj.labelText,
            description: labelObj.description,
          };
        });
        return returnList;
      } else {
        labelList = await this.systemLabelRepository.find(findObject);
      }

      let allDistractorLabelsList: distractorLabels[] = [];

      labelList.forEach(_obj => {
        if (_obj.distractorLabelList) {
          allDistractorLabelsList.push(..._obj.distractorLabelList);
        }
      });

      if (teamId) {
        let _allDistractorLabelsList = allDistractorLabelsList.map(_label => _label.distractorLabel);
        let hashDistractorLabelObjectList = await this.getHashSystemLabelsObjectList(_allDistractorLabelsList, teamId);

        let formattedSystemLabels: SystemLabelListResponse[] = [];
        for (let _obj of labelList) {
          //add distractor object list
          let _distractorLabelList = _obj.distractorLabelList ? _obj.distractorLabelList : [];
          let _distractorObjectList: SystemLabel[] = [];
          if (_distractorLabelList) {
            _distractorLabelList.forEach(_label => {
              _distractorObjectList.push(hashDistractorLabelObjectList[_label.distractorLabel]);
            });
          }

          let _newObj: SystemLabelListResponse = {
            ..._obj,
            distractorObjectList: _distractorObjectList,
          };
          _newObj.distractorLabelList = _distractorLabelList;
          // add flag to check if able to edit/delete the label
          if (
            userType == AnnotationUserType.ANNOTATION_USER_TYPE_ANNOTATOR ||
            userType == AnnotationUserType.ANNOTATION_USER_TYPE_QA
          ) {
            _newObj.isAbleDelete = false;
            _newObj.isEditable = false;
          } else {
            let isAbleDelete = _obj.isAbleDelete ? true : false;
            _newObj.isAbleDelete = isAbleDelete;
            _newObj.isEditable = true;
          }

          formattedSystemLabels.push(_newObj);
        }

        return formattedSystemLabels;
      } else {
        logger.error('getSystemLabelList: teamId not valid: teamId', teamId);
        return [];
      }
    } else if (exportApiType == Explore_API_TYPE.COUNT) {
      let countObj = await this.systemLabelRepository.count({and: filterArr});

      return {
        count: countObj.count,
      };
    }
  }

  /**
   * Use to get system label objects as object map which has key as label
   * @param labelList system label unique identifiers list
   * @param teamId teamId of requested user
   * @returns object map which has key as label
   */
  async getHashSystemLabelsObjectList(labelList: string[], teamId?: string) {
    let query: any[] = [];
    if (teamId) {
      query = [{teamId: teamId}, {label: {inq: labelList}}, {isDeleted: {neq: true}}];
    } else {
      query = [{label: {inq: labelList}}, {isDeleted: {neq: true}}];
    }
    let labelObjList = await this.systemLabelRepository.find({
      where: {
        and: query,
      },
    });
    let hashLabelObjectsList = Object.fromEntries(labelObjList.map(e => [e['label'], e]));

    return hashLabelObjectsList;
  }

  /*
   * Use to push uploaded images Src Urls to Labels
   * @param uploadedFileDetails {array} object array with urls of uploaded images
   * @param type {number} label type
   * @param label {string} label unique identifier
   * @param attributeLabel {string} attribute unique identifier
   * @param valueName {string} value unique identifier
   * @param teamId {string} id of the team
   * @returns
   */
  async addLabelImages(
    uploadedFileDetails: labelImageFilesInfo[],
    type: any,
    label: any,
    attributeLabel: any,
    valueName: any,
    userName: string,
    systemLabelId: string,
    teamId: string | undefined,
  ) {
    logger.info(`Starting to add label images to label: ${label} teamId: ${teamId}`);
    try {
      if (type == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
        //update system label
        await this.systemLabelRepository.updateManyPushToList(
          {
            teamId: new ObjectId(teamId),
            label: label,
          },
          {
            'attributes.$[attributeElem].values.$[valueElem].imgFiles': {
              $each: uploadedFileDetails,
            },
          },
          [{'attributeElem.label': attributeLabel}, {'valueElem.valueName': valueName}],
        );

        // //update annotation project
        // await this.annotationProjectRepository.updateManyPushToList({
        //   teamId: new ObjectId(teamId),
        //   "labels.label": label
        // },{
        //   "labels.$[labelElem].attributes.$[attributeElem].values.$[valueElem].imgFiles": {$each: uploadedFileDetails}
        // },[
        //   {"labelElem.label": label}, { "attributeElem.label": attributeLabel }, { "valueElem.valueName": valueName}
        // ])
      } else if (type == SystemLabelType.CLASS_ONLY) {
        //update system label
        await this.systemLabelRepository.updateManyPushToList(
          {
            teamId: new ObjectId(teamId),
            label: label,
          },
          {
            imgFiles: {$each: uploadedFileDetails},
          },
          [],
        );

        //update annotation project
        // await this.systemLabelRepository.updateManyPushToList({
        //   teamId: new ObjectId(teamId),
        //   "labels.label": label
        // },{
        //   "labels.$[labelElem].imgFiles": {$each: uploadedFileDetails}
        // },[
        //   {"labelElem.label": label}
        // ])
      }
      //update last modified details
      await this.systemLabelRepository.updateById(systemLabelId, {
        lastModifiedBy: userName,
        lastModifiedAt: new Date(),
      });
      return {success: true};
    } catch (e) {
      logger.error(`failed to add label images to label: ${label} teamId: ${teamId} : `, e);
      return {success: false};
    }
  }

  /**
   * Use to update system label aws urls
   */
  async updateSystemLabelImageUrl() {
    logger.info('system label url update begins');

    let labelList = await this.systemLabelRepository.find({
      where: {isDeleted: {neq: true}},
    });

    for (let label of labelList) {
      if (label.type == SystemLabelType.CLASS_ONLY) {
        if (label.imgFiles) {
          let imgFiles = label.imgFiles;
          for (let i in imgFiles) {
            let newUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
              imgFiles[i].key,
              24 * 3600 * 7,
              SYSTEM_DATA_BUCKET,
            );
            let urlExpiredAt = new Date(new Date().getTime() + 7 * 24 * 3600 * 1000);

            //check for url generation errors
            if (typeof newUrl != 'string') newUrl = imgFiles[i].srcUrl;
            logger.debug(newUrl);
            imgFiles[i].srcUrl = newUrl;
            imgFiles[i].urlExpiredAt = urlExpiredAt;
          }
          await this.systemLabelRepository.updateById(label.id, {
            imgFiles: imgFiles,
          });
        }
      } else if (label.type == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
        if (label.attributes) {
          for (let attribute of label.attributes) {
            if (attribute.values) {
              for (let value of attribute.values) {
                if (value.imgFiles) {
                  let imgFiles = value.imgFiles;
                  for (let i in imgFiles) {
                    let newUrl = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
                      imgFiles[i].key,
                      24 * 3600 * 7,
                      SYSTEM_DATA_BUCKET,
                    );
                    let urlExpiredAt = new Date(new Date().getTime() + 7 * 24 * 3600 * 1000);

                    //check for url generation errors
                    if (typeof newUrl != 'string') newUrl = imgFiles[i].srcUrl;
                    //logger.debug(newUrl)
                    imgFiles[i].srcUrl = newUrl;
                    imgFiles[i].urlExpiredAt = urlExpiredAt;
                  }

                  await this.systemLabelRepository.updateMany(
                    {
                      _id: new ObjectId(label.id),
                    },
                    {
                      'attributes.$[attributeElem].values.$[valueElem].imgFiles': imgFiles,
                    },
                    [{'attributeElem.label': attribute.label}, {'valueElem.valueName': value.valueName}],
                  );
                }
              }
            }
          }
        }
      }

      //let labelId = label.id;
    }
  }

  /**
   * Use to get object which has,
   * key as label(Label)
   * value as labelText(Label)
   * @returns object map which has key as label and value as labelText
   */
  async getLabelToLabelTextMapOfTeam(teamId?: string) {
    //NOTE: add teamId filtering

    let labelList = await this.systemLabelRepository.find({
      where: teamId ? {teamId: teamId, isDeleted: {neq: true}} : {isDeleted: {neq: true}},
      fields: {
        label: true,
        labelText: true,
      },
    });

    let labelToLabelTextMap: {[k: string]: string} = {};

    for (let label of labelList) {
      if (label.labelText) labelToLabelTextMap[label.label] = label.labelText;
    }

    return labelToLabelTextMap;
  }

  /**
   * Use to get object which has,
   * key as label(Label) or key(Attribute) or valueName(Value)
   * value as labelText(Label) or labelText(Attribute) or valueText(Value)
   * @param teamId teamId of requested user
   * @returns object map which has key as reference and value as text
   */
  async getLabelValRefToTextMapOfTeam(teamId: string) {
    let labelList = await this.systemLabelRepository.find({
      where: {teamId: teamId, isDeleted: {neq: true}},
      fields: {
        label: true,
        labelText: true,
        attributes: true,
      },
    });

    let referenceToTextMap: {[k: string]: string} = {};

    // label loop
    for (let _label of labelList) {
      if (_label.labelText) {
        referenceToTextMap[_label.label] = _label.labelText;

        if (_label.attributes) {
          // attributes loop
          for (let _attr of _label.attributes) {
            if (_attr.labelText) {
              referenceToTextMap[_attr.label] = _attr.labelText;

              if (_attr.values) {
                //values loop
                for (let _val of _attr.values) {
                  if (_val.valueText) {
                    referenceToTextMap[_val.valueName] = _val.valueText;
                  }
                }
              }
            }
          }
        }
      }
    }

    return referenceToTextMap;
  }

  /**
   * Use to get object which has,
   * key as label(Label) or key(Attribute) or valueName(Value)
   * value as labelText(Label) or labelText(Attribute) or valueText(Value)
   * @param teamId teamId of requested user
   * @returns object map which has key as reference and value as text
   */
  async getOnlyLabelClassRefToTextMapOfTeam(teamId: string) {
    let labelList = await this.systemLabelRepository.find({
      where: {teamId: teamId, isDeleted: {neq: true}},
      fields: {
        label: true,
        labelText: true,
        attributes: true,
      },
    });

    let referenceToTextMap: {[k: string]: string} = {};

    // label loop
    for (let _label of labelList) {
      if (_label.labelText) {
        referenceToTextMap[_label.label] = _label.labelText;
      }
    }

    return referenceToTextMap;
  }

  /**
   * Validate group name before creating new label group
   */
  async validateNewLabelGroupName(name: string, teamId: string) {
    // create unique name
    let uniqueName = name
      .toLowerCase()
      .trim()
      .replace(/[^0-9A-Z]+/gi, '');

    let existingGroup = await this.labelGroupRepo.findOne({
      where: {teamId: teamId, uniqueName: uniqueName},
    });
    if (existingGroup) {
      logger.error(
        `${FLOWS.LABEL_GROUP}| SystemLabelService.validateNewLabelGroupName | label group with name ${name} already exists`,
      );
      return {success: false, errorMsg: 'Label group with same name already exists'};
    } else {
      return {success: true, errorMsg: ''};
    }
  }

  /**
   * Create new label group
   * @returns
   */
  async createLabelGroup(groupName: string, labelKeyList?: string[], currentUserProfile?: UserProfileDetailed) {
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
    }

    let teamId = currentUserProfile?.teamId || '';
    let userId = currentUserProfile?.id;
    let userName = currentUserProfile?.name;
    // validate label group name
    let returnObj = await this.validateNewLabelGroupName(groupName, teamId);

    // if validation fails return error
    if (!returnObj.success) {
      return {
        groupId: null,
        errorMsg: returnObj.errorMsg,
      };
    }

    // create unique name
    let uniqueName = groupName
      .toLowerCase()
      .trim()
      .replace(/[^0-9A-Z]+/gi, '');

    let groupCreateRes = await this.labelGroupRepo.createGroup(groupName, teamId, uniqueName, userId, userName);
    let createdGroup = groupCreateRes.isSuccess ? groupCreateRes.createdObj : null;
    if (createdGroup) {
      logger.info(`${className}|createLabelGroup| Created group id = ${createdGroup.id}`);
      if (labelKeyList) {
        this.attachLabelsToGroup(createdGroup.id, labelKeyList, currentUserProfile);
      }
      return {
        groupId: createdGroup.id,
        errorMsg: '',
      };
    } else {
      logger.error(`${className}|createLabelGroup| Failed to create label group from teamId: ${teamId}  `);
      return {
        groupId: null,
        errorMsg: groupCreateRes.errorMsg,
      };
    }
  }

  /**
   * editing label group name and label list
   * @param groupId {string} id of the group
   * @param groupName {string} name of the group
   * @param labelKeyList {object[]} label key list
   */
  async editLabelGroup(
    groupId: string,
    groupName: string,
    labelKeyList?: string[],
    currentUserProfile?: UserProfileDetailed,
  ) {
    let teamId = currentUserProfile?.teamId || '';
    let userId = currentUserProfile?.id || '';
    let userName = currentUserProfile?.name || '';
    try {
      let existingLabelObj = await this.labelGroupRepo.findById(groupId);

      // if group name is changed then validate new group name
      if (existingLabelObj.groupName !== groupName) {
        let returnObj = await this.validateNewLabelGroupName(groupName, teamId);
        if (!returnObj.success) {
          return {
            success: false,
            errorMsg: returnObj.errorMsg,
          };
        }
      }

      let uniqueName = groupName
        .toLowerCase()
        .trim()
        .replace(/[^0-9A-Z]+/gi, '');

      await this.labelGroupRepo.updateById(groupId, {
        groupName: groupName,
        uniqueName: uniqueName,
        modifiedById: userId,
        modifiedByName: userName,
        updatedAt: new Date(),
      });

      let labelListOfLabelGroup = await this.getLabelsOfLabelGroup(groupId, {label: 1});

      let removeLabelList: string[] = [];
      for (let labelObj of labelListOfLabelGroup) {
        let isInclude = labelKeyList?.includes(labelObj.label);
        if (!isInclude) {
          removeLabelList.push(labelObj.label);
        }
      }

      if (labelKeyList && Array.isArray(labelKeyList) && labelKeyList.length > 0) {
        await this.attachLabelsToGroup(groupId, labelKeyList, currentUserProfile);
      }
      if (removeLabelList.length > 0) {
        await this.detachLabelsFromGroup(groupId, removeLabelList, currentUserProfile);
      }

      return {success: true};
    } catch (e) {
      logger.error(`${className}| editLabelGroup | Error while editing label group ${e}`);
      throw new HttpErrors.InternalServerError('Error while editing label group');
    }
  }

  /**
   * delete th label group
   * @param groupId {string} id of the group
   */
  async deleteLabelGroup(groupId: string) {
    try {
      await this.systemLabelRepository.updateManyRemoveFromList(
        {groupIdList: new ObjectId(groupId)},
        {groupIdList: new ObjectId(groupId)},
        [],
      );
      await this.labelGroupRepo.deleteById(groupId);
    } catch (e) {
      logger.error(`${className}| deleteLabelGroup | Error while deleting label group ${e}`);
      throw new HttpErrors.InternalServerError('Error while deleting label group');
    }
  }

  /**
   * Set Group Id to given list of Labels
   * @param groupId Group id which update to
   * @param labelKeyList Labels which should belong to group
   * @returns success or failure
   */
  async attachLabelsToGroup(groupId: string, labelKeyList: string[], currentUserProfile?: UserProfileDetailed) {
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
    }
    //First validate label group
    let labelGroup = await this.labelGroupRepo.findById(groupId);
    await this.labelGroupRepo.updateById(groupId, {
      modifiedById: currentUserProfile?.id || '',
      modifiedByName: currentUserProfile?.name || '',
      updatedAt: new Date(),
    });
    let updateRes = await this.systemLabelRepository.updateManyAddToSetList(
      {key: {$in: labelKeyList}},
      {groupIdList: new ObjectId(groupId)},
      [],
    );
    return updateRes;
  }

  /**
   * Detach a set of labels from given group
   * @param groupId Group id which update to
   * @param labelKeyList Labels which should be removed from the group
   * @returns success or failure
   */
  async detachLabelsFromGroup(groupId: string, labelKeyList: string[], currentUserProfile?: UserProfileDetailed) {
    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
    }

    //First validate label group
    let labelGroup = await this.labelGroupRepo.findById(groupId);
    await this.labelGroupRepo.updateById(groupId, {
      modifiedById: currentUserProfile?.id || '',
      modifiedByName: currentUserProfile?.name || '',
      updatedAt: new Date(),
    });
    let updateRes = await this.systemLabelRepository.updateManyRemoveFromList(
      {key: {$in: labelKeyList}},
      {groupIdList: new ObjectId(groupId)},
      [],
    );
    return updateRes;
  }

  /**
   * Get a list of all Label Groups
   * @param teamId
   * @returns
   */
  async getLabelGroups(teamId: string) {
    let groupList = await this.labelGroupRepo.find({
      where: {teamId: teamId},
      fields: ['id', 'groupName', 'createdAt'],
    });
    return groupList;
  }

  /**
   * Get a list of all Label Groups and count
   * @param teamId
   * @returns
   */
  async getLabelGroupsListWithLabelDetails(
    teamId: string,
    project: any,
    exportApiType: Explore_API_TYPE,
    pageIndex?: number | undefined,
    pageSize?: number | undefined,
    searchKey?: string,
  ) {
    let params: any = [{$match: {teamId: new ObjectId(teamId)}}, {$sort: {updatedAt: -1}}];

    if (searchKey) {
      params.push({$match: {groupName: {$regex: searchKey, $options: 'i'}}});
    }

    if (pageIndex != undefined && pageSize != undefined) {
      params.push({$skip: pageIndex * pageSize});
      params.push({$limit: pageSize});
    }

    if (exportApiType == Explore_API_TYPE.LIST) {
      params.push(
        {$lookup: {from: 'SystemLabel', foreignField: 'groupIdList', localField: '_id', as: 'labels'}},
        {$addFields: {labelCount: {$size: '$labels'}, modifiedBy: '$modifiedByName'}},
        {
          $project: project,
        },
      );

      let groupList = await this.labelGroupRepo.aggregate(params);
      return groupList;
    } else if (exportApiType == Explore_API_TYPE.COUNT) {
      params.push({$count: 'count'});

      let countObj = await this.labelGroupRepo.aggregate(params);

      if (Array.isArray(countObj) && countObj.length > 0) {
        return {
          count: countObj[0].count,
        };
      }
    }
  }

  /**
   * Use to get labels of label group
   * @param groupId {string} id of the label group
   * @returns label id list
   */
  async getLabelsOfLabelGroup(groupId: string, project: any) {
    let params = [
      {$match: {groupIdList: new ObjectId(groupId)}},
      {
        $project: project,
      },
    ];
    let groupLabelList: {
      _id: string;
      label: string;
    }[] = [];

    groupLabelList = await this.systemLabelRepository.aggregate(params);
    if (Array.isArray(groupLabelList) && groupLabelList.length > 0) {
      groupLabelList = groupLabelList;
    }
    return groupLabelList;
  }

  /**
   * Use to upload label image
   * @param type {number} label type
   * @param label {string} label unique identifier
   * @param attributeLabel {string} attribute unique identifier
   * @param valueName {string} value unique identifier
   * @param fileArr file data array
   * @param teamId {string} id of the team
   * @param userName {string} name of the User
   * @param userId {string} id of the User
   * @returns {success: boolean}
   */
  async labelImageUpload(
    type: any,
    label: any,
    attributeLabel: any,
    valueName: any,
    fileArr: FileInterface[],
    teamId: string,
    userName: string,
    userId?: string,
  ) {
    let systemLabelObj = await this.systemLabelRepository.findOne({
      where: {
        and: [{teamId: teamId}, {label: label}, {isDeleted: {neq: true}}],
      },
    });

    if (!systemLabelObj) {
      logger.error(
        `${className}|labelImageUpload| Upload system label image request from userId: ${userId} for non exist label: `,
        label,
      );
      throw new HttpErrors.NotAcceptable('Non exist label');
    }
    let systemLabelId = systemLabelObj.id || '';

    logger.debug(`type ${type} key ${label} fileArr length: ${fileArr.length}`);

    if (fileArr.length < 1) return;

    let uploadedFileDetails: labelImageFilesInfo[] = [];
    try {
      if (type == SystemLabelType.CLASS_WITH_ATTRIBUTES) {
        for (let file of fileArr) {
          let variableFileName = new Date().getTime().toString();
          const fileName = variableFileName + '_' + file.originalname;
          const fileBuffer = file.buffer;
          // let url = await fileStorage.createFile(
          //   teamId + '/' + label + '/attribute/' + attributeLabel + '/' + valueName,
          //   fileName,
          //   fileBuffer
          // );
          let key = `${STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS}/${teamId}/${label}/${attributeLabel}/${valueName}/${fileName}`;
          //let bucket = 'system-data-storage'
          await this.storageCrawlerService.storageServiceProvider.uploadFileFromBuffer(
            fileBuffer,
            key,
            SYSTEM_DATA_BUCKET,
          );

          let url = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
            key,
            24 * 3600 * 7,
            SYSTEM_DATA_BUCKET,
          );
          let urlExpiredAt = new Date(new Date().getTime() + 7 * 24 * 3600 * 1000);
          uploadedFileDetails.push({srcUrl: url, imageName: file.originalname, key: key, urlExpiredAt: urlExpiredAt});
          //logger.debug(url.url)
        }
      } else if (type == SystemLabelType.CLASS_ONLY) {
        for (let file of fileArr) {
          let variableFileName = new Date().getTime().toString();
          const fileName = variableFileName + '_' + file.originalname;
          const fileBuffer = file.buffer;
          // let url = await fileStorage.createFile(
          //   teamId + '/' + label + '/label',
          //   fileName,
          //   fileBuffer
          // );

          let key = `${STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS}/${teamId}/${label}/${fileName}`;
          //let bucket = 'system-data-storage'
          await this.storageCrawlerService.storageServiceProvider.uploadFileFromBuffer(
            fileBuffer,
            key,
            SYSTEM_DATA_BUCKET,
          );
          let url = await this.storageCrawlerService.storageServiceProvider.generateObjectUrl(
            key,
            24 * 3600 * 7,
            SYSTEM_DATA_BUCKET,
          );

          let urlExpiredAt = new Date(new Date().getTime() + 7 * 24 * 3600 * 1000);
          uploadedFileDetails.push({srcUrl: url, imageName: file.originalname, key: key, urlExpiredAt: urlExpiredAt});
        }
      }

      if (uploadedFileDetails.length > 0) {
        await this.addLabelImages(
          uploadedFileDetails,
          type,
          label,
          attributeLabel,
          valueName,
          userName,
          systemLabelId,
          teamId,
        );
        return {success: true};
      } else {
        logger.error('No images to add to system labels');
        return {success: false};
      }
    } catch (e) {
      logger.error(
        `${className}|labelImageUpload| failed to add label images to label: ${label} teamId: ${teamId} : `,
        e,
      );
      return {success: false};
    }
  }

  /**
   * Use to get label options list of system labels according to content type
   * @param teamId {string} id of the team
   * @param contentType {ContentType}
   * @returns list of label options
   */
  async getSystemLabelOptionsList(teamId: string, contentType: ContentType) {
    let labelOptionsList: SelectionOptionsObject[] = [];
    let labelToLabelTextMap = await this.getLabelToLabelTextMapOfTeam(teamId);

    let query = teamId ? {where: {teamId: teamId}} : {};
    let statsObject = await this.systemDataRepository.findOne(query); //NOTE: add teamId filter
    switch (contentType) {
      case ContentType.IMAGE:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.images.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.images.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.VIDEO:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.videos.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.videos.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.OTHER:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.other.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.other.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.IMAGE_COLLECTION:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.imageCollections.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.imageCollections.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.VIDEO_COLLECTION:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.videoCollections.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.videoCollections.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.OTHER_COLLECTION:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.otherCollections.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.otherCollections.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      case ContentType.DATASET:
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.datasets.labelList) {
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.datasets.labelList)) {
              let labelObject: SelectionOptionsObject = {
                name: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                selected: false,
              };
              labelOptionsList.push(labelObject);
            }
          }
        }
        break;
      default:
        break;
    }

    return labelOptionsList;
  }
}
export const SYSTEM_LABEL_SERVICE = BindingKey.create<SystemLabelService>('service.systemLabelService');
