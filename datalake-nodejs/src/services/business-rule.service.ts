/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle business logics related to the data dictionary business rules
 */

/**
 * @class BusinessRuleService
 * @description This service use for Handle business logics related to the data dictionary business rules
 * <AUTHOR>
 */

import {BindingKey, BindingScope, inject, injectable} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {KnowledgeSourceType} from '../models';
import {BusinessRuleRepository} from '../repositories/business-rule.repository';
import {FLOWS} from '../settings/constants';

@injectable({scope: BindingScope.TRANSIENT})
export class BusinessRuleService {
  constructor(
    @inject('repositories.BusinessRuleRepository')
    private businessRuleRepo: BusinessRuleRepository,
  ) {}

  /**
   * Get all business rules with optional search
   * @param searchKey Optional search key to filter rules
   * @returns List of business rules
   */
  async getBusinessRules(
    searchKey?: string,
    pageSize: number = 20,
    pageIndex: number = 0,
  ): Promise<{isSuccess: boolean; message: string; data?: {businessRules: any[]}}> {
    try {
      let filter: any = {is_active: true};

      // Add search filter if searchKey is provided
      if (searchKey) {
        filter.rule = {regexp: new RegExp(searchKey, 'i')};
      }

      const businessRules = await this.businessRuleRepo.find({
        where: filter,
        skip: pageIndex * pageSize,
        limit: pageSize,
        order: ['created_date DESC'],
      });

      let formattedBusinessRules = [];

      //format the business rules send to frontend
      for (let i = 0; i < businessRules.length; i++) {
        formattedBusinessRules.push({
          _id: businessRules[i]._id?.toString(),
          created_by: businessRules[i].created_by,
          created_date: businessRules[i].created_date,
          is_active: businessRules[i].is_active,
          is_edit: businessRules[i].is_edit,
          is_enabled: businessRules[i].is_enabled ? true : false,
          modified_date: businessRules[i].modified_date,
          modified_by: businessRules[i].modified_by,
          rule: businessRules[i].rule,
          rule_source_name: businessRules[i].rule_source_name,
          rule_source_type: businessRules[i].rule_source_type,
          tables: businessRules[i].table_columns?.map(table => table.table_name).filter(Boolean) ?? [],
        });
      }

      return {
        isSuccess: true,
        message: 'Business rules retrieved successfully',
        data: {businessRules: formattedBusinessRules},
      };
    } catch (error) {
      logger.error(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.getBusinessRules | N/A | Error: ${error.message}`);
      return {
        isSuccess: false,
        message: 'Error retrieving business rules',
      };
    }
  }

  /**
   * Get all active business rules formatted as markdown
   * @returns Markdown formatted business rules
   */
  async getFormattedBusinessRules(): Promise<{rule: string; table_columns: any}[]> {
    logger.info('BusinessRuleService | getFormattedBusinessRules | Getting active business rules');

    try {
      const filter = {is_active: true, is_enabled: true};
      const businessRules = await this.businessRuleRepo.find({
        where: filter,
      });

      const formattedRules = businessRules.map(rule => {
        return {
          rule: rule.rule,
          table_columns: rule.table_columns ?? [],
        };
      });

      return formattedRules;
    } catch (error) {
      logger.error(`BusinessRuleService | getFormattedBusinessRules | Error: ${error.message}`);
      throw new HttpErrors.InternalServerError('Error retrieving formatted business rules');
    }
  }

  /**
   * Create a new business rule
   * @param data Business rule data
   * @param currentUser Current user profile
   * @returns Success message
   */
  async createBusinessRule(
    rule: string,
    currentUser: UserProfileDetailed,
  ): Promise<{isSuccess: boolean; message: string}> {
    logger.info(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.createBusinessRule | N/A | rule: ${rule}`);

    try {
      const now = new Date();
      const userName = currentUser.name || currentUser.email || 'Unknown';

      await this.businessRuleRepo.create({
        rule: rule,
        created_date: now,
        modified_date: now,
        created_by: userName,
        modified_by: userName,
        is_edit: false,
        is_active: true,
        is_enabled: true,
        rule_source_type: KnowledgeSourceType.MANUAL,
        rule_source_name: 'Manual',
      });

      return {
        isSuccess: true,
        message: 'Business rule created successfully',
      };
    } catch (error) {
      logger.error(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.createBusinessRule | N/A | Error: ${error.message}`);
      return {
        isSuccess: false,
        message: 'Error creating business rule',
      };
    }
  }

  /**
   * Update an existing business rule
   * @param id Business rule ID
   * @param rule Updated business rule
   * @param currentUser Current user profile
   * @returns Success message
   */
  async updateBusinessRule(
    id: string,
    rule: string,
    currentUser: UserProfileDetailed,
  ): Promise<{isSuccess: boolean; message: string}> {
    logger.info(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.updateBusinessRule | N/A | id: ${id}, rule: ${rule}`);

    try {
      // Check if the business rule exists
      await this.businessRuleRepo.findById(id);

      const userName = currentUser.name || currentUser.email || 'Unknown';

      await this.businessRuleRepo.updateById(id, {
        rule: rule,
        modified_date: new Date(),
        modified_by: userName,
        is_edit: true,
      });

      return {
        isSuccess: true,
        message: 'Business rule updated successfully',
      };
    } catch (error) {
      logger.error(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.updateBusinessRule | N/A | Error: ${error.message}`);
      return {
        isSuccess: false,
        message: 'Error updating business rule',
      };
    }
  }

  /**
   * Soft delete a business rule
   * @param id Business rule ID
   * @param currentUser Current user profile
   * @returns Success message
   */
  async deleteBusinessRule(
    id: string,
    currentUser: UserProfileDetailed,
  ): Promise<{isSuccess: boolean; message: string}> {
    logger.info(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.deleteBusinessRule | N/A | id: ${id}`);

    try {
      // Check if the business rule exists
      await this.businessRuleRepo.findById(id);

      const userName = currentUser.name || currentUser.email || 'Unknown';

      // Soft delete by setting is_active to false
      await this.businessRuleRepo.updateById(id, {
        is_active: false,
        modified_date: new Date(),
        modified_by: userName,
      });

      return {
        isSuccess: true,
        message: 'Business rule deleted successfully',
      };
    } catch (error) {
      logger.error(`${FLOWS.BUSINESS_RULES} | BusinessRuleService.deleteBusinessRule | N/A | Error: ${error.message}`);
      return {
        isSuccess: false,
        message: 'Error deleting business rule',
      };
    }
  }

  async getBusinessRulesLatestModifiedDate(): Promise<Date> {
    logger.info(
      'BusinessRuleService | getBusinessRulesLatestModifiedDate | Getting latest modified date for business rules',
    );

    try {
      const latestModifiedDate = await this.businessRuleRepo.getBusinessRulesLatestModifiedDate();
      return latestModifiedDate;
    } catch (error) {
      logger.error(`BusinessRuleService | getBusinessRulesLatestModifiedDate | Error: ${error.message}`);
      throw new HttpErrors.InternalServerError('Error retrieving latest modified date for business rules');
    }
  }

  /**
   * Toggles the status of a business rule between enabled and disabled.
   *
   * @param id - The unique identifier of the business rule to toggle.
   * @returns An object containing a success flag and a message indicating the new status of the business rule.
   * @throws HttpErrors.InternalServerError - If an error occurs while toggling the business rule status.
   */

  async toggleBusinessRuleStatus(id: string, is_enabled: boolean): Promise<{isSuccess: boolean; message: string}> {
    try {
      await this.businessRuleRepo.updateById(id, {is_enabled: is_enabled, modified_date: new Date()});
      return {
        isSuccess: true,
        message: `Business rule status ${is_enabled ? 'enabled' : 'disabled'} successfully`,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | BusinessRuleService.toggleBusinessRuleStatus | N/A | Error: ${error.message}`,
      );
      return {
        isSuccess: false,
        message: 'Error toggling business rule status',
      };
    }
  }
}

export const BUSINESS_RULE_SERVICE = BindingKey.create<BusinessRuleService>('service.businessRuleService');
