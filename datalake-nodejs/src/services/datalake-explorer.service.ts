/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perfome the datalake exploring related logics
 */

/**
 * @class DatalakeExplorerService
 * purpose of this service is datalake exploring
 * @description datalake exploring logics
 * <AUTHOR> chathushka
 */

import {BindingKey, /* inject, */ BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {default as Axios} from 'axios';
import {ObjectId} from 'bson';
import dotenv from 'dotenv';
import moment from 'moment';
import {v4 as uuidV4} from 'uuid';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  AnalyticDataRes,
  AnalyticLabelStats,
  Analytics,
  ContentType,
  ContentTypeFrontendConstants,
  DatalakeSelection,
  DatalakeSelectionRequest,
  DetailTabGetRes,
  DetailTabKeyVal,
  DetailTabKeyValAllDataFormat,
  ExploreSortBy,
  ExplorerDefaultViewDetailsResponse,
  ExplorerDetailTabMetaInfo,
  ExplorerFilterV2,
  FormatMetaDataFieldAndTagSummaryRes,
  GetEmbeddingAggregationForSelectionRes,
  GraphStatus,
  LabelAnalytics,
  LabelCountInfo,
  LabelList,
  LabelMetadataSelectionOptionsObject,
  MetaFieldAndArraySummaryFormat,
  OBJECT_STATUS,
  ObjectTypeWiseCounts,
  RequiredInfoForDetailINDynamicQueryRes,
  RequiredTagsAndMetaDataInDynamicQueryRes,
  SelectionOptionsObject,
  SortOrder,
  TagsAndMetaDataConfig,
  UserSelectionRequest,
  VerificationStatusCount,
} from '../models';
import {
  MetaDataRepository,
  MetaFieldRepository,
  QueryGraphDetailsRepository,
  SystemDataRepository,
} from '../repositories';
import {UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {byteTransform, getFormattedItemCount, getLocalTime, isValidObjectId} from '../settings/tools';
import {SEARCH_QUERY_BUILDER_SERVICE, SearchQueryBuilderService} from './search-query-builder.service';
import {DetailSource, STATS_CALCULATION_SERVICE, StatsCalculationService} from './stats-calculation.service';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from './system-label.service';
import {SYSTEM_META_SERVICE, SystemMetaService} from './system-meta.service';
dotenv.config();
const PYTHON_HOST = process.env.PYTHON_BASE_URL;

@injectable({scope: BindingScope.TRANSIENT})
export class DatalakeExplorerService {
  constructor(
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
    @inject(SYSTEM_LABEL_SERVICE) private systemLabelService: SystemLabelService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(SYSTEM_META_SERVICE) private systemMetaService: SystemMetaService,
    @inject(STATS_CALCULATION_SERVICE) private statsCalculationService: StatsCalculationService,
    @repository(MetaFieldRepository) private metaFieldRepository: MetaFieldRepository,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @repository(QueryGraphDetailsRepository)
    private queryGraphDetailsRepository: QueryGraphDetailsRepository,
  ) {}

  /**
   * Use to get analytic data
   * @param filter {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @returns AnalyticDataRes[] analytic data
   */
  async getAnalyticsData(
    filter: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
  ): Promise<AnalyticDataRes[]> {
    const teamId = currentUserProfile.teamId;
    /**
     * Use for get detail source
     */
    const detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
      approachMethod: DetailSource;
    } = await this.statsCalculationService.getDetailsSource(filter, currentUserProfile);
    let analyticObj: Promise<Analytics[]>;
    switch (detailsSource.sourceType) {
      /**
       * Get required info for analytic data when source is dynamic query
       */
      case DetailSource.DYNAMIC_QUERY:
        analyticObj = this.requiredAnalyticDataInDynamicQuery(filter, currentUserProfile);
        break;
      /**
       * Get required info for  analytic data when source is meta data
       */
      case DetailSource.METADATA:
        analyticObj = this.requiredAnalyticDataInMetaData(detailsSource);
        break;
      /**
       * Get required info for analytic data when source is system data
       */
      case DetailSource.SYSTEM_DATA:
        analyticObj = this.requiredAnalyticDataInSystemData(filter);
        break;
    }

    const [analytics, labelToLabelTextMap] = await Promise.all([
      analyticObj,
      this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId),
    ]);

    /**
     * Format analytic data
     */
    return this.formatAnalyticsData(labelToLabelTextMap, analytics);
  }

  /**
   * Format analytic data
   * @param labelMap {Record<string, string>} label map
   * @param analytics {Analytics[]} analytic data
   * @returns AnalyticDataRes[] formatted analytic data
   */
  async formatAnalyticsData(labelMap: Record<string, string>, analytics: Analytics[]): Promise<AnalyticDataRes[]> {
    const formatData: AnalyticDataRes[] = analytics.map(analyticsObj => {
      if (isValidObjectId(analyticsObj.operationId)) {
        analyticsObj.operationId = analyticsObj.operationId.toString();
      }
      return {
        modelName: analyticsObj.operationId,
        precision: typeof analyticsObj.precision == 'number' ? parseFloat(analyticsObj.precision.toFixed(2)) : NaN,
        recall: typeof analyticsObj.recall == 'number' ? parseFloat(analyticsObj.recall.toFixed(2)) : NaN,
        f1Score: typeof analyticsObj.f1Score == 'number' ? parseFloat(analyticsObj.f1Score.toFixed(2)) : NaN,
        labelStats: this.formatLabelInAnalytics(analyticsObj.labelWiseAnalytics, labelMap),
      };
    });

    formatData.sort((a, b) => {
      const nameA = (a.modelName || '').toLowerCase();
      const nameB = (b.modelName || '').toLowerCase();
      return nameA.localeCompare(nameB);
    });
    return formatData;
  }

  /**
   * Use to format label wise analytics
   * @param labelWiseAnalytics {LabelAnalytics[]} label wise analytics
   * @param labelMap {Record<string, string>} label map
   * @returns AnalyticLabelStats[] label wise analytics
   */
  formatLabelInAnalytics(labelWiseAnalytics: LabelAnalytics[], labelMap: Record<string, string>): AnalyticLabelStats[] {
    if (!Array.isArray(labelWiseAnalytics) || labelWiseAnalytics.length == 0) return [];
    const formattedLabels: AnalyticLabelStats[] = labelWiseAnalytics.map(labelObj => {
      return {
        label: labelMap[labelObj.label],
        precision: typeof labelObj.precision == 'number' ? parseFloat(labelObj.precision.toFixed(2)) : NaN,
        recall: typeof labelObj.recall == 'number' ? parseFloat(labelObj.recall.toFixed(2)) : NaN,
        f1Score: typeof labelObj.f1Score == 'number' ? parseFloat(labelObj.f1Score.toFixed(2)) : NaN,
      };
    });

    formattedLabels.sort((a, b) => {
      const nameA = (a.label || '').toLowerCase();
      const nameB = (b.label || '').toLowerCase();
      return nameA.localeCompare(nameB);
    });

    return formattedLabels;
  }

  /**
   * Required info for analytic data when source is system data
   * @param filter {DatalakeSelectionRequest} filter object
   * @returns Analytics[] analytic data
   */
  async requiredAnalyticDataInSystemData(filter: DatalakeSelectionRequest): Promise<Analytics[]> {
    const contentType: ContentType = filter.contentType ?? ContentType.ALL;

    const systemData = await this.systemDataRepository.findOne({});
    let key: keyof ObjectTypeWiseCounts;
    switch (contentType) {
      case ContentType.IMAGE:
      case ContentType.IMAGE_COLLECTION:
        key = 'images';
        break;

      case ContentType.VIDEO:
      case ContentType.VIDEO_COLLECTION:
        key = 'videos';
        break;

      case ContentType.DATASET:
        key = 'datasets';
        break;

      default:
        key = 'images';
        break;
    }

    return systemData?.systemAnalytics?.[key] ?? [];
  }

  /**
   * Use to get required info for analytic data when source is meta data
   * @param detailsSource {DetailSource} details source
   * @returns Analytics[] analytic data
   */
  async requiredAnalyticDataInMetaData(detailsSource: {
    sourceType: DetailSource;
    id: string;
    contentType: ContentType;
    approachMethod: DetailSource;
  }): Promise<Analytics[]> {
    const metaData = await this.metaDataRepository.findById(detailsSource.id);

    return metaData?.analytics ?? [];
  }

  /**
   * Required info for analytic data when source is dynamic query
   * @param filter {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @returns Analytics[] analytic data
   */
  async requiredAnalyticDataInDynamicQuery(
    filter: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
  ): Promise<Analytics[]> {
    const teamId = currentUserProfile.teamId;
    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };
    const aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );
    if (!aggregateQuery) throw new HttpErrors.UnprocessableEntity('Invalid selection query');

    let {matchQuery} = aggregateQuery;

    const collectionList: ContentType[] = [
      ContentType.IMAGE_COLLECTION,
      ContentType.VIDEO_COLLECTION,
      ContentType.DATASET,
    ];

    if (collectionList.includes(filter.contentType ?? ContentType.ALL)) {
      const paramsGetIdList: any = [
        {$match: matchQuery},
        {
          $group: {
            _id: null,
            idList: {$push: '$_id'},
          },
        },
      ];

      const tempIdList = await this.metaDataRepository.aggregate(paramsGetIdList);
      let idList: string[] = [];

      if (Array.isArray(tempIdList) && tempIdList.length > 0) {
        idList = tempIdList[0].idList ? tempIdList[0].idList : [];
      }
      matchQuery = {vCollectionIdList: {$in: idList}};
    }

    const lookup: GetEmbeddingAggregationForSelectionRes[] = [];
    // if (filter?.embeddingSelection?.selection && filter?.embeddingSelection?.graphId) {
    //   lookup = await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(filter.embeddingSelection);
    // }
    const analytics: Analytics[] = await this.statsCalculationService.analyticsCalculation(matchQuery, lookup);
    return analytics;
  }

  /**
   * Use to get explorer tab
   * @param filter {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @param timeZone {number} time zone
   * @returns ExplorerDefaultViewDetailsResponse
   */
  async getExplorerDetailTab(
    filter: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
    timeZone: number,
  ): Promise<DetailTabGetRes> {
    const teamId = currentUserProfile.teamId;
    /**
     * Use for get detail source
     */
    const detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
      approachMethod: DetailSource;
    } = await this.statsCalculationService.getDetailsSource(filter, currentUserProfile);

    const data: ExplorerDetailTabMetaInfo = {};
    if (filter.collectionId) {
      const collection = await this.metaDataRepository.findById(filter.collectionId);
      data.collectionName = collection.name;
      data.collectionId = filter.collectionId;
    }

    let requiredInfo: Promise<RequiredInfoForDetailINDynamicQueryRes>;
    switch (detailsSource.sourceType) {
      /**
       * Get required info for details tab when source is dynamic query
       */
      case DetailSource.DYNAMIC_QUERY:
        requiredInfo = this.requiredInfoForDetailInDynamicQuery(currentUserProfile, filter, data);

        break;
      /**
       * Get required info for details tab when source is meta data
       */
      case DetailSource.METADATA:
        requiredInfo = this.requiredInfoForDetailInMetaData(detailsSource, data, filter, timeZone);

        break;
      /**
       * Get required info for details tab when source is system data
       */
      case DetailSource.SYSTEM_DATA:
        requiredInfo = this.requiredInfoForDetailInSystemData(filter, data);

        break;
    }

    const [{labelList, verificationStatus, metaDataFieldSummary, frameCount}, labelToLabelTextMap] = await Promise.all([
      requiredInfo,
      this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId),
    ]);

    let totalLabels: number = 0;
    const labelWithText: DetailTabKeyVal[] = labelList.map(labelObj => {
      totalLabels += labelObj.count;
      return {
        key: labelToLabelTextMap[labelObj.label],
        value: getFormattedItemCount(labelObj.count),
      };
    });

    data.labelCount = getFormattedItemCount(totalLabels);

    const frameList: DetailTabKeyVal[] = await this.formatDataTabFrameList(verificationStatus);
    const {tags, metaData, tagCount, metaDataCount} = await this.formatMetaDataFieldAndTagSummary(
      metaDataFieldSummary,
      data,
    );

    return {
      frames: frameList,
      labels: labelWithText,
      tags: tags,
      metaData: metaData,
      frameCount: frameCount,
      labelCount: totalLabels,
      tagCount: tagCount,
      metaDataCount: metaDataCount,
    };
  }

  /**
   * Use to get required info for details tab when source is system data
   * @param filter {DatalakeSelectionRequest} filter object
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @returns RequiredInfoForDetailINDynamicQueryRes
   */
  async requiredInfoForDetailInSystemData(
    filter: DatalakeSelectionRequest,
    data: ExplorerDetailTabMetaInfo,
  ): Promise<RequiredInfoForDetailINDynamicQueryRes> {
    const contentType: ContentType = filter.contentType ?? ContentType.ALL;

    const systemData = await this.systemDataRepository.findOne({});
    const objectTypeWiseCounts: ObjectTypeWiseCounts | undefined = systemData?.objectTypeWiseCounts;
    let key: keyof ObjectTypeWiseCounts;
    switch (contentType) {
      case ContentType.IMAGE:
        key = 'images';
        break;
      case ContentType.IMAGE_COLLECTION:
        key = 'imageCollections';
        break;
      case ContentType.VIDEO:
        key = 'videos';
        break;
      case ContentType.VIDEO_COLLECTION:
        key = 'videoCollections';
        break;
      case ContentType.DATASET:
        key = 'datasets';
        break;
      case ContentType.OTHER:
        key = 'other';
        break;
      case ContentType.OTHER_COLLECTION:
        key = 'otherCollections';
        break;
      default:
        key = 'images';
        break;
    }
    const framesInclude: ContentType[] = [
      ContentType.VIDEO,
      ContentType.VIDEO_COLLECTION,
      ContentType.IMAGE_COLLECTION,
      ContentType.IMAGE,
    ];

    if (framesInclude.includes(contentType)) {
      data.frames = getFormattedItemCount(objectTypeWiseCounts?.[key]?.frames ?? 0);
    }

    const metaDataFieldSummary: MetaFieldAndArraySummaryFormat[] = systemData?.metaDataFieldSummary?.[key] ?? [];

    const verificationStatus: VerificationStatusCount = {
      raw: objectTypeWiseCounts?.[key]?.raw ?? 0,
      machineAnnotated: objectTypeWiseCounts?.[key]?.machineAnnotated ?? 0,
      verified: objectTypeWiseCounts?.[key]?.verified ?? 0,
    };

    data.size = byteTransform(objectTypeWiseCounts?.[key]?.size, false);
    const labelListObj: {[k: string]: number} = objectTypeWiseCounts?.[key]?.labelList ?? {};

    const labelList: LabelList[] =
      typeof labelListObj == 'object'
        ? Object.keys(labelListObj).map(label => {
            return {
              label: label,
              count: labelListObj[label],
            };
          })
        : [];

    return {
      verificationStatus: verificationStatus,
      labelList: labelList,
      metaDataFieldSummary: metaDataFieldSummary,
      frameCount: objectTypeWiseCounts?.[key]?.frames ?? 0,
    };
  }

  /**
   * Use to get required info for details tab when source is meta data
   * @param detailsSource {DetailSource} details source
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @param filter {DatalakeSelectionRequest} filter object
   * @param timeZone {number} time zone
   * @returns RequiredInfoForDetailINDynamicQueryRes
   */
  async requiredInfoForDetailInMetaData(
    detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
      approachMethod: DetailSource;
    },
    data: ExplorerDetailTabMetaInfo,
    filter: DatalakeSelectionRequest,
    timeZone: number,
  ): Promise<RequiredInfoForDetailINDynamicQueryRes> {
    const collectionTypes: ContentType[] = [
      ContentType.IMAGE_COLLECTION,
      ContentType.VIDEO_COLLECTION,
      ContentType.OTHER_COLLECTION,
      ContentType.DATASET,
    ];

    const metaData = await this.metaDataRepository.findById(detailsSource.id);
    const verificationStatus: VerificationStatusCount = metaData.verificationStatusCount ?? {
      raw: 0,
      machineAnnotated: 0,
      verified: 0,
    };

    if (metaData.frameCount) data.frames = getFormattedItemCount(metaData.frameCount);

    const labelList: LabelList[] = metaData?.labelList ?? [];

    let metaDataFieldSummary: MetaFieldAndArraySummaryFormat[];
    const outsideCollection: boolean = !!(
      collectionTypes.includes(metaData.objectType) &&
      !filter.collectionId &&
      filter.objectIdList?.length == 1
    );

    if (
      outsideCollection ||
      detailsSource.approachMethod == DetailSource.DYNAMIC_QUERY ||
      !collectionTypes.includes(metaData.objectType)
    ) {
      const id: string = outsideCollection && filter?.objectIdList ? filter.objectIdList[0] : detailsSource.id;

      const dbQuery = {_id: new ObjectId(id)};
      const dbQueryObject = {
        matchQuery: dbQuery,
        objectType: metaData.objectType,
      };

      metaDataFieldSummary = await this.statsCalculationService.getGetMetaFieldsStatsForQuery(
        dbQueryObject,
        filter.collectionId,
      );
    } else {
      metaDataFieldSummary = metaData?.metaDataFieldSummary ?? [];
    }

    data.size = byteTransform(metaData?.fileSize, false);
    const createdAt: Date = getLocalTime(timeZone, metaData.createdAt);
    const updatedAt: Date = getLocalTime(timeZone, metaData.updatedAt);
    data.createdAt = moment(createdAt).format('MMM D, YYYY h:mm A');
    data.updatedAt = moment(updatedAt).format('MMM D, YYYY h:mm A');

    return {
      verificationStatus: verificationStatus,
      labelList: labelList,
      metaDataFieldSummary: metaDataFieldSummary,
      frameCount: metaData?.frameCount ?? 0,
    };
  }

  /**
   * Use to get required info for details tab when source is dynamic query
   * @param teamId {string} team id
   * @param filter {DatalakeSelectionRequest} filter object
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @returns RequiredInfoForDetailINDynamicQueryRes
   */
  async requiredInfoForDetailInDynamicQuery(
    currentUserProfile: UserProfileDetailed,
    filter: DatalakeSelectionRequest,
    data: ExplorerDetailTabMetaInfo,
  ): Promise<RequiredInfoForDetailINDynamicQueryRes> {
    const teamId = currentUserProfile.teamId;
    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };
    const lookup: GetEmbeddingAggregationForSelectionRes[] = [];
    // if (filter?.embeddingSelection?.selection && filter?.embeddingSelection?.graphId) {
    //   lookup = await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(filter.embeddingSelection);
    // }
    const aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );

    if (!aggregateQuery) throw new HttpErrors.UnprocessableEntity('Invalid selection query');

    const [frameLevelStats, metaDataFieldSummary] = await Promise.all([
      this.statsCalculationService.calcFrameLevelStats(
        aggregateQuery?.matchQuery,
        filter.contentType ?? ContentType.ALL,
        lookup,
      ),

      this.statsCalculationService.getGetMetaFieldsStatsForQuery(
        aggregateQuery,
        filter.collectionId,
        undefined,
        lookup,
      ),
    ]);

    if (frameLevelStats?.frameCounts?.frameCountSum) {
      data.frames = getFormattedItemCount(frameLevelStats.frameCounts.frameCountSum);
    }

    const labelList: LabelList[] = frameLevelStats?.labelCounts ?? [];

    const verificationStatus: VerificationStatusCount = {
      machineAnnotated: frameLevelStats?.frameCounts?.machineAnnotatedSum ?? 0,
      raw: frameLevelStats?.frameCounts?.rawSum ?? 0,
      verified: frameLevelStats?.frameCounts?.verifiedSum ?? 0,
    };

    data.size = byteTransform(frameLevelStats?.frameCounts?.fileSize, false);

    return {
      verificationStatus: verificationStatus,
      labelList: labelList,
      metaDataFieldSummary: metaDataFieldSummary,
      frameCount: frameLevelStats?.frameCounts?.frameCountSum ?? 0,
    };
  }

  /**
   * Use to format meta data field and tag summary for details tab in datalake explorer tab
   * @param metaDataFieldSummary {MetaFieldAndArraySummaryFormat[]} meta data field summary
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @param allMetaData {boolean} whether only custom meta data is required
   * @returns formatted meta data field and tag summary
   */
  async formatMetaDataFieldAndTagSummary(
    metaDataFieldSummary: MetaFieldAndArraySummaryFormat[],
    data: ExplorerDetailTabMetaInfo,
    allMetaData: boolean = true,
  ): Promise<FormatMetaDataFieldAndTagSummaryRes> {
    const tags: string[] = [];
    const metaDataArr: DetailTabKeyVal[] = [];
    let tagCount: number = 0;
    let metaDataCount: number = 0;

    const metaDataMapping: Record<keyof ExplorerDetailTabMetaInfo, string> = {
      collectionName: 'Collection Name',
      collectionId: 'collectionId',
      size: 'Size',
      frames: 'Frames',
      updatedAt: 'Last Modified',
      createdAt: 'Uploaded Date',
      labelCount: 'Number of Labels',
    };

    if (allMetaData && typeof data == 'object' && JSON.stringify(data) != '{}') {
      for (const [key, value] of Object.entries(data)) {
        metaDataArr.push({
          key: metaDataMapping[key as keyof ExplorerDetailTabMetaInfo],
          value: value,
        });
      }
    }

    for (const metaData of metaDataFieldSummary) {
      //Tags can be identified by _id, its value is always 'Tags'
      if (metaData._id == 'Tags' && metaData?.values?.[0]?.value) {
        tags.push(metaData.values[0].value);
        tagCount += 1;
        continue;
      }

      const field: string = metaData._id;
      if (!Array.isArray(metaData.values)) continue;
      let key: string = '';
      for (const valueObj of metaData.values) {
        if (key.length != 0) key += ',';
        key += valueObj.value;
      }

      metaDataArr.push({
        key: field,
        value: key,
      });
      metaDataCount += 1;
    }

    return {
      tags: tags,
      metaData: metaDataArr,
      tagCount: tagCount,
      metaDataCount: metaDataCount,
    };
  }

  /**
   * Use to format frame list for details tab in datalake explorer tab initial view
   * @param verificationStatus {VerificationStatusCount} verification status count object
   * @returns formatted frame list
   */
  async formatDataTabFrameList(verificationStatus: VerificationStatusCount): Promise<DetailTabKeyVal[]> {
    const frameList: DetailTabKeyVal[] = [];

    for (const [key, value] of Object.entries(verificationStatus)) {
      if (value == 0) continue;
      let fieldName: string = '';
      switch (key) {
        case 'raw':
          fieldName = 'Raw Data';
          break;
        case 'machineAnnotated':
          fieldName = 'Machine Annotated';
          break;
        case 'verified':
          fieldName = 'Human Annotated';
          break;
      }

      frameList.push({
        key: fieldName,
        value: getFormattedItemCount(value),
      });
    }

    return frameList;
  }

  /**
   * Use to get tags or meta data
   * @param filter {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @param timeZone {number} time zone
   * @param config {TagsAndMetaDataConfig} config object (whether Tag list, all meta data or custom meta data)
   * @returns tags or meta data
   */
  async getTagsOrMetaData(
    filter: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
    timeZone: number,
    config: TagsAndMetaDataConfig,
  ): Promise<string[] | DetailTabKeyVal[]> {
    const {allMetaData, isTagList} = config;

    /**
     * Both tags and meta data list get by re calculating,So source type is no longer required
     */
    /**
     * Use for get detail source
     */
    // let detailsSource: {
    //   sourceType: DetailSource;
    //   id: string;
    //   contentType: ContentType;
    //   approachMethod: DetailSource;
    // } = await this.statsCalculationService.getDetailsSource(filter);
    const teamId = currentUserProfile.teamId;
    let labelList: LabelList[] = [];
    let metaDataFieldSummary: MetaFieldAndArraySummaryFormat[];
    const data: ExplorerDetailTabMetaInfo = {};

    if (!teamId) {
      logger.error(`get tag or metadata | DatalakeExplorerService.getTagsOrMetaData | N/A | teamId is undefined`);
      throw new HttpErrors.UnprocessableEntity(`teamId is not present in currentUserProfile`);
    }

    if (filter.collectionId && allMetaData) {
      data.collectionId = filter.collectionId;
      const collection = await this.metaDataRepository.findById(filter.collectionId);
      data.collectionName = collection.name;
    }

    /**
     * Get required  tags and meta data when source is dynamic query
     **/
    ({labelList, metaDataFieldSummary} = await this.requiredTagsAndMetaDataInDynamicQuery(
      currentUserProfile,
      filter,
      data,
      allMetaData,
      isTagList,
    ));

    /**
     * Both tags and meta data list get by re calculating,So source type is no longer required
     */
    // switch (detailsSource.sourceType) {
    //   /**
    //    * Get required  tags and meta data when source is dynamic query
    //    */
    //   case DetailSource.DYNAMIC_QUERY:
    //     ({labelList, metaDataFieldSummary} = await this.requiredTagsAndMetaDataInDynamicQuery(
    //       teamId,
    //       filter,
    //       data,
    //       allMetaData,
    //       isTagList,
    //     ));
    //     break;
    //   /**
    //    * Get required tags and meta data when source is meta data
    //    */
    //   case DetailSource.METADATA:
    //     ({labelList, metaDataFieldSummary} = await this.requiredTagsAndMetaDataInMetaData(
    //       detailsSource,
    //       data,
    //       filter,
    //       timeZone,
    //       allMetaData,
    //       isTagList,
    //     ));
    //     break;
    //   /**
    //    * Get required tags and meta data when source is system data
    //    */
    //   case DetailSource.SYSTEM_DATA:
    //     ({labelList, metaDataFieldSummary} = await this.requiredTagsAndMetaDataInSystemData(filter, data, allMetaData));
    //     break;
    // }

    const totalLabels: number = labelList.reduce((accumulator, labelObj) => accumulator + labelObj.count, 0);
    data.labelCount = getFormattedItemCount(totalLabels);

    const {tags, metaData} = await this.formatMetaDataFieldAndTagSummary(metaDataFieldSummary, data, allMetaData);

    // return isTagList ? tags : metaData;
    if (isTagList) {
      return tags;
    } else {
      return this.formatMetaFieldsListWithSystemFields(metaData, teamId);
    }
  }

  /**
   * Format meta fields by including field type and options - handler
   * @param metaFields
   * @param teamId
   * @returns
   */
  async formatMetaFieldsListWithSystemFields(metaFields: DetailTabKeyVal[], teamId: string) {
    // let formattedMetaFields: DetailTabKeyValAllDataFormat[] = await Promise.all(metaFields.map(async metaField => this.formatOneMetaFieldWithSystemFields(metaField, teamId)));

    const metaFieldNameArray = metaFields.map(metaField => metaField.key);
    const systemFields = await this.metaFieldRepository.find({
      where: {teamId: teamId, fieldName: {inq: metaFieldNameArray}},
    });

    const formattedMetaFields = metaFields.map(metaField => {
      const systemFieldData = systemFields.find(systemField => systemField.fieldName == metaField.key);
      const formattedMetaField: DetailTabKeyValAllDataFormat = {
        key: metaField.key,
        value: metaField.value,
        fieldType: systemFieldData?.fieldType,
        options: systemFieldData?.options ?? [],
        fieldId: systemFieldData?._id,
      };
      return formattedMetaField;
    });

    return formattedMetaFields;
  }

  /**
   * Format one meta field by including field type and options
   * @param metaFields
   * @param teamId
   * @returns
   */
  async formatOneMetaFieldWithSystemFields(metaField: DetailTabKeyVal, teamId: string) {
    const systemFieldData = await this.metaFieldRepository.findOne({where: {fieldName: metaField.key, teamId: teamId}});
    const formattedMetaField: DetailTabKeyValAllDataFormat = {
      key: metaField.key,
      value: metaField.value,
      fieldType: systemFieldData?.fieldType,
      options: systemFieldData?.options ?? [],
    };
    return formattedMetaField;
  }

  /**
   * Use to get required tags and meta data when source is meta data
   * @param detailsSource {DetailSource} details source
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @param filter {DatalakeSelectionRequest} filter object
   * @param timeZone {number} time zone
   * @param allMetaData {boolean} whether only custom meta data is required
   * @param isTagList {boolean} whether only tags are required
   * @returns RequiredTagsAndMEtaDataINDynamicQueryRes
   */
  async requiredTagsAndMetaDataInMetaData(
    detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
      approachMethod: DetailSource;
    },
    data: ExplorerDetailTabMetaInfo,
    filter: DatalakeSelectionRequest,
    timeZone: number,
    allMetaData: boolean,
    isTagList: boolean,
  ): Promise<RequiredTagsAndMetaDataInDynamicQueryRes> {
    /**
     * If not only custom meta data, then get all the required info
     */
    if (allMetaData) {
      const {labelList, metaDataFieldSummary} = await this.requiredInfoForDetailInMetaData(
        detailsSource,
        data,
        filter,
        timeZone,
      );
      return {
        labelList: labelList,
        metaDataFieldSummary: metaDataFieldSummary,
      };
    }

    const collectionTypes: ContentType[] = [
      ContentType.IMAGE_COLLECTION,
      ContentType.VIDEO_COLLECTION,
      ContentType.OTHER_COLLECTION,
      ContentType.DATASET,
    ];

    const metaData = await this.metaDataRepository.findById(detailsSource.id);

    let metaDataFieldSummary: MetaFieldAndArraySummaryFormat[];

    const outsideCollection: boolean = !!(
      collectionTypes.includes(metaData.objectType) &&
      !filter.collectionId &&
      filter.objectIdList?.length == 1
    );

    if (
      outsideCollection ||
      detailsSource.approachMethod == DetailSource.DYNAMIC_QUERY ||
      !collectionTypes.includes(metaData.objectType)
    ) {
      const id: string = outsideCollection && filter?.objectIdList ? filter.objectIdList[0] : detailsSource.id;

      const dbQuery = {_id: new ObjectId(id)};
      const dbQueryObject = {
        matchQuery: dbQuery,
        objectType: metaData.objectType,
      };

      if (isTagList) {
        metaDataFieldSummary = await this.statsCalculationService.getMetaTagStatsForQuery(
          dbQueryObject.matchQuery,
          dbQueryObject.objectType,
        );
      } else {
        metaDataFieldSummary = await this.statsCalculationService.getMetaFieldStatsForQuery(
          dbQueryObject.matchQuery,
          dbQueryObject.objectType,
          filter.collectionId,
        );
      }
    } else {
      metaDataFieldSummary = metaData?.metaDataFieldSummary ?? [];
    }
    return {
      labelList: [],
      metaDataFieldSummary: metaDataFieldSummary,
    };
  }

  /**
   * Use to get required tags and meta data when source is system data
   * @param filter {DatalakeSelectionRequest} filter object
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @param allMetaData {boolean} whether only custom meta data is required
   * @returns RequiredTagsAndMEtaDataINDynamicQueryRes
   */
  async requiredTagsAndMetaDataInSystemData(
    filter: DatalakeSelectionRequest,
    data: ExplorerDetailTabMetaInfo,
    allMetaData: boolean,
  ): Promise<RequiredTagsAndMetaDataInDynamicQueryRes> {
    /**
     * If not only custom meta data, then get all the required info
     */
    if (allMetaData) {
      const {labelList, metaDataFieldSummary} = await this.requiredInfoForDetailInSystemData(filter, data);
      return {
        labelList: labelList,
        metaDataFieldSummary: metaDataFieldSummary,
      };
    }

    const contentType: ContentType = filter.contentType ?? ContentType.ALL;
    const systemData = await this.systemDataRepository.findOne({});
    let key: keyof ObjectTypeWiseCounts;
    switch (contentType) {
      case ContentType.IMAGE:
        key = 'images';
        break;
      case ContentType.IMAGE_COLLECTION:
        key = 'imageCollections';
        break;
      case ContentType.VIDEO:
        key = 'videos';
        break;
      case ContentType.VIDEO_COLLECTION:
        key = 'videoCollections';
        break;
      case ContentType.DATASET:
        key = 'datasets';
        break;
      case ContentType.OTHER:
        key = 'other';
        break;
      case ContentType.OTHER_COLLECTION:
        key = 'otherCollections';
        break;
      default:
        key = 'images';
        break;
    }

    const metaDataFieldSummary: MetaFieldAndArraySummaryFormat[] = systemData?.metaDataFieldSummary?.[key] ?? [];
    return {
      labelList: [],
      metaDataFieldSummary: metaDataFieldSummary,
    };
  }

  /**
   * Use to get required tags and meta data when source is dynamic query
   * @param teamId {string} team id
   * @param filter {DatalakeSelectionRequest} filter object
   * @param data {ExplorerDetailTabMetaInfo} meta data object
   * @param allMetaData {boolean} whether only custom meta data is required
   * @param isTagList {boolean} whether only tags are required
   * @returns RequiredTagsAndMEtaDataINDynamicQueryRes
   */
  async requiredTagsAndMetaDataInDynamicQuery(
    currentUserProfile: UserProfileDetailed,
    filter: DatalakeSelectionRequest,
    data: ExplorerDetailTabMetaInfo,
    allMetaData: boolean,
    isTagList: boolean,
  ): Promise<RequiredTagsAndMetaDataInDynamicQueryRes> {
    const teamId = currentUserProfile.teamId;
    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };
    if (!teamId) {
      logger.error(
        `get required tags and metadata in dynamic query | DatalakeExplorerService.requiredTagsAndMetaDataInDynamicQuery | N/A | teamId is undefined`,
      );
      throw new HttpErrors.UnprocessableEntity('Invalid team id');
    }
    /**
     * If not only custom meta data, then get all the required info
     */
    if (allMetaData) {
      const {labelList, metaDataFieldSummary} = await this.requiredInfoForDetailInDynamicQuery(
        currentUserProfile,
        filter,
        data,
      );
      return {
        labelList: labelList,
        metaDataFieldSummary: metaDataFieldSummary,
      };
    }

    const aggregateQuery = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );

    if (!aggregateQuery) throw new HttpErrors.UnprocessableEntity('Invalid selection query');

    const lookup: GetEmbeddingAggregationForSelectionRes[] = [];
    // if (filter?.embeddingSelection?.selection && filter?.embeddingSelection?.graphId) {
    //   lookup = await this.searchQueryBuilderService.getEmbeddingAggregationForSelection(filter.embeddingSelection);
    // }
    if (isTagList) {
      const TagList: MetaFieldAndArraySummaryFormat[] = await this.statsCalculationService.getMetaTagStatsForQuery(
        aggregateQuery.matchQuery,
        aggregateQuery.objectType,
        undefined,
        lookup,
      );

      return {
        labelList: [],
        metaDataFieldSummary: TagList,
      };
    }

    const metaDataList: MetaFieldAndArraySummaryFormat[] = await this.statsCalculationService.getMetaFieldStatsForQuery(
      aggregateQuery.matchQuery,
      aggregateQuery.objectType,
      filter.collectionId,
      undefined,
      lookup,
    );

    return {
      labelList: [],
      metaDataFieldSummary: metaDataList,
    };
  }

  /**
   * Use to get detailed counts of objects list for details tab in datalake explorer tab initial view
   * @param objectIdList {string[]} id list of selected objects
   * @returns ExplorerDefaultViewDetailsResponse
   */
  // async getDetailsToExplorer(
  //   objectIdList: string[],
  //   filter: ExplorerFilterV2,
  //   contentType: ContentType,
  //   teamId?: string,
  //   query?: any,
  // ) {
  //   logger.info(
  //     `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerService.getDetailsToExplorer | N/A | objectIdList=${objectIdList}`,
  //   );

  //   // initialize response
  //   let explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse = {
  //     totalSize: 0,
  //     objectStatus: OBJECT_STATUS.ACTIVE,
  //     contentType: contentType,
  //     details: {
  //       items: {
  //         totalCount: 0,
  //         details: {},
  //       },
  //       frames: {
  //         totalCount: 0,
  //         details: {
  //           raw: 0,
  //           machineAnnotated: 0,
  //           verified: 0,
  //         },
  //       },
  //       labels: {
  //         totalCount: 0,
  //         details: {
  //           labelList: [],
  //         },
  //       },
  //     },
  //   };

  //   if (objectIdList.length > 0) {
  //     //send details by calculating from meta data model

  //     let _objectIdList = objectIdList.map(e => new ObjectId(e));

  //     let distinctObjectTypes = await this.metaDataRepository.distinct('objectType', {_id: {$in: _objectIdList}});

  //     if (distinctObjectTypes.length == 1) {
  //       let contentType: ContentType = distinctObjectTypes[0];

  //       //calculation for images
  //       if (contentType == ContentType.IMAGE) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfImages(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.IMAGE_COLLECTION) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE_COLLECTION,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfImages(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE_COLLECTION,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.IMAGE_COLLECTION,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.VIDEO) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfVideos(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.VIDEO_COLLECTION) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO_COLLECTION,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfVideos(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO_COLLECTION,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.VIDEO_COLLECTION,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.DATASET) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.DATASET,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfImages(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.DATASET,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.DATASET,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.OTHER) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfImages(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER,
  //             teamId,
  //           ),
  //         ]);
  //       } else if (contentType == ContentType.OTHER_COLLECTION) {
  //         await Promise.all([
  //           this.getTotalItemCount(
  //             objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER_COLLECTION,
  //             teamId,
  //           ),
  //           this.getFrameCountsOfImages(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER_COLLECTION,
  //             teamId,
  //           ),
  //           this.getLabelCountsOfItems(
  //             _objectIdList,
  //             explorerDefaultViewDetailsResponse,
  //             ContentTypeFrontendConstants.OTHER_COLLECTION,
  //             teamId,
  //           ),
  //         ]);
  //       } else {
  //         logger.warn(
  //           `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerService.getDetailsToExplorer | N/A | Couldn't perform counting, ContentType = ${ContentType}`,
  //         );
  //         explorerDefaultViewDetailsResponse.totalSize = 'N/A';
  //         explorerDefaultViewDetailsResponse.details.items.totalCount = 'N/A';
  //         explorerDefaultViewDetailsResponse.details.frames.totalCount = 'N/A';
  //         explorerDefaultViewDetailsResponse.details.labels.totalCount = 'N/A';
  //       }
  //     } else {
  //       logger.warn(
  //         `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerService.getDetailsToExplorer | N/A | Couldn't perform counting, distinctObjectTypes = ${distinctObjectTypes}`,
  //       );
  //       explorerDefaultViewDetailsResponse.totalSize = 'N/A';
  //       explorerDefaultViewDetailsResponse.details.items.totalCount = 'N/A';
  //       explorerDefaultViewDetailsResponse.details.frames.totalCount = 'N/A';
  //       explorerDefaultViewDetailsResponse.details.labels.totalCount = 'N/A';
  //     }

  //     return explorerDefaultViewDetailsResponse;
  //   } else {
  //     //send details from system data model

  //     //let statsObject: Partial<SystemData> = {}
  //     let labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId);
  //     if (!filter.annotationTypes) filter.annotationTypes = [];

  //     if (
  //       Object.keys(query).length == 0 &&
  //       filter &&
  //       (contentType || contentType == 0) &&
  //       !filter.date &&
  //       filter.annotationTypes.length == 0
  //     ) {
  //       return this.getStatsFromSystemStats(
  //         filter,
  //         contentType,
  //         explorerDefaultViewDetailsResponse,
  //         labelToLabelTextMap,
  //         teamId,
  //       );
  //     } else {
  //       //filter match query create
  //       let filterMatchQuery: any = await this.searchQueryBuilderService.getMatchQueryForExplorerFilter(filter);

  //       //objectType match
  //       if (contentType) filterMatchQuery['objectType'] = contentType;

  //       //replace flag with enum
  //       let _matchItemAggregateQuery = {
  //         //replace flag with enum
  //         objectStatus: OBJECT_STATUS.ACTIVE,
  //         // isError: {$ne: true},
  //         // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
  //         // isMediaProcessingPending: {$ne: true},
  //         // $or: [
  //         //   {isAccessible: true},
  //         //   {objectType: {$nin: [ContentType.IMAGE, ContentType.VIDEO, ContentType.OTHER]}},
  //         // ],
  //         teamId: new ObjectId(teamId),
  //       };

  //       let matchItemAggregateQuery: {[k: string]: any} = {
  //         $and: [
  //           ...convertObjectToSinglePairObjectArray(filterMatchQuery),
  //           ...convertObjectToSinglePairObjectArray(query),
  //           ...convertObjectToSinglePairObjectArray(_matchItemAggregateQuery),
  //         ],
  //       };

  //       let matchFrameAndLabelAggregateQuery = _.cloneDeep(matchItemAggregateQuery);

  //       //logger.debug(JSON.stringify(matchItemAggregateQuery, null, 2));

  //       if (Number(contentType) == ContentType.DATASET) {
  //         let matchAggregateQuery = [{$match: matchItemAggregateQuery}, {$project: {_id: 1}}];
  //         let datasets: {_id: string}[] = await this.metaDataRepository.aggregate(matchAggregateQuery);
  //         let datasetsIdList = datasets.map(obj => new ObjectId(obj._id));
  //         matchFrameAndLabelAggregateQuery = {
  //           // 'datasetVersionList.datasetMetaId': {$in: datasetsIdList},
  //           vCollectionIdList: {$in: datasetsIdList},
  //           objectType: ContentType.IMAGE,
  //           //replace flag with enum
  //           objectStatus: OBJECT_STATUS.ACTIVE,
  //           // isError: {$ne: true},
  //           // objectStatus: {$ne: OBJECT_STATUS.TRASHED},
  //           teamId: new ObjectId(teamId),
  //         };
  //       }

  //       let promiseArray: Promise<void>[] = [
  //         this.getItemCountDetails(matchItemAggregateQuery, explorerDefaultViewDetailsResponse, Number(contentType)),
  //         this.getFrameCountDetails(
  //           matchFrameAndLabelAggregateQuery,
  //           explorerDefaultViewDetailsResponse,
  //           Number(contentType),
  //         ),
  //         this.getLabelCountDetails(
  //           matchFrameAndLabelAggregateQuery,
  //           explorerDefaultViewDetailsResponse,
  //           labelToLabelTextMap,
  //         ),
  //       ];
  //       await Promise.all(promiseArray);

  //       return explorerDefaultViewDetailsResponse;
  //     }
  //   }
  // }

  /**
   * Use to get get item count when query is present in details request
   * @param matchAggregateQuery matching query for aggregate
   * @param explorerDefaultViewDetailsResponse use for update the response counts
   */
  async getItemCountDetails(
    matchAggregateQuery: any,
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    objectType: ContentType,
  ) {
    const itemCountAggregateQuery = [
      {$match: matchAggregateQuery},
      {$group: {_id: '$objectType', count: {$sum: 1}, size: {$sum: '$fileSize'}}},
    ];

    const countList: {_id: number; count: number; size: number}[] =
      await this.metaDataRepository.aggregate(itemCountAggregateQuery);

    if (countList && countList.length > 0) {
      let totalCount = 0;
      let totalSize = 0;
      for (const countObj of countList) {
        totalCount += countObj.count;
        totalSize += countObj.size;
        switch (countObj._id) {
          case ContentType.VIDEO:
            explorerDefaultViewDetailsResponse.details.items.details.videos = countObj.count;
            break;
          case ContentType.VIDEO_COLLECTION:
            explorerDefaultViewDetailsResponse.details.items.details.videoCollections = countObj.count;
            break;
          case ContentType.IMAGE:
            explorerDefaultViewDetailsResponse.details.items.details.images = countObj.count;
            break;
          case ContentType.IMAGE_COLLECTION:
            explorerDefaultViewDetailsResponse.details.items.details.imageCollections = countObj.count;
            break;
          case ContentType.DATASET:
            explorerDefaultViewDetailsResponse.details.items.details.datasets = countObj.count;
            break;
          default:
            break;
        }
      }

      explorerDefaultViewDetailsResponse.details.items.totalCount = totalCount;
      if (objectType != ContentType.DATASET) {
        explorerDefaultViewDetailsResponse.totalSize = totalSize;
      }
    }
  }

  /**
   * Use to get get frame count when query is present in details request
   * @param matchAggregateQuery matching query for aggregate
   * @param explorerDefaultViewDetailsResponse use for update the response counts
   */
  async getFrameCountDetails(
    matchAggregateQuery: any,
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    objectType: ContentType,
  ) {
    const itemCountAggregateQuery = [
      {$match: matchAggregateQuery},
      {
        $group: {
          _id: null,
          rawSum: {$sum: '$verificationStatusCount.raw'},
          machineAnnotatedSum: {$sum: '$verificationStatusCount.machineAnnotated'},
          verifiedSum: {$sum: '$verificationStatusCount.verified'},
          frameCountSum: {$sum: '$frameCount'},
          size: {$sum: '$fileSize'},
        },
      },
    ];

    const countList: {
      rawSum: number;
      machineAnnotatedSum: number;
      verifiedSum: number;
      frameCountSum: number;
      size: number;
    }[] = await this.metaDataRepository.aggregate(itemCountAggregateQuery);

    if (countList && countList.length > 0) {
      explorerDefaultViewDetailsResponse.details.frames.totalCount = countList[0].frameCountSum || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
        countList[0].machineAnnotatedSum || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.verified = countList[0].verifiedSum || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.raw = countList[0].rawSum || 0;
      if (objectType == ContentType.DATASET) {
        explorerDefaultViewDetailsResponse.totalSize = countList[0].size || 0;
      }
    }
  }

  /**
   * Use to get get label count when query is present in details request
   * @param matchAggregateQuery matching query for aggregate
   * @param explorerDefaultViewDetailsResponse use for update the response counts
   * @param labelToLabelTextMap hashed labels for substitute
   */
  async getLabelCountDetails(
    matchAggregateQuery: any,
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    labelToLabelTextMap: {[k: string]: string},
  ) {
    const itemCountAggregateQuery = [
      {$match: matchAggregateQuery},
      {
        $unwind: '$labelList',
      },
      {
        $group: {
          _id: '$labelList.label',
          count: {$sum: '$labelList.count'},
        },
      },
    ];

    const countList: {
      _id: string;
      count: number;
    }[] = await this.metaDataRepository.aggregate(itemCountAggregateQuery);

    if (countList && countList.length > 0) {
      let totalCount = 0;
      const labelList = [];
      for (const label of countList) {
        totalCount += label.count;
        const labelCountObj = {
          label: label._id,
          labelText: labelToLabelTextMap[label._id],
          count: label.count,
        };
        labelList.push(labelCountObj);
      }

      labelList.sort((a, b) => b.count - a.count);
      explorerDefaultViewDetailsResponse.details.labels.details.labelList = labelList;
      explorerDefaultViewDetailsResponse.details.labels.totalCount = totalCount;
    }
  }

  /**
   * Use to update item count fields of ExplorerDefaultViewDetailsResponse for all meta data object types
   * @param objectIdList {string[]} id list of metadata objects
   * @param explorerDefaultViewDetailsResponse response object of this.getDetailsToExplorer function
   * @param ContentTypeFrontendConstant response object's field name according to meta object type
   * @returns none
   */
  async getTotalItemCount(
    objectIdList: string[],
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    ContentTypeFrontendConstant: ContentTypeFrontendConstants,
    teamId?: string,
  ) {
    const query = teamId ? {id: {inq: objectIdList}, teamId: new ObjectId(teamId)} : {id: {inq: objectIdList}};
    const count = await this.metaDataRepository.count(query);
    explorerDefaultViewDetailsResponse.details.items.totalCount = count.count;
    explorerDefaultViewDetailsResponse.details.items.details[ContentTypeFrontendConstant] = count.count;
  }

  /**
   * Use to update frame count field of ExplorerDefaultViewDetailsResponse for images, image collection and datasets
   * @param objectIdList {string[]} id list of metadata objects
   * @param explorerDefaultViewDetailsResponse response object of this.getDetailsToExplorer function
   * @param ContentTypeFrontendConstant response object's field name according to meta object type
   * @returns none
   */
  async getFrameCountsOfImages(
    objectIdList: ObjectId[],
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    ContentTypeFrontendConstant: ContentTypeFrontendConstants,
    teamId?: string,
  ) {
    const query = teamId ? {id: {inq: objectIdList}, teamId: teamId} : {id: {inq: objectIdList}};

    // frame count calculation
    let pipeline: any[] = [];
    switch (ContentTypeFrontendConstant) {
      case ContentTypeFrontendConstants.IMAGE:
        const queryImage = teamId
          ? [{$match: {_id: {$in: objectIdList}, teamId: new ObjectId(teamId)}}]
          : [{$match: {_id: {$in: objectIdList}}}];
        pipeline = queryImage;
        break;
      case ContentTypeFrontendConstants.IMAGE_COLLECTION:
      case ContentTypeFrontendConstants.DATASET:
        const queryDataset = teamId
          ? [
              {
                $match: {
                  parentList: {$in: objectIdList},
                  objectType: ContentType.IMAGE,
                  teamId: new ObjectId(teamId),
                },
              },
            ]
          : [{$match: {parentList: {$in: objectIdList}, objectType: ContentType.IMAGE}}];
        pipeline = queryDataset;
        break;
      default:
        return;
    }

    pipeline = [
      ...pipeline,
      {
        $group: {
          _id: null,
          rawSum: {$sum: '$verificationStatusCount.raw'},
          machineAnnotatedSum: {$sum: '$verificationStatusCount.machineAnnotated'},
          verifiedSum: {$sum: '$verificationStatusCount.verified'},
          frameCountSum: {$sum: '$frameCount'},
          size: {$sum: '$fileSize'},
        },
      },
    ];

    const countList: {
      _id: null;
      rawSum: number;
      machineAnnotatedSum: number;
      verifiedSum: number;
      frameCountSum: number;
      size: number;
    }[] = await this.metaDataRepository.aggregate(pipeline);

    //assign values to response object
    if (countList && countList.length > 0) {
      explorerDefaultViewDetailsResponse.details.frames.totalCount = countList[0].frameCountSum || 0;
      explorerDefaultViewDetailsResponse.totalSize = countList[0].size || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
        countList[0].machineAnnotatedSum || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.verified = countList[0].verifiedSum || 0;
      explorerDefaultViewDetailsResponse.details.frames.details.raw = countList[0].rawSum || 0;
    }
  }

  /**
   * Use to update frame count field of ExplorerDefaultViewDetailsResponse for videos and video collection
   * @param objectIdList {string[]} id list of metadata objects
   * @param explorerDefaultViewDetailsResponse response object of this.getDetailsToExplorer function
   * @param ContentTypeFrontendConstant response object's field name according to meta object type
   * @returns none
   */
  async getFrameCountsOfVideos(
    objectIdList: ObjectId[],
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    ContentTypeFrontendConstant: ContentTypeFrontendConstants,
    teamId?: string,
  ) {
    // frame count calculation
    let pipeline: any[] = [];
    switch (ContentTypeFrontendConstant) {
      case ContentTypeFrontendConstants.VIDEO:
        const queryVideo = teamId
          ? [{$match: {_id: {$in: objectIdList}, teamId: new ObjectId(teamId)}}]
          : [{$match: {_id: {$in: objectIdList}}}];
        pipeline = queryVideo;
        break;
      case ContentTypeFrontendConstants.VIDEO_COLLECTION:
        const queryVideoColec = teamId
          ? [
              {
                $match: {
                  parentList: {$in: objectIdList},
                  objectType: ContentType.VIDEO,
                  teamId: new ObjectId(teamId),
                },
              },
            ]
          : [{$match: {parentList: {$in: objectIdList}, objectType: ContentType.VIDEO}}];
        pipeline = queryVideoColec;
        break;
      default:
        return;
    }

    pipeline = [
      ...pipeline,
      {
        $group: {
          _id: null,
          rawSum: {$sum: '$verificationStatusCount.raw'},
          machineAnnotatedSum: {$sum: '$verificationStatusCount.machineAnnotated'},
          verifiedSum: {$sum: '$verificationStatusCount.verified'},
          fileSizeSum: {$sum: '$fileSize'},
          frameCountSum: {$sum: '$frameCount'},
        },
      },
    ];
    const counts: {
      _id: null;
      rawSum: number;
      machineAnnotatedSum: number;
      verifiedSum: number;
      fileSizeSum: number;
      frameCountSum: number;
    }[] = await this.metaDataRepository.aggregate(pipeline);

    //assign values to response object
    if (counts.length > 0) {
      explorerDefaultViewDetailsResponse.details.frames.details.raw = counts[0].rawSum;
      explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated = counts[0].machineAnnotatedSum;
      explorerDefaultViewDetailsResponse.details.frames.details.verified = counts[0].verifiedSum;
      explorerDefaultViewDetailsResponse.totalSize = counts[0].fileSizeSum;
      explorerDefaultViewDetailsResponse.details.frames.totalCount = counts[0].frameCountSum;
    }
  }

  /**
   * Use to update label count field of ExplorerDefaultViewDetailsResponse for all type of meta objects
   * @param objectIdList {string[]} id list of metadata objects
   * @param explorerDefaultViewDetailsResponse response object of this.getDetailsToExplorer function
   * @param ContentTypeFrontendConstant response object's field name according to meta object type
   * @returns none
   */
  async getLabelCountsOfItems(
    objectIdList: ObjectId[],
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    ContentTypeFrontendConstant: ContentTypeFrontendConstants,
    teamId?: string,
  ) {
    // label count calculation
    let pipeline: any[] = [];
    switch (ContentTypeFrontendConstant) {
      case ContentTypeFrontendConstants.IMAGE:
      case ContentTypeFrontendConstants.VIDEO:
        const queryVideo = teamId
          ? [{$match: {_id: {$in: objectIdList}, teamId: new ObjectId(teamId)}}]
          : [{$match: {_id: {$in: objectIdList}}}];
        pipeline = queryVideo;
        break;
      case ContentTypeFrontendConstants.IMAGE_COLLECTION:
      case ContentTypeFrontendConstants.DATASET:
        const queryDataset = teamId
          ? [
              {
                $match: {
                  parentList: {$in: objectIdList},
                  objectType: ContentType.IMAGE,
                  teamId: new ObjectId(teamId),
                },
              },
            ]
          : [{$match: {parentList: {$in: objectIdList}, objectType: ContentType.IMAGE}}];
        pipeline = queryDataset;
        break;
      case ContentTypeFrontendConstants.VIDEO_COLLECTION:
        const queryVideoColec = teamId
          ? [
              {
                $match: {
                  parentList: {$in: objectIdList},
                  objectType: ContentType.VIDEO,
                  teamId: new ObjectId(teamId),
                },
              },
            ]
          : [{$match: {parentList: {$in: objectIdList}, objectType: ContentType.VIDEO}}];
        pipeline = queryVideoColec;
        break;
      default:
        return;
    }

    pipeline = [
      ...pipeline,
      {
        $unwind: '$labelList',
      },
      {
        $group: {
          _id: '$labelList.label',
          count: {$sum: '$labelList.count'},
        },
      },
    ];
    const counts: {_id: string; count: number}[] = await this.metaDataRepository.aggregate(pipeline);

    const labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId); //NOTE: add teamId filter

    //assign values to response object
    let totalLabelCount = 0;
    for (const label of counts) {
      totalLabelCount = totalLabelCount + label.count;
      const labelObj: LabelCountInfo = {
        label: label._id,
        labelText: labelToLabelTextMap[label._id] ? labelToLabelTextMap[label._id] : label._id,
        count: label.count,
      };
      explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
    }
    explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
  }

  /**
   * Use to get a list of virtual collection
   */
  async getVirtualCollectionList(objectType: ContentType, currentUserProfile: UserProfileDetailed) {
    const teamId = currentUserProfile.teamId;
    const matchObject: any = {
      teamId: teamId,
      objectType: objectType,
    };

    if (currentUserProfile?.userType && currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
      matchObject['allowedUserIdList'] = currentUserProfile.id;
    }

    const vCollectionList = await this.metaDataRepository.find({
      where: matchObject,
      order: ['name ASC'],
      fields: {
        id: true,
        name: true,
      },
    });

    return vCollectionList;
  }

  /**
   * Use to get collectionId by using collection name and object type
   * @param name {string} collection name
   * @param objectType {ContentType}
   * @returns CollectionObject
   */
  async getCollectionIdByName(name: string, objectType: ContentType, currentUserProfile: UserProfileDetailed) {
    const collectionObj = await this.metaDataRepository.findOne({
      where: {
        name: name,
        objectType: objectType,
      },
      fields: {
        id: true,
      },
    });

    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      const allowedUserIdList = collectionObj?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    return collectionObj;
  }

  /**
   * Use to get collectionName by using collection id
   * @param id {string} collection id
   * @returns CollectionNameObject
   */
  async getNameById(id: string) {
    const collectionNameObject = await this.metaDataRepository.findById(id, {
      fields: {
        name: true,
      },
    });

    if (!collectionNameObject.name) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.NAME_NOT_EXIST);
    }

    return collectionNameObject;
  }

  /**
   * Use to retrieve information of a object in datalake
   * @param id id of the object
   */
  async getObjectInfo(id: string) {
    try {
      const obj = await this.metaDataRepository.findById(id);

      return {
        name: obj.name,
        url: obj.url,
      };
    } catch (err) {
      throw new HttpErrors.NotFound(`Object with id ${id} not found`);
    }
  }

  async getStatsFromSystemStats(
    filter: ExplorerFilterV2,
    contentType: ContentType,
    explorerDefaultViewDetailsResponse: ExplorerDefaultViewDetailsResponse,
    labelToLabelTextMap: {
      [k: string]: string;
    },
    teamId?: string,
  ) {
    const query = teamId ? {where: {teamId: teamId}} : {};
    const statsObject = await this.systemDataRepository.findOne(query); //NOTE: add teamId filter
    switch (Number(contentType)) {
      case ContentType.IMAGE:
        //assign frame counts
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.totalCount =
            statsObject.objectTypeWiseCounts?.images?.count || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.raw =
            statsObject.objectTypeWiseCounts?.images?.raw || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
            statsObject.objectTypeWiseCounts?.images?.machineAnnotated || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.verified =
            statsObject.objectTypeWiseCounts?.images?.verified || 0;
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.images?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.images?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.images =
            statsObject.objectTypeWiseCounts?.images?.count || 0;
        }

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.images.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.images.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }
        return explorerDefaultViewDetailsResponse;
      case ContentType.VIDEO:
        //assign frame counts
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.totalCount =
            statsObject.objectTypeWiseCounts?.videos?.frames || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.raw =
            statsObject.objectTypeWiseCounts?.videos?.raw || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
            statsObject.objectTypeWiseCounts?.videos?.machineAnnotated || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.verified =
            statsObject.objectTypeWiseCounts?.videos?.verified || 0;
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.videos?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.videos?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.videos =
            statsObject.objectTypeWiseCounts?.videos?.count || 0;
        }

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.videos.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.videos.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }
        return explorerDefaultViewDetailsResponse;
      case ContentType.IMAGE_COLLECTION:
        //assign frame counts
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.totalCount =
            statsObject.objectTypeWiseCounts?.imageCollections?.frames || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.raw =
            statsObject.objectTypeWiseCounts?.imageCollections?.raw || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
            statsObject.objectTypeWiseCounts?.imageCollections?.machineAnnotated || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.verified =
            statsObject.objectTypeWiseCounts?.imageCollections?.verified || 0;
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.imageCollections?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.imageCollections?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.imageCollections =
            statsObject.objectTypeWiseCounts?.imageCollections?.count || 0;
        }

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.imageCollections.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.imageCollections.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }
        return explorerDefaultViewDetailsResponse;
      case ContentType.VIDEO_COLLECTION:
        //assign frame counts
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.totalCount =
            statsObject.objectTypeWiseCounts?.videoCollections?.frames || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.raw =
            statsObject.objectTypeWiseCounts?.videoCollections?.raw || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
            statsObject.objectTypeWiseCounts?.videoCollections?.machineAnnotated || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.verified =
            statsObject.objectTypeWiseCounts?.videoCollections?.verified || 0;
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.videoCollections?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.videoCollections?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.videoCollections =
            statsObject.objectTypeWiseCounts?.videoCollections?.count || 0;
        }

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.videoCollections.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.videoCollections.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }
        return explorerDefaultViewDetailsResponse;
      case ContentType.DATASET:
        //assign frame counts
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.totalCount =
            statsObject.objectTypeWiseCounts?.datasets?.frames || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.raw =
            statsObject.objectTypeWiseCounts?.datasets?.raw || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated =
            statsObject.objectTypeWiseCounts?.datasets?.machineAnnotated || 0;
          explorerDefaultViewDetailsResponse.details.frames.details.verified =
            statsObject.objectTypeWiseCounts?.datasets?.verified || 0;
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.datasets?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.datasets?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.datasets =
            statsObject.objectTypeWiseCounts?.datasets?.count || 0;
        }

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.datasets.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.datasets.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }
        return explorerDefaultViewDetailsResponse;

      case ContentType.OTHER:
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.details = {};
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.other?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.other?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.others =
            statsObject.objectTypeWiseCounts?.other?.count || 0;
        }
        return explorerDefaultViewDetailsResponse;

      case ContentType.OTHER_COLLECTION:
        if (statsObject) {
          explorerDefaultViewDetailsResponse.details.frames.details = {};
          explorerDefaultViewDetailsResponse.totalSize = statsObject.objectTypeWiseCounts?.otherCollections?.size || 0;
          explorerDefaultViewDetailsResponse.details.items.totalCount =
            statsObject.objectTypeWiseCounts?.otherCollections?.count || 0;
          explorerDefaultViewDetailsResponse.details.items.details.otherCollections =
            statsObject.objectTypeWiseCounts?.otherCollections?.count || 0;
        }
        return explorerDefaultViewDetailsResponse;

      default:
        //assign item counts
        let totalCount = 0;
        if (statsObject?.objectTypeWiseCounts?.videos.count) {
          if (statsObject?.objectTypeWiseCounts?.videos.count > 0) {
            explorerDefaultViewDetailsResponse.details.items.details.videos =
              statsObject.objectTypeWiseCounts?.videos?.count || 0;
            totalCount += statsObject.objectTypeWiseCounts?.videos?.count || 0;
          }
        }
        if (statsObject?.objectTypeWiseCounts?.videoCollections.count) {
          if (statsObject?.objectTypeWiseCounts?.videoCollections.count > 0) {
            explorerDefaultViewDetailsResponse.details.items.details.videoCollections =
              statsObject.objectTypeWiseCounts?.videoCollections?.count || 0;
            totalCount += statsObject.objectTypeWiseCounts?.videoCollections?.count || 0;
          }
        }
        if (statsObject?.objectTypeWiseCounts?.images.count) {
          if (statsObject?.objectTypeWiseCounts?.images.count > 0) {
            explorerDefaultViewDetailsResponse.details.items.details.images =
              statsObject.objectTypeWiseCounts?.images?.count || 0;
            totalCount += statsObject.objectTypeWiseCounts?.images?.count || 0;
          }
        }
        if (statsObject?.objectTypeWiseCounts?.imageCollections.count) {
          if (statsObject?.objectTypeWiseCounts?.imageCollections.count > 0) {
            explorerDefaultViewDetailsResponse.details.items.details.imageCollections =
              statsObject.objectTypeWiseCounts?.imageCollections?.count || 0;
            totalCount += statsObject.objectTypeWiseCounts?.imageCollections?.count || 0;
          }
        }
        if (statsObject?.objectTypeWiseCounts?.datasets.count) {
          if (statsObject?.objectTypeWiseCounts?.datasets.count > 0) {
            explorerDefaultViewDetailsResponse.details.items.details.datasets =
              statsObject.objectTypeWiseCounts?.datasets?.count || 0;
            totalCount += statsObject.objectTypeWiseCounts?.datasets?.count || 0;
          }
        }
        explorerDefaultViewDetailsResponse.details.items.totalCount = totalCount;
        //assign frame counts
        const totalFrameCount =
          (statsObject?.objectTypeWiseCounts?.images.frames || 0) +
          (statsObject?.objectTypeWiseCounts?.videos.frames || 0);
        const totalRawFrameCount =
          (statsObject?.objectTypeWiseCounts?.images.raw || 0) + (statsObject?.objectTypeWiseCounts?.videos.raw || 0);
        const totalMachineAnnotatedFrameCount =
          (statsObject?.objectTypeWiseCounts?.images.machineAnnotated || 0) +
          (statsObject?.objectTypeWiseCounts?.videos.machineAnnotated || 0);
        const totalVerifiedFrameCount =
          (statsObject?.objectTypeWiseCounts?.images.verified || 0) +
          (statsObject?.objectTypeWiseCounts?.videos.verified || 0);
        explorerDefaultViewDetailsResponse.details.frames.totalCount = totalFrameCount;
        explorerDefaultViewDetailsResponse.details.frames.details.raw = totalRawFrameCount;
        explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated = totalMachineAnnotatedFrameCount;
        explorerDefaultViewDetailsResponse.details.frames.details.verified = totalVerifiedFrameCount;

        //assign label counts
        if (statsObject) {
          if (statsObject.objectTypeWiseCounts?.images.labelList) {
            let totalLabelCount = 0;
            for (const [key, value] of Object.entries(statsObject.objectTypeWiseCounts?.images.labelList)) {
              totalLabelCount = totalLabelCount + value;
              const labelObj: LabelCountInfo = {
                label: key,
                labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
                count: value,
              };
              explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj);
            }
            explorerDefaultViewDetailsResponse.details.labels.details.labelList.sort((a, b) => b.count - a.count);
            explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount;
          }
        }

        //assign total data size
        explorerDefaultViewDetailsResponse.totalSize =
          (statsObject?.objectTypeWiseCounts?.images.size || 0) +
          (statsObject?.objectTypeWiseCounts?.videos.size || 0) +
          (statsObject?.objectTypeWiseCounts?.other.size || 0);

        return explorerDefaultViewDetailsResponse;
    }
    // //assign item counts
    // explorerDefaultViewDetailsResponse.details.items.totalCount = statsObject?.objectCounts?.total || 0
    // if (statsObject?.objectCounts?.videos.count) if (statsObject?.objectCounts?.videos.count > 0) explorerDefaultViewDetailsResponse.details.items.details.videos = statsObject?.objectCounts?.videos.count
    // if (statsObject?.objectCounts?.videoCollections.count) if (statsObject?.objectCounts?.videoCollections.count > 0) explorerDefaultViewDetailsResponse.details.items.details.videoCollections = statsObject?.objectCounts?.videoCollections.count
    // if (statsObject?.objectCounts?.images.count) if (statsObject?.objectCounts?.images.count > 0) explorerDefaultViewDetailsResponse.details.items.details.images = statsObject?.objectCounts?.images.count
    // if (statsObject?.objectCounts?.imageCollections.count) if (statsObject?.objectCounts?.imageCollections.count > 0) explorerDefaultViewDetailsResponse.details.items.details.imageCollections = statsObject?.objectCounts?.imageCollections.count
    // if (statsObject?.objectCounts?.datasets.count) if (statsObject?.objectCounts?.datasets.count > 0) explorerDefaultViewDetailsResponse.details.items.details.datasets = statsObject?.objectCounts?.datasets.count

    // //assign frame counts
    // explorerDefaultViewDetailsResponse.details.frames.totalCount = statsObject?.frameCounts?.total || 0
    // explorerDefaultViewDetailsResponse.details.frames.details.raw = statsObject?.frameCounts?.raw
    // explorerDefaultViewDetailsResponse.details.frames.details.machineAnnotated = statsObject?.frameCounts?.machineAnnotated
    // explorerDefaultViewDetailsResponse.details.frames.details.verified = statsObject?.frameCounts?.verified

    // //assign label counts
    // if (statsObject) {
    //   if (statsObject.labelCounts) {
    //     let totalLabelCount = 0
    //     for (const [key, value] of Object.entries(statsObject.labelCounts)) {
    //       totalLabelCount = totalLabelCount + value
    //       let labelObj: LabelCountInfo = {
    //         label: key,
    //         labelText: labelToLabelTextMap[key] ? labelToLabelTextMap[key] : key,
    //         count: value
    //       }
    //       explorerDefaultViewDetailsResponse.details.labels.details.labelList.push(labelObj)
    //     }
    //     explorerDefaultViewDetailsResponse.details.labels.totalCount = totalLabelCount
    //   }
    // }

    // //assign total data size
    // explorerDefaultViewDetailsResponse.totalSize = statsObject?.totalDataSize || 0

    // return explorerDefaultViewDetailsResponse
  }

  /**
   * Use to get filter options for filter dialog
   * @param filterObj {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @returns LabelMetadataSelectionOptionsObject
   */
  async getFilterOptions(filterObj: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    let returnObject: LabelMetadataSelectionOptionsObject = {
      labelOptions: [],
      metadataOptions: [],
    };

    returnObject = await Promise.all([
      this.getLabelOptionsList(currentUserProfile, filterObj.collectionId, filterObj.contentType),
      this.getMetadataOptionsList(currentUserProfile, filterObj.collectionId, filterObj.contentType),
    ]).then(([labelOptionsList, metadataOptionsList]) => {
      return {
        labelOptions: labelOptionsList,
        metadataOptions: metadataOptionsList,
      };
    });

    return returnObject;
  }

  /**
   * Use to get filter options for filter meta analytics
   * @param filterObj {DatalakeSelectionRequest} filter object
   * @param teamId {string} team id
   * @returns LabelMetadataSelectionOptionsObject
   */
  async getMetaAnalyticsChartFilterOptions(
    filterObj: DatalakeSelectionRequest,
    currentUserProfile: UserProfileDetailed,
  ) {
    const metaDataArray: MetaFieldAndArraySummaryFormat[] =
      (await this.statsCalculationService.getMetaFieldsStats(filterObj, currentUserProfile)) || [];

    // format metadata options
    const metadataOptionsList: SelectionOptionsObject[] = [];

    for (const metaObj of metaDataArray) {
      let _tempMetaObj: MetaFieldAndArraySummaryFormat | {_id: string; values?: any[]} = metaObj;
      if (metaObj._id == 'Tags') {
        _tempMetaObj = {
          _id: metaObj.values[0].value,
        };
      }
      const metaOption: SelectionOptionsObject = {
        name: _tempMetaObj._id,
        selected: true,
        filterOptions: _tempMetaObj.values?.map(valueObj => {
          return {
            name: valueObj.value,
            selected: true,
          };
        }),
      };
      // if(_tempMetaObj.values && _tempMetaObj.values.length > 0){
      //   metaOption.filterOptions = _tempMetaObj.values.map(valueObj => {
      //     return {
      //       name: valueObj.value,
      //       selected: false,
      //     };
      //   })
      // }
      metadataOptionsList.push(metaOption);
    }

    const returnObject: Partial<LabelMetadataSelectionOptionsObject> = {
      // labelOptions: [],
      metadataOptions: metadataOptionsList,
    };

    return returnObject;
  }

  /**
   * Use to get label options list for filter dialog
   * @param teamId {string}
   * @param collectionId collection id
   * @param contentType content type
   * @returns list of label options
   */
  async getLabelOptionsList(currentUserProfile: UserProfileDetailed, collectionId?: string, contentType?: ContentType) {
    const startTime = new Date().getTime();
    let labelOptionsList: SelectionOptionsObject[] = [];
    const teamId = currentUserProfile.teamId;
    if (!teamId) {
      logger.error(
        `Getting label list for filter | DatalakeExplorerService.getLabelOptionsList | ${teamId} | Team id required`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    // if collection id is provided then get label list from collection head
    // else get label list from system label list according to content type
    if (collectionId) {
      logger.info(
        `Getting label list for filter | DatalakeExplorerService.getLabelOptionsList | ${teamId} | Collection id = ${collectionId}`,
      );
      const labelListObj = await this.metaDataRepository.findOne({
        where: {
          _id: new ObjectId(collectionId),
          teamId: teamId,
        },
        fields: {
          labelList: true,
        },
      });

      if (labelListObj && labelListObj.labelList && labelListObj.labelList.length > 0) {
        for (const labelObj of labelListObj.labelList) {
          const labelToLabelTextMap = await this.systemLabelService.getLabelToLabelTextMapOfTeam(teamId);

          const labelOption: SelectionOptionsObject = {
            name: labelToLabelTextMap[labelObj.label] ? labelToLabelTextMap[labelObj.label] : labelObj.label,
            selected: false,
          };
          labelOptionsList.push(labelOption);
        }
      }
    } else if (contentType) {
      if (currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
        const filterObj: DatalakeSelectionRequest = {
          isAllSelected: true,
          objectIdList: [],
          contentType: contentType,
          sortBy: {sortByField: 1, sortOrder: SortOrder.DESC},
          query: '',
          filterData: {},
          objectStatus: 2,
          embeddingSelection: {graphId: '', selectionType: 0, x: [], y: []},
        };

        const frameLevelStatDistribution = await this.statsCalculationService.getFrameLevelStats(
          filterObj,
          currentUserProfile,
        );

        const labelData = frameLevelStatDistribution.labelStats.labelList || [];

        for (const labelObj of labelData) {
          const labelOption: SelectionOptionsObject = {
            name: labelObj.label,
            selected: false,
          };
          labelOptionsList.push(labelOption);
        }
      } else {
        // get label list from systemData according to content type
        labelOptionsList = await this.systemLabelService.getSystemLabelOptionsList(teamId, contentType);
      }
    } else {
      logger.error(
        `Getting label list for filter | DatalakeExplorerService.getLabelOptionsList | ${teamId} | Collection id or content type required`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_ID_OR_CONTENT_TYPE_REQUIRED);
    }
    const endTime = new Date().getTime();
    logger.info(
      `Getting label list for filter | DatalakeExplorerService.getLabelOptionsList | ${teamId} | Total response time for get label : ${
        endTime - startTime
      }ms`,
    );
    return labelOptionsList;
  }

  /**
   * Use to get metadata options list for filter dialog
   * @param teamId {string}
   * @param collectionId collection id
   * @param contentType content type
   * @returns list of metadata options
   */
  async getMetadataOptionsList(
    currentUserProfile: UserProfileDetailed,
    collectionId?: string,
    contentType?: ContentType,
  ) {
    let metadataOptionsList: SelectionOptionsObject[] = [];
    const startTime = new Date().getTime();
    const teamId = currentUserProfile.teamId;
    if (!teamId) {
      logger.error(
        `Getting metadata list for filter | DatalakeExplorerService.getMetadataOptionsList | ${teamId} | Team id required`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    // if collection id is provided then get metadata list from collection head
    // else get metadata list from system metadata list according to content type
    if (collectionId) {
      const metaOptionObject = await this.metaDataRepository.findOne({
        where: {
          _id: new ObjectId(collectionId),
          teamId: teamId,
        },
        fields: {
          metaDataFieldSummary: true,
        },
      });

      if (
        metaOptionObject &&
        metaOptionObject.metaDataFieldSummary &&
        metaOptionObject.metaDataFieldSummary.length > 0
      ) {
        for (const metaObj of metaOptionObject.metaDataFieldSummary) {
          if (metaObj._id != 'Tags') {
            const metaOption: SelectionOptionsObject = {
              name: metaObj._id,
              selected: false,
              filterOptions: metaObj.values.map(valueObj => {
                return {
                  name: valueObj.value,
                  selected: false,
                };
              }),
            };
            metadataOptionsList.push(metaOption);
          } else {
            const metaOption: SelectionOptionsObject = {
              name: metaObj.values[0].value,
              selected: false,
            };
            metadataOptionsList.push(metaOption);
          }
        }
      }
    } else if (contentType) {
      if (currentUserProfile.userType == UserType.USER_TYPE_COLLABORATOR) {
        const filterObj: DatalakeSelectionRequest = {
          isAllSelected: true,
          objectIdList: [],
          contentType: contentType,
          sortBy: {sortByField: 1, sortOrder: SortOrder.DESC},
          query: '',
          filterData: {},
          objectStatus: 2,
          embeddingSelection: {graphId: '', selectionType: 0, x: [], y: []},
        };

        const returnObject = await this.getMetaAnalyticsChartFilterOptions(filterObj, currentUserProfile);
        metadataOptionsList = returnObject.metadataOptions || [];
      } else {
        // get metadata list from systemData according to content type
        metadataOptionsList = await this.systemMetaService.getSystemMetadataOptionsList(teamId, contentType);
      }
    } else {
      logger.error(
        `Getting metadata list for filter | DatalakeExplorerService.getMetadataOptionsList | ${teamId} | Collection id or content type required`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.COLLECTION_ID_OR_CONTENT_TYPE_REQUIRED);
    }
    const endTime = new Date().getTime();
    logger.info(
      `Getting metadata list for filter | DatalakeExplorerService.getMetadataOptionsList | ${teamId} | Total response time for get metadata : ${
        endTime - startTime
      }ms`,
    );
    return metadataOptionsList;
  }

  /**
   * Use to get available sort options for content type or inside a collection
   * @param filterObj DatalakeSelectionRequest
   * @param teamId team of the id
   * @returns list of available filter options
   */
  async getSortOptions(filterObj: DatalakeSelectionRequest, teamId: string) {
    if (filterObj.collectionId) {
      const isVideoFrameExist = await this.metaDataRepository.aggregate([
        {
          $match: {
            vCollectionIdList: new ObjectId(filterObj.collectionId),
            objectStatus: OBJECT_STATUS.ACTIVE,
            videoFrameIndex: {$gte: 0},
            teamId: new ObjectId(teamId),
          },
        },
        {
          $limit: 1,
        },
      ]);

      if (Array.isArray(isVideoFrameExist) && isVideoFrameExist.length > 0) {
        return [
          ExploreSortBy.DATE_MODIFIED,
          ExploreSortBy.DATE_CREATED,
          ExploreSortBy.NAME,
          ExploreSortBy.SIZE,
          ExploreSortBy.VIDEO_INDEX,
        ];
      } else {
        return [ExploreSortBy.DATE_MODIFIED, ExploreSortBy.DATE_CREATED, ExploreSortBy.NAME, ExploreSortBy.SIZE];
      }
    } else {
      if (filterObj.contentType) {
        if (filterObj.contentType == ContentType.IMAGE) {
          const isVideoFrameExist = await this.metaDataRepository.aggregate([
            {
              $match: {
                objectType: ContentType.IMAGE,
                objectStatus: OBJECT_STATUS.ACTIVE,
                videoFrameIndex: {$gte: 0},
                teamId: new ObjectId(teamId),
              },
            },
            {
              $limit: 1,
            },
          ]);

          if (Array.isArray(isVideoFrameExist) && isVideoFrameExist.length > 0) {
            return [
              ExploreSortBy.DATE_MODIFIED,
              ExploreSortBy.DATE_CREATED,
              ExploreSortBy.NAME,
              ExploreSortBy.SIZE,
              ExploreSortBy.VIDEO_INDEX,
            ];
          } else {
            return [ExploreSortBy.DATE_MODIFIED, ExploreSortBy.DATE_CREATED, ExploreSortBy.NAME, ExploreSortBy.SIZE];
          }
        } else {
          return [ExploreSortBy.DATE_MODIFIED, ExploreSortBy.DATE_CREATED, ExploreSortBy.NAME, ExploreSortBy.SIZE];
        }
      } else {
        return [ExploreSortBy.DATE_MODIFIED, ExploreSortBy.DATE_CREATED, ExploreSortBy.NAME, ExploreSortBy.SIZE];
      }
    }
  }

  /**
   * Use to get precalculated graph id from metadata or system data
   * @param filter datalake selection request filter
   * @param teamId team id
   * @returns graph id if precalculated graph is available
   */
  async getPreCalculatedGraphId(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    const teamId = currentUserProfile.teamId;
    const detailsSource: {
      sourceType: DetailSource;
      id: string;
      contentType: ContentType;
      approachMethod: DetailSource;
    } = await this.statsCalculationService.getDetailsSource(filter, currentUserProfile);

    if (
      detailsSource &&
      detailsSource.sourceType != DetailSource.DYNAMIC_QUERY &&
      [ContentType.DATASET, ContentType.IMAGE, ContentType.IMAGE_COLLECTION].includes(detailsSource.contentType)
    ) {
      if (detailsSource.sourceType == DetailSource.METADATA) {
        if ((detailsSource.contentType == ContentType.IMAGE && filter.isAllSelected == false) || !detailsSource.id) {
          return undefined;
        }
        const metaData = await this.metaDataRepository.findById(detailsSource.id);
        if (metaData) {
          if ([ContentType.DATASET, ContentType.IMAGE_COLLECTION].includes(metaData.objectType)) {
            if (metaData.isFeatureGraphPending) {
              try {
                const graphId = await this.searchQueryBuilderService.insertGraphCoordinatesForOneCollection(
                  metaData,
                  currentUserProfile,
                );
                if (graphId) {
                  this.searchQueryBuilderService.insertSystemGraphCoordinates(
                    metaData.objectType == ContentType.DATASET ? true : false,
                  );
                  return graphId;
                } else {
                  return undefined;
                }
              } catch (e) {
                logger.error(
                  `Error in inserting graph coordinates for collection ${metaData.name} | DatalakeExplorerService.getPreCalculatedGraphId | ${teamId} | ${e}`,
                );
                return undefined;
              }
            } else {
              if (metaData.graphId) {
                return metaData.graphId;
              } else {
                return undefined;
              }
            }
          } else {
            return undefined;
          }
        } else {
          logger.warn(
            `Getting precalculate graphData | DatalakeExplorerService.getPreCalculatedGraphId | ${teamId} |No metadata found for id ${detailsSource.id}`,
          );
          return undefined;
        }
      } else if (detailsSource.sourceType == DetailSource.SYSTEM_DATA) {
        const systemData = await this.systemDataRepository.findOne({where: {teamId: teamId}});
        if (systemData) {
          if ([ContentType.IMAGE, ContentType.IMAGE_COLLECTION].includes(detailsSource.contentType)) {
            return systemData.systemEmbeddingGraphDetail?.images.graphId || undefined;
          } else if (detailsSource.contentType == ContentType.DATASET) {
            return systemData.systemEmbeddingGraphDetail?.datasets.graphId || undefined;
          } else {
            return undefined;
          }
        } else {
          logger.warn(
            `Getting precalculate graphData | DatalakeExplorerService.getPreCalculatedGraphId | ${teamId} |No system data found for team ${teamId}`,
          );
          return undefined;
        }
      } else {
        return undefined;
      }
    } else {
      return undefined;
    }
  }

  /**
   * Use to get collaborator user list for selection
   * @param filter {DatalakeSelectionRequest} filter object
   * @param currentUserProfile current user profile - user info
   * @returns collaborator user List
   */
  async getUserListForSelection(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    const returnObj: {
      id: string;
      name: string;
      email: string;
      userType: number;
      isSelected: boolean;
      previousSelection?: boolean;
    }[] = [];
    const teamId = currentUserProfile.teamId;
    if (!teamId) {
      logger.error(
        `Getting user list for selection | DatalakeExplorerService.getUserListForSelection | ${teamId} | Team id required`,
      );
      throw new HttpErrors.NotAcceptable('Team id required');
    }
    const datalakeSelection: Partial<DatalakeSelection> = {
      teamId: teamId,
      selectionRequest: filter,
      objectType: filter.contentType,
    };
    const matchQueryObject = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      undefined,
      datalakeSelection,
    );

    if (!matchQueryObject?.matchQuery || Object.keys(matchQueryObject.matchQuery).length == 0) {
      logger.error(
        `Getting user list for selection | DatalakeExplorerService.getUserListForSelection | ${teamId} | No match query found`,
      );
      throw new HttpErrors.NotAcceptable('Invalid selection query');
    }
    const matchQuery = matchQueryObject.matchQuery;

    // aggregate and get the count of objects
    const matchObjectCount = await this.metaDataRepository.aggregate([
      {
        $match: matchQuery,
      },
      {$group: {_id: null, count: {$sum: 1}}},
      {$project: {_id: 0, count: 1}},
    ]);

    let commonUserIdList: {_id: ObjectId}[] = [];

    if (
      matchObjectCount &&
      Array.isArray(matchObjectCount) &&
      matchObjectCount.length > 0 &&
      matchObjectCount[0].count > 0
    ) {
      commonUserIdList = await this.metaDataRepository.aggregate([
        {
          $match: matchQuery,
        },
        {$project: {allowedUserIdList: 1}},
        {$unwind: '$allowedUserIdList'},
        {$group: {_id: '$allowedUserIdList', count: {$sum: 1}}},
        {$match: {count: matchObjectCount[0].count}},
        {$project: {_id: 1}},
      ]);
    }

    const userTypes: number[] = [UserType.USER_TYPE_COLLABORATOR];
    const userList = await this.basicAuthService.getUserList(teamId, userTypes, []);

    if (userList && userList.length > 0) {
      for (const userObj of userList) {
        let isSelected = false;
        if (
          commonUserIdList &&
          commonUserIdList.length > 0 &&
          commonUserIdList.find(userIdObj => userIdObj._id.toString() == userObj.id.toString())
        ) {
          isSelected = true;
        }
        returnObj.push({
          id: userObj.id,
          name: userObj.name,
          email: userObj.email,
          userType: userObj.userType,
          isSelected: isSelected,
          previousSelection: isSelected,
        });
      }
    }

    return returnObj;
  }

  /**
   * Use to update allowed user list for selection
   * @param selectionObject selected and unselected user list
   * @param currentUserProfile current user profile - user info
   * @returns success or failure
   */
  async updateAllowedUserListForSelection(
    selectionObject: UserSelectionRequest,
    currentUserProfile: UserProfileDetailed,
  ) {
    const selectionId = selectionObject.selectionId;
    const selectedUserIdList = selectionObject.selectedUserIds || [];
    const unSelectedUserIds = selectionObject.unSelectedUserIds || [];
    const teamId = currentUserProfile.teamId;

    if (!selectionId) {
      logger.error(
        `Updating allowed user list for selection | DatalakeExplorerService.updateAllowedUserListForSelection | ${teamId} | Selection id required`,
      );
      throw new HttpErrors.NotAcceptable('Selection id required');
    }

    const matchQueryObj = await this.searchQueryBuilderService.getMatchQueryForSelection(
      currentUserProfile,
      selectionId,
    );

    if (!matchQueryObj?.matchQuery || Object.keys(matchQueryObj.matchQuery).length == 0) {
      logger.error(
        `Updating allowed user list for selection | DatalakeExplorerService.updateAllowedUserListForSelection | ${teamId} | No match query found`,
      );
      throw new HttpErrors.NotAcceptable('Invalid selection query');
    }

    let matchQuery = matchQueryObj.matchQuery;
    const objectType = matchQueryObj.objectType;

    if ([ContentType.IMAGE, ContentType.OTHER, ContentType.VIDEO].includes(objectType)) {
      // then the query can be used directly
      // matchQuery = matchQueryObj.matchQuery;
      logger.error(
        `Updating allowed user list for selection | DatalakeExplorerService.updateAllowedUserListForSelection | ${teamId} | Invalid object type`,
      );
      throw new HttpErrors.NotAcceptable('Invalid object type');
    } else if (
      [
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.OTHER_COLLECTION,
        ContentType.DATASET,
        // ContentType.VIDEO,
      ].includes(matchQueryObj.objectType)
    ) {
      // need to update children of the collection

      // get the collection id list
      const collectionDataList = await this.metaDataRepository.aggregate([
        {$match: matchQueryObj.matchQuery},
        {$project: {collectionId: 1}},
      ]);
      const collectionOidList = collectionDataList.map((collectionData: any) => new ObjectId(collectionData._id));

      const matchOrQuery: any[] = [matchQueryObj.matchQuery];

      matchOrQuery.push({
        $and: [{$or: [{vCollectionIdList: {$in: collectionOidList}}, {parentList: {$in: collectionOidList}}]}],
      });

      matchQuery = {
        $or: matchOrQuery,
      };
    }

    try {
      if (unSelectedUserIds.length > 0) {
        const unselectedUserObjectIds = unSelectedUserIds.map(userId => new ObjectId(userId));
        await this.metaDataRepository.updateManyPullAll(matchQuery, {allowedUserIdList: unselectedUserObjectIds}, []);
      }

      if (selectedUserIdList.length > 0) {
        const selectedUserObjectIds = selectedUserIdList.map(userId => new ObjectId(userId));
        await this.metaDataRepository.updateManyAddToSetToList(
          matchQuery,
          {allowedUserIdList: {$each: selectedUserObjectIds}},
          [],
        );
      }
    } catch (e) {
      logger.error(
        `Error in updating allowed user list for selection | DatalakeExplorerService.updateAllowedUserListForSelection | ${teamId} | ${e}`,
      );
      throw new HttpErrors.InternalServerError('Error in updating allowed user list for selection');
    }

    return {success: true};
  }

  /**
   * Use to generate graph embeddings
   * @param collectionId {string} details for generate graph
   * @returns {success: boolean}
   */
  async generateGraphEmbeddings(filter: DatalakeSelectionRequest, currentUserProfile: UserProfileDetailed) {
    try {
      const url = `${PYTHON_HOST}/internal/generate/graph/embeddings`;

      const response = await Axios({
        url,
        method: 'POST',
        data: {
          collectionId: filter.collectionId,
          query: filter.query,
          filter: filter.filterData,
          referenceImage: filter.referenceImage,
          isAllSelected: filter.isAllSelected,
          objectIdList: filter.objectIdList,
        },
      });

      const responseData = response.data;

      const jobId = responseData.jobId;
      const graphId = `graph-${uuidV4()}`;

      const graphDetails = await this.queryGraphDetailsRepository.create({
        graphId: graphId,
        createdAt: new Date(),
        isPending: false,
        progress: 0,
        isCronJobDeletable: true,
        isEmbeddingAvailable: false,
        jobId: jobId,
        status: GraphStatus.EMBEDDING_GENERATION,
      });

      const detailsSource: {
        sourceType: DetailSource;
        id: string;
        contentType: ContentType;
      } = await this.statsCalculationService.getDetailsSource(filter, currentUserProfile);

      if (detailsSource.sourceType == DetailSource.METADATA && filter.collectionId) {
        await this.metaDataRepository.updateById(filter.collectionId, {
          graphId: graphId,
        });
        await this.queryGraphDetailsRepository.updateById(graphDetails._id, {
          isCronJobDeletable: false,
        });
      }

      return {
        success: true,
        graphId: graphId,
        jobId: jobId,
      };
    } catch (err) {
      logger.error(
        `Graph Embedding Generation | SearchQueryBuilderService.getGraphSampleCoordinates | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }

  /**
   * Use to locate matching elements in documents for given user question
   * @returns {doc_elements: list}
   */
  async getMatchingElements(
    userQuestion: string,
    originalQuestion: string | '',
    docObjectKeyList: string[],
    isTargetDocumentsExist: boolean,
    converasationId: string,
  ) {
    try {
      const url = `${PYTHON_HOST}/internal/metalake/explorer/document/elements`;
      logger.debug(
        `Document element matching | DataLakeExplorerService.getMatchingElements | given docs: ${docObjectKeyList}, original question: ${originalQuestion} target docs exist: ${isTargetDocumentsExist} | requesting`,
      );
      const response = await Axios({
        url,
        method: 'POST',
        data: {
          objectKeyList: docObjectKeyList,
          userQuestion: userQuestion,
          originalQuestion: originalQuestion,
          isTargetDocumentsExist: isTargetDocumentsExist,
          converasationId: converasationId,
        },
        timeout: 600000, // Timeout after 600,000 ms or 10 minutes
      });

      const responseData = response.data;
      const responseDataStr = JSON.stringify(responseData);
      logger.debug(
        `Document element matching | DataLakeExplorerService.getMatchingElements | Conversation id: ${converasationId}, given docs: ${docObjectKeyList} | Received response: ${responseDataStr}`,
      );
      return responseData;
    } catch (err) {
        if (err.response.status == 500){
        logger.error(
          `Document element matching | DataLakeExplorerService.getMatchingElements | N/A | failed to post request to python host, err = ${err.response.data.error.message}`,
        );
        throw new HttpErrors.UnprocessableEntity(`Error : ${err.response.data.error.message}`);
      }
      logger.error(
        `Document element matching | DataLakeExplorerService.getMatchingElements | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host ${err.response.data.error.message}`);

    }
  }

  /**
   * Use to extract data (text or image base64) from a document for a user question
   * @returns {extractd_data: object}
   */
  async extractDocumentDataForAI(
    userQuestion: string,
    docElements: object,
    extractionKeys: object[],
    converasationId: string,
  ) {
    try {
      const url = `${PYTHON_HOST}/internal/metalake/explorer/document/extract`;
      logger.debug(
        `Document data extraction| DataLakeExplorerService.extractDocumentDataForAI | Conversation id: ${converasationId},  given elements: ${docElements}, extraction keys: ${extractionKeys} | Requesting`,
      );
      const response = await Axios({
        url,
        method: 'POST',
        data: {
          docElements: docElements,
          userQuestion: userQuestion,
          extractionKeys: extractionKeys,
          converasationId: converasationId,
        },
        timeout: 600000, // Timeout after 600,000 ms or 10 minutes
      });

      const responseData = response.data;
      const responseDataStr = JSON.stringify(responseData);
      logger.debug(
        `Document data extraction | DataLakeExplorerService.extractDocumentDataForAI | Conversation id: ${converasationId}, given elements: ${docElements}, extraction keys: ${extractionKeys} | Received response: ${responseDataStr}`,
      );
      return responseData;
    } catch (err) {
      if (err.response.status == 500){
        logger.error(
          `Document data extraction| DataLakeExplorerService.extractDocumentDataForAI | N/A | failed to post request to python host, err = ${err.response.data.error.message}`,
        );
        throw new HttpErrors.UnprocessableEntity(`Error : ${err.response.data.error.message}`);
      }
      logger.error(
        `Document data extraction| DataLakeExplorerService.extractDocumentDataForAI | N/A | failed to post request to python host, err = ${err}`,
      );
      throw new HttpErrors.NotAcceptable(`Failed to post request to python host`);
    }
  }
}

export const DATALAKE_EXPLORER_SERVICE = BindingKey.create<DatalakeExplorerService>('service.datalakeExplorerService');
