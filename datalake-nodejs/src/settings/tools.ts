import {ObjectId} from 'bson';
import {LabelAnalytics, LabelDataInAgg} from '../models';
import {ContentType} from '../models/meta-data.model';
import {UserType} from './constants';

const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
const FILE_SIZE_UNITS_LONG = [
  'Bytes',
  'Kilobytes',
  'Megabytes',
  'Gigabytes',
  'Pettabytes',
  'Exabytes',
  'Zettabytes',
  'Yottabytes',
];
const minuteToMilliSeconds = 60 * 1000;

function byteTransform(sizeInBytes: number | undefined, longForm: boolean): string {
  if (!sizeInBytes) {
    return '0 B';
  }
  const units = longForm ? FILE_SIZE_UNITS_LONG : FILE_SIZE_UNITS;

  let power = Math.floor(Math.log(sizeInBytes) / Math.log(1024));
  power = Math.min(power, units.length - 1);

  const size = sizeInBytes / Math.pow(1024, power); // size in new units
  const formattedSize = Math.round(size * 100) / 100; // keep up to 2 decimals
  const unit = units[power];

  return `${formattedSize} ${unit}`;
}

function nFormatter(num: number, digits: number) {
  const lookup = [
    {value: 1, symbol: ''},
    {value: 1e3, symbol: 'k'},
    {value: 1e6, symbol: 'M'},
    {value: 1e9, symbol: 'G'},
    {value: 1e12, symbol: 'T'},
    {value: 1e15, symbol: 'P'},
    {value: 1e18, symbol: 'E'},
  ];
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  var item = lookup
    .slice()
    .reverse()
    .find(function (item) {
      return num >= item.value;
    });
  return item ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol : '0';
}

/**
 * Use to format video length from Seconds to Hours
 * @param length video length in seconds
 * @returns video length in hours
 */
function getFormattedVideoLength(length: number | undefined) {
  if (!length) {
    return '0 hrs';
  } else {
    let lengthInHours = length / 3600;
    return lengthInHours.toFixed(2) + ' hrs';
  }
}

/**
 * Use to get Counts in compact format
 * @param count {number} Count
 * @returns {string} short formed count
 */
function getFormattedItemCount(count: number | undefined) {
  if (!count) {
    return '0';
  } else {
    if (count < 1000000) {
      return new Intl.NumberFormat().format(count);
    } else {
      return new Intl.NumberFormat('en-GB', {
        notation: 'compact',
        compactDisplay: 'short',
      }).format(count);
    }
  }
}

/**
 * Find the file type by extention of its name
 * @param fileName {string} name of file with extention
 * @param parentObjectType {ContentType} - Object type of collection
 * @returns file type
 */
function getContentType(fileName: string, parentObjectType?: ContentType) {
  //Give priority to determine object type from parent's type
  //If it's unavailable only, get it from the extension
  if (parentObjectType) {
    switch (parentObjectType) {
      case ContentType.IMAGE_COLLECTION:
      case ContentType.DATASET:
        return ContentType.IMAGE;
      case ContentType.VIDEO_COLLECTION:
        return ContentType.VIDEO;
      case ContentType.OTHER_COLLECTION:
        return ContentType.OTHER;
      default:
        //Will proceed to check from file extension
        break;
    }
  }

  let fileExtention = fileName.split('.').pop();
  fileExtention = fileExtention?.toLowerCase();
  if (fileExtention == 'jpeg' || fileExtention == 'png' || fileExtention == 'jpg') {
    return ContentType.IMAGE;
  } else if (fileExtention == 'mp4' || fileExtention == 'avi') {
    return ContentType.VIDEO;
  } else {
    return ContentType.OTHER;
  }
}

function getObjectTypeName(objectType: ContentType) {
  switch (objectType) {
    case ContentType.IMAGE:
      return 'Image File';
    case ContentType.VIDEO:
      return 'Video File';
    case ContentType.OTHER:
      return 'Other File';
    case ContentType.IMAGE_COLLECTION:
      return 'Image Collection';
    case ContentType.VIDEO_COLLECTION:
      return 'Video Collection';
    case ContentType.OTHER_COLLECTION:
      return 'Other Collection';
    case ContentType.DATASET:
      return 'Dataset';
    default:
      return 'Unknown';
  }
}

function isLiteralObject(value?: any) {
  return !!value && value.constructor === Object;
}

function isValidObjectId(id?: string) {
  if (!id) {
    return false;
  }

  id = id.toString();

  if (ObjectId.isValid(id)) {
    if (String(new ObjectId(id)) === id) return true;
    return false;
  }

  return false;
}

/**
 * Format addToSet to array field
 * @param fieldName name of the field to be updated
 * @param valueArray values
 * @returns addToSet each query
 */
function formatToAddToSetEachArray(fieldName: string, valueArray: any[]) {
  let query = {
    [fieldName]: {
      $each: valueArray,
    },
  };
  return query;
}

function regExpEscape(literal_string: string) {
  return literal_string.replace(/[-[\]{}()*+!<=:?.\/\\^$|#\s,]/g, '\\$&');
}

/**
 * Convert to local time using UTC time
 * @param {number} timeZoneOffset time zone offset
 * @param {Date} UTCDate UTC date
 * @returns local time
 */
function getLocalTime(timeZoneOffset: number, UTCDate: Date): Date {
  let machineOffsetTime = new Date().getTimezoneOffset() * minuteToMilliSeconds;
  return new Date(UTCDate.getTime() - timeZoneOffset * minuteToMilliSeconds + machineOffsetTime);
}

/**
 * Use to convert values of object as arrays
 * {field1:value1} ----> {field1:[value1]}
 * @param Obj
 */
function convertValuesToArrays(obj: {[k: string]: any}) {
  let newObj: {[k: string]: any[]} = {};

  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      newObj[key] = value;
    } else {
      newObj[key] = [value];
    }
  }

  return newObj;
}

/**
 * use to check if variable is array with length
 * @param elem variable to check
 * @returns boolean
 */
function isArrayWithLength(elem: any) {
  return Array.isArray(elem) && elem.length > 0;
}

export function getEnumNumberValues(enumerator: any): number[] {
  let valueArray: number[] = [];

  for (let item in enumerator) {
    if (isNaN(Number(item))) continue;
    valueArray.push(Number(item));
  }

  return valueArray;
}

/**
 * Get string values of an enum
 * @param enumerator enum
 * @returns value array
 */
export function getEnumStringValue(enumerator: any): string[] {
  let valueArray: string[] = [];

  for (let item in enumerator) {
    valueArray.push(enumerator[item]);
  }

  return valueArray;
}

export function getUserTypeString(userType: UserType) {
  switch (userType) {
    case UserType.USER_TYPE_TEAM_ADMIN:
      return 'TEAM_ADMIN';
    case UserType.USER_TYPE_ANNOTATOR:
      return 'ANNOTATOR';
    case UserType.USER_TYPE_AUDITOR:
      return 'MEMBER';
    case UserType.USER_TYPE_QA:
      return 'QA';
    case UserType.USER_TYPE_SUPER_ADMIN:
      return 'SUPER_ADMIN';
    case UserType.USER_TYPE_COLLABORATOR:
      return 'COLLABORATOR';
    default:
      return 'Unknown';
  }
}

export function convertObjectToSinglePairObjectArray(obj: {[k: string]: any}) {
  return Object.keys(obj).map(key => ({[key]: obj[key]}));
}

/**
 * Get label wise analytics
 * @param labelData {LabelDataInAgg[]} label data
 * @returns label wise analytics
 */
export function getLabelWiseCount(labelData: LabelDataInAgg[]): LabelAnalytics[] {
  if (!Array.isArray(labelData) || labelData.length == 0) return [];

  let labelWiseAnalytics: LabelAnalytics[] = labelData.map(labelObj => {
    let truePositive: number = labelObj.truePositive ?? 0;
    let falsePositive: number = labelObj.falsePositive ?? 0;
    let falseNegative: number = labelObj.falseNegative ?? 0;

    let precision = truePositive + falsePositive > 0 ? (100 * truePositive) / (truePositive + falsePositive) : 'NaN';
    let recall = truePositive + falseNegative > 0 ? (100 * truePositive) / (truePositive + falseNegative) : 'NaN';
    let f1Score =
      2 * truePositive + falseNegative + falsePositive > 0
        ? (100 * 2 * truePositive) / (2 * truePositive + falseNegative + falsePositive)
        : 'NaN';

    return {
      label: labelObj.label,
      truePositive: truePositive,
      falsePositive: falsePositive,
      falseNegative: falseNegative,
      precision: precision,
      recall: recall,
      f1Score: f1Score,
    };
  });
  return labelWiseAnalytics;
}

export const CONTENT_TYPE_VALUES = getEnumNumberValues(ContentType) as ContentType[];

/**
 * Use to round number to given decimal places
 * @param decimals required decimal places
 * @param num  number to round
 * @returns rounded number
 */
function roundNumberToGivenDecimalPlaces(decimals: number, num?: number) {
  if (!num) return 0;

  let factor = 10 ** decimals;

  return Math.round((num + Number.EPSILON) * factor) / factor;
}

function timeAgo(past: Date): string {
  const now = new Date();
  const seconds = Math.round((now.getTime() - past.getTime()) / 1000);
  const minutes = Math.round(seconds / 60);
  const hours = Math.round(minutes / 60);
  const days = Math.round(hours / 24);
  const weeks = Math.round(days / 7);
  const months = Math.round(days / 30);

  if (seconds < 0) {
    return 'just now';
  }
  else if (seconds < 60) {
    return `${seconds} seconds ago`;
  } else if (minutes === 1) {
    return '1 minute ago';
  } else if (minutes < 60) {
    return `${minutes} minutes ago`;
  } else if (hours === 1) {
    return '1 hour ago';
  } else if (hours < 24) {
    return `${hours} hours ago`;
  } else if (days === 1) {
    return '1 day ago';
  } else if (days < 7) {
    return `${days} days ago`;
  } else if (weeks === 1) {
    return '1 week ago';
  } else if (weeks < 5) {
    return `${weeks} weeks ago`;
  } else if (months === 1) {
    return '1 month ago';
  } else {
    return `${months} months ago`;
  }
}

function extractTextBeforeAndAfter(text: string, separator: string) {
  // Create a regular expression to match the part before and after the separator
  const regex = new RegExp(`(.*?)${separator}(.*)`, 's'); // 's' flag to handle multiline strings
  const match = text.match(regex);

  if (match) {
    return {
      before: match[1].trim(),
      after: match[2].trim(),
    };
  } else {
    return {before: text, after: ''}; // In case separator is not found
  }
}

export {
  byteTransform,
  convertValuesToArrays,
  formatToAddToSetEachArray,
  getContentType,
  getFormattedItemCount,
  getFormattedVideoLength,
  getLocalTime,
  getObjectTypeName,
  isArrayWithLength,
  isLiteralObject,
  isValidObjectId,
  minuteToMilliSeconds,
  nFormatter,
  regExpEscape,
  roundNumberToGivenDecimalPlaces,
  timeAgo,
  extractTextBeforeAndAfter,
};

export interface SuccessResponse {
  isSuccess: boolean;
  message?: string;
  warning?: string;
  error?: string;
}
