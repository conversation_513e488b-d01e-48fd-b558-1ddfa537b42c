/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */
import dotenv from 'dotenv';
dotenv.config()

export const AwsConfiguration = {
  AWS_ACCESS_KEY: process.env.AWS_ACCESS_KEY,
  AWS_SECRET_KEY: process.env.AWS_SECRET_KEY,
  AWS_REGION: process.env.AWS_REGION,
  AWS_BUCKET_NAME: process.env.DEFAULT_BUCKET_NAME,
  AWS_BUCKET_NAME_PREFIX_KEY: process.env.AWS_BUCKET_NAME_PREFIX_KEY,
  AWS_DATALAKE_MEDIA_BUCKET_NAME: process.env.AWS_DATALAKE_MEDIA_BUCKET_NAME
}
