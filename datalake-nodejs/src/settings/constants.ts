/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */

import dotenv from 'dotenv';
dotenv.config();

export enum FLOWS {
  'METADATA_INPUT_PROCESS' = 'Processing metaData input Queue',
  'METADATA_UPDATE_OPERATIONS' = 'MetadataUpdates updating',
  'METADATA_DETAIL_VIEW' = 'Metadata Detail View',
  'DATALAKE_EXPLORER_VIEW_DEFAULT' = 'Getting explorer default view',
  'DATALAKE_EXPLORER_VIEW_COLLECTION' = 'Getting explorer collection view',
  'DATALAKE_EXPLORER_SERACH_QUERY' = 'Datalake serach query',
  'DATALAKE_OVERVIEW' = 'datalake overview',
  'CRAWL_STORAGE' = 'Crawl storage for populate data',
  'SYSTEM_LABEL_COCO_INTERNAL_CREATE' = 'Create System Label for COCO import',
  'API_CLIENT_REQUEST' = 'API client request',
  'DATALAKE_SELECTION' = 'select datalake objects',
  'DATASET_MANAGER_REQ' = 'Request From DatasetManager',
  'META_UPDATE' = 'Meta data updates',
  'ANALYTICS_CALCULATION' = 'Analytics calculation',
  'VIRTUAL_COLLECTION' = 'Virtual collection',
  'DATALAKE_COLLECTION' = 'Datalake Collection',
  'DATALAKE_TRASH' = 'Datalake Trash',
  'MANAGE_AUGMENTATIONS' = 'MANAGE_AUGMENTATIONS',
  'DIRECT_DB_OPERATION' = 'Direct DB operation',
  'LABEL_GROUP' = 'Label Group',
  'GET_OPTIONS' = 'Get Options',
  'GET_PROGRESS' = 'Get Progress',
  'FILE_UPLOAD' = 'File Upload',
  'DATA_DICT_EVAL' = 'Data Dictionary Evaluation',
  'KNOWLEDGE_SOURCE' = 'Knowledge Source',
  'BUSINESS_RULES' = 'Business Rules',
  'DB_CRAWLING' = 'DB Crawling',
  'KNOWLEDGE_BLOCK' = 'Knowledge Block',
}

export const URL_REGX = new RegExp('^https:', 'i');

export const STORAGE_SYSTEM_FOLDER = 'LayerNext';
export const STORAGE_SYSTEM_SUB_FOLDER_THUMBNAILS = `${STORAGE_SYSTEM_FOLDER}/thumbnails`;
export const STORAGE_SYSTEM_SUB_FOLDER_VIDEO_FRAMES = `${STORAGE_SYSTEM_FOLDER}/video-frames`;
export const STORAGE_SYSTEM_SUB_FOLDER_PROFILE_IMAGES = `${STORAGE_SYSTEM_FOLDER}/profile-images`;
export const STORAGE_SYSTEM_SUB_FOLDER_SYSTEM_LABELS = `${STORAGE_SYSTEM_FOLDER}/system-labels`;
export const STORAGE_SYSTEM_SUB_FOLDER_AUGMENTATIONS = `${STORAGE_SYSTEM_FOLDER}/augmentations`;

//Application Codes, used for request origin identification
// export const APP_CODE = {
//   SDK_VIA_DATASET: 'bca16b55-bb73-49c7-9b94-3aa9e57234b3',
// };

export const SYSTEM_BOT = 'system-bot';

export enum UserType {
  USER_TYPE_ANNOTATOR = 0,
  USER_TYPE_AUDITOR = 1,
  USER_TYPE_SUPER_ADMIN = 2,
  USER_TYPE_TEAM_ADMIN = 3,
  USER_TYPE_QA = 4,
  USER_TYPE_COLLABORATOR = 5,
}

export enum UserTypeDetailed {
  ANNOTATOR = 'ANNOTATOR',
  MEMBER = 'MEMBER',
  SUPER_ADMIN = 'SUPER_ADMIN',
  TEAM_ADMIN = 'TEAM_ADMIN',
  QA = 'QA',
  COLLABORATOR = 'COLLABORATOR',
}

export enum EvalFeedbackType {
  GROUND_TRUTH = 'ground_truth',
  COMMENT = 'comment',
}

export const SIMILARITY_SCORE_THRESHOLD = Number(process.env.SIMILARITY_SCORE_THRESHOLD) || 0.7;
export const SIMILARITY_SCORE_THRESHOLD_NOT_APPLICABLE = -1;
export const EMBEDDING_COLLECTION = 'Resnet50';

export enum ObjectRemoveMeSsages {
  NO_OBJECT_TO_REMOVE_FROM_COLLECTION = 'All objects are belong to only one collection',
  ALL_SELECTED_OBJECT_NOT_REMOVE_FROM_COLLECTION = 'All selected objects can not be removed',
  CAN_NOT_REMOVE_COLLECTION = 'Can not remove a collection due to some objects are belong to only one collection',
  NO_COLLECTION_REMOVED = 'Single collection can not be removed',
}

export const DEFAULT_DISK_STORAGE_BASE_PATH = '/usr/src/app/buckets/'; //Applied when DISK_STORAGE_BASE_PATH environment variable is not defined

export interface OverallDetailsInOverviewCount {
  totalSize: string;
  totalItems: string;
  videoCollections: string;
  videoFiles: string;
  imageCollections: string;
  images: string;
  otherCollections: string;
  otherFiles: string;
}

export interface TotalFramesInOverviewCount {
  total: string;
  rawData: string;
  machineAnnotated: string;
  humanAnnotated: string;
}

export interface TotalLabelsInOverviewCount {
  size: string;
  humanAnnotated: string;
  machineAnnotated: string;
}
export interface OverviewCountRes {
  overallDetails: OverallDetailsInOverviewCount;
  totalFrames: TotalFramesInOverviewCount;
  totalLabels: TotalLabelsInOverviewCount;
}

export enum DataBaseTimeOuts {
  SERVER_SIDE_TIMEOUT_MS = 20000, //stop the query running in database server
  CLIENT_SIDE_TIMEOUT_MS = 20000, //stop waiting for query in application side
}

// Max. number of records allowed to fetch from database via run_sql_query
export const MAX_FETCH_DB_RECORD_LIMIT = 5000;
