/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */

export const DatalakeUserMessages = {
  META_DATA_DETAIL_VIEW_FRAME_COLLECTION_NOT_EXIST: 'Frame collection not exist',
  META_DATA_DETAIL_VIEW_META_OBJECT_PROJECT_LIST_FETCH_FAILED: 'Failed to fetch project info',
  META_DATA_DETAIL_VIEW_NOT_APPLICABLE: 'Detail View Not Applicable for this object',
  META_DATA_DETAIL_VIEW_FETCH_METAUPDATES_NOT_APPLICABLE: 'Fetch MetaUpdates Invalid',
  DATA_CRAWL_GET_STATUS_FAILED: 'Failed to get crawling status',
  SYSTEM_LABEL_LIKELY_EXIST: 'likely exist labelText',
  DATALAKE_TEAM_NOT_EXIST: "Couldn't find the team of the user",
  DATALAKE_USER_NOT_EXIST: "Couldn't find the user",
  OBJECT_SELECTION_FAILED: 'Meta Objects Selection Failed',
  DATASET_MANAGER_REQUEST_FAILED: "Request couldn't complete",
  LAYERNEXT_TEAM_NOT_EXIST: "Couldn't find the LayerNext team",
  COLLECTION_VIEW_NOT_APPLICABLE: 'Invalid Collection View Request',
  INVALID_NAME: 'invalid name',
  FAILED_REQUEST: 'request failed',
  NOT_VIRTUAL_COLLECTION: 'given collection is not virtual',
  SIMILAR_OBJECT_EXIST: 'Failed to create new object since a similar object already exist',
  INVALID_OBJECT_TYPE: 'Invalid object type',
  INVALID_COLLECTION_NAME: 'Invalid or missing collection id or name',
  INVALID_COLLECTION_NAME_OR_OBJECT_TYPE: 'Invalid collection name or object type',
  FILE_UPLOAD_NOT_ALLOWED_TO_LOGICAL_COLLECTIONS: "Couldn't upload files to a virtual collection",
  FILE_UPLOAD_NOT_ALLOWED_TO_DERIVED_COLLECTIONS: "Couldn't upload files to a derived collection",
  FILE_UPLOAD_NOT_ALLOWED_TO_TRASHED_COLLECTIONS: "Couldn't upload files to a trashed collection",
  FILE_UPLOAD_NOT_ALLOWED_TO_DATASET_COLLECTIONS: "Couldn't upload files to a dataset",
  FILE_TYPE_COLLECTION_TYPE_MISMATCH: 'file type mismatch with collection type',
  UPLOAD_INITIATION_FAILED: 'Failed to create upload record',
  NAME_NOT_EXIST: 'Name not exist',
  FAILED_TO_FETCH: 'Failed to fetch from DataLake',
  INVALID_PROJECT_LIST: 'invalid project list',
  DATSET_META_HEAD_NOT_EXIST: 'Dataset meta head not exist',
  INVALID_SELECTION_ID: 'Invalid selection id',
  INVALID_SELECTION_QUERY: 'Invalid selection query',
  NO_OBJECT_FOUND_TO_TRASH: 'No object found to trash',
  COLLECTION_IS_NOT_VIRTUAL: 'Collection is not virtual',
  LABEL_GROUPNAME_EMPTY: 'label Group name cannot be empty',
  LABEL_GROUPNAME_EXIST: 'label Group name already exist',
  OPERATION_NOT_PERMITTED: 'Operation not permitted',
  INVALID_OTP: 'Invalid OTP',
  TAG_ALREADY_EXISTS: 'Tag already exists',
  OTP_SEND_FAILED: 'OTP sending failed',
  OTP_VALIDATION_FAILED: 'OTP validation failed',
  JOB_STARTED_IN_BACKGROUND: 'Job started in background',
  BACKGROUND_JOB_FAILED: 'Background job failed',
  COLLECTION_NOT_EXIST: 'Collection not exist',
  BUCKET_NOT_DEFINED: 'Storage not defined',
  STORAGE_ACCOUNT_NOT_DEFINED: 'Storage account not defined',
  COLLECTION_NAME_SAME: 'Collection name same as existing collection',
  COLLECTION_NAME_EXIST: 'Collection name already exist',
  FAILED_TO_UPDATE_COLLECTION: 'Failed to update collection',
  OBJECT_NOT_TRASHED: 'Object not trashed',
  COLLECTION_ID_OR_CONTENT_TYPE_REQUIRED: 'Collection id or content type required',
  MERGE_UNAVAILABLE: 'Merge only available for image, video and other collections',
  USER_NOT_ALLOWED: 'User not allowed',
  REMOVED_OBJECT_UPLOAD:
    'The file that was previously removed from this collection has already been attached to another collection',
  //If this message is changed, it should be changes in python SDK (layernext.datalake.constants) file
  UNAUTHORIZED_CONTENT: "User doesn't have access to perform action",
  UPLOAD_FAILED: 'Upload failed: The file could not be uploaded to the system',
  INVALID_USER: 'Invalid User',
  INVALID_SOURCE_TYPE: 'Invalid source type',
  NOT_FOUND_SYSTEM_DATA: 'System data not found',
  MYSQL_SCHEMA_RETRIEVAL_FAILED: 'Error retrieving MySQL schema',
  EXECUTE_MYSQL_QUERY_FAILED: 'Error executing MySQL query',
  EXECUTE_SQL_SERVER_QUERY_FAILED: 'Error executing sql server query',
  EXECUTE_SNOWFLAKE_QUERY_FAILED: 'Error executing snowflake query',
  EXECUTE_POSTGRESQL_QUERY_FAILED: 'Error executing postgreSQL query',
  INVALID_MYSQL_QUERY: 'Invalid MySQL query',
  INVALID_SQL_SERVER_QUERY: 'Invalid sql server query',
  INVALID_POSTGRESQL_QUERY: 'Invalid postgreSQL query',
  MYSQL_POOL_CONNECTION_ERROR: 'Error while creating MySQL connection pool',
  SQL_SERVER_POOL_CONNECTION_ERROR: 'Error while creating Microsoft SQL server connection pool',
  POSTGRESQL_POOL_CONNECTION_ERROR: 'Error while creating postgreSQL connection pool',
  DESTINATION_CONNECTION_ERROR: 'Error while creating destination connection',
  PYTHON_REQUEST_ERROR: 'Failed to post request to python host',
  MISSING_REQUIRED_FIELDS: 'Missing required fields in configuration object',
  DATA_SOURCE_REGISTRATION_FAILED: 'Failed to register data source',
  DATA_SOURCE_CONNECTION_FAILED: 'Cannot established database connection',
  DATA_SOURCE_NAME_ALREADY_EXIST: 'Source name already exists or name is empty',
  CONNECTION_FAILED: 'Connection failed',
  INVALID_PAGE_INDEX: 'Invalid page index',
  SQL_SERVER_CONNECTION_ERROR: 'Error connecting to SQL Server',
  POSTGRESQL_CONNECTION_ERROR: 'Error connecting to postgreSQL',
  CREATE_NEW_CONNECTION_FAILED: 'Failed to create new data source connection',
  //data dictionary evaluation and auto tuneup related user messages
  EVAL_CREATE_EVALUATION_FAILED: 'Unable to create evaluation',
  EVAL_CREATE_EVALUATION_SUCCESS: 'Evaluation created successfully.',
  EVAL_UPDATE_EVALUATION_FAILED: 'Unable to update evaluation',
  EVAL_UPDATE_EVALUATION_SUCCESS: 'Evaluation updated successfully.',
  EVAL_GET_EVALUATION_LIST_FAILED: 'Unable to retrieve evaluations',
  EVAL_GET_EVALUATION_LIST_INFO_FAILED: 'Unable to retrieve evaluation info',
  EVAL_GET_QUESTION_LIST_FAILED: 'Unable to retrieve questions',
  EVAL_DATA_DICT_TUNEUP_ONGOING: 'Data dict tune up is ongoing',
  EVAL_DATA_DICT_TUNEUP_FAILED: 'Data dict tune up failed',
  EVAL_EVALUATION_SET_RUNNING: 'Evaluation is running',
  EVAL_EVALUATION_SET_RUNNING_FAILED: 'Unable to start evaluation',
  EVAL_DELETE_EVALUATION_FAILED: 'Unable to delete evaluation',
  EVAL_DELETE_EVALUATION_SUCCESS: 'Evaluation deleted successfully.',
  EVAL_EDIT_EVALUATION_FAILED: 'Unable to edit evaluation',
  EVAL_ADD_NEW_QUESTION_FAILED: 'Unable to add new question',
  EVAL_ADD_NEW_QUESTION_SUCCESS: 'Question added successfully.',
  EVAL_DELETE_QUESTION_FAILED: 'Unable to update question',
  EVAL_QUESTION_RUNNING: 'Question is running',
  EVAL_QUESTION_STATUS_ALREADY_IN_THIS_STATUS: 'Question is already in this status',
  EVAL_QUESTION_EVALUATION_STATUS_CHANGE_FAILED: 'Unable to change question evaluation status',
  EVAL_INVALID_EVALUATION_STATUS_CHANGE_REQUEST: 'Invalid evaluation status change request',
  EVAL_QUESTIONS_NOT_FOUND: 'Questions not found',
  EVAL_INSUFFICIENT_USER_INFO: 'Insufficient user info',
  EVAL_QUESTION_RUN_STARTED: 'Question running started',
  EVAL_HISTORY_RETRIEVAL_FAILED: 'Unable to retrieve history',
  KNOWLEDGE_SOURCE_REFRESH_FAILED: 'Unable to refresh the source',
  KNOWLEDGE_SOURCE_LIST_FAILED: 'Unable to retrieve source list',
  KNOWLEDGE_SOURCE_CREATE_FAILED: 'Unable to create the source',
  KNOWLEDGE_SOURCE_DELETE_FAILED: 'Unable to delete the source',
  KNOWLEDGE_SOURCE_DETAILS_FAILED: 'Unable to retrieve source details',
  KNOWLEDGE_SOURCE_UPDATE_FAILED: 'Unable to update the source',
  BUSINESS_RULES_FETCH_FAILED: 'Unable to retrieve business rules',
  BUSINESS_RULE_CANNOT_BE_EMPTY: 'Business rule cannot be empty',
  BUSINESS_RULE_CREATION_FAILED: 'Unable to create business rule',
  BUSINESS_RULE_UPDATE_FAILED: 'Unable to update business rule',
  BUSINESS_RULE_DELETION_FAILED: 'Unable to delete business rule',
  BUSINESS_RULE_TOGGLE_STATUS_FAILED: 'Unable to toggle business rule status',
  KNOWLEDGE_BLOCK_LIST_FAILED: 'Unable to retrieve knowledge blocks',
  KNOWLEDGE_BLOCK_CREATE_FAILED: 'Unable to create knowledge blocks',
  KNOWLEDGE_BLOCK_SET_VISIBILITY_FAILED: 'Unable to set visibility of knowledge block',
};
