/*
 * Copyright (c) 2024 LayerNext Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */
/**
 * @description Configuration file for the local disk storage.Used to export configuration to be used by other classes when setting up
 * the local disk storage
 * <AUTHOR>
 */
import dotenv from 'dotenv';
dotenv.config()
import { DEFAULT_DISK_STORAGE_BASE_PATH } from './constants';

export const DiskStorageConfiguration = {
  DISK_BASE_URL: process.env.NODEJS_BASE_URL,//The Base URL of the local disk storage
  DISK_BUCKET_NAME: process.env.DEFAULT_BUCKET_NAME, //the relative path to the folder that acts as the bucket 
  DISK_SECRET_KEY:  process.env.JWT_SECRET, //JWT secret key 
  DISK_BASE_PATH: process.env.DISK_STORAGE_BASE_PATH || DEFAULT_DISK_STORAGE_BASE_PATH  //Absolute base path of storage location (eg: /usr/src/app/buckets/)
}
