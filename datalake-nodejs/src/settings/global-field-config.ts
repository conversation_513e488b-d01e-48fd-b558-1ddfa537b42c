/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 */

import {IgnoredSearchType, QueryGroupOperators, QueryOperators} from '../models';
import {MetaDataKeyInputListFormat, MetaDataKeyInputType} from '../models/global-field-config.model';

const numberOperators = [
  QueryOperators.eq,
  QueryOperators.neq,
  QueryOperators.gt,
  QueryOperators.gte,
  QueryOperators.lt,
  QueryOperators.lte,
];
const stringOperators = [QueryOperators.eq, QueryOperators.neq, QueryOperators.reg];
const groupOperators = [QueryGroupOperators.dot];

export const TeamDefaultQueryOption: {
  key: string;
  isRootGroup: boolean;
  operators: QueryGroupOperators[] | QueryOperators[];
  values: string[] | number[];
  ignoredSearchType?: IgnoredSearchType;
}[] = [
  {
    key: 'metadata',
    isRootGroup: true,
    operators: groupOperators,
    values: ['name'],
  },
  {
    key: 'metadata',
    isRootGroup: true,
    operators: groupOperators,
    values: ['frameRate', 'frameCount'],
    ignoredSearchType: IgnoredSearchType.IMAGE_COLLECTION_SEARCH,
  },
  {
    key: 'metadata',
    isRootGroup: true,
    operators: groupOperators,
    values: ['resolution'],
  },
  {
    key: 'metadata.resolution',
    isRootGroup: false,
    operators: groupOperators,
    values: ['width', 'height'],
  },
  {
    key: 'metadata.name',
    isRootGroup: false,
    operators: stringOperators,
    values: [],
  },
  {
    key: 'metadata.frameRate',
    isRootGroup: false,
    operators: numberOperators,
    values: [5, 10, 15, 20, 25, 30, 60],
    ignoredSearchType: IgnoredSearchType.IMAGE_COLLECTION_SEARCH,
  },
  {
    key: 'metadata.frameCount',
    isRootGroup: false,
    operators: numberOperators,
    values: [100, 500, 1000],
    ignoredSearchType: IgnoredSearchType.IMAGE_COLLECTION_SEARCH,
  },
  {
    key: 'metadata.resolution.width',
    isRootGroup: false,
    operators: numberOperators,
    values: [640, 800, 1024, 1280, 1360, 1440, 1600, 1680, 1920, 2048, 2560, 3440, 3840],
  },
  {
    key: 'metadata.resolution.height',
    isRootGroup: false,
    operators: numberOperators,
    values: [360, 600, 720, 768, 800, 900, 1024, 1080, 1200, 1440, 1600, 2160],
  },
];

/**
 * Default metadata fields
 * Used in metadata update form suggestions
 */
export const DefaultMetaDataKeys: MetaDataKeyInputListFormat[] = [
  {
    fieldName: 'Collection Name',
    fieldKey: 'Collection Name',
    type: MetaDataKeyInputType.SELECT_OR_WRITE,
    isDeletable: false,
    isEditable: false,
    // --------------------------------- generate suggestions in code
  },
  {
    fieldName: 'Captured Location',
    fieldKey: 'Captured Location',
    type: MetaDataKeyInputType.TEXT_LINE,
    isDeletable: false,
    isEditable: false,
  },
  {
    fieldName: 'Camera Id',
    fieldKey: 'Camera Id',
    type: MetaDataKeyInputType.TEXT_LINE,
    isDeletable: false,
    isEditable: false,
  },
  {
    fieldName: 'Add Tags',
    fieldKey: 'Tags',
    type: MetaDataKeyInputType.TAG,
    isDeletable: false,
    isEditable: false,
    isTagDeletable: true,
    // --------------------------------- generate suggestions and values in code
  },
  {
    fieldName: 'Description',
    fieldKey: 'Description',
    type: MetaDataKeyInputType.TEXT_AREA,
    isDeletable: false,
    isEditable: false,
  },
];

export const AnalyticsDefaultQueryOption = {
  recall: [50, 75, 90, 100],
  precision: [50, 75, 90, 100],
  f1Score: [50, 75, 90, 100],
};

export const DEFAULT_ITEM_SORT_IN_COLLECTION = {sourceVideoId: 1, videoFrameIndex: 1, _id: 1};
export const DEFAULT_ITEM_SORT_IN_TRASH = {trashedAt: -1, _id: -1};

export enum OTPTypes {
  PERMANENT_DELETE = 1,
}
/**
 * comatible python sdk version list for each datalake versions
 *
 * {
 *    datalakeVerionId : string ex: '1.0.15'
 *   sdkVersionRange: {
 *    FROM: string ex: '1.0.10'
 *    TO: string ex: '1.0.12'
 *  }
 * }
 *
 */
export const COMPATIBLE_PYTHON_SDK_VERSIONS: {
  layerNextVersion: string;
  sdkVersionRange: {FROM: string; TO: string};
} = {
  layerNextVersion: '3.5.1',
  sdkVersionRange: {
    FROM: '3.3.0',
    TO: '3.3.0',
  },
};
