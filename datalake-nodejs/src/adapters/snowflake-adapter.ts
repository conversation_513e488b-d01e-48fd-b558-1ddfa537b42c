/**
 * @class SnowflakAdapter
 * Handles the initialization of the snowflake connection, connection establishment, and data retrieval operations.
 * @description This SnowflakeAdapter class is used to manage connections to Snowflake,, execute queries, and retrieve data. It implements the DataSourceAdapter interface to ensure compatibility with other data source adapters in the system.
 * <AUTHOR>
 */

import {DataSourceAdapter} from "./interfaces/data-source-adapter";
import snowflake from 'snowflake-sdk';
import {logger} from '../config'; // Assuming you have a logger set up
import {ConnectionSourceCredentials} from "../models/source.model";
import {HttpErrors} from "@loopback/rest";
import {DatalakeUserMessages} from "../settings/datalake.user.messages";
import {MAX_FETCH_DB_RECORD_LIMIT} from "../settings/constants";

export class SnowflakeAdapter implements DataSourceAdapter {
    private client: snowflake.Connection;

    constructor(private config: ConnectionSourceCredentials) {
        if (!this.config.account_id) {
            logger.error('Run sql query | SnowflakeAdapter.constructor | N/A | Account ID is required.');
            throw new Error('Run sql query | SnowflakeAdapter.constructor | N/A | Account ID is required.');
        }
        this.client = snowflake.createConnection({
            account: this.config.account_id,
            username: this.config.username,
            password: this.config.password,
            warehouse: this.config.warehouse,
            database: this.config.database,
            schema: this.config.schema,
            role: this.config.role
        });
    }

    async connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.client.connect((err, conn) => {
                if (err) {
                    logger.error(`Snowflake connection failed: ${err.message}`);
                    return reject(new Error(`Connection to Snowflake failed: ${err.message}`));
                }
                logger.info('Successfully connected to Snowflake.');
                resolve();
            });
        });
    }

    async disconnect(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.client.destroy((err) => {
                if (err) {
                    logger.error(`Failed to disconnect Snowflake: ${err.message}`);
                    return reject(new Error(`Disconnection from Snowflake failed: ${err.message}`));
                }
                logger.info('Successfully disconnected from Snowflake.');
                resolve();
            });
        });
    }

    async getData(properties: {function?: string; query?: string; collection?: string}): Promise<any> {
        const query = properties.query;
        if (!query) {
            throw new Error('Query must be provided.');
        }

        logger.info(`Executing query: ${query}`);

        try {
            const stmt: any = await new Promise((resolve, reject) => {
                const statement = this.client.execute({
                    sqlText: query,
                    streamResult: true,
                    complete: (err, stmt) => {
                        if (err) {
                            logger.error(`Snowflake Query execution failed: ${err.message}`);
                            return reject(new Error(err.message));
                        }
                        resolve(stmt);
                    },
                });
            });

            let rowCount = 0;
            const result: any[] = [];

            for await (const row of stmt.streamRows()) {
                result.push(row);
                rowCount++;

                if (rowCount > MAX_FETCH_DB_RECORD_LIMIT) {
                    logger.warn("Run SQL query | SnowflakeAdapter.getData | Too many records fetched");
                    throw new Error("The query returns over 500 records. Please refine your data retrieval strategy.");
                }
            }

            return result;
        } catch (error) {
            logger.error(`Run sql execute | SnowflakeAdapter.getData | N/A | Error while executing query, Error: `, error);
            throw new HttpErrors.NotAcceptable(`Snowflake Error: ${DatalakeUserMessages.EXECUTE_SNOWFLAKE_QUERY_FAILED} | ${error}`);
        }
    }

    async getSchema(): Promise<any> {
        // No use of this function so not implemented
        throw new Error('Method not implemented.');
    }
}
