/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * adapter class that use handle the request-response lifecycle for API to handle unified data connections
 */

/**
 * @class AdapterFactory
 * Handle the request related to the unified data connections
 * @description This AdapterFactory use for <PERSON><PERSON> to initiate and unified data connections
 * <AUTHOR>
 */
import {HttpErrors} from '@loopback/rest';
import {logger} from '../config';
import {ConnectionSourceCredentials, ConnectionSourceType} from '../models/source.model';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {MongoDBAdapter} from './mongodb-adapter';
import {MySQLAdapter} from './mysql-adapter';
import {SQLServerAdapter} from './mssql-adapter'
import {PostgreSQLAdapter} from './postgresql-adapter';
import {BigQueryAdapter} from './bigquery-adapter';
import {SnowflakeAdapter} from './snowflake-adapter';

export class AdapterFactory {
  /**
   * Use to get adapter or client of relevant connection
   * @param sourceType Enum - mongodb, mysql, s3
   * @param configurationObject data source configuration use to connect relevant data source
   * @returns adaptor or client of data source Connection
   */
  async getAdapter(sourceType: ConnectionSourceType, configurationObject: ConnectionSourceCredentials) {
    switch (sourceType) {
      case ConnectionSourceType.MONGO_DB:
        try {
          return new MongoDBAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating mongodb class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating mongodb class instance');
        }
      case ConnectionSourceType.MYSQL_DB:
        try {
          return new MySQLAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating mysql class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating mysql class instance');
        }
      // connection adapter for microsoft sql server
      case ConnectionSourceType.MSSQL_DB:
        try {
          return new SQLServerAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating mssql class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating mysql class instance');
        }
      // connection adapter for postgreSQL
      case ConnectionSourceType.POSTGRESQL:
        try {
          return new PostgreSQLAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating postgresql class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating postgresql class instance');
        }
      case ConnectionSourceType.AWS_S3:
      // return new S3Adapter();
      // Add other cases
      case ConnectionSourceType.BIGQUERY:
        try {
          return new BigQueryAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating bigquery class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating bigquery class instance');
        }
      case ConnectionSourceType.SNOWFLAKE:
        try {
          return new SnowflakeAdapter(configurationObject);
        } catch (err) {
          logger.error(
            `Add new data source connection | AdapterFactory.getAdapter | N/A | Error while creating snowflake class instance`,
            err,
          );
          throw new HttpErrors.NotAcceptable('Error while creating snowflake class instance');
        }
      default:
        logger.error(
          `Add new data source connection | AdapterFactory.getAdapter | N/A | Invalid source type: ${sourceType}`,
        );
        throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_SOURCE_TYPE);
    }
  }
}
