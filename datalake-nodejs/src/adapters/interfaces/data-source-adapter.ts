/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Interface used to follow up on API requests to relevant data sources such as MongoDB, MySQL, S3, etc.
 */

/**
 * @class DataSourceAdapter
 * @description This DataSourceAdapter use for Interface used to follow up on API requests to relevant data sources such as MongoDB, MySQL, S3, etc.
 * <AUTHOR>
 */
export interface DataSourceAdapter {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  getData(properties: {function?: string; query?: any; collection?: string}): Promise<any>;
  getSchema(): Promise<any>;
}
