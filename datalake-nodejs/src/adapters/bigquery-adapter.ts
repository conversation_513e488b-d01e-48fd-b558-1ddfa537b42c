/**
 * @class BigQueryAdapter
 * Handles the initialization of the BigQuery client, connection establishment, and data retrieval operations.
 * @description This BigQueryAdapter class is used to manage connections to Google BigQuery, execute queries, and retrieve data. It implements the DataSourceAdapter interface to ensure compatibility with other data source adapters in the system.
 * <AUTHOR>
 */

import {BigQuery} from '@google-cloud/bigquery';
import {HttpErrors} from '@loopback/rest';
import {logger} from '../config';
import {ConnectionSourceCredentials} from '../models/source.model';
import {DataSourceAdapter} from './interfaces/data-source-adapter';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DataBaseTimeOuts, MAX_FETCH_DB_RECORD_LIMIT} from '../settings/constants';

export class BigQueryAdapter implements DataSourceAdapter {
  private client: BigQuery;

  constructor(private config: ConnectionSourceCredentials) {
    // Initialize BigQuery client during instantiation
    this.client = new BigQuery({
      projectId: this.config.project_id,
      keyFilename: this.config.credentials_path,
    });
  }
  async connect(): Promise<void> {
    if (!this.client) {
      throw new Error('BigQuery client not initialized');
    } else {
      logger.info('Run sql execute | BigQueryAdapter.connect | N/A | Successfully connected to BigQuery');
    }

    return;
  }
  async disconnect(): Promise<void> {
    // Close the BigQuery client connection
    // (BigQuery doesn't require an explicit disconnect, so we just log here)
    logger.info('Disconnected from BigQuery');
  }
  async getData(properties: {function?: string; query?: any; collection?: string}): Promise<any> {
    const query = properties.query;
    if (!query) {
      throw new HttpErrors.NotAcceptable('Missing query or collection name');
    }
    logger.info(`Run sql execute | BigQueryAdapter.getData | N/A | Trying to retrieve data. query: ${query}`);
    try {
      const options = {
        query: query,
        defaultDataset: {
          projectId: this.config.project_id,
          datasetId: this.config.database,
        },
        jobTimeoutMs: DataBaseTimeOuts.SERVER_SIDE_TIMEOUT_MS,
      };
      const [job] = await this.client.createQueryJob(options);
      //Put limit to the result set as MAX_FETCH_DB_RECORD_LIMIT+1 and if more than MAX_FETCH_DB_RECORD_LIMIT returned, give an error instead of data
      const [rows] = await job.getQueryResults({maxResults: MAX_FETCH_DB_RECORD_LIMIT + 1, timeoutMs: DataBaseTimeOuts.CLIENT_SIDE_TIMEOUT_MS});
      // Throw an error if the result count exceeds the limit
      if (rows.length > MAX_FETCH_DB_RECORD_LIMIT) {
        logger.warn("Run sql query | BigQueryAdapter.getData | N/A | Too many records fetched");
        throw new Error(`Too many records fetched at once. Fetched ${rows.length} records. Please refine your data retrieval strategy.`);
      }
      return rows;
    } catch (err) {
      logger.error(`Run sql execute | BigQueryAdapter.getData | N/A | Error while executing query, Error: `, err);
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_SQL_SERVER_QUERY_FAILED} | ${err}`);
    }
  }

  async getSchema(): Promise<any> {
    // No use of this function so not implemented
    throw new Error('Method not implemented.');
  }
}
