/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Perform the mongodb related services like retrieve data, connect, disconnect...
 */

/**
 * @class MongoDBAdapter
 * @description purpose of this service is to Perform the mongodb related services like retrieve data, connect, disconnect...
 * <AUTHOR>
 */
import {HttpErrors} from '@loopback/rest';
import {EJSON} from 'bson';
import {Collection, Db, Document, MongoClient} from 'mongodb';
import {logger} from '../config';
import {ConnectionSourceCredentials} from '../models/source.model';
import {DataSourceAdapter} from './interfaces/data-source-adapter';
import { MAX_FETCH_DB_RECORD_LIMIT } from '../settings/constants';

export class MongoDBAdapter implements DataSourceAdapter {
  private client: MongoClient;
  private db: Db;

  constructor(private config: ConnectionSourceCredentials) {
    this.config = config;
    let uri = `mongodb://`;
    if (this.config.username && this.config.password) {
      uri += `${this.config.username}:${this.config.password}@`;
    }
    uri += `${this.config.host}`;
    if (this.config.port) {
      uri += `:${this.config.port}`;
    }
    if (this.config.username != 'admin') {
      if (this.config.database) {
        uri += `/${this.config.database}`;
      }
    }

    logger.info(
      `Add new data source connection | MongoDBAdapter.constructor | N/A | Trying to connect mongodb client. Connector URI: ${uri}`,
    );
    try {
      this.client = new MongoClient(uri);
    } catch (err) {
      logger.error(
        `Add new data source connection | MongoDBAdapter.constructor | N/A | Error while creating mongodb client, Error: `,
        err,
      );

      throw new HttpErrors.NotAcceptable('Error while creating mongodb client');
    }
    try {
      this.db = this.client.db(this.config.database);
    } catch (err) {
      logger.error(
        `Add new data source connection | MongoDBAdapter.constructor | N/A | Error while creating mongodb database, Error: `,
        err,
      );

      throw new HttpErrors.NotAcceptable('Error while creating mongodb database');
    }
    logger.info(
      `Add new data source connection | MongoDBAdapter.constructor | N/A | Successfully connect mongodb client`,
    );
  }

  /**
   * Use to connect mongodb client
   */
  async connect(): Promise<void> {
    await this.client.connect();
  }

  /**
   * Use to disconnect mongodb client
   */
  async disconnect(): Promise<void> {
    await this.client.close();
  }

  /**
   * Use to execute mongo db query
   * @param fn function like find, findOne, count, aggregate...
   * @param query this query may be array or object
   * @param collectionName mongodb collection name
   * @returns results of mongodb query
   */
  async getData(properties: {function?: string; query?: any; collection?: string}): Promise<any> {
    let fn = properties.function;
    let query = properties.query;
    let collectionName = properties.collection;
    if (!fn || !query || !collectionName) {
      throw new HttpErrors.NotAcceptable('Missing function, query or collection name');
    }
    logger.info(
      `Run mongodb execute | MongoDBAdapter.getData | N/A | Trying to retrieve data. function: ${fn}, query before convert: ${JSON.stringify(
        query,
      )}, collection: ${collectionName}`,
    );
    try {
      // query = EJSON.stringify(query);
      query = EJSON.parse(query);
    } catch (err) {
      logger.error(`Run mongodb execute | MongoDBAdapter.getData | N/A | Error while parsing query, Error: `, err);
      // throw new HttpErrors.NotAcceptable('Error while parsing query');
    }

    if (!this.db) {
      throw new HttpErrors.NotAcceptable('Not connected to MongoDB Database. Call connect() first.');
    }
    logger.info(
      `Run mongodb execute | MongoDBAdapter.getData | N/A | Trying to retrieve data. function: ${fn}, query: ${JSON.stringify(
        query,
      )}, collection: ${collectionName}`,
    );

    try {
      logger.info(`Run mongodb execute | MongoDBAdapter.getData | N/A | Trying to connect db collection...`);
      const collection: Collection<Document> = this.db.collection(collectionName);
      logger.info(`Run mongodb execute | MongoDBAdapter.getData | N/A | Successfully connect to db collection.`);
      // Use a type assertion to tell TypeScript to treat 'collection' as any
      const anyCollection: any = collection;
      logger.info(`Run mongodb execute | MongoDBAdapter.getData | N/A | Trying to execute db query`);
      // Check if the function exists on the collection
      if (typeof anyCollection[fn] === 'function') {
        // Execute the MongoDB function on the collection with the provided input
        const queryResult = anyCollection[fn](query);

        // Handle cursor for 'find' and similar operations
        if (queryResult && typeof queryResult.toArray === 'function') {
          let res = await queryResult.toArray();
          logger.info(
            `Run mongodb execute | MongoDBAdapter.getData | N/A | Successfully execute db query - queryResult.toArray:`,
            JSON.stringify(res),
          );

          //Prevent querying large number of records at once
          if (res.length > MAX_FETCH_DB_RECORD_LIMIT) {
            logger.warn(`Run mongodb execute | MongoDBAdapter.getData | N/A | Too many records fetched at once. Fetched ${res.length} records.`);
            throw new Error(`Run mongodb execute | MongoDBAdapter.getData | N/A | Too many records fetched at once. Fetched ${res.length} records.`);
          }
          return res;
        }

        let res = await queryResult;
        logger.info(
          `Run mongodb execute | MongoDBAdapter.getData | N/A | Successfully execute db query - queryResult:`,
          JSON.stringify(res),
        );
        return await res;
      } else {
        logger.error(`Run mongodb execute | MongoDBAdapter.getData | N/A | Invalid MongoDB function provided.`);
        throw new HttpErrors.NotAcceptable('Invalid MongoDB function provided.');
      }
    } catch (error) {
      let message = `${error.name} - ${error.message}`;
      logger.error(`Run mongodb execute | MongoDBAdapter.getData | N/A | MongoDB operation failed: `, error);
      throw new HttpErrors.NotAcceptable(`${message}`);
    }
  }

  async getSchema(): Promise<any> {
    // Implement schema retrieval logic
  }

  /**
 * Use to insert data into a MongoDB collection
 * @param collectionName - The name of the collection to insert data into
 * @param data - Array of documents to be inserted
 * @returns result of the insertion operation
 */
  async insertData(collectionName: string, data: any[]): Promise<any> {
    logger.info(
      `Run mongodb insert | MongoDBAdapter.insertData | N/A | Attempting to insert data. collection: ${collectionName}, data: ${JSON.stringify(data)}`
    );
    if (!this.db) {
      logger.error(`Run mongodb insert | MongoDBAdapter.insertData | N/A | Not connected to MongoDB Database.`);
      throw new HttpErrors.NotAcceptable('Not connected to MongoDB Database. Call connect() first.');
    }

    let parsedData: any[];


    // Parse data if it is a JSON string
    if (typeof data === 'string') {
      try {
        const parsed = EJSON.parse(data);
        if (Array.isArray(parsed)) {
          parsedData = parsed;
        } else if (parsed !== null && typeof parsed === 'object') {
          parsedData = [parsed];  // Wrap single object into an array
        } else {
          throw new Error('Parsed data is not an object or array.');
        }

        logger.info(
          `Run mongodb insert | MongoDBAdapter.insertData | N/A | Successfully parsed data: ${JSON.stringify(parsedData)}`
        );
      } catch (error) {
        logger.error(
          `Run mongodb insert | MongoDBAdapter.insertData | N/A | Failed to parse data: ${error.message}`,
          error
        );
        throw new HttpErrors.NotAcceptable('Failed to parse data: ' + error.message);
      }
    } else if (Array.isArray(data)) {
      parsedData = data;  // If data is already an array, assign it directly
    } else {
      throw new HttpErrors.NotAcceptable('Data provided is not a valid array or string.');
    }

    if (!collectionName || !parsedData || !Array.isArray(parsedData)) {
      logger.error(
        `Run mongodb insert | MongoDBAdapter.insertData | N/A | Invalid collection name or data for insertion.`
      );
      throw new HttpErrors.NotAcceptable('Invalid collection name or data for insertion.');
    }

    try {
      logger.info(
        `Run mongodb insert | MongoDBAdapter.insertData | N/A | Connecting to collection: ${collectionName}`
      );
      const collection: Collection<Document> = this.db.collection(collectionName);
      logger.info(
        `Run mongodb insert | MongoDBAdapter.insertData | N/A | Executing insert operation. collection: ${collectionName}, data: ${JSON.stringify(parsedData)}`
      );
      const result = await collection.insertMany(parsedData);
      logger.info(
        `Run mongodb insert | MongoDBAdapter.insertData | N/A | Successfully inserted data. result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      logger.error(`Failed to insert data into ${collectionName}: `, error);
      throw new HttpErrors.NotAcceptable(`Failed to insert data into ${collectionName}: ${error.message}`);
    }
  }

  /**
   * Use to delete data from a MongoDB collection
   * @param collectionName - The name of the collection from which data is to be deleted
   * @param filter - The filter criteria used to identify the records to delete
   * @returns result of the deletion operation
   */
  async deleteData(collectionName: string, filter: object): Promise<any> {
    logger.info(
      `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Attempting to delete data. collection: ${collectionName}, filter: ${JSON.stringify(filter)}`
    );
    if (!this.db) {
      logger.error(`Run mongodb delete | MongoDBAdapter.deleteData | N/A | Not connected to MongoDB Database.`);
      throw new HttpErrors.NotAcceptable('Not connected to MongoDB Database. Call connect() first.');
    }
    if (typeof filter == 'string') {
      try {
        filter = JSON.parse(filter);
        logger.info(
          `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Successfully parsed filter: ${JSON.stringify(filter)}`
        );
      } catch (error) {
        logger.error(
          `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Failed to parse filter: ${error.message}`,
          error
        );
        throw new HttpErrors.NotAcceptable('Failed to parse filter: ' + error.message);
      }
    }

    if (filter === null || filter === undefined) {
      logger.error(
        `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Invalid filter criteria for deletion.`
      );
      throw new HttpErrors.NotAcceptable('Filter is null or undefined.');
    }

    if (!collectionName || !filter || typeof filter !== 'object') {
      logger.error(
        `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Invalid collection name for deletion.`
      );
      throw new HttpErrors.NotAcceptable('Invalid collection name or filter criteria for deletion.');
    }

    try {
      logger.info(
        `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Connecting to collection: ${collectionName}`
      );
      const collection: Collection<Document> = this.db.collection(collectionName);
      const result = await collection.deleteMany(filter);
      logger.info(
        `Run mongodb delete | MongoDBAdapter.deleteData | N/A | Successfully deleted data. result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      logger.error(`Failed to delete data from ${collectionName}: `, error);
      throw new HttpErrors.NotAcceptable(`Failed to delete data from ${collectionName}: ${error.message}`);
    }
  }
}
