import {HttpErrors} from '@loopback/rest';
import {RowDataPacket} from 'mysql2';
import {createPool, Pool} from 'mysql2/promise';
import {logger} from '../config';
import {ConnectionSourceCredentials} from '../models/source.model';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DataSourceAdapter} from './interfaces/data-source-adapter';
import {DataBaseTimeOuts, MAX_FETCH_DB_RECORD_LIMIT} from '../settings/constants';

export class MySQLAdapter implements DataSourceAdapter {
  private pool: Pool;

  constructor(private config: ConnectionSourceCredentials) {
    this.config = config;
    try {
      this.pool = createPool({
        host: this.config.host,
        user: this.config.username,
        password: this.config.password,
        database: this.config.database,
        port: this.config.port,
      });
      logger.info('Add new data source connection | MySQLAdapter.constructor | N/A | MySQL connection pool created');
    } catch (err) {
      logger.error(
        'Add new data source connection | MySQLAdapter.constructor | N/A | Error while creating MySQL connection pool',
        err,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.MYSQL_POOL_CONNECTION_ERROR);
    }
  }

  async connect(): Promise<void> {
    // In MySQL with connection pools, explicit connect isn't necessary as connections are managed by the pool
    logger.info(
      'Add new data source connection | MySQLAdapter.constructor | N/A | MySQLAdapter connect called, but action is not necessary with connection pools',
    );

    return;
  }

  async disconnect(): Promise<void> {
    await this.pool.end();
    logger.info('Add new data source connection | MySQLAdapter.constructor | N/A | MySQL connection pool closed');
  }

  async getData(properties: {function?: string; query?: any; collection?: string}): Promise<any> {
    let query = properties.query;
    if (!query) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_MYSQL_QUERY);
    }
    logger.info('Add new data source connection | MySQLAdapter.constructor | N/A | Executing MySQL query', query);
    try {
      const [rows, fields] = await this.pool.query({
        sql: query,
        timeout: DataBaseTimeOuts.CLIENT_SIDE_TIMEOUT_MS,
      });
      const rowDataPackets = rows as RowDataPacket[];

      //Prevent querying large number of records at once
      if (Array.isArray(rowDataPackets) && rowDataPackets.length > MAX_FETCH_DB_RECORD_LIMIT) {
        logger.warn(`Run sql query | MySQLAdapter.getData | N/A | Too many records fetched at once. Fetched ${rowDataPackets.length} records.`);
        throw new Error(`Run sql query | MySQLAdapter.getData | N/A | Too many records fetched at once. Fetched ${rowDataPackets.length} records.`);
      }

      // Function to filter out unwanted keys
      function filterRow(row: RowDataPacket) {
        const filtered: {[key: string]: any} = {};
        for (const key in row) {
          if (!key.startsWith('_airbyte') && !key.startsWith('_ab_cdc')) {
            filtered[key] = row[key];
          }
        }
        return filtered;
      }

      // Apply the filter function to each row
      const filteredRows = rowDataPackets.map(filterRow);
      return filteredRows;
    } catch (err) {
      logger.error(
        'Add new data source connection | MySQLAdapter.constructor | N/A | Error executing MySQL query',
        err,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_MYSQL_QUERY_FAILED} | ${err}`);
    }
  }

  async getSchema(): Promise<any> {
    let schemaInfo: any;
    try {
      // Query to get table and column information
      const query = `
        SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ?
        ORDER BY TABLE_NAME, ORDINAL_POSITION
      `;

      const [rows] = await this.pool.query(query);

      const rowDataPackets = rows as RowDataPacket[];

      // Format and display the schema information
      schemaInfo = rowDataPackets.reduce((acc: any, row: any) => {
        const {TABLE_NAME, COLUMN_NAME, DATA_TYPE} = row;
        if (!acc[TABLE_NAME]) {
          acc[TABLE_NAME] = [];
        }
        acc[TABLE_NAME].push({COLUMN_NAME, DATA_TYPE});
        return acc;
      }, {});
    } catch (error) {
      console.error('Error fetching schema:', error);
    } finally {
      await this.pool.end();
    }

    return schemaInfo;
  }
}
