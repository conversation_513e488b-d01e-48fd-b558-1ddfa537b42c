/**
 * @class SQLServerAdapter
 * Handles sql server data source connections and operations
 * @description This SQLServerAdapter class is used to manage connections to sql server databases, and retrieve data.
 * <AUTHOR>
 */
import {HttpErrors} from '@loopback/rest';
import {ConnectionPool, Request} from 'mssql';
import {logger} from '../config';
import {ConnectionSourceCredentials} from '../models/source.model';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DataSourceAdapter} from './interfaces/data-source-adapter';
import {DataBaseTimeOuts, MAX_FETCH_DB_RECORD_LIMIT} from '../settings/constants';

export class SQLServerAdapter implements DataSourceAdapter {
  private pool: ConnectionPool;

  constructor(private config: ConnectionSourceCredentials) {
    this.config = config;
    if (!this.config.host) {
      logger.error('Run SQL query | SQLServerAdapter.constructor | N/A | Missing host parameter.');
      throw new Error('Run SQL query | SQLServerAdapter.constructor | N/A | Host is required.');
    }

    try {
      const poolConfig: any = {
        server: this.config.host,
        database: this.config.database,
        port: this.config.port,
        options: {
          encrypt: true,
          trustServerCertificate: true,
        },
        pool: {
          max: 10,
          min: 4,
        },
        requestTimeout: DataBaseTimeOuts.CLIENT_SIDE_TIMEOUT_MS
      };

      if (this.config.is_windows_authentication) {
        poolConfig.authentication = {
          type: 'ntlm',
          options: {
            domain: this.config.domain,
            userName: this.config.username,
            password: this.config.password
          }
        };
        logger.info('Using Windows Authentication (NTLM) for SQL Server connection.');
      } else {
        poolConfig.user = this.config.username;
        poolConfig.password = this.config.password;
        logger.info('Using SQL Server Authentication (Username/Password) for SQL Server connection.');
      }

      this.pool = new ConnectionPool(poolConfig);

      logger.info(
        'SQL Server connection pool successfully created.',
      );
    } catch (err) {
      logger.error(
        'Error occurred while creating SQL Server connection pool.',
        err,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.SQL_SERVER_POOL_CONNECTION_ERROR);
    }
  }


  /**
   * Connects to the SQL server database using the connection pool.
   * @returns Promise<void>
   */

  async connect(): Promise<void> {
    try {
      await this.pool.connect();
      logger.info('Add new data source connection | SQLServerAdapter.connect | N/A | SQL Server connected');
    } catch (err) {
      logger.error(
        'Add new data source connection | SQLServerAdapter.connect | N/A | Error connecting to SQL Server',
        err,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.SQL_SERVER_CONNECTION_ERROR);
    }
  }

  /**
   * Closes the SQL server  connection pool.
   * @returns Promise<void>
   */

  async disconnect(): Promise<void> {
    await this.pool.close();
    logger.info(
      'Add new data source connection | SQLServerAdapter.disconnect | N/A | SQL Server connection pool closed',
    );
  }

  /**
   * Executes a query on the sql server database and retrieves data.
   * @param properties Object - Contains the query to be executed
   * @returns Promise<any> - The result of the query execution
   */

  async getData(properties: {function?: string; query?: any; collection?: string}): Promise<any> {
    let query = properties.query;
    if (!query) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_SQL_SERVER_QUERY);
    }
    logger.info('MS SQL Server Adapter | SQLServerAdapter.getData | N/A | Executing SQL Server query:\n', query + '\n --------------------------------- ');
    try {
      // Use streaming mode to handle large result sets
      let isUseStream = false;
      if (isUseStream) {
        const resultRows: any[] = [];
        let rowCountReturnedSoFar = 0;
        const request = new Request(this.pool);
        request.stream = true
        const results = request.query(query);
        request.on('error', (err: any) => {
          logger.error('Run sql query | SQLServerAdapter.getData | N/A | Error while executing SQL Server query', err);
          throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_SQL_SERVER_QUERY_FAILED} | ${err}`);
        })
        request.on('row', (result: any) => {
          rowCountReturnedSoFar++;
          resultRows.push(result);
          if (rowCountReturnedSoFar >= MAX_FETCH_DB_RECORD_LIMIT) {
            logger.debug(`Run sql query | SQLServerAdapter.getData | N/A | ${rowCountReturnedSoFar} rows returned so far`);
            request.cancel();
          }
        })
        request.on('error', (err: any) => {
          logger.error('Run sql query | SQLServerAdapter.getData | N/A | Error while executing SQL Server query', err);
          throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_SQL_SERVER_QUERY_FAILED} | ${err}`);

        })
        request.on('done', (result: any) => {
          logger.debug(`Run sql query | SQLServerAdapter.getData - Done | N/A | ${rowCountReturnedSoFar} rows returned `);
          return resultRows;
        })
      }
      else {
        const request = new Request(this.pool);
        // Kelum: 17/04/2025 - Now we don't need to put limit here as its already handled by LLM backend
        //// Replace all 'SELECT' with 'SELECT TOP 501' to limit the number of rows returned - Do this only if the query doesn't have TOP clause or OFFSET clause
        //if (!query.toLowerCase().includes('top') && !query.toLowerCase().includes('offset')) {
          //query = query.replace('SELECT', 'SELECT TOP ' + String(MAX_FETCH_DB_RECORD_LIMIT + 1));
        //}
        const result = await request.query(query);
        const rows = result.recordset;
        //// Throw an error if the result count exceeds the limit
        //if (rows && rows.length > MAX_FETCH_DB_RECORD_LIMIT) {
        //  logger.warn("Run sql query | SQLServerAdapter.getData | N/A | Too many records fetched");
        //  throw new Error("The query returns over " + String(MAX_FETCH_DB_RECORD_LIMIT) + " records. Please refine your data retrieval strategy.");
        //}

        return rows;
      }

    } catch (err) {
      logger.error(
        'Add new data source connection | SQLServerAdapter.getData | N/A | Error executing SQL Server query',
        err,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_SQL_SERVER_QUERY_FAILED} | ${err}`);
    }
  }

  async getSchema(): Promise<any> { }
}
