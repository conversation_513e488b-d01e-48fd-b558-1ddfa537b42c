/**
 * @class PostgreSQLAdapter
 * Handles PostgreSQL data source connections and operations
 * @description This PostgreSQLAdapter class is used to manage connections to PostgreSQL databases, and retrieve data.
 * <AUTHOR>
 */
import {HttpErrors} from '@loopback/rest';
import {Pool} from 'pg';
import {logger} from '../config';
import {ConnectionSourceCredentials} from '../models/source.model';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {DataSourceAdapter} from './interfaces/data-source-adapter';
import {DataBaseTimeOuts, MAX_FETCH_DB_RECORD_LIMIT} from '../settings/constants';

export class PostgreSQLAdapter implements DataSourceAdapter {
  private pool: Pool;

  constructor(private config: ConnectionSourceCredentials) {
    this.config = config;
    try {
      this.pool = new Pool({
        user: this.config.username,
        host: this.config.host,
        database: this.config.database,
        password: this.config.password,
        port: this.config.port,
        ssl: {
          rejectUnauthorized: false // Change based on your SSL requirements
        },
        query_timeout: DataBaseTimeOuts.CLIENT_SIDE_TIMEOUT_MS,
        statement_timeout: DataBaseTimeOuts.SERVER_SIDE_TIMEOUT_MS
      });
      logger.info('Add new data source connection | PostgreSQLAdapter.constructor | N/A | PostgreSQL connection pool created');
    } catch (err) {
      logger.error(
        'Add new data source connection | PostgreSQLAdapter.constructor | N/A | Error while creating PostgreSQL connection pool',
        err,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.POSTGRESQL_POOL_CONNECTION_ERROR);
    }
  }

  /**
   * Connects to the PostgreSQL database using the connection pool.
   * @returns Promise<void>
   */
  async connect(): Promise<void> {
    try {
      // await this.pool.connect();    // manully creating connection is not neccessary. (If this was manully created, then pool.end will getting stuck)
      logger.info('Add new data source connection | PostgreSQLAdapter.connect | N/A | PostgreSQL connected');
    } catch (err) {
      logger.error('Add new data source connection | PostgreSQLAdapter.connect | N/A | Error connecting to PostgreSQL', err);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.POSTGRESQL_CONNECTION_ERROR);
    }
  }

  /**
   * Closes the PostgreSQL connection pool.
   * @returns Promise<void>
   */

  async disconnect(): Promise<void> {
    await this.pool.end();
    logger.info('Add new data source connection | PostgreSQLAdapter.disconnect | N/A | PostgreSQL connection pool closed');
  }

  /**
 * Executes a query on the PostgreSQL database and retrieves data.
 * @param properties Object - Contains the query to be executed
 * @returns Promise<any> - The result of the query execution
 */

  async getData(properties: {function?: string; query?: any; collection?: string}): Promise<any> {
    let query = properties.query;
    if (!query) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_POSTGRESQL_QUERY);
    }
    logger.info('Add new data source connection | PostgreSQLAdapter.getData | N/A | Executing PostgreSQL query', query);
    try {
      const result = await this.pool.query(query);
      const rows = result.rows;

      //Prevent querying large number of records at once
      if (Array.isArray(rows) && rows.length > MAX_FETCH_DB_RECORD_LIMIT) {
        logger.warn(`Run sql query | PostgreSQLAdapter.getData | N/A | Too many records fetched at once. Fetched ${rows.length} records.`);
        throw new Error(`Run sql query | PostgreSQLAdapter.getData | N/A | Too many records fetched at once. Fetched ${rows.length} records.`);
      }

      // Function to filter out unwanted keys
      function filterRow(row: any) {
        const filtered: {[key: string]: any} = {};
        for (const key in row) {
          if (!key.startsWith('_airbyte') && !key.startsWith('_ab_cdc')) {
            filtered[key] = row[key];
          }
        }
        return filtered;
      }

      // Apply the filter function to each row
      const filteredRows = rows.map(filterRow);
      return filteredRows;
    } catch (err) {
      logger.error(
        'Add new data source connection | PostgreSQLAdapter.getData | N/A | Error executing PostgreSQL query',
        err,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.EXECUTE_POSTGRESQL_QUERY_FAILED} | ${err}`);
    }
  }

  async getSchema(): Promise<any> {

  }
}
