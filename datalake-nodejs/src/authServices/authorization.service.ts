import {
    AuthorizationContext,
    AuthorizationDecision,
    AuthorizationMetadata,
    Authorizer
  } from '@loopback/authorization/';
  import {Binding<PERSON><PERSON>, Provider} from '@loopback/core';
  import {logger} from '../config';
  import {getUserTypeString} from '../settings/tools';
  
  export class MyAuthorizationProvider implements Provider<Authorizer> {
    constructor() { }
  
    /**
     * @returns authenticateFn
     */
    value(): Authorizer {
      return this.authorize.bind(this);
    }
  
    async authorize(
      authorizationCtx: AuthorizationContext,
      metadata: AuthorizationMetadata,
    ) {
  
      let status = AuthorizationDecision.ALLOW
      //console.log('authorization provider not getting called ')
      const clientRole = getUserTypeString(
        (authorizationCtx.principals[0].userType || authorizationCtx.principals[0].userType == 0) ? authorizationCtx.principals[0].userType : -1
      );
      const allowedRoles = metadata.allowedRoles;
      const deniedRoles = metadata.deniedRoles;
  
      if (clientRole == "Unknown") {
        status = AuthorizationDecision.DENY
        logger.info(
          `MyAuthorizationProvider | MyAuthorizationProvider.authorize | ${JSON.stringify(authorizationCtx.principals[0])} | Access denied`
        )
        return status
      }
  
  
      if (
        (deniedRoles && Array.isArray(deniedRoles) && deniedRoles.length > 0) &&
        (allowedRoles && Array.isArray(allowedRoles) && allowedRoles.length > 0)
      ) {
        let accessDenied = deniedRoles.includes(clientRole.toString())
        let accessAllowed = allowedRoles.includes(clientRole.toString())
  
        if (accessDenied) status = AuthorizationDecision.DENY
        else if (accessAllowed) status = AuthorizationDecision.ALLOW
        else status = AuthorizationDecision.DENY
      }
      if (deniedRoles && Array.isArray(deniedRoles) && deniedRoles.length > 0) {
        status = deniedRoles.includes(clientRole.toString())
          ? AuthorizationDecision.DENY
          : AuthorizationDecision.ALLOW;
      }
      if (allowedRoles && Array.isArray(allowedRoles) && allowedRoles.length > 0) {
        status = allowedRoles.includes(clientRole.toString())
          ? AuthorizationDecision.ALLOW
          : AuthorizationDecision.DENY;
      }
  
      if (status == AuthorizationDecision.DENY) {
        logger.info(
          `MyAuthorizationProvider | MyAuthorizationProvider.authorize | ${JSON.stringify(authorizationCtx.principals[0])} | Access denied`
        )
      }
      return status
    }
  }
  export const SYSTEM_META_SERVICE = BindingKey.create<MyAuthorizationProvider>(
    'authorizationProviders.my-authorizer-provider',
  );