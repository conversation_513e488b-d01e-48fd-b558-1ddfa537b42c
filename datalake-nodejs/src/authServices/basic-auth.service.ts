/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Basic Authentication  Service
 */

/**
 * @class BasicAuthService
 * Basic Authentication Service
 * @description  use to authenticate the basic authentication header
 * <AUTHOR>
 */
import {BindingKey} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import Axios from 'axios';
import dotenv from 'dotenv';
import {logger} from '../config';
import {SystemDataRepository} from '../repositories';
import {ApiKeyRepository} from '../repositories/api-key.repository';
import {FLOWS} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
var base64 = require('base-64');
var utf8 = require('utf8');
dotenv.config();

const BASIC_AUTH_VALIDATION_URL = `${process.env.SSO_INTERNAL_SERVER}/internal/basicAuth/validate`;
const GET_USER_LIST_URL = `${process.env.SSO_INTERNAL_SERVER}/internal/getUserList`;

export class BasicAuthService {
  constructor(
    @repository(ApiKeyRepository)
    private apiKeyRepository: ApiKeyRepository,
    @repository(SystemDataRepository)
    private systemDataRepository: SystemDataRepository,
  ) {}

  /**
   * Use to authenticate basic authorization string with base 64 encoded
   * @param basicAuth {string} basic authorization string with base 64 encoded
   * @returns api key details
   */
  async basicAuthentication(basicAuth?: string) {
    //check the authentication key and secret valid or not
    try {
      let details = {
        basicAuthCode: basicAuth,
      };
      let url = BASIC_AUTH_VALIDATION_URL;

      let response: any = {};
      response = await Axios({
        url,
        method: 'POST',
        data: details,
      });

      let returnData: {
        id: string;
        key: string;
        secret: string;
        teamId: string;
        name: string;
        userId: string;
        userName: string;
        userType: number;
        email: string;
        type: number;
      } = response.data;
      let isApiKeyExists = await this.apiKeyRepository.findOne({
        where: {
          key: returnData.key,
          teamId: returnData.teamId,
        },
      });
      if (!isApiKeyExists) {
        let lastApiKeyObj = await this.apiKeyRepository.findOne({
          where: {
            teamId: returnData.teamId,
          },
          order: ['lastSyncTimestamp DESC'],
        });
        if (lastApiKeyObj) {
          await this.apiKeyRepository.create({
            key: returnData.key,
            teamId: returnData.teamId,
            apiConfigs: lastApiKeyObj.apiConfigs,
            lastSyncTimestamp: lastApiKeyObj.lastSyncTimestamp ? lastApiKeyObj.lastSyncTimestamp : new Date(),
            isProcessingLocked: false,
          });
        } else {
          let systemData = await this.systemDataRepository.findOne({where: {teamId: returnData.teamId}});
          if (!systemData) {
            logger.error(`BasicAuthService | basicAuthentication | Error : 'team doesn't exists on system data: `);
            throw new HttpErrors.Unauthorized(`Error : 'team doesn't exists on system data`);
          }
          await this.apiKeyRepository.create({
            key: returnData.key,
            teamId: returnData.teamId,
            apiConfigs: systemData.apiConfigs,
            isProcessingLocked: false,
          });
        }
      }
      return returnData;
    } catch (err) {
      if (err && err.response && err.response.status === 401) {
        logger.warn(
          `basic authentication failed with error: 401 - unauthorized, url: ${BASIC_AUTH_VALIDATION_URL}, basicAuth: ${basicAuth}, error: ${err}`,
        );
      } else {
        logger.error('basic authentication failed with error: ', err);
      }
      throw new HttpErrors.Unauthorized(`Error : 'Basic Authentication failed with ${err}`);
    }
  }

  /**
   * Need a key to dequeue file uploads. Since FE users doesn't use keys, save userId as key
   * @param userId {string} id of the user
   * @returns key secret details
   */
  async insertUserIdAsKeySecretPairFroFrontendUploads(userId: string, teamId: string): Promise<{key: string}> {
    try {
      let apiKey = 'key_' + userId.toString();
      if (apiKey) {
        let isApiKeyExists = await this.apiKeyRepository.findOne({
          where: {
            key: apiKey,
            teamId: teamId,
          },
        });
        if (!isApiKeyExists) {
          let lastApiKeyObj = await this.apiKeyRepository.findOne({
            where: {
              teamId: teamId,
            },
            order: ['lastSyncTimestamp DESC'],
          });
          if (lastApiKeyObj) {
            await this.apiKeyRepository.create({
              key: apiKey,
              teamId: teamId,
              apiConfigs: lastApiKeyObj.apiConfigs,
              lastSyncTimestamp: lastApiKeyObj.lastSyncTimestamp ? lastApiKeyObj.lastSyncTimestamp : new Date(),
              isProcessingLocked: false,
            });
          } else {
            let systemData = await this.systemDataRepository.findOne({where: {teamId: teamId}});
            if (!systemData) {
              logger.error(
                `BasicAuthService | BasicAuthService.insertUserIdAsKeySecretPairFroFrontendUploads | Error : 'team doesn't exist on system`,
              );
              throw new HttpErrors.UnprocessableEntity(`${DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST}`);
            }
            await this.apiKeyRepository.create({
              key: apiKey,
              teamId: teamId,
              apiConfigs: systemData.apiConfigs,
              isProcessingLocked: false,
            });
          }
        }

        return {key: apiKey};
      } else {
        logger.error(
          `${FLOWS.FILE_UPLOAD} | BasicAuthService.insertUserIdAsKeySecretPairFroFrontendUploads | ${userId} | Unable to get userId to save as upload key`,
        );
        throw new HttpErrors.UnprocessableEntity(`${DatalakeUserMessages.UPLOAD_FAILED}`);
      }
    } catch (err) {
      logger.error(
        `${FLOWS.FILE_UPLOAD} | BasicAuthService.insertUserIdAsKeySecretPairFroFrontendUploads | ${userId} | Failed to save userId as upload key`,
      );

      throw new HttpErrors.UnprocessableEntity(`${DatalakeUserMessages.UPLOAD_FAILED}`);
    }
  }

  /**
   * Use to get user list
   * @param teamId {string} id of the team
   * @returns key secret details
   */
  async getUserList(teamId: string, userTypes: number[], userIdList: string[]) {
    try {
      let details = {
        teamId: teamId,
        userTypes: userTypes,
        userIdList: userIdList,
      };
      let url = GET_USER_LIST_URL;

      let response: any = {};
      response = await Axios({
        url,
        method: 'POST',
        data: details,
      });

      if (!response || !response.data) {
        throw new HttpErrors.Unauthorized(`Error : 'Basic Authentication failed`);
      }

      let userList: {
        id: string;
        name: string;
        email: string;
        userType: number;
      }[] = response.data;

      return userList;
    } catch (err) {
      logger.error(
        `Get user list | BasicAuthService.getUserList | ${teamId} | basic authentication failed with error: `,
        err,
      );

      throw new HttpErrors.Unauthorized(`Error : 'Basic Authentication failed with ${err}`);
    }
  }
}
export const BASIC_AUTH_SERVICE = BindingKey.create<BasicAuthService>('service.basicAuth');
