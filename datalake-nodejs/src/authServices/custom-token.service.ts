/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * custom token generation services
 */

/**
 * @class JWTService
 * User custom token generation service
 * @description  use to generate custome parameter included jwt token for authorization
 * <AUTHOR>
 */

import {TokenService} from '@loopback/authentication';
import {inject} from '@loopback/context';
import {HttpErrors} from '@loopback/rest';
import {securityId, UserProfile} from '@loopback/security';
import {promisify} from 'util';
import {logger} from '../config';
import {TokenServiceBindings} from '../keys';

const jwt = require('jsonwebtoken');
const signAsync = promisify(jwt.sign);
const verifyAsync = promisify(jwt.verify);

export class JWTService implements TokenService {
  constructor(
    @inject(TokenServiceBindings.TOKEN_SECRET)
    private jwtSecret: string,
    @inject(TokenServiceBindings.TOKEN_EXPIRES_IN)
    private jwtExpiresIn: string,
  ) { }

  /**
   * Use to verify the custom jwt token
   * @param token jst token
   * @returns user details
   */
  async verifyToken(token: string): Promise<UserProfileDetailed> {
    if (!token) {
      throw new HttpErrors.Unauthorized(
        `Error verifying token : 'token' is null`,
      );
    }

    let userProfile: UserProfileDetailed;

    try {
      // decode user profile from token
      const decodedToken = await verifyAsync(token, this.jwtSecret) as UserProfileDetailed

      userProfile = {
        [securityId]: decodedToken[securityId] ?? decodedToken.id
      }
      userProfile.id = decodedToken.id
      userProfile.name = decodedToken.name
      userProfile.userType = decodedToken.userType
      userProfile.email = decodedToken.email
      userProfile.teamId = decodedToken.teamId

    } catch (error: any) {
      logger.error(`JWTService | JWTService.verifyToken | ${token} | verify failed with error: ${error}`)
      throw new HttpErrors.Unauthorized(
        `Error verifying token : ${error.message}`,
      );
    }
    //User type access control
    // if (!userProfile.userType && userProfile.userType != 0) {
    //   throw new HttpErrors.Unauthorized(
    //     `User Type doesn't exists`,
    //   );
    // } else if (!AccessibleUsers.includes(userProfile.userType)) {
    //   throw new HttpErrors.Unauthorized(
    //     `User Type doesn't have access to this app`,
    //   );
    // }
    return userProfile;
  }


  /**
   * Use to generate custom jwt token
   * @param userProfile custome user details
   * @returns generated custom jwt token
   */
  async generateToken(userProfile: UserProfileDetailed): Promise<string> {
    if (!userProfile) {
      throw new HttpErrors.Unauthorized(
        'Error generating token : userProfile is null',
      );
    }
    const userInfoForToken = {
      [securityId]: userProfile[securityId] || userProfile.id,
      id: userProfile[securityId] || userProfile.id,
      name: userProfile.name,
      userType: userProfile.userType,
      email: userProfile.email,
      teamId: userProfile.teamId
    };
    //console.log('encode user', userInfoForToken)
    // Generate a JSON Web Token
    let token: string;
    try {
      token = await signAsync(userInfoForToken, this.jwtSecret, {
        expiresIn: Number(this.jwtExpiresIn),
      });
    } catch (error) {
      throw new HttpErrors.Unauthorized(`Error encoding token : ${error}`);
    }

    return token;
  }
}


/**
 * Extended user profile for jwt generation
 */
export interface UserProfileDetailed extends UserProfile {
  id?: string;
  userType?: number;
  email?: string;
  name?: string;
  teamId?: string;
}
