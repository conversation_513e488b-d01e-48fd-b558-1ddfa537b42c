/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * OTP Authentication  Service
 */

/**
 * @class OTPAuthService
 * OTP Authentication Service
 * @description  use to authenticate the OTP authentication header
 * <AUTHOR>
 */
import {BindingKey} from "@loopback/core";
import {HttpErrors} from '@loopback/rest';
import Axios from 'axios';
import dotenv from 'dotenv';
import {logger} from '../config';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {OTPTypes} from '../settings/global-field-config';
import {UserProfileDetailed} from './custom-token.service';
dotenv.config();

const OTP_AUTH_VALIDATION_URL = `${process.env.SSO_INTERNAL_SERVER}/internal/otpAuth/validate`;
const OTP_AUTH_SEND_URL = `${process.env.SSO_INTERNAL_SERVER}/internal/otpAuth/send`;


export class OTPAuthService {
  constructor(
  ) { }

  /**
   * Use to send otp email to user
   * @param details {object} details of the otp
   * @param currentUserProfile {UserProfileDetailed} user details
   * @returns {success: boolean}
   */
  async sendOTP(currentUserProfile: UserProfileDetailed, type: number) {


    try {
      let details = {
        userId: currentUserProfile.id,
        email: currentUserProfile.email,
        teamId: currentUserProfile.teamId
      }
      let url = OTP_AUTH_SEND_URL

      let response: any = {}
      response = await Axios({
        url,
        method: 'POST',
        data: details,
        params: {
          type: type
        }
      });

      logger.info(
        `OTPAuthService | OTPAuthService.sendOTP | ${JSON.stringify(currentUserProfile)} | OTP Send success type: ${type}`
      )
      return response.data
    } catch (err) {
      logger.error(
        `OTPAuthService | OTPAuthService.sendOTP | ${JSON.stringify(currentUserProfile)} | OTP Send failed type: ${type} error: ${err}`
      )
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OTP_SEND_FAILED)
    }




  }


  /**
  * Use to validate otp of user
  * @param details {object} details of the otp
  * @param currentUserProfile {UserProfileDetailed} user details
  * @returns {success: boolean}
  */
  async validateOTP(otp: string, type: OTPTypes, currentUserProfile: UserProfileDetailed) {
    try {
      let details = {
        userId: currentUserProfile.id,
        email: currentUserProfile.email,
        teamId: currentUserProfile.teamId,
        otp: otp
      }
      let url = OTP_AUTH_VALIDATION_URL

      let response: any = {}
      response = await Axios({
        url,
        method: 'POST',
        data: details,
        params: {
          type: type
        }
      });

      logger.info(
        `OTPAuthService | OTPAuthService.validateOTP | ${JSON.stringify(currentUserProfile)} | OTP validate success type: ${type} otp: ${otp}`
      )
      return response.data
    } catch (err) {

      logger.error(
        `OTPAuthService | OTPAuthService.validateOTP | ${JSON.stringify(currentUserProfile)} | OTP validate failed type: ${type} otp: ${otp} error: ${err}`
      )
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.OTP_VALIDATION_FAILED)
    }
  }




} export const OTP_AUTH_SERVICE = BindingKey.create<OTPAuthService>('service.otpAuth');
