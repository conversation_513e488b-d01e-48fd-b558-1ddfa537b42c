/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * custom refresh token generation services
 */

/**
 * @class RefreshService
 * User custom refresh token generation service
 * @description  use to generate custom parameter included refresh jwt token for authorization
 * <AUTHOR>
 */
import {TokenService} from '@loopback/authentication';
import {RefreshTokenService, TokenObject, TokenServiceBindings} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {securityId} from '@loopback/security';
import {promisify} from 'util';
import {logger} from '../config';
import {RefreshTokenServiceBindings} from '../keys';
import {UserProfileDetailed} from './custom-token.service';

const jwt = require('jsonwebtoken');
const signAsync = promisify(jwt.sign);
const verifyAsync = promisify(jwt.verify);

export class RefreshService implements RefreshTokenService {
  constructor(
    @inject(RefreshTokenServiceBindings.REFRESH_SECRET)
    private jwtRefreshSecret: string,
    @inject(RefreshTokenServiceBindings.REFRESH_EXPIRES_IN)
    private jwtRefreshExpiresIn: string,
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(TokenServiceBindings.TOKEN_EXPIRES_IN)
    public jwtExpiresIn: string,
  ) {}

  /**
   * Use to generate token using refresh token
   * @param refreshToken refresh token
   * @returns jwt token
   */
  async refreshToken(refreshToken: string): Promise<TokenObject> {
    if (!refreshToken) {
      throw new HttpErrors.Unauthorized(`Error verifying token : 'refreshToken' is null`);
    }

    let userProfile: UserProfileDetailed;

    try {
      // decode user profile from token
      const decodedToken = (await verifyAsync(refreshToken, this.jwtRefreshSecret)) as UserProfileDetailed;

      userProfile = {
        [securityId]: decodedToken[securityId] ?? decodedToken.id,
      };
      userProfile.id = decodedToken.id;
      userProfile.name = decodedToken.name;
      userProfile.userType = decodedToken.userType;
      userProfile.email = decodedToken.email;
      userProfile.teamId = decodedToken.teamId;
      logger.info(`RefreshService | RefreshService.refreshToken | ${refreshToken} | refresh verify success`);
      logger.debug('user', userProfile);
    } catch (error: any) {
      logger.error(
        `RefreshService | RefreshService.refreshToken | ${refreshToken} | refresh failed with error: ${error}`,
      );
      throw new HttpErrors.Unauthorized(`Error verifying token : ${error.message}`);
    }

    const newaccessToken = await this.jwtService.generateToken(userProfile);
    const newRefreshToken = await this.generateToken(userProfile, newaccessToken);

    let tokenObj: TokenObject = {
      accessToken: newaccessToken,
      expiresIn: this.jwtExpiresIn,
      refreshToken: newRefreshToken.refreshToken,
    };
    logger.info(`RefreshService | RefreshService.refreshToken | ${refreshToken} | ${tokenObj}`);
    return tokenObj;
  }

  /**
   * Use to generate custom jwt refreshtoken
   * @param userProfile custom user details
   * @param token jwt token
   * @returns generated custom jwt refreshtoken token
   */
  async generateToken(userProfile: UserProfileDetailed, token: string): Promise<TokenObject> {
    if (!userProfile) {
      throw new HttpErrors.Unauthorized('Error generating token : userProfile is null');
    }

    const userInfoForToken = {
      [securityId]: userProfile[securityId] || userProfile.id,
      id: userProfile[securityId] || userProfile.id,
      name: userProfile.name,
      userType: userProfile.userType,
      email: userProfile.email,
      teamId: userProfile.teamId,
      accessToken: token,
    };

    let refreshToken: string;
    try {
      refreshToken = await signAsync(userInfoForToken, this.jwtRefreshSecret, {
        expiresIn: Number(this.jwtRefreshExpiresIn),
      });
    } catch (error) {
      throw new HttpErrors.Unauthorized(`Error encoding token : ${error}`);
    }

    let tokenObj: TokenObject = {
      accessToken: token,
      expiresIn: this.jwtRefreshExpiresIn,
      refreshToken: refreshToken,
    };
    return tokenObj;
  }
}
