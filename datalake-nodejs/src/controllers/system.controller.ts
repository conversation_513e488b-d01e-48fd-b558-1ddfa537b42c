/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta-data model
 */

/**
 * @class SystemController
 * Handle the request related to the system Controller for expose internal enpoints to internal server api calls
 * @description This controller use for Handle the request related to the system controller
 * <AUTHOR> channa
 */

import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, HttpErrors, param, post, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import multer from 'multer';
import {logger} from '../config';
import {FileInterface, LabelInfoCreateRequest, LabelInfoEditRequest, SystemLabel} from '../models';
import {
  MetaDataRepository,
  MetaDataUpdateRepository,
  QueryOptionRepository,
  SystemChangeRepository,
  SystemDataRepository,
  SystemLabelRepository,
} from '../repositories';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from '../services';
import {META_DATA_SERVICE, MetaDataService} from '../services/meta-data.service';
import {FLOWS} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {regExpEscape} from '../settings/tools';

const REMOVE_ATTRIBUTE = 1;
const REMOVE_VALUE = 2;
const DELETE_FRAMES_WITH_LABEL_DELETE = 3;
const DELETE_FRAMES_WITH_UNSELECTED_IN_POROJECTS = 4;

export class SystemController {
  constructor(
    @inject(SYSTEM_LABEL_SERVICE) private systemLabelService: SystemLabelService,
    @repository(SystemLabelRepository)
    public systemLabelRepository: SystemLabelRepository,
    @repository(SystemChangeRepository)
    public systemChangeRepository: SystemChangeRepository,
    @repository(MetaDataUpdateRepository)
    private metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @repository('SystemDataRepository') private systemDataRepository: SystemDataRepository,
    @repository(QueryOptionRepository)
    private queryOptionRepository: QueryOptionRepository,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
  ) {}

  /**
   * Use to get list of system labels
   * @param filter {any} type of system label filter
   * @returns list of system lables of the team
   */
  @get('/internal/system/label/list')
  async getLabelList(@requestBody() filter: any) {
    try {
      let returnList = await this.systemLabelRepository.find(filter);
      return returnList;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use to get aggregated result system label
   * @param body {any} aggregate query
   * @returns aggregated result from system lables of the team
   */
  @post('/internal/system/label/aggregate')
  async getAggregateResult(@requestBody() body: {query: string}) {
    logger.debug(
      `Aggregate MetaData | MetaDataController.getAggregateResult | N/A | received query for aggregate on systemLabels ${body.query} `,
    );
    //logger.debug(JSON.stringify(body.query, null, 2));

    /**
     * aggregate meta data
     */
    let data = await this.systemLabelRepository.aggregateSystemLabel(body.query);

    return data;
  }

  /**
   * Use for findone system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @get('/internal/system/label/findOne')
  async findOneLabel(@param.query.boolean('regex') regex: boolean, @requestBody() filter: any) {
    if (regex) {
      const pattern = new RegExp('^' + regExpEscape(filter.labelText) + '$', 'i');
      filter = {
        where: {
          and: [
            {labelText: {regexp: pattern}},
            {teamId: filter.teamId},
            {label: {nin: filter.ninArr || []}},
            {isDeleted: {neq: true}},
          ],
        },
      };
    }

    try {
      let returnObj = await this.systemLabelRepository.findOne(filter);
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for finding system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @get('/internal/system/label/findByID')
  async findByID(@requestBody() filter: any) {
    try {
      let returnObj = await this.systemLabelRepository.findById(filter.id);
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for create new system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/create')
  async create(@requestBody() labelData: {label: LabelInfoCreateRequest; userName: string; teamId: string}) {
    return await this.systemLabelService.createSystemLabel(labelData.label, labelData.userName, labelData.teamId);
  }

  /**
   * Use for update system label for team
   * @param data {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/updateById')
  async updateById(@requestBody() data: {id: string; label: SystemLabel}) {
    try {
      let returnObj = await this.systemLabelRepository.updateById(data.id, data.label);
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for delete system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/deleteById')
  async deleteById(@requestBody() data: any) {
    try {
      let labelToBeDeleted = await this.systemLabelRepository.findById(data.id);
      let metaDataFilter = {'labelList.label': labelToBeDeleted.label};

      // flag verification status pending
      //  flag to recalculate verification
      await this.metaDataRepository.markStatPendingFlagsTrue(metaDataFilter);

      //remove label related stats
      //remove stats from MetaData
      await this.metaDataRepository.updateManyRemoveFromList(
        metaDataFilter,
        {
          labelList: {
            label: labelToBeDeleted.label,
          },
        },
        [],
      );

      //remove stats from SystemData
      // let _unsetData: {[k: string]: string} = {};
      // _unsetData["labelCounts." + labelToBeDeleted.label] = ''
      // //NOTE: add teamId filtering
      // await this.systemDataRepository.updateOneUnsetData(
      //   {},
      //   _unsetData,
      //   []
      // )

      //remove search query options belongs to label
      await this.queryOptionRepository.deleteQueryOption(
        'annotation.label',
        labelToBeDeleted.label,
        labelToBeDeleted.teamId,
      );

      //delete system label
      let returnObj = await this.systemLabelRepository.deleteById(data.id);
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for update system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/updateAll')
  async updateAll(@requestBody() data: any) {
    try {
      let returnObj = await this.systemLabelRepository.updateAll(data.data, data.query);
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for update system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/updateLabelImageDetails')
  async updateLabelImageDetails(@requestBody() data: any) {
    try {
      let returnObj = await this.systemLabelService.addLabelImages(
        data.uploadedFileDetails,
        data.type,
        data.label,
        data.attributeLabel,
        data.valueName,
        data.userName,
        data.systemLabelId,
        data.teamId,
      );
      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for update system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/label/updateMany')
  async updateManyRemoveFromList(@requestBody() data: any) {
    let returnObj;
    try {
      if (data.type == 'updateManyRemoveFromList') {
        returnObj = await this.systemLabelRepository.updateManyRemoveFromList(data.params, data.data, data.arrayFilter);
      } else if (data.type == 'updateManyPushToList') {
        returnObj = await this.systemLabelRepository.updateManyPushToList(data.params, data.data, data.arrayFilter);
      }

      return returnObj;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use to get list of system labels
   * @param filter {any} type of system label filter
   * @returns list of system lables of the team
   */
  @get('/internal/system/change/list')
  async getChangeList(@requestBody() filter: any) {
    try {
      let returnList = await this.systemChangeRepository.find(filter);
      return returnList;
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use to update farmes when changes of system labels
   * @param data {any} type of system label filter and frame filtes
   * @returns list of system lables of the team
   */
  @post('/internal/system/frames/unsetLabelAttibuteInFrame')
  async unsetLabelAttibuteInFrame(@requestBody() data: FrameAttributeUpdateRequestBody) {
    try {
      let taskIdList = data.taskObjIdList;
      let taskObjIdList = taskIdList.map(_id => new ObjectId(_id));
      let oldLabel = data.oldLabel;

      //When System Label Attribute removed remove annotation attributes
      if (data.type == REMOVE_ATTRIBUTE) {
        let removedAttr = data.removedAttr;
        if (removedAttr && oldLabel) {
          let _unsetAttrParams: {[k: string]: any} = {};
          _unsetAttrParams['taskId'] = {$in: taskObjIdList};
          _unsetAttrParams['annotationObjects.label.label'] = oldLabel.label;
          _unsetAttrParams['annotationObjects.label.attributeValues.' + removedAttr.label] = {$exists: true};

          let _unsetAttrData: {[k: string]: any} = {};
          _unsetAttrData['annotationObjects.$[annotationObject].label.attributeValues.' + removedAttr.label] = '';

          let _arrayFilterUnsetAttrData = [
            {
              'annotationObject.label.label': oldLabel.label,
            },
          ];

          await this.metaDataUpdateRepository.unsetLabelAttibuteInFrame(
            _unsetAttrParams,
            _unsetAttrData,
            _arrayFilterUnsetAttrData,
          );
        }

        //When System Label attribute value removed remove annotation attributes values
      } else if (data.type == REMOVE_VALUE) {
        let commonAttrInOldLabel = data.commonAttrInOldLabel;
        let removedValuesOfAttribute = data.removedValuesOfAttribute;
        if (commonAttrInOldLabel && removedValuesOfAttribute && oldLabel) {
          let _unsetValuesParams: {[k: string]: any} = {};
          _unsetValuesParams['taskId'] = {$in: taskObjIdList};
          _unsetValuesParams['annotationObjects.label.label'] = oldLabel.label;
          _unsetValuesParams['annotationObjects.label.attributeValues.' + commonAttrInOldLabel.label] = {
            $in: removedValuesOfAttribute,
          };

          let _unsetValuesData: {[k: string]: any} = {};
          _unsetValuesData[
            'annotationObjects.$[annotationObject].label.attributeValues.' + commonAttrInOldLabel.label
          ] = '';

          let _arrayFilterUnsetValuesData: {[k: string]: any} = {};
          _arrayFilterUnsetValuesData['annotationObject.label.attributeValues.' + commonAttrInOldLabel.label] = {
            $in: removedValuesOfAttribute,
          };

          await this.metaDataUpdateRepository.unsetLabelAttibuteInFrame(_unsetValuesParams, _unsetValuesData, [
            _arrayFilterUnsetValuesData,
          ]);
        }

        //Delete related frame when system label is deleted
      } else if (data.type == DELETE_FRAMES_WITH_LABEL_DELETE) {
        let existingLabel = data.existingLabel;
        if (existingLabel) {
          let paramAnnotationFrame = {
            taskId: {
              $in: taskObjIdList,
            },
            'annotationObjects.label.label': existingLabel.label,
          };
          let dataAnnotationFrame = {
            annotationObjects: {
              'label.label': existingLabel.label,
            },
          };
          await this.metaDataUpdateRepository.updateManyRemoveFromList(paramAnnotationFrame, dataAnnotationFrame);
        }

        //Deelete project frames annotations when system label unselected from frame
      } else if (data.type == DELETE_FRAMES_WITH_UNSELECTED_IN_POROJECTS) {
        let _label = data._label;
        if (_label) {
          let paramAnnotationFrame = {
            taskId: {
              $in: taskObjIdList,
            },
          };
          let dataAnnotationFrame = {
            annotationObjects: {
              'label.label': _label.label,
            },
          };
          await this.metaDataUpdateRepository.updateManyRemoveFromList(paramAnnotationFrame, dataAnnotationFrame);
        }
      }
    } catch (error) {
      logger.debug('error', error);
      throw new HttpErrors.NotAcceptable('filter Invalid');
    }
  }

  /**
   * Use for update system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/internal/system/cocojson/import/label/create')
  async createSystemLabel(
    @requestBody() labelData: {labels: LabelInfoCreateRequest; userName: string; teamId: string},
  ) {
    try {
      let res = await this.systemLabelService.createSystemLabel(labelData.labels, labelData.userName, labelData.teamId);
      return res;
    } catch (error) {
      logger.debug('internally createSystemLabel error', error);
      if (error.message == DatalakeUserMessages.SYSTEM_LABEL_LIKELY_EXIST) {
        const pattern = new RegExp('^' + regExpEscape(labelData.labels.labelText) + '$', 'i');
        let likelyLabel = await this.systemLabelRepository.findOne({
          where: {
            and: [
              {labelText: {regexp: pattern}},
              {teamId: labelData.teamId},
              {label: {nin: []}},
              {isDeleted: {neq: true}},
            ],
          },
        });

        if (likelyLabel) {
          //console.log(likelyLabel)
          if (likelyLabel.attributes) {
            let nameAttrIdx = likelyLabel.attributes.findIndex(attr => attr.labelText?.toLocaleLowerCase() == 'name');

            //if "name" attribute exist
            if (nameAttrIdx > -1) {
              let existNameAttr = likelyLabel.attributes[nameAttrIdx];
              let requestNameAttr = labelData.labels.attributes[0];

              for (let requestedAttrVal of requestNameAttr.values) {
                let attrValIdx = existNameAttr.values.findIndex(
                  val => val.valueText.toLocaleLowerCase() == requestedAttrVal.valueText.toLocaleLowerCase(),
                );
                if (attrValIdx == -1) {
                  likelyLabel.attributes[nameAttrIdx].values.push({
                    valueText: requestedAttrVal.valueText,
                    valueName: requestedAttrVal.valueText, // this will be automatically replaced by actual reference
                    description: requestedAttrVal.description,
                    imgFiles: requestedAttrVal.imgFiles,
                  });
                }
              }
            } else {
              likelyLabel.attributes.push(labelData.labels.attributes[0]);
            }
          } else {
            likelyLabel.attributes = labelData.labels.attributes;
          }

          let res = await this.systemLabelService.editSystemLabel(
            likelyLabel as LabelInfoEditRequest,
            labelData.userName,
            labelData.teamId,
          );
          return res;
        } else {
          logger.error(
            `${
              FLOWS.SYSTEM_LABEL_COCO_INTERNAL_CREATE
            } | SystemController.createSystemLabel | N/A | Failed to find likely label for labelData: ${JSON.stringify(
              labelData,
            )}`,
          );
          throw new HttpErrors.NotAcceptable('internally createSystemLabel error');
        }
      } else {
        logger.error(
          `${
            FLOWS.SYSTEM_LABEL_COCO_INTERNAL_CREATE
          } | SystemController.createSystemLabel | N/A | Failed to create system label for labelData: ${JSON.stringify(
            labelData,
          )}, error: `,
          error,
        );
        throw new HttpErrors.NotAcceptable('internally createSystemLabel error');
      }
    }
  }

  /**
   * Use for delete project and task
   * Update taskIdList and projectList in metaData
   * remove relavent metaDataUpdates
   * @param projectId string projectId
   * @param deleteData : {taskIdList:string[]} - project ans task ids
   * @returns error or success
   */
  @post('/internal/system/projectData/{projectId}/delete')
  async deleteProjectData(
    @param.path.string('projectId') projectId: string,
    @requestBody() data: {taskIdList: string[]; projectName: string; teamId: string},
  ) {
    logger.debug(
      `Delete project from metaData and metaDataUpdates | SystemController.deleteProjectData | projectId: ${projectId} | request recieved`,
    );

    let deletePromiseList: Promise<any>[] = [];

    // remove virtual collection id from metaData and delete virtual collection
    this.metaDataService.removeProjectRelatedVirtualCollection(projectId, data.teamId);

    // remove tasks from taskIdList in metaData
    this.metaDataService.removeTasksFromMetaData(data.taskIdList);

    // add promise to remove metaDataUpdate of project
    deletePromiseList.push(this.metaDataUpdateRepository.deleteAll({operationId: new ObjectId(projectId)}));
    // remove project from projectList in metaData
    deletePromiseList.push(this.metaDataService.removeProjectFromMetaData(projectId)); // add promise to remove projectData from metaData's annotationProjectList

    //---
    //remove search query options belongs to label
    deletePromiseList.push(
      this.queryOptionRepository.deleteQueryOption('annotation.project', data.projectName, data.teamId),
    );

    let resultArr = await Promise.all(deletePromiseList);

    logger.debug(
      `Delete project from metaData and metaDataUpdates | SystemController.deleteProjectData | projectId: ${projectId} | deleted`,
    );
    return {success: true};
    //------------------------------------
  }

  /**
   * Use for edit project name and update it on metaData and metaDataUpdates and query options
   * @param data : {projectId: string, projectName: string, teamId: string} - project delete data
   * @returns error or success
   */
  @post('/internal/system/projectData/edit')
  async editProjectData(
    @requestBody()
    data: {
      projectId: string;
      newProjectDetails: {name: string};
      previousProjectDetails: {name: string};
      teamId: string;
    },
  ) {
    logger.debug(
      `edit project from metaData and metaDataUpdates | SystemController.deleteProjectData | projectId: ${data.projectId} | request received`,
    );

    // add promise to edit metaDataUpdate of project

    await this.metaDataUpdateRepository.updateAll(
      {operationName: data.newProjectDetails.name},
      {operationId: new ObjectId(data.projectId)},
    );
    // edit project from projectList in metaData and metaDataUpdates

    await this.metaDataService.editProjectFromMetaData(data.projectId, data.newProjectDetails.name, data.teamId);
    // add promise to edit projectData from metaData's annotationProjectList and operationList

    //edit search query options belongs to label

    await this.queryOptionRepository.updateQueryKeyValue(
      'annotation.project',
      data.newProjectDetails.name,
      data.previousProjectDetails.name,
      data.teamId,
    );

    logger.debug(
      `Edit project from metaData and metaDataUpdates | SystemController.editProjectData | projectId: ${data.projectId} | edited`,
    );
    return {success: true};
  }

  /**
   * to upload label image for system label from studio app
   * @param data request body for label image upload
   * @returns success or error
   */
  @post('/internal/system/label/image/upload')
  async uploadLabelImage(
    @requestBody({
      description: 'label picture',
      required: true,
      content: {
        'multipart/form-data': {
          'x-parser': 'stream',
          schema: {type: 'object'},
        },
      },
    })
    request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    const storage = multer.memoryStorage();
    const upload = multer({storage});
    const fileArr = <FileInterface[]>await new Promise<object>((resolve, reject) => {
      upload.any()(<any>request, <any>response, (err: any) => {
        if (err) reject(err);
        else {
          resolve(request.files!);
        }
      });
    });

    let type = Number(request.body.type);
    let label = request.body.label;
    let attributeLabel = request.body.attributeLabel;
    let valueName = request.body.valueName;
    let teamId = request.body.teamId;
    let userName = request.body.userName;
    let userId = request.body.userId;

    return await this.systemLabelService.labelImageUpload(
      type,
      label,
      attributeLabel,
      valueName,
      fileArr,
      teamId,
      userName,
      userId,
    );
  }
}

export interface FrameAttributeUpdateRequestBody {
  type: number;
  taskObjIdList: ObjectId[];
  oldLabel?: {
    label: string;
  };
  removedAttr?: {
    label: string;
  };
  commonAttrInOldLabel?: {
    label: string;
  };
  removedValuesOfAttribute?: string[];
  existingLabel?: {
    label: string;
  };
  _label?: {
    label: string;
  };
}
