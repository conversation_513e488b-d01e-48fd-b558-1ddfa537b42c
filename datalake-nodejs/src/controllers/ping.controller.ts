import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get} from '@loopback/rest';
import {MetaDataRepository, MetaDataUpdateRepository} from '../repositories';
import {
  DISK_STORAGE_SERVICE,
  DiskStorageService,
  META_DATA_SERVICE,
  MetaDataService,
  OBJECT_META_UPDATER_SERVICE,
  ObjectMetaUpdaterService,
  SEARCH_QUERY_BUILDER_SERVICE,
  STORAGE_CRAWLER_SERVICE,
  SYSTEM_STATS_SERVICE,
  SearchQueryBuilderService,
  StorageCrawlerService,
  SystemStatsService
} from '../services';
import {EMBEDDINGS_SERVICE, EmbeddingsService} from '../services/embeddings.service';
import {STATS_CALCULATION_SERVICE, StatsCalculationService} from '../services/stats-calculation.service';
import {FileListParams} from '../services/storage-provider.service';

/**
 * A simple controller to bounce back http requests
 */
export class PingController {
  constructor(
    @repository(MetaDataUpdateRepository) public metaDataUpdateRepository: MetaDataUpdateRepository,
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @inject(SYSTEM_STATS_SERVICE) private systemStatsService: SystemStatsService,
    @inject(STATS_CALCULATION_SERVICE) private statsCalculationService: StatsCalculationService,
    @inject(META_DATA_SERVICE) private metadataService: MetaDataService,
    @inject(EMBEDDINGS_SERVICE) private embeddingsService: EmbeddingsService,
    @inject(OBJECT_META_UPDATER_SERVICE) private objectMetaUpdaterService: ObjectMetaUpdaterService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(DISK_STORAGE_SERVICE) private diskStorageService: DiskStorageService,
    @inject(STORAGE_CRAWLER_SERVICE) private TriggerService: StorageCrawlerService,
  ) {}

  ///Api call to get presigned URL
  @get('api/getPresignedURL')
  async testGetURL()
  {
    console.log("started testing getting presigned URL");
    console.log(this.diskStorageService.generateObjectUrl('bnjcn/elmkrmxk.txt'));
  }

  ///Api call to start intial crawl
  @get('api/cronTrigger')
  async testCronTrigger()
  {
    console.log("started testing cron triggere");
    this.TriggerService.triggerInitialCralwing();
  }

  ///Api call to get bucket file list
  @get('api/diskStorageGetList')
  async getObjectListInDiskStorage()
  {
    const fileListParams: FileListParams = {
      Bucket: "",//"D:\\testing repos\\minio_testing",//"D:\\testing repos\\test_local_storage\\random_directory",          // required
      //ContinuationToken: "token123",     // optional
      Prefix: "folder/subfolder/",       // optional
      NextQueryParam: { key: "value" }   // optional
    };
    const val  = await this.diskStorageService.getFileList(fileListParams);
    console.log("done");

    console.log(val);
  }
  @get('api/testFn')
  async labelWiseMetaDataDistributionFormatting() {
    let rawData = [
      {
        _id: '39d6089e-48f6-48bf-aac6-693a6e37912f',
        labelText: 'Car',
        keys: [
          {
            key: 'Light Condition',
            values: [
              {
                value: 'Day',
                valueFrameCount: 3.0,
                valueLabelCount: 10,
              },
              {
                value: 'Night',
                valueFrameCount: 4.0,
                valueLabelCount: 5,
              },
            ],
            keyFrameCount: 7.0,
            keyLabelCount: 5,
          },
          {
            key: 'Location',
            values: [
              {
                value: 'UK',
                valueFrameCount: 3.0,
                valueLabelCount: 1,
              },
              {
                value: 'USA',
                valueFrameCount: 2.0,
                valueLabelCount: 5,
              },
              {
                value: 'LK',
                valueFrameCount: 7.0,
                valueLabelCount: 3,
              },
              {
                value: 'AUS',
                valueFrameCount: 4.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 16.0,
            keyLabelCount: 9,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'red',
                valueFrameCount: 5.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 5.0,
            keyLabelCount: 3,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'high',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 3.0,
            keyLabelCount: 3,
          },
        ],
        labelFrameCount: 13.0,
        labelLabelCount: 15,
      },
      {
        _id: '39d6089e-48f6-48bf-aac6-693a6e37912f',
        labelText: 'Truck',
        keys: [
          {
            key: 'Light Condition',
            values: [
              {
                value: 'Day',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
              {
                value: 'Night',
                valueFrameCount: 4.0,
                valueLabelCount: 2,
              },
            ],
            keyFrameCount: 7.0,
            keyLabelCount: 5,
          },
          {
            key: 'Location',
            values: [
              {
                value: 'UK',
                valueFrameCount: 5.0,
                valueLabelCount: 2,
              },
              {
                value: 'USA',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
              {
                value: 'LK',
                valueFrameCount: 6.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 14.0,
            keyLabelCount: 8,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'red',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 3.0,
            keyLabelCount: 3,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'high',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 3.0,
            keyLabelCount: 3,
          },
        ],
        labelFrameCount: 10.0,
        labelLabelCount: 20,
      },
      {
        _id: '39d6089e-48f6-48bf-aac6-693a6e37912f',
        labelText: 'Car',
        keys: [
          {
            key: 'Light Condition',
            values: [
              {
                value: 'Day',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
              {
                value: 'Night',
                valueFrameCount: 4.0,
                valueLabelCount: 2,
              },
            ],
            keyFrameCount: 7.0,
            keyLabelCount: 5,
          },
          {
            key: 'Location',
            values: [
              {
                value: 'UK',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
              {
                value: 'USA',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
              {
                value: 'LK',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 9.0,
            keyLabelCount: 17,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'red',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 3.0,
            keyLabelCount: 3,
          },
          {
            key: 'Tag',
            values: [
              {
                value: 'high',
                valueFrameCount: 3.0,
                valueLabelCount: 3,
              },
            ],
            keyFrameCount: 3.0,
            keyLabelCount: 3,
          },
        ],
        labelFrameCount: 15.0,
        labelLabelCount: 15,
      },
    ];

    let mainLabels: string[] = [];
    let boldLabels: string[] = [];

    let labelClassStylePercentages: number[] = [];
    let labelClassStylePercentageLabels: string[] = [];
    let labelClassStyleData: number[] = [];

    let dynamicStylePercentages: number[][] = [];
    let dynamicStylePercentageLabels: string[][] = [];
    let dynamicStyleData: number[][] = [];

    let tagStylePercentages: number[] = [];
    let tagStylePercentageLabels: string[] = [];
    let tagStyleData: number[] = [];

    let labelStyleColor: string[] = ['bg-pattern', 'no-hover', 'rgba(153, 146, 251, 1)', 'rgba(101, 91, 224, 1)'];
    let dynamicStyleColors: string[][] = [];
    let tagStyleColor: string[] = ['bg-static', 'hover-static', 'rgba(212, 209, 253, 1)', 'rgba(226, 224, 253, 1)'];

    let totalLabelCount = 0;
    let numberOfDynamicStyles = 0;
    for (let _label of rawData) {
      //max value of key length will be the numberOfDynamicStyles
      for (let _key of _label.keys) {
        if (_key.values.length > numberOfDynamicStyles) {
          numberOfDynamicStyles = _key.values.length;
        }
      }

      totalLabelCount += _label.labelLabelCount;
    }

    //initialize dynamic styles arrays
    for (let i = 0; i < numberOfDynamicStyles; i++) {
      dynamicStylePercentages.push([]);
      dynamicStylePercentageLabels.push([]);
      dynamicStyleData.push([]);

      //push dynamic style colors
      let dynamicStyleColor: string[] = [];
      dynamicStyleColor.push('bg-static');
      dynamicStyleColor.push('hover-pattern');
      let opacity = 1 - ((1 - 0.6) / Math.max(1, numberOfDynamicStyles - 1)) * i;
      dynamicStyleColor.push(`rgba(212, 209, 253, ${parseFloat(opacity.toFixed(2))})`);
      dynamicStyleColor.push('rgba(45, 40, 99, 1)');
      dynamicStyleColors.push(dynamicStyleColor);
    }

    //iterate through the data and populate the arrays
    for (let _label of rawData) {
      mainLabels.push(_label.labelText); //push label class to main labels
      boldLabels.push(_label.labelText); //push label class to bold labels

      labelClassStyleData.push(_label.labelLabelCount); //push label count to label class style data and zero to other style data
      labelClassStylePercentages.push(parseFloat(((_label.labelLabelCount / totalLabelCount) * 100).toFixed(2))); //push label class percentage to label class style percentages
      labelClassStylePercentageLabels.push(_label.labelText); //push label class percentage label to label class style percentages labels
      for (let i = 0; i < numberOfDynamicStyles; i++) {
        dynamicStyleData[i].push(0);
        dynamicStylePercentages[i].push(0);
        dynamicStylePercentageLabels[i].push('');
      }
      tagStyleData.push(0);
      tagStylePercentages.push(0);
      tagStylePercentageLabels.push('');

      for (let _key of _label.keys) {
        mainLabels.push(_key.key); //push mata field to main labels
        boldLabels.push(''); //push empty string to bold labels for each meta field
        labelClassStyleData.push(0); //push zero to label class style data for each meta field
        labelClassStylePercentages.push(0); //push zero to label class style percentages for each meta field
        labelClassStylePercentageLabels.push(''); //push empty string to label class style percentages labels for each meta field

        if (_key.key === 'Tag') {
          for (let i = 0; i < numberOfDynamicStyles; i++) {
            dynamicStyleData[i].push(0); //push zero to style data for each meta field
            dynamicStylePercentages[i].push(0); //push zero to style percentages for each meta field
            dynamicStylePercentageLabels[i].push(''); //push empty string to dynamic style percentages labels
          }
          tagStyleData.push(_key.values[0].valueLabelCount); //push meta value count to tag style data
          tagStylePercentages.push(
            parseFloat(((_key.values[0].valueLabelCount / _key.keyLabelCount) * 100).toFixed(2)),
          ); //push meta value percentage to tag style percentages
          tagStylePercentageLabels.push(_key.values[0].value); //push meta value to tag style percentages labels
        } else {
          for (const [idx, _value] of _key.values.entries()) {
            dynamicStyleData[idx].push(_value.valueLabelCount); //push meta value count to dynamic style data
            dynamicStylePercentages[idx].push(
              parseFloat(((_value.valueLabelCount / _key.keyLabelCount) * 100).toFixed(2)),
            ); //push meta value percentage to dynamic style percentages
            dynamicStylePercentageLabels[idx].push(_value.value); //push meta value to dynamic style percentages labels
          }
          tagStyleData.push(0); //push empty string to tag style data for each meta field
          tagStylePercentages.push(0); //push empty string to tag style percentages for each meta field
          tagStylePercentageLabels.push(''); //push empty string to tag style percentages labels for each meta field
        }
      }

      mainLabels.push(''); //push empty string to main labels for each label
      boldLabels.push(''); //push empty string to bold labels for each label
      labelClassStyleData.push(0); //push zero to style data for each label
      labelClassStylePercentages.push(0); //push zero to style percentages for each label
      labelClassStylePercentageLabels.push(''); //push empty string to label class style percentages labels for each label
      for (let i = 0; i < numberOfDynamicStyles; i++) {
        dynamicStyleData[i].push(0);
        dynamicStylePercentages[i].push(0);
        dynamicStylePercentageLabels[i].push('');
      }
      tagStyleData.push(0);
      tagStylePercentages.push(0);
      tagStylePercentageLabels.push('');
    }

    return {
      mainLabels: mainLabels,
      boldLabels: boldLabels,
      percentages: [labelClassStylePercentages, ...dynamicStylePercentages, tagStylePercentages],
      percentageLabels: [labelClassStylePercentageLabels, ...dynamicStylePercentageLabels, tagStylePercentageLabels],
      data: [labelClassStyleData, ...dynamicStyleData, tagStyleData],
      colors: [labelStyleColor, ...dynamicStyleColors, tagStyleColor],
    };
  }
}
