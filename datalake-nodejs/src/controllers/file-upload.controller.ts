/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the File Uplaod
 */

/**
 * @class FileUploadController
 * Handle the request related to the FileUploadController for expose internal enpoints to internal server api calls
 * @description This controller use for Handle the request related to the FileUploadController
 * <AUTHOR> vinura
 */
// Uncomment these imports to begin using these cool features!

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {HttpErrors, param, post, put, requestBody, Request, Response} from '@loopback/rest';
import {DiskStorageConfiguration} from '../settings/disk.configuration';
import jwt, { TokenExpiredError } from 'jsonwebtoken';
import {SecurityBindings} from '@loopback/security';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {FILE_UPLOAD_HANDLER, FileUploadHandlerService} from '../services/file-upload-handler.service';
import {UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {MetaDataCollectionInputObjectFormat} from '../models';
import path from 'path';
const baseUrl = '/api/fileUpload';

const DISK_BUCKET_NAME = DiskStorageConfiguration.DISK_BUCKET_NAME;
const DISK_BASE_URL = DiskStorageConfiguration.DISK_BASE_URL;
const DISK_SECRET_KEY = DiskStorageConfiguration.DISK_SECRET_KEY;
const DISK_BASE_PATH = DiskStorageConfiguration.DISK_BASE_PATH;

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
export class FileUploadController {
  constructor(
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @inject(FILE_UPLOAD_HANDLER) private fileUploadHandlerService: FileUploadHandlerService,
    @inject(SecurityBindings.USER, {optional: true}) public currentUserProfile: UserProfileDetailed,
  ) {}

  /**
   * Use for handle uploading files to s3 from frontend
   * Generate a presigned upload url
   * @param filePath {string} path to save the amazon media file
   * @returns Presigned upload url
   */
  @post(baseUrl + '/generateUploadUrl')
  async saveMetadata(
    // @param.path.string('taskId') taskId: string,
    @param.query.string('filePath') filePath: string,
    //  @requestBody() metaDataInputObject: MetaDataInputFeedObject,
  ) {
    return await this.fileUploadHandlerService.getFileUploadUrl(filePath);
  }

  /**
   * Use for handle intialize uploading multipart files to s3 from frontend
   */
  @post(baseUrl + '/initializeMultipartUpload')
  async initializeMultipartUpload(
    // @param.path.string('taskId') taskId: string,
    // @param.query.string('filePath') filePath: string,
    @requestBody() body: {fileName: string; collectionName: string; uploadId: string},
  ) {
      return await this.fileUploadHandlerService.initializeMultipartUpload(
      body.fileName,
      body.collectionName,
      body.uploadId,
      undefined,
      this.currentUserProfile,
    );
     
  }

  /**
   * Use for handle chunk list urls for uploading multipart files to cloud storage from frontend
   */
  @post(baseUrl + '/getMultipartPreSignedUrls')
  async generateMultipartPreSignedUrls(
    // @param.path.string('taskId') taskId: string,
    // @param.query.string('filePath') filePath: string,
    @requestBody()
    body: {
      fileKey: string;
      fileId: number;
      parts: number;
      contentType?: string;
      isDisableMultipart?: boolean;
    },
  ) {
    return await this.fileUploadHandlerService.generateMultipartPreSignedUrls(
      body.fileKey,
      body.fileId,
      body.parts,
      body.contentType,
      body.isDisableMultipart,
    );
  }

  /**
   * Use for finalize multipart file upload to s3 from frontend
   */
  @post(baseUrl + '/finalizeMultipartUpload')
  async finalizeMultipartUpload(
    // @param.path.string('taskId') taskId: string,
    // @param.query.string('filePath') filePath: string,
    @requestBody()
    body: {
      fileKey: string;
      fileId: string;
      parts: any;
      uploadId: string;
      finalizeUrl?: string;
      isDisableMultipart?: boolean;
      isSkipMetadataQueue?: boolean;
    },
  ) {
    let teamId = this.currentUserProfile.teamId;
    if (!teamId) {
      throw new HttpErrors.UnprocessableEntity(`${DatalakeUserMessages.INVALID_USER}`);
    }

    if (!this.currentUserProfile.id) {
      throw new HttpErrors.UnprocessableEntity(`${DatalakeUserMessages.INVALID_USER}`);
    }

    let keyObj = await this.basicAuthService.insertUserIdAsKeySecretPairFroFrontendUploads(
      this.currentUserProfile.id,
      teamId,
    );
    let apiKey = keyObj.key;
    logger.info(`finalizeMultipartUpload | FileUploadController.finalizeMultipartUpload | apiKey: ${apiKey}`);

    return await this.fileUploadHandlerService.finalizeMultipartUpload(
      body.fileKey,
      body.fileId,
      body.parts,
      body.uploadId,
      apiKey,
      body.finalizeUrl,
      body.isDisableMultipart,
      body.isSkipMetadataQueue,
      this.currentUserProfile,
    );
  }

  /**
   * Use for handle saving of files to local disk storage from frontend when uploaded 
   * @param token {string} JWT token of the the URL 
   * @returns success status of the upload
   */
  @post(baseUrl + '/saveToLocalDisk')
  async saveUploadedFiles(
    @param.query.string('token') token: string,
    @requestBody({
      description: 'multipart/form-data file upload',
      required: true,
      content: {
        'multipart/form-data': {
          'x-parser': 'stream',
          schema: {
            type: 'object',
            properties: {
              file: {type: 'string', format: 'binary'}
            }
          }
        }
      }
    }) request: Request
  ): Promise<object> {
    try {
      const decodedToken = jwt.verify(token, DISK_SECRET_KEY) as {
        bucket: string;
        keyValue: string;
        exp: number;
        parts: number;
      };
  
      const {bucket, keyValue} = decodedToken;
      // calling handler
      if (!bucket || !keyValue) {
        // Handle JWT token expiration error
        console.error('Bucket or key is empty');
        return {success: false, message: 'Bucket or key empty'};
      }
      let fileUploadStatus = await this.fileUploadHandlerService.saveUploadedFilesToDiskStorage(
        DISK_BASE_PATH,
        bucket,
        keyValue,
        request,
      );
      if (fileUploadStatus)
      {     
        return {success: true, message: 'File saved successfully'};
      }
      else{
        return {success: false, message: 'File not saved'};
      }
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        // Handle JWT token expiration error
        console.error('JWT token expired:', error);
        return {success: false, message: 'JWT token expired'};
      } else if (error.name === 'JsonWebTokenError') {
        console.error('Invalid token');
        return {success: false, message: 'Invalid token'};
      }
        else {
        // Handle other errors
        console.error('Error saving file:', error);
        throw new Error('Failed to save file');
      }
    }
  }

  /**
   * Mark uploading status of the collection to completed
   */
  @put(baseUrl + '/collectionUploadingStatus/{uploadId}/complete')
  async markCollectionUploadComplete(@param.path.string('uploadId') uploadId: string) {
    // new Promise(resolve => setTimeout(resolve, 60* 1000)) // wait 1 min // ?????
    await this.fileUploadHandlerService.markCollectionUploadComplete(uploadId);
    return;
  }
}
