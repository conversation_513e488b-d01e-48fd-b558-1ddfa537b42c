/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle knowledge source related APIs
 */

/**
 * @class KnowledgeSourceController
 * Handle knowledge source related APIs
 * @description This controller use for Handle the request related to the KnowledgeSource controller eg: create, update, delete, list, refresh, details
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {KnowledgeSourceType} from '../models/knowledge-source.model';
import {KNOWLEDGE_SOURCE_SERVICE, KnowledgeSourceService} from '../services/knowledge-source.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({
  allowedRoles: [
    UserTypeDetailed.TEAM_ADMIN,
    UserTypeDetailed.SUPER_ADMIN,
    UserTypeDetailed.MEMBER,
    UserTypeDetailed.COLLABORATOR,
  ],
})
export class KnowledgeSourceController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(KNOWLEDGE_SOURCE_SERVICE)
    private knowledgeSourceService: KnowledgeSourceService,
  ) {}

  /**
   * Create a new knowledge source
   * @param data - The data of the knowledge source
   */
  @post('/api/knowledgeSource/create')
  async createKnowledgeSource(@requestBody() data: {name: string; type: KnowledgeSourceType; credentials: object}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.createKnowledgeSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.createKnowledgeSource | N/A | request by user ${currentUserProfile.id}, payload: `,
      data,
    );

    if (!data.name || !data.type || !data.credentials) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.createKnowledgeSource | N/A | Missing required fields`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_CREATE_FAILED);
    }

    let res = await this.knowledgeSourceService.createKnowledgeSource(
      data.name,
      data.type,
      data.credentials,
      currentUserProfile,
    );

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_CREATE_FAILED);
    }
  }

  /**
   * Get the list of knowledge sources
   */
  @get('/api/knowledgeSource/list')
  async getKnowledgeSourceList() {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.getKnowledgeSourceList | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.getKnowledgeSourceList | N/A | request by user ${currentUserProfile.id}`,
    );

    let res = await this.knowledgeSourceService.getKnowledgeSourceList();

    if (res.isSuccess) {
      return res.data;
    } else {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.getKnowledgeSourceList | N/A | Error: ${res.message}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_LIST_FAILED);
    }
  }

  /**
   * Refresh the knowledge source
   * @param data - The data of the knowledge source
   */
  @post('/api/knowledgeSource/refresh')
  async refreshKnowledgeSource(@requestBody() data: {knowledgeSourceId: string}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.refreshKnowledgeSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.refreshKnowledgeSource | N/A | request by user ${currentUserProfile.id}`,
    );

    if (!data.knowledgeSourceId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.refreshKnowledgeSource | N/A | No knowledge source id provided`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.KNOWLEDGE_SOURCE_REFRESH_FAILED}`);
    }

    let res = await this.knowledgeSourceService.refreshKnowledgeSource(data.knowledgeSourceId);

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.KNOWLEDGE_SOURCE_REFRESH_FAILED}`);
    }
  }

  /**
   * Delete the knowledge source
   * @param data - The data of the knowledge source
   */
  @post('/api/knowledgeSource/delete')
  async deleteKnowledgeSource(@requestBody() data: {knowledgeSourceId: string}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.deleteKnowledgeSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    if (!data.knowledgeSourceId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.deleteKnowledgeSource | N/A | No knowledge source id provided`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.KNOWLEDGE_SOURCE_DELETE_FAILED}`);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.deleteKnowledgeSource | N/A | request by user ${currentUserProfile.id}`,
    );

    let res = await this.knowledgeSourceService.deleteKnowledgeSource(data.knowledgeSourceId);

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.KNOWLEDGE_SOURCE_DELETE_FAILED}`);
    }
  }

  /**
   * Get the details of the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   */
  @get('/api/knowledgeSource/{knowledgeSourceId}/details')
  async getKnowledgeSourceDetails(@param.path.string('knowledgeSourceId') knowledgeSourceId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.getKnowledgeSourceDetails | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.knowledgeSourceService.getKnowledgeSourceDetails(knowledgeSourceId);

    if (res.isSuccess) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_DETAILS_FAILED);
    }
  }

  /**
   * Update the knowledge source
   * @param knowledgeSourceId - The id of the knowledge source
   * @param data - The data of the knowledge source
   */
  @post('/api/knowledgeSource/{knowledgeSourceId}/update')
  async updateKnowledgeSource(
    @param.path.string('knowledgeSourceId') knowledgeSourceId: string,
    @requestBody() data: {name: string; type: KnowledgeSourceType; credentials: object},
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.updateKnowledgeSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.updateKnowledgeSource | N/A | request by user ${currentUserProfile.id}, payload: `,
      data,
    );

    if (!data.name || !data.type || !data.credentials) {
      logger.error(
        `${FLOWS.KNOWLEDGE_SOURCE} | KnowledgeSourceController.updateKnowledgeSource | N/A | Missing required fields`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_UPDATE_FAILED);
    }

    let res = await this.knowledgeSourceService.updateKnowledgeSource(
      knowledgeSourceId,
      data.name,
      data.type,
      data.credentials,
      currentUserProfile,
    );

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_SOURCE_UPDATE_FAILED);
    }
  }
}
