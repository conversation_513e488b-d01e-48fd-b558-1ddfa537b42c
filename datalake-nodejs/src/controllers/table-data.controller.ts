import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {v4 as uuidV4} from 'uuid';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {CONNECTIONS_SERVICE, ConnectionService, TABLE_DATA_SERVICE, TableDataService} from '../services';
@authenticate('jwt')
export class TableDataController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,

    @inject(TABLE_DATA_SERVICE) private tableDataService: TableDataService,
    @inject(CONNECTIONS_SERVICE) private connectionDataService: ConnectionService,
  ) {}

  /**
   * Retrieves the list of tables related to data source.
   * @param dataSourceId {string} - The ID of the data source.
   * @param pageIndex {number} - The index of the page.
   * @param pageSize {number} - The size of the page.
   * @param searchKey {string} - (Optional) The search key for filtering tables.
   * @returns {Promise<any>} - A promise that resolves to the list of connected data sources.
   * @throws {HttpErrors.InternalServerError} - If there is an error retrieving the data.
   */

  @get('/api/tableData/{dataSourceId}/tables')
  async getTables(
    @param.path.string('dataSourceId') dataSourceId: string,
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number,
    @param.query.string('searchKey') searchKey?: string,
  ) {
    try {
      // logged user teamId
      let teamId = this.currentUserProfile.teamId;
      const result = await this.tableDataService.getTablesForDataSource(dataSourceId, pageIndex, pageSize, searchKey);
      return result;
    } catch (error) {
      logger.error(`Failed to get tables for data source ${dataSourceId}: ${error.message}`);
      throw new HttpErrors.InternalServerError(`Failed to get tables: ${error.message}`);
    }
  }

  /**
   * Retrieves fields for a specified table with pagination and optional search key.
   * @param tableId {string} - The ID of the table.
   * @param pageIndex {number} - The index of the page.
   * @param pageSize {number} - The size of the page.
   * @param searchKey {string} - (Optional) The search key for filtering fields.
   * @returns {Promise<{data: any[]; total: number}>} - A promise that resolves to an object containing the field data and total count.
   */
  @get('/api/tables/{tableId}/fields')
  async getFields(
    @param.path.string('tableId') tableId: string,
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number = 20,
    @param.query.string('searchKey') searchKey?: string,
  ): Promise<{data: any[]; total: number}> {
    return this.tableDataService.getFields(tableId, pageIndex, pageSize, searchKey);
  }

  /**
   * Updates the description of a specific field in a table.
   * @param id {string} - The table ID.
   * @param fieldName {string} - The name of the field.
   * @param requestBody {object} - The request body containing the new description.
   * @returns {Promise<{message: string}>} - A promise that resolves to an object indicating the success message.
   * @throws {HttpErrors.InternalServerError} - If there is an error updating the description.
   */

  @post('/api/tableData/{id}/fields/{fieldName}/description', {
    responses: {
      '200': {
        description: 'TableData description update success',
      },
    },
  })
  async updateFieldDescription(
    @param.path.string('id') id: string,
    @param.path.string('fieldName') fieldName: string,
    @requestBody()
    requestBody: {description: string},
  ) {
    try {
      await this.tableDataService.updateFieldDescription(id, fieldName, requestBody.description);
      return {message: 'Description updated successfully'};
    } catch (error) {
      throw new HttpErrors.InternalServerError(`Failed to update description: ${error.message}`);
    }
  }

  // /**
  //  * Updates the visibility of a specific fields in a tableInfo.
  //  *
  //  * @param id {string} - The unique identifier of the table.
  //  * @param fieldName {string} - The name of the field whose visibility is being updated.
  //  * @param requestBody {object} - The request body containing the new visibility state (isVisible).
  //  * @returns {Promise<{message: string}>} - A promise that resolves to an object with a success message indicating the update was successful.
  //  * @throws {HttpErrors.InternalServerError} - If there is an error updating the field visibility, it throws an internal server error.
  //  */
  // @post('/api/tableData/{id}/fields/{fieldName}/fieldVisibility', {
  //   responses: {
  //     '200': {
  //       description: 'Field visibility is update success',
  //     },
  //     '404': {
  //       description: 'Table or field not found',
  //     },
  //     '400': {
  //       description: 'Bad request',
  //     },
  //     '500': {
  //       description: 'Internal server error',
  //     },
  //   },
  // })
  // async updateFieldVisibility(
  //   @param.path.string('id') id: string,
  //   @param.path.string('fieldName') fieldName: string,
  //   @requestBody() updateVisibilityToAIDto: {isVisible?: boolean},
  // ) {
  //   try {
  //     const isVisible = updateVisibilityToAIDto.isVisible ?? true;
  //     const result = await this.tableDataService.updateFieldVisibility(id, fieldName, isVisible);
  //     if (!result.isSuccess) {
  //       if (result.message.includes('not found')) {
  //         throw new HttpErrors.NotFound(result.message);
  //       } else {
  //         throw new HttpErrors.BadRequest(result.message);
  //       }
  //     }
  //     return result;
  //   } catch (error) {
  //     if (error instanceof HttpErrors.HttpError) {
  //       throw error;
  //     }
  //     throw new HttpErrors.InternalServerError(`Failed to update isVisible: ${error.message}`);
  //   }
  // }

  /**
   * Updates the visibility to AI (manually update layernext data dictionary)
   **/
  @post('/api/tableData/{id}/fields/{fieldName}/fieldVisibility')
  async updateFieldVisibilityToAI(
    @param.path.string('id') id: string,
    @param.path.string('fieldName') fieldName: string,
    @requestBody() updateVisibilityToAI: {isVisible: boolean},
  ) {
    if (!this.currentUserProfile || !this.currentUserProfile.name) {
      throw new HttpErrors.Unauthorized('Current user profile is missing or invalid');
    }

    const requestId = uuidV4();

    logger.info(
      `Update field visibility to AI | TableDataController.updateFieldVisibilityToAI | ${requestId} | Trying to update field visibility to AI, tableId: ${id}, fieldName: ${fieldName}, isVisible: ${updateVisibilityToAI.isVisible}`,
    );

    const currentUserName = this.currentUserProfile.name;

    try {
      const isVisibleToAI = updateVisibilityToAI.isVisible;
      const result = await this.tableDataService.updateFieldVisibilityToAI(
        id,
        fieldName,
        isVisibleToAI,
        currentUserName,
        requestId,
      );
      if (!result.isSuccess) {
        if (result.message.includes('not found')) {
          throw new HttpErrors.NotFound(result.message);
        } else {
          throw new HttpErrors.BadRequest(result.message);
        }
      }
      return result;
    } catch (error) {
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.InternalServerError(`Failed to update isVisible: ${error.message}`);
    }
  }

  /**
   * Updates the description of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param requestBody {object} - The request body containing the new description.
   * @returns {Promise<{message: string}>} - A promise that resolves to an object indicating the success message.
   * @throws {HttpErrors.InternalServerError} - If there is an error updating the description.
   */

  @post('/api/tableData/{tableId}/description', {
    responses: {
      '200': {
        description: 'TableData description update success',
      },
    },
  })
  async updateTableDescription(
    @param.path.string('tableId') tableId: string,
    @requestBody()
    requestBody: {description: string},
  ) {
    try {
      if (this.currentUserProfile.name) {
        await this.tableDataService.updateTableDescription(
          tableId,
          requestBody.description,
          this.currentUserProfile.name,
        );
        return {message: `Description updated successfully by ${this.currentUserProfile.name}`};
      } else {
        await this.tableDataService.updateTableDescription(tableId, requestBody.description);
        return {message: 'Description updated successfully'};
      }
    } catch (error) {
      throw new HttpErrors.InternalServerError(`Failed to update description: ${error.message}`);
    }
  }

  /**
   * Updates the overview of a specific field in a table.
   * @param tableId {string} - The table ID.
   * @param requestBody {object} - The request body containing the new overview.
   * @returns {Promise<{message: string}>} - A promise that resolves to an object indicating the success message.
   * @throws {HttpErrors.InternalServerError} - If there is an error updating the overview.
   */
  @post('/api/tableData/{tableId}/overview', {
    responses: {
      '200': {
        description: 'TableData overview update success',
      },
    },
  })
  async updateTableOverview(
    @param.path.string('tableId') tableId: string,
    @requestBody() requestBody: {overview: string},
  ) {
    try {
      if (this.currentUserProfile.name) {
        await this.tableDataService.updateTableOverview(tableId, requestBody.overview, this.currentUserProfile.name);
        return {message: `Overview updated successfully by ${this.currentUserProfile.name}`};
      } else {
        await this.tableDataService.updateTableOverview(tableId, requestBody.overview);
        return {message: 'Overview updated successfully'};
      }
    } catch (error) {
      throw new HttpErrors.InternalServerError(`Failed to update overview: ${error.message}`);
    }
  }

  /**
   * Retrieves navigation data for tables based on connection ID and current table ID.
   * @param connectionId {string} - The connection ID.
   * @param currentTableId {string} - The current table ID.
   * @returns {Promise<any>} - A promise that resolves to an object containing the navigation data for the tables.
   * @throws {HttpErrors.InternalServerError} - If there is an error retrieving the navigation data.
   */
  @get('/api/tableData/{connectionId}/navigation', {
    responses: {
      '200': {
        description: 'Navigation data for tables',
      },
    },
  })
  async getNavigationData(
    @param.path.string('connectionId') connectionId: string,
    @param.query.string('currentTableId') currentTableId: string,
  ): Promise<any> {
    try {
      return await this.tableDataService.getNavigationData(connectionId, currentTableId);
    } catch (error) {
      throw new HttpErrors.InternalServerError(`Failed to get navigation data: ${error.message}`);
    }
  }

  // /**
  //  * Updates the visibility status of a specific table.
  //  * @param connectionId {string} - The connection ID.
  //  * @param tableName {string} - The name of the table.
  //  * @param updateVisibilityDto {object} - The request body containing the visibility status.
  //  * @returns {Promise<{isSuccess: boolean}>} - A promise that resolves to an object indicating the success of the update.
  //  * @throws {HttpErrors.NotFound} - If the table data is not found.
  //  */
  // @post('/api/tableData/{connectionId}/visibility')
  // async updateVisibility(
  //   @param.path.string('connectionId') connectionId: string,
  //   @param.query.string('tableName') tableName: string,
  //   @requestBody() updateVisibilityDto: {isVisible: boolean},
  // ): Promise<{isSuccess: boolean}> {
  //   if (!this.currentUserProfile || !this.currentUserProfile.name) {
  //     throw new HttpErrors.Unauthorized('Current user profile is missing or invalid');
  //   }

  //   const currentUserName = this.currentUserProfile.name;

  //   const result = await this.tableDataService.updateVisibility(
  //     connectionId,
  //     tableName,
  //     updateVisibilityDto.isVisible,
  //     currentUserName,
  //   );

  //   if (!result.isSuccess) {
  //     throw new HttpErrors.NotFound('TableData not found');
  //   }

  //   return result;
  // }

  @post('/api/tableData/{connectionId}/visibility')
  async updateTableVisibilityToAI(
    @param.path.string('connectionId') connectionId: string,
    @param.query.string('tableName') tableName: string,
    @requestBody() updateVisibilityToAI: {isVisible: boolean},
  ) {
    if (!this.currentUserProfile || !this.currentUserProfile.name) {
      throw new HttpErrors.Unauthorized('Current user profile is missing or invalid');
    }

    const currentUserName = this.currentUserProfile.name;

    const requestId = uuidV4();

    logger.info(
      `Update table visibility to AI | TableDataController.updateTableVisibilityToAI | ${requestId} | Trying to update table visibility to AI, connectionId: ${connectionId}, tableName: ${tableName}, isVisible: ${updateVisibilityToAI.isVisible}`,
    );

    const result = await this.tableDataService.updateTableVisibilityToAI(
      connectionId,
      tableName,
      updateVisibilityToAI.isVisible,
      currentUserName,
      requestId,
    );

    if (!result.isSuccess) {
      logger.error(`Failed to update visibility: ${result.message}`);
      throw new HttpErrors.NotFound('TableData not found');
    }

    return result;
  }

  /**
   * Updates the verification status for both tableInfo and a specific field if provided.
   * @param connectionId {string} - The connection ID.
   * @param tableName {string} - The name of the table.
   * @param fieldName {string} - (Optional) The name of the field.
   * @param updateVerificationDto {object} - The request body containing the verification status.
   * @returns {Promise<{isSuccess: boolean}>} - A promise that resolves to an object indicating the success of the update.
   * @throws {HttpErrors.NotFound} - If the table data is not found or no modification was made.
   */
  @post('/api/tableData/{connectionId}/verification')
  async updateVerification(
    @param.path.string('connectionId') connectionId: string,
    @param.query.string('tableName') tableName: string,
    @param.query.string('fieldName') fieldName: string, // Make it optional
    @requestBody() updateVerificationDto: {isVerified: boolean},
  ): Promise<{isSuccess: boolean}> {
    const result = await this.tableDataService.updateVerification(
      connectionId,
      tableName,
      updateVerificationDto.isVerified,
      fieldName,
    );

    if (!result.isSuccess) {
      throw new HttpErrors.NotFound('TableData not found or no modification was made');
    }

    return result;
  }

  @get('/api/tableData/{dataSourceId}/overview')
  async getOverviewForConnection(
    @param.path.string('dataSourceId') dataSourceId: string,
  ): Promise<{dataSourceOverview: object; tableOverviews: object}> {
    try {
      let dataSourceOverview = await this.connectionDataService.getDataSourceOverview(dataSourceId);
      let tableOverviews = await this.tableDataService.getSectionOverviewsForDataSource(dataSourceId);
      let result = {
        dataSourceOverview: {
          description: dataSourceOverview,
        },
        tableOverviews: {
          description: tableOverviews,
        },
      };

      logger.info(`Retrieved data source overviews for data source ${dataSourceId}`);
      return result;
    } catch (error) {
      logger.error(`Failed to load data source overviews for data source ${dataSourceId}: ${error.message}`);
      throw new HttpErrors.InternalServerError(`Failed to get data source overviews: ${error.message}`);
    }
  }

  @post('/api/tableData/{dataSourceId}/dataSourceOverview')
  async updateOverviewForConnection(
    @param.path.string('dataSourceId') dataSourceId: string,
    @requestBody() dataSourceOverview: {overview: string},
  ): Promise<{isSuccess: boolean}> {
    try {
      if (!dataSourceOverview.overview) {
        throw new HttpErrors.BadRequest('Overview is required');
      }
      let result = await this.connectionDataService.updateDataSourceOverview(dataSourceId, dataSourceOverview.overview);
      logger.info(`Updated data source overviews for data source ${dataSourceId}`);
      if (result) {
        return {
          isSuccess: true,
        };
      } else {
        return {
          isSuccess: false,
        };
      }
    } catch (error) {
      logger.error(`Failed to load data source overviews for data source ${dataSourceId}: ${error.message}`);
      throw new HttpErrors.InternalServerError(`Failed to get data source overviews: ${error.message}`);
    }
  }
}
