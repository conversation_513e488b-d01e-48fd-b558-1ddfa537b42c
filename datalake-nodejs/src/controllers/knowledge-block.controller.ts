// Uncomment these imports to begin using these cool features!

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {KNOWLEDGE_BLOCK_SERVICE, KnowledgeBlockService} from '../services/knowledge-block.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

// import {inject} from '@loopback/core';

@authenticate('jwt')
@authorize({
  allowedRoles: [
    UserTypeDetailed.TEAM_ADMIN,
    UserTypeDetailed.SUPER_ADMIN,
    UserTypeDetailed.MEMBER,
    UserTypeDetailed.COLLABORATOR,
  ],
})
export class KnowledgeBlockController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(KNOWLEDGE_BLOCK_SERVICE)
    private knowledgeBlockService: KnowledgeBlockService,
  ) {}

  @get('api/knowledge-blocks')
  async getKnowledgeBlocks(
    @param.query.string('search') search?: string,
    @param.query.number('pageSize') pageSize: number = 20,
    @param.query.number('pageIndex') pageIndex: number = 0,
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.getKnowledgeBlocks | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info(
      `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.getKnowledgeBlocks | N/A | request by user ${currentUserProfile.id}`,
    );

    let res = await this.knowledgeBlockService.getKnowledgeBlocks(pageSize, pageIndex, search);

    if (res.isSuccess) {
      return res.data;
    } else {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.getKnowledgeBlocks | N/A | Error: ${res.message}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_BLOCK_LIST_FAILED);
    }
  }

  @post('api/knowledge-blocks/{knowledgeBlockId}/set-visibility')
  async setVisibility(
    @param.path.string('knowledgeBlockId') knowledgeBlockId: string,
    @requestBody() body: {isEnabled: boolean},
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.setVisibility | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    if (typeof body.isEnabled !== 'boolean') {
      logger.error(
        `${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.setVisibility | N/A | isEnabled is required and must be a boolean`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_BLOCK_SET_VISIBILITY_FAILED);
    }

    let res = await this.knowledgeBlockService.setVisibility(knowledgeBlockId, body.isEnabled);

    if (res.isSuccess) {
      return res.data;
    } else {
      logger.error(`${FLOWS.KNOWLEDGE_BLOCK} | KnowledgeBlockController.setVisibility | N/A | Error: ${res.message}`);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_BLOCK_SET_VISIBILITY_FAILED);
    }
  }
}
