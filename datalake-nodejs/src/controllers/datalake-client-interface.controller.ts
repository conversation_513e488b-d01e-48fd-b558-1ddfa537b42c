/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for API clients
 */

/**
 * @class DatalakeClientInterfaceController
 * API clients(python sdk) interact with datalake through this controller
 * Handle the request related to the DatalakeClientInterface Controller
 * @description This controller use for Handle the request related to the DatalakeExplorer controller eg: get object list to explorer view
 * <AUTHOR>
 */

import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, Request, Response, RestBindings, get, param, post, requestBody} from '@loopback/rest';
import {securityId} from '@loopback/security';
import {EJSON, ObjectId} from 'bson';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  APIClientLabelRefRequest,
  AnnotationTypeString,
  AnnotationUserType,
  Attributes,
  ContentType,
  DatalakeSelectionRequest,
  ExploreObjectListRequest,
  ExploreSortBy,
  Explore_API_TYPE,
  ExplorerCollectionViewRequest,
  ExplorerDefaultViewRequest,
  ExplorerFilterV2,
  LabelInfoCreateRequest,
  LabelInfoEditRequest,
  MetaData,
  MetaDataCollectionInputObjectFormat,
  SDKMetaUpdateRequestBody,
  SortObject,
  SystemLabel,
  SystemLabelListResponse,
  SystemLabelType,
  TrashRestoreRequest,
  UpdateJobConfig,
} from '../models';
import {ConnectionSourceType} from '../models/source.model';
import {DatalakeSelectionRepository, MetaDataRepository, SystemLabelRepository} from '../repositories';
import {
  ANNO_DOWNLOAD_HANDLER_SERVICE,
  AnnotationDownloadHandlerService,
  CONNECTIONS_SERVICE,
  ConnectionService,
  DATALAKE_EXPLORER_SERVICE,
  DATALAKE_SELECTION_SERVICE,
  DATALAKE_TRASH_SERVICE,
  DatalakeExplorerService,
  DatalakeSelectionService,
  DatalakeTrashService,
  FILE_UPLOAD_HANDLER,
  FileUploadHandlerService,
  FilterKeyValues,
  InputMetadataFeedService,
  KNOWLEDGE_BLOCK_SERVICE,
  KnowledgeBlockService,
  META_DATA_SERVICE,
  META_FIELD_PROPAGATOR_SERVICE,
  MetaDataService,
  MetaFieldPropagatorService,
  OBJECT_META_UPDATER_SERVICE,
  ObjectMetaUpdaterService,
  SEARCH_QUERY_BUILDER_SERVICE,
  STORAGE_CRAWLER_SERVICE,
  SYSTEM_LABEL_SERVICE,
  SYSTEM_VALIDATION_SERVICE,
  SearchQueryBuilderService,
  StorageCrawlerService,
  SystemLabelService,
  SystemValidationService,
  TABLE_DATA_SERVICE,
  TableDataService,
} from '../services';
import {BUSINESS_RULE_SERVICE, BusinessRuleService} from '../services/business-rule.service';
import {EMBEDDINGS_SERVICE, EmbeddingsService} from '../services/embeddings.service';
import {EVALUATION_SERVICE, EvaluationService} from '../services/evaluation.service';
import {JOB_SERVICE, JobService} from '../services/job.service';
import {META_DATA_UPDATE_SERVICE, MetaDataUpdateService} from '../services/meta-data-update.service';
import {SRC_DEST_CONNECTION_SERVICE, SrcDestConnectionService} from '../services/src-dest-connection.service';
import {SYSTEM_META_SERVICE, SystemMetaService} from '../services/system-meta.service';
import {EMBEDDING_COLLECTION, FLOWS, SIMILARITY_SCORE_THRESHOLD, UserType} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {regExpEscape} from '../settings/tools';
const baseUrl = 'api/client';

export class DatalakeClientInterfaceController {
  constructor(
    @inject(SYSTEM_LABEL_SERVICE)
    private systemLabelService: SystemLabelService,
    @repository(SystemLabelRepository)
    public systemLabelRepository: SystemLabelRepository,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @service(InputMetadataFeedService)
    private inputMetadataFeedService: InputMetadataFeedService,
    @inject(FILE_UPLOAD_HANDLER)
    private fileUploadHandlerService: FileUploadHandlerService,
    @inject(META_DATA_UPDATE_SERVICE)
    private metaDataUpdateService: MetaDataUpdateService,
    @inject(ANNO_DOWNLOAD_HANDLER_SERVICE)
    private annotationDownloadHandlerService: AnnotationDownloadHandlerService,
    @inject(DATALAKE_SELECTION_SERVICE)
    private datalakeSelectionService: DatalakeSelectionService,
    @inject(META_DATA_SERVICE)
    private metaDataService: MetaDataService,
    @inject(JOB_SERVICE)
    private jobService: JobService,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(DATALAKE_TRASH_SERVICE)
    private datalakeTrashService: DatalakeTrashService,
    @inject(SYSTEM_VALIDATION_SERVICE)
    private systemValidationService: SystemValidationService,
    @inject(OBJECT_META_UPDATER_SERVICE)
    private objectMetaUpdaterService: ObjectMetaUpdaterService,
    @inject(STORAGE_CRAWLER_SERVICE)
    private storageCrawlerService: StorageCrawlerService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE)
    private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(DATALAKE_EXPLORER_SERVICE) private datalakeExplorerService: DatalakeExplorerService,
    @inject(META_FIELD_PROPAGATOR_SERVICE)
    private metaFieldPropagatorService: MetaFieldPropagatorService,
    @inject(EMBEDDINGS_SERVICE)
    private embeddingService: EmbeddingsService,
    @inject(SRC_DEST_CONNECTION_SERVICE)
    private srcDestConnectionService: SrcDestConnectionService,
    @inject(CONNECTIONS_SERVICE)
    private connectionService: ConnectionService,
    @inject(TABLE_DATA_SERVICE)
    private tableDataService: TableDataService,
    @inject(SYSTEM_META_SERVICE)
    private systemMetaService: SystemMetaService,
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
    @inject(EVALUATION_SERVICE)
    private evaluationService: EvaluationService,
    @inject(BUSINESS_RULE_SERVICE)
    private businessRuleService: BusinessRuleService,
    @inject(KNOWLEDGE_BLOCK_SERVICE)
    private knowledgeBlockService: KnowledgeBlockService,
  ) {}

  /**
   * Used for getting a list of all system labels for a team
   * When using Python SDK
   * @param groupId {string}: optional filter by label group
   * @returns list of all labels in the system
   */
  @get('/api/client/labels/list')
  async getAllLabelList(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('groupId') groupId?: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    const labelList = await this.systemLabelService.getSystemLabelList(
      AnnotationUserType.ANNOTATION_USER_TYPE_AUDITOR,
      teamId,
      Explore_API_TYPE.LIST,
      undefined,
      undefined,
      '',
      0,
      true,
      groupId,
    );

    return labelList;
  }

  /**
   * Used for create system label
   * From Python SDK
   * @returns created label if success
   */
  @post('/api/client/system/labels/create')
  async createSystemLabel(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelData: {
      label: LabelInfoCreateRequest;
      userName: string;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const userName = authorizeDetails.userName || authorizeDetails.name || 'Unnamed';

    const res = await this.systemLabelService.createSystemLabel(labelData.label, userName, authorizeDetails.teamId);
    return res;
  }

  /**
   * Used for creating a group of labels
   * From Python SDK
   * @returns id of the created label group if success
   */
  @post('/api/client/label_groups/create')
  async createLabelGroup(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]; groupName: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    const labelGroupRes = await this.systemLabelService.createLabelGroup(
      labelGroupData.groupName,
      labelGroupData.labelKeys,
      currentUserProfile,
    );

    if (labelGroupRes.groupId) {
      return labelGroupRes.groupId;
    } else {
      throw new HttpErrors.NotAcceptable(labelGroupRes.errorMsg);
    }
  }

  /** Add label list to a group
   * @param labelGroupId {string}: ID of the label group
   * @param labelGroupData.labelKeys {array}: List of label keys belonging to the labels that we need to add to this group
   */
  @post('/api/client/label_groups/{groupId}/addLabels')
  async addLabelsToGroup(
    @param.path.string('groupId') labelGroupId: string,
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    const labelGroupRes = await this.systemLabelService.attachLabelsToGroup(
      labelGroupId,
      labelGroupData.labelKeys,
      currentUserProfile,
    );

    return {
      isSuccess: true,
    };
  }

  /** remove label from a group
   * @param labelGroupId {string}: ID of the label group
   * @param labelGroupData.labelKeys {array}: List of label keys belonging to the labels that we need to add to this group
   */

  @post('/api/client/label_groups/{groupId}/removeLabels')
  async removeLabelsFromGroup(
    @param.path.string('groupId') labelGroupId: string,
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    const labelGroupRes = await this.systemLabelService.detachLabelsFromGroup(
      labelGroupId,
      labelGroupData.labelKeys,
      currentUserProfile,
    );

    return {
      isSuccess: true,
    };
  }

  /**
   * Used to get list of all label groups in the team
   * By Python SDK
   * @returns list of all label group ids and names
   */
  @get('/api/client/label_groups/list')
  async getAllLabelGroups(@param.header.string('Authorization') authorization: string) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    const groupList = await this.systemLabelService.getLabelGroups(teamId);
    return groupList;
  }

  /**
   * Used to get list of all label groups in the team
   * By Python SDK
   * @returns list of all label group ids and names
   */
  @get('/api/client/getDetails/system')
  async getSystemData(@param.header.string('Authorization') authorization: string) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(`Get system data | DatalakeClientInterfaceController.getSystemData | N/A | Failed to get data`);
      throw new HttpErrors.NotAcceptable('Can not find team');
    }

    return this.searchQueryBuilderService.getSystemData(teamId);
  }

  /**
   * Use for update system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/api/client/cocojson/import/label/create')
  async createSystemLabelFromImportCOCOJson(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelData: {labels: LabelInfoCreateRequest; userName: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    const userName = authorizeDetails.userName || authorizeDetails.name || 'Unnamed';

    try {
      const res = await this.systemLabelService.createSystemLabel(labelData.labels, userName, teamId);
      return res;
    } catch (error) {
      if (error.message == DatalakeUserMessages.SYSTEM_LABEL_LIKELY_EXIST) {
        logger.info(
          `${
            FLOWS.API_CLIENT_REQUEST
          } | DatalakeClientInterfaceController.createSystemLabelFromImportCOCOJson | ${teamId} | Editing likely exit label to add attributes,values from labelData: ${JSON.stringify(
            labelData,
          )}`,
        );
        const pattern = new RegExp('^' + regExpEscape(labelData.labels.labelText) + '$', 'i');
        const likelyLabel = await this.systemLabelRepository.findOne({
          where: {
            and: [{labelText: {regexp: pattern}}, {teamId: teamId}, {label: {nin: []}}, {isDeleted: {neq: true}}],
          },
        });

        if (likelyLabel) {
          if (likelyLabel.attributes) {
            const nameAttrIdx = likelyLabel.attributes.findIndex(attr => attr.labelText?.toLocaleLowerCase() == 'name');

            //if "name" attribute exist
            if (nameAttrIdx > -1) {
              const existNameAttr = likelyLabel.attributes[nameAttrIdx];
              const requestNameAttr = labelData.labels.attributes[0];

              for (const requestedAttrVal of requestNameAttr.values) {
                const attrValIdx = existNameAttr.values.findIndex(
                  val => val.valueText.toLocaleLowerCase() == requestedAttrVal.valueText.toLocaleLowerCase(),
                );
                if (attrValIdx == -1) {
                  likelyLabel.attributes[nameAttrIdx].values.push({
                    valueText: requestedAttrVal.valueText,
                    valueName: requestedAttrVal.valueText, // this will be automatically replaced by actual reference
                    description: requestedAttrVal.description,
                    imgFiles: requestedAttrVal.imgFiles,
                  });
                }
              }
            } else {
              likelyLabel.attributes.push(labelData.labels.attributes[0]);
            }
          } else {
            likelyLabel.attributes = labelData.labels.attributes;
          }

          const res = await this.systemLabelService.editSystemLabel(
            likelyLabel as LabelInfoEditRequest,
            userName,
            teamId,
          );
          return res;
        } else {
          logger.error(
            `${
              FLOWS.API_CLIENT_REQUEST
            } | DatalakeClientInterfaceController.createSystemLabelFromImportCOCOJson | ${teamId} | Failed to find likely label for labelData: ${JSON.stringify(
              labelData,
            )}, error: `,
            error,
          );
          throw new HttpErrors.NotAcceptable('Create System Label error');
        }
      } else {
        logger.error(
          `${
            FLOWS.API_CLIENT_REQUEST
          } | DatalakeClientInterfaceController.createSystemLabelFromImportCOCOJson | ${teamId} | Failed to create system label for labelData: ${JSON.stringify(
            labelData,
          )}, error: `,
          error,
        );
        throw new HttpErrors.NotAcceptable('Create System Label error');
      }
    }
  }

  /**
   * Use for update system label for team while modelrun data upload via API client
   * @param labelData {object} label data
   *
   *   ** SAMPLE INPUT **
   *
   {
      "labels": {
          "vehicle": {
              "color": [
                  "red",
                  "blue",
                  "white"
              ],
              "name": [
                  "car"
              ]
          },
          "house": {
              "color": [
                  "red",
                  "blue",
                  "white"
              ],
              "name": [
                  "car"
              ]
          }
        },
      "userName" : "Test User 1"
    }
   *   ** SAMPLE OUTPUT **
   *
   {
      "vehicle": {
          "ref": "1a772c7f-ca96-4f5c-ad0b-7f993967e73d",
          'color': '#e88f6d'
          "texts": {
              "color": {
                  "ref": "8ea34f88-d80f-4a6d-a608-b56a5aa15bbd",
                  "texts": {
                      "red": "6db192eb-d022-4e24-aac2-d3b8ea224a86",
                      "blue": "8f215b82-9e37-40e4-a266-a591aa437f50",
                      "white": "7867829d-6c87-4819-ac7c-229367d89195"
                  }
              },
              "name": {
                  "ref": "7ce8d9e2-ca3d-47e1-8fba-de20e78c5c52",
                  "texts": {
                      "car": "74998a8c-9a54-4e8f-925f-2aa7ab74f9e8"
                  }
              }
          }
      },
      "house": {
          "ref": "3e6f0dfc-7ceb-4401-a2af-dc671be8cc02",
          'color': '#a8e868'
          "texts": {
              "color": {
                  "ref": "f57bde4a-86f8-475c-920a-c739e5f2d91e",
                  "texts": {
                      "red": "bdd635f0-d040-45c8-837d-d341fd8aa66d",
                      "blue": "66691db5-b1a0-4e6c-b5b9-c4a3986cb4a9",
                      "white": "def31615-132f-44c8-8cb8-aa027349f754"
                  }
              },
              "name": {
                  "ref": "408f90fd-48bb-442f-a11e-b5125048f058",
                  "texts": {
                      "car": "a94dde58-3a77-42b5-9031-ebeed1d65c14"
                  }
              }
          }
      }
    }
   *
   * @returns label text with references
   */
  @post('/api/client/system/label/references')
  async findSystemLabelReferences(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    labelData: {labels: APIClientLabelRefRequest; userName: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    const userName = authorizeDetails.userName || authorizeDetails.name || 'Unnamed';

    const labelWithRefs: {
      [k: string]: {
        ref?: string;
        color?: string;
        texts: {
          [k: string]: {
            ref?: string;
            texts: {
              [k: string]: string;
            };
          };
        };
      };
    } = {};

    for (const [labelText, attrs] of Object.entries(labelData.labels)) {
      const pattern = new RegExp('^' + regExpEscape(labelText) + '$', 'i');
      const existLabelClass = await this.systemLabelRepository.findOne({
        where: {
          and: [{labelText: {regexp: pattern}}, {teamId: teamId}, {label: {nin: []}}, {isDeleted: {neq: true}}],
        },
      });

      let updatedLabel: SystemLabel | SystemLabelListResponse;

      if (existLabelClass) {
        /**
         * check all requested attributes & values exists, otherwise edit the system label to add them
         */

        let isNewAttributesValuesAdded = false;

        //check attributes
        if (!existLabelClass.attributes) {
          existLabelClass.attributes = []; // if attributes field not exist, add it before iterate
        }
        for (const [attr, values] of Object.entries(attrs)) {
          const attrIndex = existLabelClass.attributes.findIndex(
            elem => attr.toLowerCase() == elem.labelText?.toLowerCase(),
          );
          // if attribute exist, check for values
          if (attrIndex > -1) {
            if (!existLabelClass.attributes[attrIndex].values) {
              existLabelClass.attributes[attrIndex].values = []; // if values field not exist, add it before iterate
            }
            for (const val of values) {
              const valIndex = existLabelClass.attributes[attrIndex].values.findIndex(
                elem => val.toLowerCase() == elem.valueText.toLowerCase(),
              );
              //if value not exist add it
              if (valIndex == -1) {
                isNewAttributesValuesAdded = true;
                existLabelClass.attributes[attrIndex].values.push({
                  valueText: val,
                  valueName: val, // temporary assignment, this will be automatically replaced by actual reference
                  description: '',
                  imgFiles: [],
                });
              }
            }
          }
          // if attribute not exist, add it and its values
          else {
            isNewAttributesValuesAdded = true;
            const attribute: Attributes = {
              labelText: attr,
              label: attr, // temporary assignment, this will be automatically replaced by actual reference
              key: attr, // temporary assignment, this will be automatically replaced by actual reference
              values: [],
            };
            for (const value of values) {
              attribute.values.push({
                valueText: value,
                valueName: value, // temporary assignment, this will be automatically replaced by actual reference
                description: '',
                imgFiles: [],
              });
            }
            existLabelClass.attributes.push(attribute);
          }
        }

        if (isNewAttributesValuesAdded) {
          updatedLabel = await this.systemLabelService.editSystemLabel(
            existLabelClass as LabelInfoEditRequest,
            userName,
            teamId,
          );
        } else {
          updatedLabel = existLabelClass;
        }
      } else {
        // create new label
        const newLabelCreateRequest: LabelInfoCreateRequest = {
          labelText: labelText,
          description: '',
          attributes: [],
          imgFiles: [],
          //type: Object.keys(attrs).length > 0 ? SystemLabelType.CLASS_WITH_ATTRIBUTES : SystemLabelType.CLASS_ONLY
          type: SystemLabelType.CLASS_WITH_ATTRIBUTES,
        };
        for (const [attr, values] of Object.entries(attrs)) {
          const attribute: Attributes = {
            labelText: attr,
            label: attr, // temporary assignment, this will be automatically replaced by actual reference
            key: attr, // temporary assignment, this will be automatically replaced by actual reference
            values: [],
          };
          for (const value of values) {
            attribute.values.push({
              valueText: value,
              valueName: value, // temporary assignment, this will be automatically replaced by actual reference
              description: '',
              imgFiles: [],
            });
          }
          newLabelCreateRequest.attributes.push(attribute);
        }

        updatedLabel = await this.systemLabelService.createSystemLabel(newLabelCreateRequest, userName, teamId);
      }

      // build the response
      labelWithRefs[labelText] = {
        ref: updatedLabel.label,
        color: updatedLabel.color,
        texts: {},
      };
      for (const [attr, values] of Object.entries(attrs)) {
        if (updatedLabel.attributes) {
          const attrIndex = updatedLabel.attributes.findIndex(
            elem => attr.toLowerCase() == elem.labelText?.toLowerCase(),
          );
          if (attrIndex > -1) {
            labelWithRefs[labelText].texts[attr] = {
              ref: updatedLabel.attributes[attrIndex].label,
              texts: {},
            };
            if (updatedLabel.attributes[attrIndex].values) {
              for (const val of values) {
                const valIndex = updatedLabel.attributes[attrIndex].values.findIndex(
                  elem => val.toLowerCase() == elem.valueText.toLowerCase(),
                );
                if (valIndex > -1) {
                  labelWithRefs[labelText].texts[attr].texts[val] =
                    updatedLabel.attributes[attrIndex].values[valIndex].valueName;
                }
              }
            }
          }
        }
      }
    }

    return labelWithRefs;
  }

  /**
   * use to save input metadata feed in bulk
   * Gets object key list and a common metadataUpdateSet
   * Create fileUpload document (to track progress)
   * called from frontend
   */
  @post('api/client/uploadMetadataInCollection')
  async startMetadataUploadInCollection(
    @requestBody() metaDataInputCollection: MetaDataCollectionInputObjectFormat,
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!metaDataInputCollection) {
      return;
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    for (const [key, value] of Object.entries(metaDataInputCollection.metaDataObject)) {
      if (!MetaData.definition.properties.hasOwnProperty(key)) {
        this.metaFieldPropagatorService.validateAndCreateCustomMetaField(
          key,
          currentUserProfile.teamId || '',
          currentUserProfile.name || '',
        );
      }
    }

    return this.inputMetadataFeedService.handleMetaDataCollectionInputStart(
      metaDataInputCollection,
      currentUserProfile,
      teamId,
      authorizeDetails.userId,
      authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
    );
  }

  /**
   * use to get status of the uploads
   * @param collectionName {string} name of the collection
   * @param authorization basic auth header
   * @returns
   */
  @get('/api/client/collection/getuploadProgress')
  async getUploadStatus(
    @param.query.string('collectionName') collectionName: string,
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    if (!collectionName) {
      throw new HttpErrors.Unauthorized(`Error : 'collectionName not received`);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.fileUploadHandlerService.getUploadStatus(collectionName, currentUserProfile);
  }

  /**
   * Use to remove annotations of relevent collection and operationId
   * @param collectionId {string} id of the collection
   * @param operationId {string} id of the operation
   * @param authorization basic auth header
   * @returns {isSuccess: boolean}
   */
  @get('/api/client/collection/deleteAnnotation')
  async deleteAnnotation(
    @param.query.string('collectionId') collectionId: string,
    @param.query.string('operationId') operationId: string,
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    data: {
      sessionId: string;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    if (!collectionId || !operationId) {
      throw new HttpErrors.Unauthorized(`Error : 'collectionId or operationId not received`);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.metaDataUpdateService.deleteCollectionAnnotation(
      collectionId,
      operationId,
      data.sessionId,
      currentUserProfile,
    );
  }

  /**
   * Use for handle initialize uploading multipart files to s3 from frontend
   */
  @post('api/client/fileUpload/initializeMultipartUpload')
  async initializeMultipartUpload(
    @requestBody()
    body: {
      fileName: string;
      collectionName: string;
      uploadId: string;
    },
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    const apiKey = authorizeDetails.key;

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.fileUploadHandlerService.initializeMultipartUpload(
      body.fileName,
      body.collectionName,
      body.uploadId,
      apiKey,
      currentUserProfile,
    );
  }

  /**
   * Use for handle chunk list urls for uploading multipart files to s3 from frontend
   */
  @post('api/client/fileUpload/getMultipartPreSignedUrls')
  async generateMultipartPreSignedUrls(
    @requestBody()
    body: {
      fileKey: string;
      fileId: number;
      parts: number;
      contentType?: string;
      isDisableMultipart?: boolean;
    },
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    return this.fileUploadHandlerService.generateMultipartPreSignedUrls(
      body.fileKey,
      body.fileId,
      body.parts,
      body.contentType,
      body.isDisableMultipart,
    );
  }

  /**
   * Use for finalize multipart file upload to s3 from frontend
   */
  @post('api/client/fileUpload/finalizeMultipartUpload')
  async finalizeMultipartUpload(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      fileKey: string;
      fileId: string;
      parts: any;
      uploadId: string;
      finalizeUrl: string;
      isDisableMultipart?: boolean;
      isSkipInputMetadataQueue?: boolean;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    const apiKey = authorizeDetails.key;

    logger.info(
      `finalizeMultipartUpload | DatalakeClientInterfaceController.finalizeMultipartUpload | apiKey: ${apiKey}`,
    );

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.fileUploadHandlerService.finalizeMultipartUpload(
      body.fileKey,
      body.fileId,
      body.parts,
      body.uploadId,
      apiKey,
      body.finalizeUrl,
      body.isDisableMultipart,
      body.isSkipInputMetadataQueue,
      currentUserProfile,
      this.objectMetaUpdaterService,
    );
  }

  @get('api/client/collectionUploadingStatus/{uploadId}/complete')
  async markCollectionUploadComplete(
    @param.header.string('Authorization') authorization: string,
    @param.path.string('uploadId') uploadId: string,
    @param.query.boolean('isReturnedUniqueName') isReturnedUniqueName: boolean,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    return this.fileUploadHandlerService.markCollectionUploadComplete(uploadId, isReturnedUniqueName);
  }

  /**
   * export annotation of a collection
   * use for get data of all images and annotations of given collection
   * @param groupName, versionNo
   * @returns list of frames
   */
  @get(baseUrl + '/getAnnotations/{collectionId}/collection')
  async getAnnotationsOfCollection(
    @param.path.string('collectionId') collectionId: string,
    //  @param.path.string('exportType') exportType: string,
    @param.query.number('pageNo') pageNo: number,
    @param.query.number('pageSize') pageSize: number,
    @inject(RestBindings.Http.REQUEST) request: Request,
    @param.query.string('annotationType')
    annotationTypeString: AnnotationTypeString,
    @param.query.string('operationIdList') operationIdList: string,
    @param.query.string('sessionId') sessionId: string,
  ) {
    logger.debug(
      `Download annotations| DatasetController.getGroupVersionDataset | N/A | Get collection data request collectionId:${collectionId}`,
    );

    const headers = {...request.headers};
    const basicAuth = headers.authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(basicAuth);

    //  logger.debug('api key details: ', apiKeyDetails)
    // logger.debug(`get annotations request for collectionId: ${collectionId}`);

    const teamId = authorizeDetails.teamId; //??????????????????????

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.annotationDownloadHandlerService.getAnnotationsOfCollection(
      collectionId,
      pageNo,
      pageSize,
      teamId,
      JSON.parse(operationIdList),
      annotationTypeString,
      sessionId,
      currentUserProfile,
    );
  }

  /**
   * Use to get selection id from query, filter and collection
   */
  @post('api/client/query/getSelectionId')
  async getSelectionId(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: DatalakeSelectionRequest,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    logger.info(
      `finalizeMultipartUpload | DatalakeClientInterfaceController.finalizeMultipartUpload | request body: `,
      body,
    );

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getSelectionId | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    return this.datalakeSelectionService.createProjectSelectionTag(body, currentUserProfile);
  }

  /**
   * Use to create virtual collection
   * @param authorization basic auth
   * @param body
   * @returns virtual collection id
   */
  @post('api/client/vCollection/create')
  async createVirtualCollection(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    data: {
      selectionId: string;
      vCollectionId?: string;
      vCollectionName?: string;
      customMeta?: {[k: string]: any};
    },
    @param.query.string('userInfo') userInfo: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    logger.debug(
      `Virtual collection | DatalakeClientInterfaceController.createVirtualCollection | ${teamId} | request body: ${data}`,
    );

    if (!teamId) {
      logger.error(
        `Virtual collection | DatalakeSelectionController.createVirtualCollection | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );

      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    // if custom meta have annotationProjectList then iterate list and {id:string, name:string} id replace with ObjectId
    if (data.customMeta?.annotationProjectList) {
      data.customMeta.annotationProjectList = data.customMeta.annotationProjectList.map(
        (project: {id: string; name: string}) => {
          return {
            id: new ObjectId(project.id),
            name: project.name,
          };
        },
      );
    }

    const config: UpdateJobConfig = {isAwait: false};

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    if (userInfo) {
      currentUserProfile = JSON.parse(userInfo);
    }

    if (!currentUserProfile?.teamId) {
      logger.error(
        `find operation list | DatasetManagerInterfaceController.findOperationList | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return this.metaDataService.updateVirtualCollection(
      currentUserProfile,
      data.selectionId,
      config,
      data.vCollectionId,
      data.vCollectionName,
      data.customMeta,
    );
  }

  @post('api/client/file/trash')
  async trashObjectsBySelectionId(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {selectionId: string; userId: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getSelectionId | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    // validate selectionId
    const selectionObj = await this.datalakeSelectionRepository.findById(body.selectionId);

    return this.datalakeTrashService.trashSelectionObject(selectionObj, currentUserProfile, body.userId);
  }

  @post('api/client/file/restore')
  async restoreTrashedObjects(
    @param.header.string('Authorization') authorization: string,
    @requestBody() data: TrashRestoreRequest,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getSelectionId | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const searchKey = data.searchKey ? data.searchKey : '';
    const isAllSelected = data.isAllSelected ? data.isAllSelected : false;
    const objectIdList = data.objectIdList ? data.objectIdList : [];

    let filter: ExplorerFilterV2 = {};
    if (data.filterData) filter = data.filterData;

    const startTime = new Date().getTime();

    const response = await this.datalakeTrashService.restoreTrashObject(
      objectIdList,
      searchKey,
      isAllSelected,
      currentUserProfile,
      undefined,
      filter,
      ContentType.ALL,
      authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
    );

    const endTime = new Date().getTime();
    const responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.restoreTrashObject | N/A | Response Time = ${responseTime} ms`,
    );

    return response;
  }

  /**
   * use to get object type by object id
   * @params id : string
   * @returns {objectType: number}
   */
  @get('api/client/{id}/getObjectTypeById')
  async getObjectTypeById(
    @param.header.string('Authorization') authorization: string,
    @param.path.string('id') id: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    return this.metaDataService.getObjectTypeById(id);
  }

  /**
   * Use for get the job status
   * @param authorization basic auth key
   * @param jobId {string} id of the job
   * @returns status of the job
   */
  @get('/api/client/jobs/{jobId}/getStatus')
  async getJobStatus(
    @param.header.string('Authorization') authorization: string,
    @param.path.string('jobId') jobId: string,
  ) {
    const authorizationKeySecret = authorization;

    await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const job = await this.jobService.getJobStatus(jobId);

    return job;
  }

  /**
   * check compatibility with python sdk
   * @param authorization basic auth key
   * @param sdkVersion sdk version
   * @returns { isCompatible: boolean, message: string}
   */
  @get('/api/client/sdk/compatibility/{sdkVersion}')
  async checkCompatibilityWithPythonSdk(
    @param.header.string('Authorization') authorization: string,
    @param.path.string('sdkVersion') sdkVersion: string,
  ) {
    const authorizationKeySecret = authorization;

    await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const response = await this.systemValidationService.checkCompatibilityWithPythonSdk(sdkVersion);

    console.log('response', response);

    return response;
  }

  /**
   * Get the download url (pre-signed url) of given object key
   * @param authorization basic auth key
   * @param objectKey object key
   */
  @get('/api/client/downloadUrl')
  async getDownloadUrl(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('file_key') objectKey: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    objectKey = decodeURIComponent(objectKey);

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    if (
      (currentUserProfile?.userType || currentUserProfile?.userType == 0) &&
      [UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA, UserType.USER_TYPE_COLLABORATOR].includes(
        currentUserProfile?.userType,
      )
    ) {
      if ([UserType.USER_TYPE_ANNOTATOR, UserType.USER_TYPE_QA].includes(currentUserProfile?.userType)) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
      const metaObject = await this.metaDataRepository.findOne({
        where: {objectKey: objectKey},
      });
      const allowedUserIdList = metaObject?.allowedUserIdList?.map(id => String(id));
      if (!allowedUserIdList?.includes(String(currentUserProfile.id))) {
        throw new HttpErrors.Forbidden(DatalakeUserMessages.UNAUTHORIZED_CONTENT);
      }
    }

    const downloadUrl = await this.metaDataService.getDownloadableUrl(objectKey);

    return {
      url: downloadUrl,
      isSuccess: true,
    };
  }

  /**
   * Delete cloud prefix objects
   * @param authorization basic auth key
   */
  @post('/api/client/delete/cloudPrefix')
  async deleteCloudPrefix(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    details: {
      prefix: string;
    },
  ) {
    const authorizationKeySecret = authorization;

    await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    return this.metaDataService.deleteCloudPrefix(details.prefix);
  }

  /**
   * Upload metadata by using json file with object keys
   * @param authorization basic auth key
   * @param body {collectionName: string, metadata: SDKMetaUpdateInput[]}
   * @returns
   */
  @post('/api/client/metadata/uploadMetadataByJson')
  async uploadMetadataByJsonObjectKeys(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: SDKMetaUpdateRequestBody,
  ) {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DataLakeClientInterface.uploadMetadataByJsonObjectKeys | N/A | Couldn't find the team for:`,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    logger.info(`Upload metadata by sdk | DataLakeClientInterface.uploadMetadataByJsonObjectKeys | N/A | N/A`);
    logger.debug(JSON.stringify(body));

    return this.objectMetaUpdaterService.handleAddOrUpdateMetaDataByObjectKeys(
      body.metadata,
      currentUserProfile,
      body.bucketName,
      body.jobId,
      body.collectionId,
    );
  }

  /**
   * upload metadata by using metadata object
   * @param authorization basic auth key
   * @param body {collectionName: string, metadata: Record<string, string | string[]>, isApplyToAllFiles: boolean}
   * @returns success or error
   */
  @post('/api/client/metadata/uploadMetadataByMetaObject')
  async uploadMetadataByMetaObject(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      collectionName: string;
      objectType: ContentType;
      metadata: Record<string, string | string[]>;
      isApplyToAllFiles: boolean;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getSelectionId | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.objectMetaUpdaterService.addOrUpdateMetaDataByMetaObject(
      body.collectionName,
      body.objectType,
      body.metadata,
      body.isApplyToAllFiles,
      currentUserProfile,
    );
  }

  /**
   * Use for validate tags
   * @param authorization basic auth key
   * @param body {tags: string[]}
   * @returns if error occurs then it will return error message
   */
  @post('/api/client/metadata/validateTags')
  async validateTags(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {tags: string[]},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Validate Tags | DatalakeClientInterfaceController.validateTags | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.objectMetaUpdaterService.sdkTagsValidate(body.tags, teamId, currentUserProfile);
  }

  /**
   * Use for validate metadata fields
   * @param authorization basic auth key
   * @param body {fields: string[]}
   * @returns if error occurs then it will return error message
   */
  @post('/api/client/metadata/validateMetaFields')
  async validateMetaFields(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {fields: string[]},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(
      `Metadata field validation reuquest from sdk | DatalakeClientInterfaceController.validateMetaFields | N/A | teamId: ${teamId}`,
    );

    if (!teamId) {
      logger.error(
        `Validate fields | DatalakeClientInterfaceController.validateMetaFields | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.objectMetaUpdaterService.sdkMetaFieldsValidate(body.fields, teamId, currentUserProfile);
  }

  /**
   * Use for get item details with required metadata
   * @param authorization basic auth key
   * @param body {uniqueFileName: string; requiredMetaObj: {[k: string]: boolean}}
   * @returns item details with required metadata
   */
  @post('/api/client/metadata/getItemDetails')
  async getItemDetails(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {uniqueFileName: string; requiredMetaObj: {[k: string]: boolean}},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get Item Details | DatalakeClientInterfaceController.getItemDetails | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.metaDataService.getItemDetails(body.uniqueFileName, body.requiredMetaObj, currentUserProfile);
  }

  /**
   * Use to get object list for datalake explorer tab initial view
   * @param filterObj {ExplorerDefaultViewRequest} object list filtering options
   * contentType {ContentType} type of which objects should include in response
   * pageIndex {number} page number [start from 0]
   * pageSize {number} page size [default 4]
   * @param converstationId
   * str
   * @returns ExplorerDefaultViewResponse
   */
  @post('/api/client/explorer/objects/list')
  async getObjectListToExplorer(
    @requestBody() filterObj: ExploreObjectListRequest,
    @param.header.string('Authorization') authorization: string,
  ) {
    logger.debug(`Requested`);
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Validate Tags | DatalakeClientInterfaceController.validateTags | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let pageSize = 4;
    if (filterObj.pageSize) {
      pageSize = filterObj.pageSize;
      if (pageSize > 1000) {
        pageSize = 1000;
      }
    }

    const startTime = new Date().getTime();
    const pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;

    const referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    const sortObj: SortObject = {
      sortByField: filterObj.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj.sortBy?.sortOrder,
    };
    let query: object = {};

    const rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    const projectObj = {
      $project: {
        _id: 1,
        name: 1,
        frameCount: 1,
        objectKey: 1,
        otherCount: 1,
        objectType: 1,
        url: 1,
        objectStatus: 1,
        score: 1,
        fileTitle: 1,
        namedEntities: 1,
        fileType: 1,
      },
    };

    const response = await this.searchQueryBuilderService.getObjectListToExplorer(
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      pageIndex,
      pageSize,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.LIST,
      projectObj,
      sortObj.sortOrder,
    );

    let itemList: any[] = [];
    const contentType: ContentType = filterObj.contentType;
    if (contentType == ContentType.IMAGE) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
            uniqueName: item.objectKey,
            url: item.url,
            fileType: item.fileType,
          };
        });
      }
    } else if (contentType == ContentType.VIDEO) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
            uniqueName: item.objectKey,
            url: item.url,
            fileType: item.fileType,
          };
        });
      }
    } else if (contentType == ContentType.OTHER) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
            uniqueName: item.objectKey,
            url: item.url,
            score: item.score,
            documentTitle: item.fileTitle,
            namedEntities: item.namedEntities,
            fileType: item.fileType,
          };
        });
      }
    } else if (contentType == ContentType.DATASET) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      }
    } else if (contentType == ContentType.IMAGE_COLLECTION) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      }
    } else if (contentType == ContentType.VIDEO_COLLECTION) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      }
    } else if (contentType == ContentType.OTHER_COLLECTION) {
      if (response && response.itemList) {
        itemList = response.itemList.map(item => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      }
    }

    return itemList;
  }

  /**
   * Use to get object count for datalake explorer
   * @param filterObj {ExplorerCollectionViewRequest} object list filtering options
   * @param authorization Authorization header use to validate user
   * @returns {count: count}
   */
  @post('/api/client/explorer/objects/count')
  async getObjectCountToExplorer(
    @requestBody() filterObj: ExploreObjectListRequest,
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Validate Tags | DatalakeClientInterfaceController.validateTags | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let pageSize = 4;
    if (filterObj.pageSize) {
      pageSize = filterObj.pageSize;
      if (pageSize > 1000) {
        pageSize = 1000;
      }
    }

    const pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;

    const referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    const sortObj: SortObject = {
      sortByField: filterObj.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj.sortBy?.sortOrder,
    };
    let query: object = {};

    const rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    const projectObj = {
      $project: {
        _id: 1,
        name: 1,
        frameCount: 1,
        objectKey: 1,
        otherCount: 1,
        objectType: 1,
        url: 1,
        objectStatus: 1,
      },
    };

    const response = await this.searchQueryBuilderService.getObjectListToExplorer(
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      pageIndex,
      pageSize,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.COUNT,
      projectObj,
      sortObj.sortOrder,
    );

    let count = response.count;
    if (referenceImage) {
      count += 1;
    }

    return {count: count};
  }

  /**
   * Use to get object list for datalake explorer collection view
   * @param filterObj {ExplorerCollectionViewRequest} object list filtering options
   * collectionId {string} id of the collection which is going to view
   * pageIndex {number} page number [start from 0]
   * pageSize {number} page size [default 16]
   * @returns ExplorerCollectionViewResponse
   */
  @post('/api/client/explorer/{collectionId}/objects/list')
  async getObjectListOfCollection(
    @param.path.string('collectionId') collectionId: string,
    @requestBody() filterObj: ExploreObjectListRequest,
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let pageSize = 4;
    if (filterObj.pageSize) {
      pageSize = filterObj.pageSize;
      if (pageSize > 1000) {
        pageSize = 1000;
      }
    }

    const startTime = new Date().getTime();
    const pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    const referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    const sortObj: SortObject = {
      sortByField: filterObj.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj.sortBy?.sortOrder,
    };

    const rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    let query: object = {};
    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    const projectObj = {
      $project: {
        _id: 1,
        name: 1,
        frameCount: 1,
        otherCount: 1,
        objectType: 1,
        objectKey: 1,
        url: 1,
        objectStatus: 1,
      },
    };

    const response = await this.searchQueryBuilderService.getObjectListOfCollection(
      collectionId,
      pageIndex,
      pageSize,
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.LIST,
      projectObj,
      sortObj.sortOrder,
    );

    const itemList = response.itemList.map(item => {
      return {
        id: item.id,
        name: item.name,
        uniqueName: item.objectKey,
        url: item.url,
      };
    });

    return itemList;
  }

  /**
   * Use to get object count for datalake explorer collection view
   * @param collectionId {string} id of the collection which is going to view
   * @param filterObj {ExplorerCollectionViewRequest} object list filtering options
   * @param authorization Authorization header use to validate user
   * @returns {count: count}
   */
  @post('/api/client/explorer/{collectionId}/objects/count')
  async getObjectCountOfCollection(
    @param.path.string('collectionId') collectionId: string,
    @requestBody() filterObj: ExploreObjectListRequest,
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let pageSize = 4;
    if (filterObj.pageSize) {
      pageSize = filterObj.pageSize;
      if (pageSize > 1000) {
        pageSize = 1000;
      }
    }

    const pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    const referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    const sortObj: SortObject = {
      sortByField: filterObj.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj.sortBy?.sortOrder,
    };

    const rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    let query: object = {};
    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    const projectObj = {
      $project: {
        _id: 1,
        name: 1,
        frameCount: 1,
        otherCount: 1,
        objectType: 1,
        objectKey: 1,
        url: 1,
        objectStatus: 1,
      },
    };

    const response = await this.searchQueryBuilderService.getObjectListOfCollection(
      collectionId,
      pageIndex,
      pageSize,
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.COUNT,
      projectObj,
      sortObj.sortOrder,
    );

    let count = response.count;
    if (referenceImage) {
      count += 1;
    }

    return {count: count};
  }

  /**
   * Use for get collection details with required metadata
   * @param authorization basic auth key
   * @param body {collectionId: string; requiredMetaObj: {[k: string]: boolean}}
   * @returns collection details with required metadata
   */
  @post('/api/client/metadata/getCollectionDetails')
  async getCollectionDetails(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {collectionId: string; requiredMetaObj: {[k: string]: boolean}},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get Collection Details | DatalakeClientInterfaceController.getCollectionDetails | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return this.metaDataService.getCollectionDetails(body.collectionId, body.requiredMetaObj, currentUserProfile);
  }

  /**
   * Use for get collection id by name
   * @param authorization basic auth key
   * @param body {collectionName: string; objectType: ContentType}
   * @returns collection id
   */
  @post('/api/client/metadata/getCollectionIdByName')
  async getCollectionIdByName(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {collectionName: string; objectType: ContentType},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get Collection Id by Name | DatalakeClientInterfaceController.getCollectionIdByName | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    //validate objectType
    if (
      ![
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.DATASET,
        ContentType.OTHER_COLLECTION,
      ].includes(body.objectType)
    ) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    const result = await this.datalakeExplorerService.getCollectionIdByName(
      body.collectionName,
      body.objectType,
      currentUserProfile,
    );

    if (result == null) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_COLLECTION_NAME_OR_OBJECT_TYPE);
    }

    return {
      isSuccess: true,
      collectionId: result.id,
    };
  }

  @post('/api/client/metadata/collection/create')
  async createCollectionHead(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {collectionName: string; objectType: ContentType; customMetaObject: {[k: string]: any}},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Create collection head | DatalakeClientInterfaceController.createCollectionHead | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    //validate objectType
    if (
      ![ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
        body.objectType,
      )
    ) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    return this.metaDataService.createCollectionHead(
      body.collectionName,
      body.objectType,
      currentUserProfile,
      body.customMetaObject,
    );
  }

  /**
   * Use for find the text chunk for given search keys
   * @param authorization
   * @param body search keys and unique names
   * @returns text chunk list
   */
  @post('/api/client/find/chunk')
  async findChunk(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      searchKeys: string[];
      uniqueNames: string[];
      chunkLimit: number;
      adjacentChunkLimit: number;
      pageIndex: number;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Create collection head | DatalakeClientInterfaceController.createCollectionHead | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    return this.embeddingService.findChunk(
      body.searchKeys,
      body.uniqueNames,
      body.pageIndex,
      body.chunkLimit,
      body.adjacentChunkLimit,
      currentUserProfile,
    );
  }

  /**
   * Use for find the text chunk for given search keys
   * @param authorization
   * @param body search keys and unique names
   * @returns text chunk list
   */
  @post('/api/client/find/document/chunk')
  async findDocumentChunk(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      searchKeys: string[];
      uniqueNames: string[];
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Create collection head | DatalakeClientInterfaceController.createCollectionHead | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    return this.embeddingService.findDocumentChunk(body.searchKeys, body.uniqueNames, currentUserProfile);
  }

  /**
   * Use for find the matching document elements for the user question from the given list of documents
   * @param authorization
   * @param body question and document keys
   * @returns elements list
   */
  @post('/api/client/find/elements')
  async findDocElements(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      userQuestion: string;
      originalQuestion: string | '';
      objectKeyList: string[];
      isTargetDocumentsExist: boolean;
      converasationId: string | null;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get matching doc elements | DatalakeClientInterfaceController.findDocElements | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const elementResponse = this.datalakeExplorerService.getMatchingElements(
      body.userQuestion,
      body.originalQuestion,
      body.objectKeyList,
      body.isTargetDocumentsExist,
      body.converasationId || '',
    );
    return elementResponse;
  }

  /**
   * Use for extracting the document content for the user question from the given json document structural data
   * @param authorization
   * @param body question and document keys
   * @returns elements list
   */
  @post('/api/client/extract/doc_content')
  async extractDocumentDataforAI(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      docElements: object;
      userQuestion: string;
      extractionKeys: object[];
      converasationId: string | null;
    },
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get matching doc elements | DatalakeClientInterfaceController.findDocElements | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const contentResponse = this.datalakeExplorerService.extractDocumentDataForAI(
      body.userQuestion,
      body.docElements,
      body.extractionKeys,
      body.converasationId || '',
    );
    return contentResponse;
  }

  /**
   * Use to get dictionary data
   * @returns if it is success return dictionary data other wise return failed reason with message
   */
  @get('api/client/get/data/dictionary')
  async getDataDictionary(@param.header.string('Authorization') authorization: string) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    return this.srcDestConnectionService.getDictionaryData();
  }

  /**
   * Use to execute mongo db query
   * @param authorization authorization head
   * @param body {function: string (find, aggregate...); query: object or list; collection: string}
   * @returns result of execute mongo query
   */
  @post('/api/client/execute/mongoQuery')
  async executeMongoQuery(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceName: string; function: string; query: any; collection: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Run mongodb execute | DatalakeClientInterfaceController.executeMongoQuery | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const properties = {
      query: body.query,
      function: body.function,
      collection: body.collection,
    };

    const res = await this.srcDestConnectionService.retrieveData(body.sourceName, properties);
    const eJSONRes = EJSON.stringify(res);

    return eJSONRes;
  }

  /**
   * Use to insert data into a specified MongoDB collection
   * @param authorization authorization head
   * @param body {collectionName: string; data: any[]; sourceName: string;}
   * @returns result of the insertion operation
   */
  @post('/api/client/execute/mongoInsert')
  async insertMongoData(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceName: string; collectionName: string; data: any[]},
  ) {
    const authorizationKeySecret = authorization;

    // Assuming a basic authentication check
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `Run mongodb Insert | DatalakeClientInterfaceController.insertMongoData | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable('Team ID is missing or invalid.');
    }

    // Perform the insert operation for the specified collection
    const insertResult = await this.srcDestConnectionService.insertData(
      body.sourceName,
      ConnectionSourceType.MONGO_DB,
      body.collectionName,
      body.data,
    );

    return insertResult;
  }
  /**
   * Use to delete data from a specified MongoDB collection
   * @param authorization authorization header
   * @param body {collectionName: string; filter: object; sourceName: string;}
   * @returns result of the delete operation
   */
  @post('/api/client/execute/mongoDelete')
  async deleteMongoData(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceName: string; collectionName: string; filter: object},
  ) {
    const authorizationKeySecret = authorization;

    // Assuming a basic authentication check
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `Run mongodb Delete | DatalakeClientInterfaceController.deleteMongoData | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable('Team ID is missing or invalid.');
    }

    // Perform the delete operation for the specified collection
    const deleteResult = await this.srcDestConnectionService.deleteData(
      body.sourceName,
      ConnectionSourceType.MONGO_DB,
      body.collectionName,
      body.filter,
    );

    return deleteResult;
  }

  /**
   * Get the data dictionary from the database.
   *
   * This endpoint fetches the list of connection IDs, retrieves table data for each connection,
   * and structures the final response object containing overviews and source data with table schemas.
   * @param authorization authorization head
   * @param response - The HTTP response object used to return the result or error.
   * @returns {Promise<{ OverViews: any; sourceData: { dataSourceName: string; tables: any[] }[] }>}
   *          The structured JSON response with overviews and table schemas.
   */
  @get('/api/client/get/dataDictionaryFromDB')
  async syncDataDictionary(
    @param.header.string('Authorization') authorization: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<{OverViews: any; sourceData: {dataSourceName: string; tables: any[]}[]}> {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Run mongodb execute | DatalakeClientInterfaceController.executeMongoQuery | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    try {
      const businessOverview = await this.systemMetaService.getBusinessOverview();
      const dataSourcesOverviews = await this.systemMetaService.getDataSourcesOverview();
      const businessRules = await this.businessRuleService.getFormattedBusinessRules();
      const knowledgeBlocksWithTree = await this.knowledgeBlockService.getKnowledgeBlocksWithTree();

      const dataSourcesAndBusinessOverviews = {
        dataSourcesOverview: dataSourcesOverviews,
        businessOverview: businessOverview.description,
        businessRules: businessRules,
        knowledgeBlocksWithTree: knowledgeBlocksWithTree,
      };

      logger.info(
        `syncDataDictionary | DatalakeClientInterfaceController.syncDataDictionary | N/A | dataSourcesAndBusinessOverviews : `,
        dataSourcesAndBusinessOverviews,
      );

      const dataDictionaryMappingList = await this.connectionService.getDataDictionarySectionList();
      const tableSchemas = await this.connectionService.getTableSchemas();

      // Structure the final response object
      const final = {
        OverViews: dataSourcesAndBusinessOverviews,
        sourceData: tableSchemas,
        dataDictMapping: dataDictionaryMappingList,
      };

      logger.info(`syncDataDictionary | DatalakeClientInterfaceController.syncDataDictionary | N/A | Final : `, final);

      // Return the structured JSON response
      return final;
    } catch (error) {
      // Log and handle the error
      console.error('Error syncing data dictionary:', error);

      // Return a structured error response with appropriate HTTP status code
      response.status(500).send({
        message: 'Internal Server Error',
        details: error.message,
      });
      // Throw the error to stop further execution
      throw error;
    }
  }

  /**
   * Endpoint to check the latest update timestamp of data dictionary-related data.
   *
   * This endpoint fetches the list of connection IDs, retrieves the last modified time for each data source,
   * and determines the latest update timestamp among all the sources.
   * @param authorization authorization head
   * @param response - The HTTP response object used to return the result or error.
   *
   * @returns {Promise<Date>}
   *          The latest update timestamp among all data sources and tables.
   */
  @get('/api/client/get/dataDictionaryUpdatedTime')
  async CheckDataDictionary(
    @param.header.string('Authorization') authorization: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<Date> {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Run mongodb execute | DatalakeClientInterfaceController.executeMongoQuery | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    try {
      let latestUpdate = new Date(0);
      const collectionIdList = await this.connectionService.returnConnectionId();
      const connectionDetails: {connectionId: string; name: string}[] = JSON.parse(collectionIdList);
      const dataSourcesAndBusinessOverviewsModifiedTime =
        await this.systemMetaService.getDataSourcesAndBusinessOverviewsLatestModified();
      if (dataSourcesAndBusinessOverviewsModifiedTime > latestUpdate) {
        latestUpdate = dataSourcesAndBusinessOverviewsModifiedTime;
      }
      const dataDictionaryMappingModifiedTime = await this.systemMetaService.getDataDictionaryMappingLatestModified();
      if (dataDictionaryMappingModifiedTime > latestUpdate) {
        latestUpdate = dataDictionaryMappingModifiedTime;
      }
      for (const connection of connectionDetails) {
        const tableDataUpdatedAt = await this.tableDataService.getTableDataUpdatedAtByConnectionID(
          connection.connectionId,
        );
        if (tableDataUpdatedAt > latestUpdate) {
          latestUpdate = tableDataUpdatedAt;
        }
      }
      const businessRulesModifiedTime = await this.businessRuleService.getBusinessRulesLatestModifiedDate();
      if (businessRulesModifiedTime > latestUpdate) {
        latestUpdate = businessRulesModifiedTime;
      }

      const metaDataLastUpdatedAt = await this.systemMetaService.getMetaDataLastUpdatedAt();
      if (metaDataLastUpdatedAt > latestUpdate) {
        latestUpdate = metaDataLastUpdatedAt;
      }

      return latestUpdate;
    } catch (error) {
      // Log and handle the error
      console.error('Error getting data dictionary last update:', error);

      // Return a structured error response with appropriate HTTP status code
      response.status(500).send({
        message: 'Internal Server Error',
        details: error.message,
      });
      // Throw the error to stop further execution
      throw error;
    }
  }
  /**
   * Use to execute sql db query
   * @param authorization authorization head
   * @param body {function: string (find, aggregate...); query: object or list; collection: string}
   * @returns result of execute sql query
   */
  @post('/api/client/execute/sqlQuery')
  async executeSQLQuery(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceName: string; query: any},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Run mysql execute | DatalakeClientInterfaceController.executeSQLQuery | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const properties = {
      query: body.query,
    };
    return this.srcDestConnectionService.retrieveData(body.sourceName, properties);
  }

  /**
   * Use to get sql schema
   * @param authorization authorization head
   * @param body - sourceName
   * @returns result of execute sql schema
   */
  @post('/api/client/schema/sql')
  async getSQLSchema(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceName: string},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Run mysql execute | DatalakeClientInterfaceController.executeSQLQuery | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    return this.srcDestConnectionService.getDatabaseSchema(body.sourceName, ConnectionSourceType.MYSQL_DB);
  }

  /**
   * Get connection source and their types
   * @param authorization authorization head
   * @param body list of sources to be filtered
   * @returns list of source and their types
   */
  @post('api/client/connection/get/sourceAndType')
  async getConnectionSourceType(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {sourceList: string[]},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | DatalakeClientInterfaceController.getConnectionSourceType | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    return this.srcDestConnectionService.getConnectionSourceType(body.sourceList);
  }

  /**
   * Use for get meta data details for objectKey
   * @param authorization authorization head
   * @param objectKey object key of the metadata
   * @returns details
   */
  @get('api/client/metadata/getDetails')
  async getMetadataDetails(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('objectKey') objectKey: string,
    @param.query.string('fields') fields: string,
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | DatalakeClientInterfaceController.getConnectionSourceType | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const projectArray = JSON.parse(String(fields));
    const projectObject: any = {};
    projectArray.forEach((project: string) => {
      projectObject[project] = true;
    });
    return this.metaDataRepository.findOne({
      where: {objectKey: objectKey},
      fields: projectObject,
    });
  }

  /**
   * Retrieve Db Unstructured Records
   * @param body.categories {string[]} Category names relevant to each section of data being considered for locating records.
   * @param body.filterKeyValues {[key: string]: any} Key value pairs derived from the question to locate matching records.
   * @param body.pageIndex {number} Index for the batch required.
   * @returns [{"keyInfo": {"key_1": <value_1>, "key_2": <value_2>},"data": ["data_1", "data_2", ....]}]
   */
  @post('/api/client/retrieve/unstructuredRecords')
  async retrieveDbUnstructuredRecords(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {categories: string[]; filterKeyValues: FilterKeyValues; pageIndex: number},
  ) {
    const authorizationKeySecret = authorization;

    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | DatalakeClientInterfaceController.getConnectionSourceType | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    return this.metaDataService.retrieveDbUnstructuredRecords(body.categories, body.filterKeyValues, body.pageIndex);
  }

  /**
   * Endpoint to retrieve the schema for a specific table from the data source.
   *
   * This endpoint takes in the source name and table name, authenticates the user,
   * and retrieves the schema information for the specified table from the corresponding data source.
   * It returns the table name, description, and a list of visible fields, including their name, data type, and description.
   *
   * @param authorization The authorization token in the request header.
   * @param sourceName The name of the data source from which the table schema will be retrieved.
   * @param tableName The name of the table whose schema is to be retrieved.
   *
   * @returns {Promise<{
   *   table_name: string;
   *   table_description: string;
   *   fields: { name: string; dataType: string; description: string; }[]
   * } | { error: string }>}
   *          The table schema containing the table name, description, and list of fields, or an error message.
   */
  @get('api/client/get/tableSchema')
  async getTableSchema(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('sourceName') sourceName: string,
    @param.query.string('tableName') tableName: string,
  ): Promise<
    | {
        table_name: string;
        table_description: string;
        fields: {name: string; dataType: string; description: string}[];
      }
    | {error: string}
  > {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | DatalakeClientInterfaceController.getTableSchema | N/A |Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    logger.debug(
      `Successfully connected | DatalakeClientInterfaceController.getTableSchema | N/A | teamId: ${teamId}, sourceName: ${sourceName}, tableName: ${tableName}`,
    );
    try {
      const tableSchema = await this.tableDataService.getTableSchema(sourceName, tableName);
      return tableSchema;
    } catch (error) {
      logger.error(
        `DatalakeClientInterfaceController | getTableSchema | N/A | Failed to retrieve schema for tableName: ${tableName} in sourceName: ${sourceName}. Error: ${error.message}`,
      );
      throw new HttpErrors.InternalServerError(
        `Failed to fetch the table schema for table ${tableName} in source ${sourceName}. Error: ${error.message}`,
      );
    }
  }

  /**
   * Endpoint to submit user feedback for the AI response to the user query.
   *
   * This endpoint takes in the user query, AI response, and feedback comment,
   * and submits the user feedback to the Auto Tuneup service
   *
   * @param authorization The authorization token in the request header.
   * @param userQuestion The user query for which the AI response was received.
   * @param aiAnswer The AI response for the user query.
   * @param feedbackComment The user's feedback comment for the AI response.
   *
   * @returns {Promise<{ success: boolean } | { error: string }>}
   *          The response indicating whether the feedback was successfully submitted, or an error message.
   */
  @post('api/client/chat_feedback/submit')
  async submitUserFeedback(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      userQuestion: string;
      aiAnswer: string;
      feedbackComment: string;
    },
  ): Promise<{success: boolean} | {error: string}> {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | submitUserFeedback | N/A |Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    logger.debug(
      `Successfully connected | submitUserFeedback | N/A | teamId: ${teamId}, userQuestion: ${body.userQuestion}, aiAnswer: ${body.aiAnswer}, feedbackComment: ${body.feedbackComment}`,
    );
    try {
      const feedbackResponse = await this.evaluationService.onUserFeedback(
        body.userQuestion,
        body.aiAnswer,
        body.feedbackComment,
      );
      return {success: feedbackResponse};
    } catch (error) {
      logger.error(
        `DatalakeClientInterfaceController | submitUserFeedback | N/A | Failed to submit user feedback. Error: ${error.message}`,
      );
      throw new HttpErrors.InternalServerError(`Failed to submit user feedback. Error: ${error.message}`);
    }
  }

  @post('api/client/knowledge-blocks/create')
  async createKnowledgeBlocks(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      knowledge_blocks: {[key: string]: any}[];
      knowledge_tree: {[key: string]: any};
    },
  ) {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | createKnowledgeBlocks | N/A |Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    logger.debug(`Successfully connected | createKnowledgeBlocks | N/A | teamId: ${teamId}`);

    if (!body || !body.knowledge_blocks || body.knowledge_blocks.length === 0) {
      logger.error(`DatalakeClientInterfaceController | createKnowledgeBlocks | N/A | No knowledge blocks to create`);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_BLOCK_CREATE_FAILED);
    }

    if (!body.knowledge_tree) {
      logger.error(`DatalakeClientInterfaceController | createKnowledgeBlocks | N/A | No knowledge tree to create`);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.KNOWLEDGE_BLOCK_CREATE_FAILED);
    }

    try {
      const res = await this.knowledgeBlockService.createKnowledgeBlocks(body.knowledge_blocks, body.knowledge_tree);
      if (res.isSuccess) {
        return {success: true};
      } else {
        logger.error(
          `DatalakeClientInterfaceController | createKnowledgeBlocks | N/A | Failed to create knowledge blocks. Error: ${res.message}`,
        );
        throw new HttpErrors.NotAcceptable(res.message);
      }
    } catch (error) {
      logger.error(
        `DatalakeClientInterfaceController | createKnowledgeBlocks | N/A | Failed to create knowledge blocks. Error: ${error.message}`,
      );
      throw new HttpErrors.InternalServerError(`Failed to create knowledge blocks. Error: ${error.message}`);
    }
  }

  /**
   * Endpoint to retrieve the suggested questions from the SystemData collection.
   *
   * This endpoint takes in the authorization token, authenticates the user, and retrieves the suggested
   * questions from the SystemData collection. It returns the suggested questions as an array of strings.
   * If the suggestedQuestions field is missing or if an error occurs during the database query, an empty
   * array is returned. Errors encountered during the operation are logged for debugging purposes.
   *
   * @param authorization The authorization token in the request header.
   *
   * @returns {Promise<{ suggestedQuestions: string[] } | { error: string }>} A promise that resolves to an object
   *          containing the suggested questions or an error message.
   */
  @get('api/client/retrieve/suggested_questions')
  async getSuggestedQuestions(@param.header.string('Authorization') authorization: string): Promise<
    | {
        suggestedQuestions: string[];
      }
    | {error: string}
  > {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | getSuggestedQuestions | N/A |Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    logger.debug(`Successfully connected | getSuggestedQuestions | N/A | teamId: ${teamId}`);

    try {
      const suggestedQuestions = await this.systemMetaService.getSuggestedQuestions();
      return {suggestedQuestions: suggestedQuestions};
    } catch (error) {
      logger.error(
        `DatalakeClientInterfaceController | getSuggestedQuestions | N/A | Failed to get suggested questions. Error: ${error.message}`,
      );
      throw new HttpErrors.InternalServerError(`Failed get suggested questions. Error: ${error.message}`);
    }
  }

  @get('api/client/dataSourceMetaData')
  async getDataSourceMetaData(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('connectionId') connectionId: string,
  ) {
    const authorizationKeySecret = authorization;
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `DatalakeClientInterfaceController | DatalakeClientInterfaceController.getConnectionSourceType | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    if (!connectionId) {
      throw new HttpErrors.BadRequest(`connectionId is required`);
    }
    const res = await this.tableDataService.getDataSourceMetaData(connectionId);

    if (res.isSuccess) {
      return res.data;
    } else {
      logger.error(`Failed to get data source meta data for connectionId: ${connectionId}, message: ${res.message}`);
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }
}
