/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta-data model
 */

/**
 * @class JobController
 * Handle the request related to the JobController
 * @description This controller use for Handle the request related to the jobs
 * <AUTHOR>
 */
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, HttpErrors, param, post, Request, requestBody, RestBindings} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  GET_JOB_COUNT_BODY,
  GET_JOB_LIST_BODY,
  JobFilter,
  JobSpecificDetails,
  JobStatus,
  JobType,
  SubJobs,
} from '../models/job.model';
import {JobRepository} from '../repositories/job.repository';
import {JOB_SERVICE, JobService} from '../services/job.service';
import {SYSTEM_LABEL_SERVICE, SystemLabelService} from '../services/system-label.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

/**
 * A simple controller to bounce back http requests
 */
export class JobController {
  constructor(
    @inject(RestBindings.Http.REQUEST) private req: Request,
    @repository(JobRepository) private jobRepository: JobRepository,
    @inject(JOB_SERVICE) private jobService: JobService,
    @inject(SYSTEM_LABEL_SERVICE) private systemLabelService: SystemLabelService,
    @inject(SecurityBindings.USER, {optional: true}) public currentUserProfile: UserProfileDetailed,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
  ) {}

  /**
   * Use to get job list by status, search key and filter obj filtering
   * @param body {object} filtering fields and page size and page index
   * @returns rerun job list
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR,UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/job/getJobList')
  async getJobList(
    @param.header.number('timeZoneOffset') timeZoneOffset: number,
    @requestBody(GET_JOB_LIST_BODY)
    body: {
      searchKey: string;
      status: JobStatus[];
      filterObj: {
        fromDate?: string;
        toDate?: string;
        filterBy?: JobType[];
      };
      pageSize?: number;
      pageIndex?: number;
    },
  ) {
    if (!body.pageIndex && body.pageIndex != 0) body.pageIndex = 0;
    if (!body.pageSize && body.pageSize != 0) body.pageSize = 12;

    let teamId = this.currentUserProfile.teamId;
    let labelHashList: {[k: string]: string} = {};
    if (teamId) {
      labelHashList = await this.systemLabelService.getOnlyLabelClassRefToTextMapOfTeam(teamId);
    }

    return await this.jobService.getJobList(
      body.pageIndex,
      body.pageSize,
      body.status,
      body.searchKey,
      body.filterObj,
      labelHashList,
      timeZoneOffset,
    );
  }

  /**
   * Use to get job list by status, search key and filter obj filtering
   * @param body {object} filtering fields
   * @returns rerun job list total count
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/job/getTotalJobCount')
  async getTotalJobCount(
    @requestBody(GET_JOB_COUNT_BODY) body: {searchKey: string; status: number[]; filterObj: JobFilter},
  ) {
    return await this.jobService.getTotalJobCount(body.status, body.searchKey, body.filterObj);
  }

  /**
   * Use to update or create the job document for new job or existing job
   * @param body {object} job data
   * @returns return nothing
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/job/updateJob/jwtAuthRequest')
  async updateJobByappRequest(
    @requestBody()
    body: {
      jobName: string;
      sessionId: string;
      jobType?: number;
      progress?: number;
      status?: number;
      jobSpecificDetails?: JobSpecificDetails;
      subJobs?: SubJobs;
    },
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `JobController | JobController.updateJobByappRequest | ${currentUserProfile} | job create or update failed: ${body}`,
      );
      return {success: false};
    }

    let jobOwnerId = currentUserProfile.id;
    let teamId = currentUserProfile.teamId;
    let jobOwnerName = currentUserProfile.name;

    try {
      let job = await this.jobService.createOrUpdateJob(
        body.jobName,
        body.sessionId,
        jobOwnerId,
        teamId,
        jobOwnerName,
        body.jobType,
        body.progress,
        body.status,
        body.jobSpecificDetails,
        body.subJobs,
      );
      logger.info(
        `JobController | JobController.updateJobByappRequest | ${this.currentUserProfile} | job create or update success: ${body}`,
      );
      return {success: true, jobId: job?._id};
    } catch (error) {
      logger.error(
        `JobController | JobController.updateJobByappRequest | ${this.currentUserProfile} | job create or update failed: ${body}`,
      );
      return {success: false};
    }
  }

  /**
   * Use to update or create the job document for new job or existing job
   * @param body {object} job data
   * @returns return nothing
   */
  @post('/api/job/updateJob/basicAuthRequest')
  async updateJobBySDKRequest(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      jobName: string;
      sessionId: string;
      jobType?: number;
      progress?: number;
      status?: number;
      jobSpecificDetails?: JobSpecificDetails;
      subJobs?: SubJobs;
    },
  ) {
    let authorizeDetails: {
      id: string | undefined;
      key: string | undefined;
      secret: string | undefined;
      teamId: string | undefined;
      name: string | undefined;
      userId: string | undefined;
      userName: string | undefined;
      userType: number | undefined;
      email: string | undefined;
      type: number | undefined;
    } = {
      id: undefined,
      key: undefined,
      secret: undefined,
      teamId: undefined,
      name: undefined,
      userId: undefined,
      userName: undefined,
      userType: undefined,
      email: undefined,
      type: undefined,
    };

    try {
      const authorizationKeySecret = authorization;
      authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
      logger.info(
        `JobController | JobController.updateJobBySDKRequest | ${authorizeDetails} | user basic auth success`,
      );
    } catch (error) {
      logger.error(`JobController | JobController.updateJobBySDKRequest | N/A | job create or update failed: ${body}`);
      throw new HttpErrors.Unauthorized(`Basic Authorization failed ${error}`);
    }

    if (!authorizeDetails || !authorizeDetails.teamId) {
      logger.error(
        `JobController | JobController.updateJobByappRequest | ${authorizeDetails} | job create or update failed: ${body}`,
      );
      throw new HttpErrors.Unauthorized(`can not find team id`);
    }

    try {
      let teamId = authorizeDetails.teamId;
      let jobOwnerId = authorizeDetails.userId;
      let jobOwnerName = authorizeDetails.userName || authorizeDetails.name || 'Unnamed';

      let job = await this.jobService.createOrUpdateJob(
        body.jobName,
        body.sessionId,
        jobOwnerId,
        teamId,
        jobOwnerName,
        body.jobType,
        body.progress,
        body.status,
        body.jobSpecificDetails,
        body.subJobs,
      );
      logger.info(
        `JobController | JobController.updateJobBySDKRequest | ${authorizeDetails} | job create or update success: ${body}`,
      );
      return {success: true, jobId: job?._id};
    } catch (error) {
      logger.error(`JobController | JobController.updateJobBySDKRequest | N\A | job create or update: ${body}`);
      return {success: true};
    }
  }

  /**
   * Use to get job document
   * @param body {object} job data
   * @returns return nothing
   */
  @post('/api/job/getJob/basicAuthRequest')
  async getJobBySDKRequest(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    body: {
      sessionId: string;
    },
  ) {
    try {
      const authorizationKeySecret = authorization;
      let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
      logger.info(
        `JobController | JobController.updateJobBySDKRequest | ${authorizeDetails} | user basic auth success`,
      );

      let job = await this.jobRepository.aggregate([
        {$match: {$or: [{sessionId: body.sessionId}, {'jobSpecificDetails.sessionId': body.sessionId}]}},
      ]);

      if (job && Array.isArray(job) && job.length > 0) {
        return job[0];
      } else {
        return;
      }
    } catch (error) {
      logger.error(`JobController | JobController.updateJobBySDKRequest | N/A | job create or update failed: ${body}`);
      throw new HttpErrors.Unauthorized(`Basic Authorization failed ${error}`);
    }
  }

  /**
   * Use for get the job status
   * @param jobId {string} id of the job
   * @returns status of the job
   */
  @authenticate('jwt')
  @get('/api/job/{jobId}/getStatus')
  async getJobStatus(@param.path.string('jobId') jobId: string) {
    let teamId: string | undefined = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | JobController.getJobStatus | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let job = await this.jobService.getJobStatus(jobId);

    return job;
  }
}
