/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for admin allowed APIs only
 */

/**
 * @class AdminController
 * APIs only authorized for admin users
 * @description This controller use for Handle the request related to the APIs only authorized for admin users
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {HttpErrors, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {TrashDeleteRequest} from '../models';
import {DATALAKE_TRASH_SERVICE, DatalakeTrashService} from '../services';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.SUPER_ADMIN]})
export class AdminController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(DATALAKE_TRASH_SERVICE)
    private datalakeTrashService: DatalakeTrashService,
  ) { }

  /**
   * to permanently delete trash object
   * @param data TrashDeleteRequest
   * @returns
   */
  @post('api/trash/objects/delete')
  async deleteTrashObject(
    @requestBody() reqBody: TrashDeleteRequest
  ) {

    //logged user team id
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(`${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.deleteTrashObject | N/A | Couldn't find the team for: `, this.currentUserProfile)
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    try {

      this.datalakeTrashService.deleteTrashedObjects(reqBody, teamId, this.currentUserProfile);

      return {
        message: DatalakeUserMessages.JOB_STARTED_IN_BACKGROUND,
        isSuccess: true
      }
    } catch (e) {
      logger.error(`${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.deleteTrashObject | N/A | ${DatalakeUserMessages.BACKGROUND_JOB_FAILED}, reqBody: ${JSON.stringify(reqBody)}, currentUserProfile: ${JSON.stringify(this.currentUserProfile)} | Error: `, e)
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BACKGROUND_JOB_FAILED);
    }

  }
}
