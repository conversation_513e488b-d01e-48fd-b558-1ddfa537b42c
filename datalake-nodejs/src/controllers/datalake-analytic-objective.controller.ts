import {authorize} from "@loopback/authorization";
import {FLOWS, UserTypeDetailed} from "../settings/constants";
import {authenticate} from "@loopback/authentication";
import {inject} from "@loopback/core";
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from "../authServices/custom-token.service";
import {del, get, HttpErrors, param, patch, post, requestBody} from "@loopback/rest";
import {logger} from "../config";
import {DatalakeUserMessages} from "../settings/datalake.user.messages";
import {AnalyticObjectiveService} from "../services/analytic-objective.service";

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN,UserTypeDetailed.SUPER_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR]})
export class DatalakeAnalyticObjectiveController{
    constructor(
        @inject( SecurityBindings.USER, {optional: true})
        public currentUserProfile: UserProfileDetailed,
        @inject('services.AnalyticObjectiveService')
        private analyticObjectiveService: AnalyticObjectiveService,
    ){

    }


    /**
     * Get list of analytic objective for Metalake
     * @returns list of analytic objective
     */
    @get('api/analyticObjective/objectives')
    async getObjectives(
        @param.query.string('search') search?: string, // Optional query parameter
      ): Promise<{objectives: any[]}> {    
        try {
            const teamId = this.currentUserProfile?.teamId;
      
            if (!teamId) {
                logger.error(
                    `Objective Retrieval | DatalakeAnalyticObjectiveController.getObjectives | N/A | Couldn't find teamId for user: `,
                    this.currentUserProfile,
                  );
              throw new HttpErrors.NotAcceptable('Team does not exist');
            }
            const objectives = await this.analyticObjectiveService.getObjectives(search);
            return {objectives: objectives};
        } catch (error) {
            logger.error(
                `Objective Retrieval | DatalakeAnalyticObjectiveController.getObjectives | N/A | Failed to fetch objectives: ${error.message}`,
              );
            throw new HttpErrors.InternalServerError(error.message);
          }
        }

    /**
     * Create a new analytic objective
     * @param data The objective details
     * @returns {isSuccess: boolean; message: string}
     */
    @post('/api/analyticObjective/create')
    async createObjective(
        @requestBody() data: {objectiveName: string},
    ): Promise<{isSuccess: boolean; message: string}> {
        try {
        
            if (!data.objectiveName) {
                logger.error(
                    `Objective Creation | DatalakeAnalyticObjectiveController.createObjective | N/A | Missing objectiveName field in request`,
                );
                return {isSuccess:false,message:'The "objective" field is required.'}
            }

            if(this.currentUserProfile.name){
                const result = await this.analyticObjectiveService.createObjective(data,this.currentUserProfile.name);
                return result;
            }else{
                return {isSuccess:false,message:'Username not found'}
            }

            
            
        } catch (error) {
            logger.error(
                `Objective Creation | DatalakeAnalyticObjectiveController.createObjective | N/A | Failed to create objective: ${error.message}`,
              );
            throw new HttpErrors.InternalServerError('Failed to create objective.');
        }
    }


    /**
     * Delete an analytic objective by ID
     * @param id The ID of the objective to delete
     * @returns {isSuccess: boolean, message: string}
     */
    @del('/api/analyticObjective/delete/{id}')
    async deleteObjective(
        @param.path.string('id') id: string,
    ): Promise<{isSuccess: boolean; message: string}> {
        try {
            const result = await this.analyticObjectiveService.deleteObjective(id);
            return result; 
        } catch (error) {
            logger.error(
                `Objective Deletion | DatalakeAnalyticObjectiveController.deleteObjective | ${id} | Failed to delete objective: ${error.message}`,
              );
            throw new HttpErrors.InternalServerError('Failed to delete objective.');
        }
    }


    /**
   * Update an analytic objective by ID
   * @param id The ID of the objective to update
   * @param data The updated details
   * @returns {isSuccess: boolean, message: string}
   */
    @patch('/api/analyticObjective/update/{id}')
    async updateObjective(
        @param.path.string('id') id: string,
        @requestBody() data: {objectiveName: string},
    ): Promise<{isSuccess: boolean; message: string}> {
        try {
            if (!data.objectiveName || data.objectiveName.trim() === '') {
                logger.error(
                `Objective Update | DatalakeAnalyticObjectiveController.updateObjective | ${id} | Invalid or empty objectiveName field`,
                );
                return {isSuccess: false, message: 'Objective name cannot be empty or whitespace.'};
            }

            if(this.currentUserProfile.name){
                const result = await this.analyticObjectiveService.updateObjective(id, data.objectiveName.trim(),this.currentUserProfile.name);
                return result;
            }else{
                return {isSuccess:false,message:'Username not found'}
            }
            
        } catch (error) {
            logger.error(
                `Objective Update | DatalakeAnalyticObjectiveController.updateObjective | ${id} | Failed to update objective: ${error.message}`,
            );
            throw new HttpErrors.InternalServerError('Failed to update objective.');
        }
    }

    /**
     * Get an analytic objective by ID
     * @param id The ID of the objective to fetch
     * @returns The analytic objective details
     */
    @get('/api/analyticObjective/{id}')
    async getObjectiveById(
        @param.path.string('id') id: string,
    ): Promise<any> {
        try {
            const result = await this.analyticObjectiveService.getObjectiveById(id);
            if (!result.isSuccess) {
                logger.error(
                `Objective Retrieval | DatalakeAnalyticObjectiveController.getObjectiveById | ${id} | Objective not found`,
                );
                throw new HttpErrors.NotFound(result.message);
            }

            return result.data;
        } catch (error) {
            logger.error(
                `Objective Retrieval | DatalakeAnalyticObjectiveController.getObjectiveById | ${id} | Failed to fetch objective: ${error.message}`,
            );
            throw new HttpErrors.InternalServerError('Failed to fetch objective.');
        }
    }



    /**
     * Reactivate a soft-deleted analytic objective.
     * This endpoint checks if the objective exists and reactivates it by setting `is_active` to `true`.
     *
     * @param id - The ID of the objective to reactivate.
     * @returns A response indicating success or failure, along with a message.
     *
     * @throws Returns a 404 error if the objective is not found or has an invalid ID.
     * @throws Returns a 500 error if the reactivation process fails due to a server error.
     */
    @patch('/api/analyticObjective/reactivate/{id}')
    async reactivateObjective(
    @param.path.string('id') id: string,
    ): Promise<{isSuccess: boolean; message: string}> {
        try {
            const result = await this.analyticObjectiveService.reactivateObjective(id);
            if (!result.isSuccess) {
            logger.error(
                `Objective Reactivation | DatalakeAnalyticObjectiveController.reactivateObjective | ${id} | Reactivation failed: ${result.message}`,
            );
            throw new HttpErrors.NotFound(result.message);
            }

            logger.info(
            `Objective Reactivation | DatalakeAnalyticObjectiveController.reactivateObjective | ${id} | Objective reactivated successfully`,
            );

            return result;
        } catch (error) {
            logger.error(
            `Objective Reactivation | DatalakeAnalyticObjectiveController.reactivateObjective | ${id} | Error: ${error.message}`,
            );
            throw new HttpErrors.InternalServerError('Failed to reactivate objective.');
        }
    }    

}