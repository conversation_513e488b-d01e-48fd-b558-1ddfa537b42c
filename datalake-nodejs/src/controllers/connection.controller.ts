import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import fs from 'fs';
import multer from 'multer';
import path from 'path';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ConnectionCredentials, DataSourceConnectionType} from '../models/connection.model';
import {ConnectionSourceType} from '../models/source.model';
import {CONNECTIONS_SERVICE, ConnectionService} from '../services';
import {UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

// Configure multer to handle file uploads in memory
const upload = multer({storage: multer.memoryStorage()});

@authenticate('jwt')
@authorize({
  allowedRoles: [
    UserTypeDetailed.TEAM_ADMIN,
    UserTypeDetailed.SUPER_ADMIN,
    UserTypeDetailed.MEMBER,
    UserTypeDetailed.COLLABORATOR,
  ],
})
export class ConnectionController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,

    @inject(CONNECTIONS_SERVICE) private connectionService: ConnectionService,
  ) {}

  @get('api/status/is-data-structure-crawled/{connectionId}')
  async isDataStructureCrawled(
    @param.path.string('connectionId') connectionId: string,
  ): Promise<{isDataStructureCrawled: boolean}> {
    const isDataStructureCrawled = await this.connectionService.isCrawled(connectionId);
    return {isDataStructureCrawled};
  }

  /**
   * Create a connection for a source to data lake.
   * @param data - the data to create the connection
   * @returns the message of the result
   * @throws {HttpErrors.NotAcceptable} if the team is not found
   */
  @post('api/connection/create-connection')
  async createConnection(
    @requestBody()
    data: {
      sourceName: string;
      sourceType: ConnectionSourceType;
      credentials: ConnectionCredentials;
      connectionType: DataSourceConnectionType;
      additionalData: any;
    },
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Add new data source connection | ConnectionController.addDataSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.createConnection(
      data.sourceName,
      data.sourceType,
      data.credentials,
      data.connectionType,
      currentUserProfile,
      data.additionalData,
    );

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  //get connection schema
  @get('api/connection/{connectionId}/connection-schema')
  async getConnectionSchema(@param.path.string('connectionId') connectionId: string) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Fetch connection schema | ConnectionController.fetchConnectionSchema | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.getConnectionSchema(connectionId);

    if (res.isSuccess) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/connection/{connectionId}/start-data-source-crawling')
  async startDataSourceCrawling(
    @param.path.string('connectionId') connectionId: string,
    @requestBody() data: {schema: {tableName: string; isSelected: boolean}[]},
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Start data source crawling | ConnectionController.startDataSourceCrawling | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.startDataSourceCrawling(connectionId, data.schema);

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  /**
   * Fetches the list of data sources for the current user
   * @returns a promise that resolves to an object with the following properties:
   * - success: a boolean indicating whether the operation was successful
   * - data: an array of objects
   * @throws {HttpErrors.NotAcceptable} if the team is not found
   */
  @get('api/connection/fetch-data-source-list')
  async fetchDataSourceList() {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Fetch data source list | ConnectionController.fetchDataSourceList | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.getsourceConnectionList();

    if (res.isSuccess) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  /**
   * Fetches the connection credential details for a given connection ID.
   * @param connectionId {string} - The ID of the connection to fetch details for.
   * @returns {Promise<{success: boolean, data?: {connectionId?: string, sourceName?: string, connectionCredentials?: ConnectionCredentials}, message: string}>}
   * An object containing the success status, optional connection details, and a message.
   * Returns an error message if the connection is not found or if an error occurs.
   * @throws {HttpErrors.NotAcceptable} if the team is not found
   */
  @get('api/connection/fetch-connection-details/{connectionId}')
  async fetchConnectionDetails(@param.path.string('connectionId') connectionId: string) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Fetch connection details | ConnectionController.fetchConnectionDetails | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.fetchConnectionDetails(connectionId);

    if (res.isSuccess) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  /**
   * Updates a connection in the database with new credentials. If the connection
   * is of type BIGQUERY, it handles writing the credentials to a file. It also
   * updates the corresponding Layernext connection if it exists.
   *
   * @param connectionId {string} - The ID of the connection to update.
   * @param connectionCredentials {ConnectionCredentials} - The new connection credentials.
   * @param additionalData {any} - Optional content for writing to a file if the connection
   * type is BIGQUERY.
   * @returns {Promise<{success: boolean, message: string}>} - A promise that resolves to an object
   * containing the success status, and a message.
   * Returns an error message if the connection is not found or if an error occurs.
   */
  @post('api/connection/update-connection/{connectionId}')
  async updateConnection(
    @param.path.string('connectionId') connectionId: string,
    @requestBody() data: {credentials: ConnectionCredentials; additionalData: any},
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Update data source connection | ConnectionController.updateDataSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let connCred = data.credentials;

    if (!connCred) {
      logger.error(
        `Update data source connection | ConnectionController.updateDataSource | N/A | No credentials were sent`,
      );
      return {
        isSuccess: false,
        message: 'No data were sent',
      };
    }

    let res = await this.connectionService.updateConnection(connectionId, connCred, data.additionalData);

    if (res.isSuccess) {
      return res;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  /**
   * Fetches the total number of tables for both raw and Layernext database connections.
   *
   * This method first verifies the existence of a team ID in the current user profile.
   * It then calls the `fetchTotalTableCounts` method from the `connectionService` to
   * retrieve the total count of tables associated with the current user's team.
   *
   * @throws {HttpErrors.NotAcceptable} - Throws an error if the team ID is not found in the
   * current user profile or if there is an issue fetching the table counts.
   *
   * @returns {Promise<{crawledTables: number, configuredTables: number}>} - A promise that resolves
   * to an object containing the total number of crawled and configured tables.
   */
  @get('api/connection/fetch-total-table-counts')
  async fetchTotalTableCounts() {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Fetch total table counts | ConnectionController.fetchTotalTableCounts | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let res = await this.connectionService.fetchTotalTableCounts();

    if (res.isSuccess) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @get('api/connection/fetch-connection-list-with-summary')
  async fetchConnectionListWithSummary() {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Fetch connection list with summary | ConnectionController.fetchConnectionListWithSummary | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    //call fetchDataSourceList to get source list
    let res = await this.connectionService.getsourceConnectionList();

    if (!res.isSuccess) {
      throw new HttpErrors.NotAcceptable(res.message);
    }

    //call fetchTotalTableCounts to get table counts
    let tableCounts = await this.connectionService.fetchTotalTableCounts();

    if (!tableCounts.isSuccess) {
      throw new HttpErrors.NotAcceptable(tableCounts.message);
    }

    let summary = [
      {
        fieldCount: tableCounts.data?.configuredTables,
        fieldIcon: 'icon-records-fill',
        fieldText: 'Configured Tables',
      },
      {
        fieldCount: tableCounts.data?.crawledTables,
        fieldIcon: 'icon-table-count',
        fieldText: 'Crawled Tables',
      },
    ];

    let aggrResponse = {
      isSuccess: true,
      data: {
        sources: res.data?.sources,
        summary: summary,
      },
      message: 'Source list fetched successfully',
    };

    return aggrResponse;
  }

  /**
   * Upload a JSON file which contains ERD and return success status.
   * This is a fresh implementation using multer for file handling.
   *
   * @param connectionId - The connection ID to associate with the ERD file
   * @param req - The HTTP request containing the file upload
   * @param res - The HTTP response
   * @returns The success status of the uploaded file
   */
  @post('api/connection/{connectionId}/upload-erd', {
    responses: {
      '200': {
        description: 'Success status of the uploaded file',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })
  async uploadErdFile(
    @param.path.string('connectionId') connectionId: string,
    @inject(RestBindings.Http.REQUEST) req: Request,
    @inject(RestBindings.Http.RESPONSE) res: Response,
  ): Promise<object> {
    return new Promise<object>((resolve, reject) => {
      upload.single('file')(req, res, (err: any) => {
        if (err) {
          reject(new HttpErrors.BadRequest('Error uploading file: ' + err.message));
          return;
        }

        if (!req.file) {
          reject(new HttpErrors.BadRequest('No file uploaded'));
          return;
        }

        // Check if file is JSON
        const fileExtension = path.extname(req.file!.originalname).toLowerCase();
        if (fileExtension !== '.json') {
          reject(new HttpErrors.BadRequest('Only JSON files are allowed'));
          return;
        }

        // Create folder structure: storage/connection/{connectionId}/
        const storagePath = path.join(__dirname, '../../storage/connection', connectionId);

        // Ensure directory exists
        if (!fs.existsSync(storagePath)) {
          fs.mkdirSync(storagePath, {recursive: true});
        }

        // Define the target file path
        const targetFilePath = path.join(storagePath, 'erd.json');

        try {
          // Get file content from memory
          const fileContent = req.file!.buffer.toString('utf8');

          // Validate JSON
          const json = JSON.parse(fileContent);

          // Write directly to target location
          fs.writeFileSync(targetFilePath, fileContent, 'utf8');

          resolve({
            success: true,
            connectionId: connectionId,
            filePath: targetFilePath,
            message: 'ERD file uploaded and saved successfully',
          });
        } catch (parseErr) {
          reject(new HttpErrors.BadRequest('Uploaded file is not valid JSON: ' + parseErr.message));
        }
      });
    });
  }
}
