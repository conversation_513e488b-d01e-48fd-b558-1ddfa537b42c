/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the data crawl model
 */

/**
 * @class StorageProviderController
 * @description Handle the request related to storage managing eg: trigger storage crawling
 * <AUTHOR>
 */

// Uncomment these imports to begin using these cool features!

import {inject} from "@loopback/core";
import {HttpErrors, param, post, requestBody, Request, Response} from '@loopback/rest';
import {logger} from "../config";
import {StorageCrawlerService, STORAGE_CRAWLER_SERVICE} from "../services/storage-crawler.service";
import {DATALAKE_TRASH_SERVICE,DatalakeTrashService, } from '../services';
import {OBJECT_STATUS,} from '../models/meta-data.model';
import { MetaDataRepository,} from '../repositories';
import {repository} from '@loopback/repository';
import * as path from 'path';
import {DiskStorageConfiguration} from '../settings/disk.configuration';
// import {inject} from '@loopback/core';

const baseUrl = `api/storageProvider/`
const DISK_BUCKET_NAME = DiskStorageConfiguration.DISK_BUCKET_NAME;
export class StorageProviderController {
  constructor(
    @repository(MetaDataRepository)
    private metaDataRepository: MetaDataRepository,
    @inject(STORAGE_CRAWLER_SERVICE) private storageCrawlerService: StorageCrawlerService,
    @inject(DATALAKE_TRASH_SERVICE) private datalakeTrashService: DatalakeTrashService,
    // @inject(STORAGE_OPERATION_HANDLER_SERVICE) protected storageOperationHandlerService: StorageOperationHandlerService,
  ) { }

  /**
 * Use to crawl the storage (s3 bucket again)
 */
  @post(baseUrl + 's3/crawl')
  async crawlStorage() {
    logger.debug(`Crawl storage for populate data (subsequent) | StorageProviderController.crawlStorage | N/A | request`)
    let response = this.storageCrawlerService.handleTriggerSubsequentCrawling()
    return
    // let response = await this.storageCrawlerService.createNewBucket()
    // console.log(`response -------------------------------------\n${response}`)
  }
  /**
 * Use to trigger a partial crawl
 * @param filePath {string} the absolute path of the directory which contains the the iserted or modified file
 */
  @post(baseUrl + 'folderUpdate')
  async folderUpdate(
    @requestBody()
    body: {
      filePath: string;
    },
  ) {
    // Get the relative path
    if(!DISK_BUCKET_NAME)
    {
      return {Success:"failed"}
    }
  	const relativePath = path.relative(DISK_BUCKET_NAME, body.filePath);

    // Split the relative path into segments
    const pathSegments = relativePath.split(path.sep);
    
    // Get the first directory
    const firstDirectory = pathSegments.length > 0 ? pathSegments[0] : '';
    // if the changed folder name is chat partial crawl is not called
    if (firstDirectory == "chat"){
      return {Success:"True, chat upload detected"}
    }
    logger.debug(`Crawl storage for populate data (subsequent) | StorageProviderController.folder update | ${body.filePath}  | starting partial crawl | request`)
    
    return this.storageCrawlerService.partialCrawl(body.filePath);
    
  }

   /**
 * Use to remove files that were deleted by the user, flags the meta data data as trash
 * @param filePath {string} the absolute path of the deleted file
 */
  @post(baseUrl + 'deleted')
  async deletedfile(
    @requestBody()
    body: {
      filePath: string;
    },
  )
  {
    
    if (!DISK_BUCKET_NAME)
    {
      return {status: "failed"};
    }
    
    const relativePath = path.relative(DISK_BUCKET_NAME, body.filePath);
    let metaObject = await this.metaDataRepository.findOne({
      where: {storagePath: relativePath},
    });
    
    let trashedInfo = {
      objectStatus: OBJECT_STATUS.TRASHED,
      statPending: true,
      statPendingAt: new Date(),
      trashedAt: new Date(),
      showInTrash: true,
      trashedBy: 'forced_user',
      updatedAt: new Date(),
    };
    if (!metaObject)
    {
      return {status: "failed"};
    }
    await this.metaDataRepository.updateById(metaObject.id, trashedInfo);
    return {status: "success"};
  }
}
