/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the data crawl model
 */

/**
 * @class DatalakeOverviewController
 * Handle the request related to the DatalakeOverview Controller
 * @description This controller use for Handle the request related to the DatalakeOverview controller eg: get data crawlig status
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {OverviewType} from '../models/system-data.model';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService, SYSTEM_STATS_SERVICE, SystemStatsService} from '../services';
import {SRC_DEST_CONNECTION_SERVICE, SrcDestConnectionService} from '../services/src-dest-connection.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
export class DatalakeOverviewController {
  constructor(
    @inject(STORAGE_CRAWLER_SERVICE) private storageCrawlerService: StorageCrawlerService,
    @inject(SYSTEM_STATS_SERVICE)
    private systemStatService: SystemStatsService,
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(SRC_DEST_CONNECTION_SERVICE)
    private srcDestConnectionService: SrcDestConnectionService,
  ) {}

  /**
   * Use to show latest crawling status in datalake overview section
   * @param timeZoneOffset {number} use to transform UTC timestap into user's timestamp
   * @returns CrawlingStatusResponse
   */
  @get('api/overview/crawl/status')
  async getCrawlStatus(@param.header.number('timeZoneOffset') timeZoneOffset: number) {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getCrawlStatus | N/A | Get crawling status, timeZoneOffset: ${timeZoneOffset} `,
    );

    let response = await this.storageCrawlerService.getCrawlingStatus(timeZoneOffset, teamId);

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getCrawlStatus | N/A | Response Time = ${responseTime} `,
    );

    return response;
  }

  /**
   * Use to show crawling history in datalake overview section
   * @param timeZoneOffset {number} use to transform UTC timestap into user's timestamp
   * @param pageIndex page number [strart from 0]
   * @param pageSize page size
   * @returns CrawlHistory[]
   */
  @get('api/overview/crawl/history')
  async getCrawlHistory(
    @param.header.number('timeZoneOffset') timeZoneOffset: number,
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number,
  ) {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getCrawlHistory | N/A | Get crawling History pageIndex: ${pageIndex}, pageSize: ${pageSize}`,
    );

    let response = await this.storageCrawlerService.getCrawlingHistory(pageIndex, pageSize, timeZoneOffset, teamId);

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getCrawlHistory | N/A | Response Time = ${responseTime} `,
    );

    return response;
  }

  /**
   * Use to get count details to show in datalake overview section
   * @returns DataOverview
   */
  @get('api/overview/object/counts')
  async getObjectCounts() {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getObjectCounts | N/A | Get Meta object counts`,
    );

    let response = await this.storageCrawlerService.getObjectOverview(teamId);

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.debug(
      `${FLOWS.DATALAKE_OVERVIEW} | DatalakeOverviewController.getObjectCounts | N/A | Response Time = ${responseTime} `,
    );

    return response;
  }

  /**
   * Get list of connected data sources for Metalake
   * @returns list of connected data sources
   */
  @get('api/overview/datasources')
  async getDataSources() {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeOverviewController.getDataSources | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return await this.srcDestConnectionService.getsourceConnectionList();
  }

  /**
   * Create or update dataSourcesOverview or businessOverview
   * @param type The type of field to update (dataSourcesOverview or businessOverview)
   * @param data The value to update
   * @returns The updated SystemData object
   */
  @post('api/overview')
  async createOrUpdateOverview(
    @param.query.string('type') type: OverviewType,
    @requestBody() data: {overview: string},
  ): Promise<{isSuccess: boolean}> {
    try {
      return await this.systemStatService.createOrUpdateOverview(type, data.overview);
    } catch (error) {
      throw new HttpErrors.InternalServerError(error.message);
    }
  }

  /**
   * Get the whole overview object
   * @returns The overview object with dataSourcesOverview and businessOverview
   */
  @get('api/overview')
  async getOverview() {
    try {
      return await this.systemStatService.getOverview();
    } catch (error) {
      throw new HttpErrors.NotFound(error.message);
    }
  }
}
