/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for APIs between the annotation studio and the datalake
 */

/**
 * @class StudioInterfaceController
 * Handle the request related to the internal annotation studio Application
 * @description APIs between the annotation studio and the datalake
 * <AUTHOR> isuru, chathushka
 */

import {inject} from '@loopback/core';
import {HttpErrors, param, post, requestBody} from '@loopback/rest';
import {securityId} from '@loopback/security';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {Explore_API_TYPE} from '../models';
import {
  ANNO_DOWNLOAD_HANDLER_SERVICE,
  AnnotationDownloadHandlerService,
  META_DATA_SERVICE,
  MetaDataService,
  SYSTEM_LABEL_SERVICE,
  SystemLabelService,
} from '../services';
import {
  DATASET_MANAGER_INTERFACE_SERVICE,
  DatasetManagerInterfaceService,
} from '../services/dataset-manager-interface.service';
import {STUDIO_INTERFACE_SERVICE, StudioInterfaceService} from '../services/studio-interface.service';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

const internalBaseUrl = '/internal/studioInterface';
const baseUrl = 'api/studioInterface';

export class StudioInterfaceController {
  constructor(
    @inject(DATASET_MANAGER_INTERFACE_SERVICE) private datasetManagerInterfaceService: DatasetManagerInterfaceService,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @inject(STUDIO_INTERFACE_SERVICE) private studioInterfaceService: StudioInterfaceService,
    @inject(ANNO_DOWNLOAD_HANDLER_SERVICE)
    private annotationDownloadHandlerService: AnnotationDownloadHandlerService,
    @inject(SYSTEM_LABEL_SERVICE) private systemLabelService: SystemLabelService,
  ) {}

  /**
   * use to retrive file information of a list
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  @post(baseUrl + '/getFileList')
  async getFileList(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    reqBody: {
      selectionId: string;
      isSelectedOnly: boolean;
    },
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basic key: ', authorizationKeySecret);
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    let teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get file list | StudioInterfaceController.getFileList | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return await this.metaDataService.getFileArray(reqBody.isSelectedOnly, reqBody.selectionId, currentUserProfile);
  }

  /**
   * use to retrive file information of a list
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  @post(baseUrl + '/projectCollectionList')
  async projectCollectionList(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    reqBody: {
      projectId: string;
    },
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basic key: ', authorizationKeySecret);
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `Get file list | StudioInterfaceController.getDataListWithChildFiles | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return await this.metaDataService.getProjectCollectionList(reqBody.projectId, currentUserProfile);
  }

  /**
   * use to retrive file information of a id list and include child items
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  @post(baseUrl + '/getDataListWithChildFiles')
  async getDataListWithChildFiles(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('projectId') projectId: string,
    @param.query.string('projectName') projectName: string,
    @param.query.string('currentUserProfile') currentUserProfileStr: string,
    @requestBody()
    reqBody: {
      selectionId: string;
      limit: number;
    },
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basic key: ', authorizationKeySecret);
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let currentUserProfile: UserProfileDetailed = JSON.parse(currentUserProfileStr);

    logger.debug(
      `get file info | StudioInterfaceController.getDataListWithChildFiles | N/A | currentUserProfile: ${currentUserProfileStr}`,
    );

    let teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `Get file list | StudioInterfaceController.getDataListWithChildFiles | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(`get file info | StudioInterfaceController.getDataListWithChildFiles | N/A | limit: ${reqBody.limit}`);

    let objectIdObj = await this.studioInterfaceService.getChildFilesForSelection(
      reqBody.selectionId,
      reqBody.limit,
      projectId,
      currentUserProfile,
    );

    logger.debug(
      `get file info | StudioInterfaceController.getDataListWithChildFiles | N/A | objectId list length: ${objectIdObj.objectIdList.length}`,
    );

    return await this.metaDataService.handleGetMetaDataArraytWithChildFiles(
      objectIdObj.objectIdList,
      projectId,
      projectName,
      currentUserProfile,
      objectIdObj.objectType,
    );
  }

  /**
   * use to retrive file information of a id list and include child items
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  @post(baseUrl + '/getDataListWithChildFilesCount')
  async getDataListWithChildFilesCount(
    @param.header.string('Authorization') authorization: string,
    @requestBody()
    reqBody: {
      selectionId: string;
      projectId: string;
    },
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basic key: ', authorizationKeySecret);
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `get file count | StudioInterfaceController.getDataListWithChildFiles | N/A | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `get file count | StudioInterfaceController.getDataListWithChildFiles | N/A | selectionId: ${reqBody.selectionId}`,
    );

    let countObj = await this.studioInterfaceService.getDataListWithChildFilesCount(
      reqBody.selectionId,
      currentUserProfile,
      reqBody.projectId,
    );

    return countObj;
  }

  /**
   *
   * @param projectId
   * @param pageNo
   * @param pageSize
   * @param reqBody
   * @param authorization
   * @returns
   */
  @post(baseUrl + '/projects/fetchAnnotations')
  async getDataOfAnnotationProject(
    @param.query.number('pageNo') pageNo: number,
    @param.query.number('pageSize') pageSize: number,
    @requestBody()
    reqBody: {
      isAnnotatedOnly: boolean;
      isAllTasksSelected: boolean;
      projectIdList: string[];
      taskIdList: string[]; // if isAllTasksSelected false, then use this
    },
    @param.header.string('Authorization') authorization: string,
  ) {
    const authorizationKeySecret = authorization;
    let apiKeyDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let teamId = apiKeyDetails.teamId;

    logger.debug(
      `Download annotations| StudioInterfaceController.getDataOfAnnotationProject | ${teamId} | Get project annotation data request}`,
      reqBody,
    );

    if (
      !reqBody ||
      !reqBody.projectIdList ||
      !Array.isArray(reqBody.projectIdList) ||
      reqBody.projectIdList.length < 1
    ) {
      logger.error(
        `Download annotations| StudioInterfaceController.getDataOfAnnotationProject | ${teamId} | Get project annotation data request, invalid projectIdList`,
        reqBody,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_PROJECT_LIST);
    }

    let downloadRes = await this.annotationDownloadHandlerService.getDataOfAnnotationProject(
      reqBody.projectIdList,
      reqBody.taskIdList,
      reqBody.isAnnotatedOnly,
      reqBody.isAllTasksSelected,
      pageNo,
      pageSize,
      teamId,
    );
    if (downloadRes && downloadRes.data) {
      return downloadRes;
    } else {
      throw new HttpErrors.NotAcceptable(downloadRes ? downloadRes.errorMsg : DatalakeUserMessages.FAILED_TO_FETCH);
    }
  }

  @post('/api/studioInterface/labelGroupList')
  async getLabelGroupList(@param.header.string('Authorization') authorization: string) {
    const authorizationKeySecret = authorization;
    let apiKeyDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let teamId = apiKeyDetails.teamId;

    if (!teamId) {
      logger.error(`StudioInterfaceController | getLabelGroupList | Could not find Team of the user `);
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let project = {
      'labels._id': 1,
      'labels.label': 1,
      'labels.labelText': 1,
      'labels.type': 1,
      _id: 1,
      groupName: 1,
    };
    let labelGroupList = await this.systemLabelService.getLabelGroupsListWithLabelDetails(
      teamId,
      project,
      Explore_API_TYPE.LIST,
    );
    return labelGroupList;
  }

  /**
   * use to find metaUpdates operation list of the selected frames for a particular project
   * @param reqBody
   * datasetVersionId?: string, id of the project
   * selectionId?: string, id of the selection object
   * removedCollectionIdList?: string[], ids of collections which are removed from the project
   * addedCollectionIdList?: string[], ids of collection which are added to the project
   * @returns
   */
  @post(internalBaseUrl + '/operationList')
  async findOperationList(
    @requestBody()
    reqBody: {
      projectId?: string;
      selectionId?: string;
      removedCollectionIdList?: string[];
      addedCollectionIdList?: string[];
    },
    @param.query.string('userInfo') userInfo: string,
  ) {
    let currentUserProfile: UserProfileDetailed = userInfo ? JSON.parse(userInfo) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `StudioInterfaceController | StudioInterfaceController.findOperationList | N/A | Could not find user info `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    return this.studioInterfaceService.findOperationList(
      currentUserProfile,
      reqBody.projectId,
      reqBody.selectionId,
      reqBody.removedCollectionIdList,
      reqBody.addedCollectionIdList,
    );
  }


  /**
   * use to get auto annotation
   * @param requestBody 
   * @returns annotation data
   */
  @post(internalBaseUrl + '/get/autoAnnotation')
  async getAutoAnnotation(
    @param.header.string('Authorization') authorization: string,
    @requestBody() reqBody: any
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basic key: ', authorizationKeySecret);
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);
    let teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get file list | StudioInterfaceController.getFileList | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };
    return this.studioInterfaceService.getAutoAnnotation(reqBody)
  }
}
