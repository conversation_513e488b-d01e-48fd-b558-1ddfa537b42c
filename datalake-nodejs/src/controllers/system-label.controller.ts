/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the SystemLabel model
 */

/**
 * @class SystemLabelController
 * Handle the request related to the SystemLabel Controller
 * @description This controller use for Handle the request related to the SystemLabel controller eg: get system label list
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, HttpErrors, param, post, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import dotenv from 'dotenv';
import multer from 'multer';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {Explore_API_TYPE, FileInterface, LabelInfoCreateRequest, LabelInfoEditRequest} from '../models';
import {SystemLabelRepository} from '../repositories';
import {STORAGE_CRAWLER_SERVICE, StorageCrawlerService, SYSTEM_LABEL_SERVICE, SystemLabelService} from '../services';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
dotenv.config();

const SYSTEM_DATA_BUCKET = process.env.DEFAULT_BUCKET_NAME;

const className = 'SystemLabelController';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
export class SystemLabelController {
  constructor(
    @repository(SystemLabelRepository)
    public systemLabelRepository: SystemLabelRepository,
    @inject(SYSTEM_LABEL_SERVICE) private systemLabelService: SystemLabelService,
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(STORAGE_CRAWLER_SERVICE) private storageCrawlerService: StorageCrawlerService,
  ) {}

  /**
   * Use for create new system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/api/system/label/create')
  async createLabel(
    //  @inject(SecurityBindings.USER)
    //  currentUserProfile: UserProfile,
    @requestBody() labelData: {labels: LabelInfoCreateRequest},
  ) {
    //const userId = currentUserProfile[securityId];
    //logger.debug(`${className}|createLabel| Create System label ${labelData.labels.labelText} initiated by userId: ${userId} `, labelData.labels)
    //let userObj = await this.annotationUserRepository.findById(userId)
    //let teamId = userObj.teamId
    //let userName =  userObj.name || ''
    //this.userActivityLogService.createUserActivityLog(userId, UserActivityType.SYSTEM_LABEL_CREATE,`Create System label: ${labelData.labels.labelText} initiated by userId: ${userId} teamId: ${teamId}`, new Date())

    //  if(!teamId){
    //    logger.error(`${className}|createLabel| Create system label image request from userId: ${userId} : Could not find Team of the user `)
    //    throw new HttpErrors.NotAcceptable(AnnotationUserMessages.DATALAKE_TEAM_NOT_EXIST);
    //  }
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userName = this.currentUserProfile ? this.currentUserProfile.name : '';
    if (!teamId) {
      throw new HttpErrors.NotAcceptable('System label team does not exists');
    }
    return await this.systemLabelService.createSystemLabel(labelData.labels, userName, teamId);
  }

  /**
   * Use for create new system label for team
   * @param labelData {object} label data
   * @returns error or success
   */
  @post('/api/system/label/edit')
  async editLabel(
    // @inject(SecurityBindings.USER)
    // currentUserProfile: UserProfile,
    @requestBody() labelData: {labels: LabelInfoEditRequest},
  ) {
    // const userId = currentUserProfile[securityId];
    // logger.debug(`${className}|editLabel| Edit System label ${labelData.labels.label} initiated by userId: ${userId} `, labelData.labels)
    // let userObj = await this.annotationUserRepository.findById(userId)
    // let teamId = userObj.teamId

    // if(!teamId){
    //   logger.error(`${className}|editLabel| editLabel request from userId: ${userId} : Could not find Team of the user `)
    //   throw new HttpErrors.NotAcceptable(AnnotationUserMessages.DATALAKE_TEAM_NOT_EXIST);
    // }

    // let userName = userObj.name || ''
    // this.userActivityLogService.createUserActivityLog(userId, UserActivityType.SYSTEM_LABEL_CREATE,`Edit System label: ${labelData.labels.label} initiated by userId: ${userId} teamId: ${teamId}`, new Date())
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userName = this.currentUserProfile ? this.currentUserProfile.name : '';
    if (!teamId) {
      throw new HttpErrors.NotAcceptable('System label team does not exists');
    }
    return await this.systemLabelService.editSystemLabel(labelData.labels, userName, teamId);
  }

  /**
   * Use for delete system label of team
   * @param label {string} unique identifier of the label
   * @returns error or success
   */
  @post('/api/system/label/delete')
  async deleteLabel(
    @requestBody() labelIdentity: {label: string},
    //  @inject(SecurityBindings.USER)
    //  currentUserProfile: UserProfile,
  ) {
    //  const userId = currentUserProfile[securityId];
    //  logger.debug(`${className}|deleteLabel| delete System label: ${labelIdentity.label} initiated by userId: ${userId}`)
    //  let userObj = await this.annotationUserRepository.findById(userId)
    //  let teamId = userObj.teamId
    //  if(!teamId){
    //    logger.error(`${className}|deleteLabel| deleteLabel request from userId: ${userId} : Could not find Team of the user `)
    //    throw new HttpErrors.NotAcceptable(AnnotationUserMessages.DATALAKE_TEAM_NOT_EXIST);
    //  }

    //  this.userActivityLogService.createUserActivityLog(userId, UserActivityType.SYSTEM_LABEL_DELETE,`Delete System label: ${labelIdentity.label} initiated by userId: ${userId} teamId: ${teamId}`, new Date())
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    if (!teamId) {
      throw new HttpErrors.NotAcceptable('System label team does not exists');
    }
    return await this.systemLabelService.deleteSystemLabel(labelIdentity.label, teamId);
  }

  /**
   * Use to get list of system labels
   * @param labelType {number} type of system label to filter
   * @returns list of system lables of the team
   */
  @get('/api/system/label/list')
  async getLabelList(
    @param.query.number('labelType') labelType: number,
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number,
    @param.query.string('searchKey') searchKey: string,
    //@inject(SecurityBindings.USER) currentUserProfile: UserProfileDetailed,
  ) {
    // const userId = currentUserProfile[securityId];
    // let userObj = await this.annotationUserRepository.findById(userId)
    // let teamId = userObj.teamId
    // let userType = userObj.userType || AnnotationUserType.ANNOTATION_USER_TYPE_ANNOTATOR

    // if(!teamId){
    //   logger.error(`${className}|getLabelList| getLabelListrequest from userId: ${userId} : Could not find Team of the user `)
    //   throw new HttpErrors.NotAcceptable(AnnotationUserMessages.DATALAKE_TEAM_NOT_EXIST);
    // }

    // logger.debug(`${className}|getLabelList| Get System label list of teamId: ${teamId} initiated by userId: ${userId}`)
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userType = this.currentUserProfile ? this.currentUserProfile.userType || 0 : 0;

    // let index = pageIndex ? pageIndex : 0;
    // let size = pageSize ? pageSize : 20;
    let searchText = searchKey ? searchKey : '';

    if (!teamId) {
      throw new HttpErrors.NotAcceptable('System label team does not exists');
    }
    return await this.systemLabelService.getSystemLabelList(
      userType,
      teamId,
      Explore_API_TYPE.LIST,
      pageIndex,
      pageSize,
      searchText,
      labelType,
    );
  }

  /**
   * Use to get count of system labels
   * @param labelType {number} type of system label to filter
   * @param searchKey search by label text
   * @returns count of system lables of the team
   */
  @get('/api/system/label/count')
  async getLabelCount(
    @param.query.string('searchKey') searchKey: string,
    @param.query.number('labelType') labelType: number,
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userType = this.currentUserProfile ? this.currentUserProfile.userType || 0 : 0;

    let searchText = searchKey ? searchKey : '';

    if (!teamId) {
      throw new HttpErrors.NotAcceptable('System label team does not exists');
    }
    return await this.systemLabelService.getSystemLabelList(
      userType,
      teamId,
      Explore_API_TYPE.COUNT,
      undefined,
      undefined,
      searchText,
      labelType,
    );
  }

  /**
   * Use to upload images to system labels
   * @param request {object} image data
   * @param response {object}
   * @returns {object} created label object
   */
  @post('api/system/label/image/upload')
  async labelImageUpload(
    @requestBody({
      description: 'label picture',
      required: true,
      content: {
        'multipart/form-data': {
          'x-parser': 'stream',
          schema: {type: 'object'},
        },
      },
    })
    request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
    //  @inject(SecurityBindings.USER)
    //  currentUserProfile: UserProfile,
  ) {
    const userId = this.currentUserProfile ? this.currentUserProfile.id : '';
    // let userObj = await this.annotationUserRepository.findById(userId)
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userName = this.currentUserProfile ? this.currentUserProfile.name || '' : '';
    if (!teamId || !userId) {
      throw new HttpErrors.NotAcceptable('System label team or user does not exists');
    }

    //const fileStorage = new FileStorageService(StorageName.LABELS);
    const storage = multer.memoryStorage();
    const upload = multer({storage});
    const fileArr = <FileInterface[]>await new Promise<object>((resolve, reject) => {
      upload.any()(<any>request, <any>response, (err: any) => {
        if (err) reject(err);
        else {
          resolve(request.files!);
        }
      });
    });
    //logger.debug(request);

    let type = Number(request.body.type);
    let label = request.body.label;
    let attributeLabel = request.body.attributeLabel;
    let valueName = request.body.valueName;

    // logger.debug(`project label image upload for label: ${request.body.label} initiated`)
    // logger.debug(`${className}|labelImageUpload| Upload System label image for label: ${request.body.label} initiated by userId: ${userId}`)
    // this.userActivityLogService.createUserActivityLog(userId, UserActivityType.SYSTEM_LABEL_IMAGE_UPLOAD,`Upload System label image for label: ${request.body.label} initiated by userId: ${userId} teamId: ${teamId}`, new Date())

    return await this.systemLabelService.labelImageUpload(
      type,
      label,
      attributeLabel,
      valueName,
      fileArr,
      teamId,
      userName,
      userId,
    );
  }

  /**
   * Used for creating a group of labels
   * @returns id of the created label group if success
   */
  @post('/api/labelGroups/validateNameToCreate')
  async validateNewLabelGroupName(
    @requestBody()
    labelGroupData: {
      groupName: string;
    },
  ) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.LABEL_GROUP}| SystemLabelController.validateNewLabelGroupName | Could not find Team of the user: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    if (!labelGroupData.groupName || labelGroupData.groupName.trim().length == 0) {
      logger.error(
        `${FLOWS.LABEL_GROUP}| SystemLabelController.validateNewLabelGroupName | label Group name cannot be empty`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LABEL_GROUPNAME_EMPTY);
    }

    return await this.systemLabelService.validateNewLabelGroupName(labelGroupData.groupName, teamId);
  }

  /**
   * Used for creating a group of labels
   * @returns id of the created label group if success
   */
  @post('/api/labelGroups/create')
  async createLabelGroup(
    @requestBody()
    labelGroupData: {
      labelKeys: string[];
      groupName: string;
    },
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : '';
    let userName = this.currentUserProfile ? this.currentUserProfile.name : '';

    if (!teamId) {
      logger.error(
        `${className}| createLabelGroup | creating a group of labels from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }
    let currentUserProfile = this.currentUserProfile;
    let labelGroupRes = await this.systemLabelService.createLabelGroup(
      labelGroupData.groupName,
      labelGroupData.labelKeys,
      currentUserProfile,
    );

    if (labelGroupRes.groupId) {
      return {groupId: labelGroupRes.groupId};
    } else {
      throw new HttpErrors.NotAcceptable(labelGroupRes.errorMsg);
    }
  }

  /**
   * Used for editing a group of labels
   * @returns id of the edited label group if success
   */
  @post('/api/labelGroups/{groupId}/edit')
  async editLabelGroup(
    @param.path.string('groupId') labelGroupId: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]; groupName: string},
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : '';
    if (!teamId) {
      logger.error(
        `${className}| createLabelGroup | creating a group of labels from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let currentUserProfile = this.currentUserProfile;

    let returnObj = await this.systemLabelService.editLabelGroup(
      labelGroupId,
      labelGroupData.groupName,
      labelGroupData.labelKeys,
      currentUserProfile,
    );

    if (returnObj.success) {
      return returnObj;
    } else {
      throw new HttpErrors.NotAcceptable(returnObj.errorMsg);
    }
  }

  /**
   * Used for deleting a group of labels
   * @returns id of the deleted label group if success
   */
  @post('/api/labelGroups/{groupId}/delete')
  async deleteLabelGroup(@param.path.string('groupId') labelGroupId: string) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : null;

    if (!teamId) {
      logger.error(
        `${className}| createLabelGroup | creating a group of labels from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    await this.systemLabelService.deleteLabelGroup(labelGroupId);

    return {success: true};
  }

  /** Add label list to a group
   * @param labelGroupId {string}: ID of the label group
   * @param labelGroupData.labelKeys {array}: List of label keys belonging to the labels that we need to add to this group
   */
  @post('/api/labelGroups/{groupId}/addLabels')
  async addLabelsToGroup(
    @param.path.string('groupId') labelGroupId: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]},
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : '';
    let userName = this.currentUserProfile ? this.currentUserProfile.name : '';

    if (!teamId) {
      logger.error(
        `${className}| addLabelsToGroup | Add label list to a group from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }

    let currentUserProfile = this.currentUserProfile;

    await this.systemLabelService.attachLabelsToGroup(labelGroupId, labelGroupData.labelKeys, currentUserProfile);

    return {
      isSuccess: true,
    };
  }

  /** remove label from a group
   * @param labelGroupId {string}: ID of the label group
   * @param labelGroupData.labelKeys {array}: List of label keys belonging to the labels that we need to add to this group
   */

  @post('/api/labelGroups/{groupId}/removeLabels')
  async removeLabelsFromGroup(
    @param.path.string('groupId') labelGroupId: string,
    @requestBody()
    labelGroupData: {labelKeys: string[]},
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : '';
    let userName = this.currentUserProfile ? this.currentUserProfile.name : '';

    if (!teamId) {
      logger.error(
        `${className}| removeLabelsFromGroup | remove label from a group from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    if (labelGroupData.labelKeys.length == 0) {
      throw new HttpErrors.NotAcceptable('Label list is empty');
    }

    await this.systemLabelService.detachLabelsFromGroup(
      labelGroupId,
      labelGroupData.labelKeys,
      this.currentUserProfile,
    );

    return {
      isSuccess: true,
    };
  }

  /**
   * Used to get list of all label groups in the team
   * @returns list of all label group ids and names
   */
  @get('/api/labelGroups/list')
  async getAllLabelGroups(
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number,
    @param.query.string('searchKey') searchKey: string,
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : null;

    let index = pageIndex ? pageIndex : 0;
    let size = pageSize ? pageSize : 20;
    let searchText = searchKey ? searchKey : '';

    if (!teamId) {
      logger.error(
        `${className}| getAllLabelGroups | get list of all label groups from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let project = {
      'labels._id': 1,
      'labels.type': 1,
      'labels.label': 1,
      'labels.key': 1,
      'labels.labelText': 1,
      'labels.lastModifiedAt': 1,
      'labels.lastModifiedBy': 1,
      'labels.imgFiles': 1,
      'labels.attributes': 1,
      _id: 1,
      groupName: 1,
      createdAt: 1,
      teamId: 1,
      labelCount: 1,
      modifiedBy: 1,
      updatedAt: 1,
    };

    let groupList = await this.systemLabelService.getLabelGroupsListWithLabelDetails(
      teamId,
      project,
      Explore_API_TYPE.LIST,
      index,
      size,
      searchText,
    );
    return groupList;
  }

  /**
   * Used to get count of all label groups in the team
   * @param searchKey search by label group name
   * @returns count of all label group ids and names
   */
  @get('/api/labelGroups/count')
  async getAllLabelGroupsCount(@param.query.string('searchKey') searchKey: string) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : null;

    let searchText = searchKey ? searchKey : '';

    if (!teamId) {
      logger.error(
        `${className}| getAllLabelGroupsCount | get list of all label groups from userId: ${userId} : Could not find Team of the user `,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let countObj = await this.systemLabelService.getLabelGroupsListWithLabelDetails(
      teamId,
      {},
      Explore_API_TYPE.COUNT,
      undefined,
      undefined,
      searchText,
    );
    return countObj;
  }
}
