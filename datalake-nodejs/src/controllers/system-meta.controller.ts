/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta tag and field model
 */

/**
 * @class SystemMetaController
 * Handle the request related to the SystemMetaController
 * @description This controller use for Handle the request related to the SystemLabel controller eg: get system meta tag and field list
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {SYSTEM_META_SERVICE, SystemMetaService} from '../services/system-meta.service';
import {UserTypeDetailed} from '../settings/constants';

const className = 'SystemMetaController';

@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
@authenticate('jwt')
export class SystemMetaController {
  constructor(
    @inject(SYSTEM_META_SERVICE) private systemMetaService: SystemMetaService,
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
  ) {}

  /**
   * Use to get Meta tag list
   * @param metaTagFilter {object} tag filter
   * @returns {success: boolean}
   */
  @post('/api/systemMeta/tags/list')
  async getMetaTagList(@requestBody() metaTagFilter: {searchKey: string; pageIndex: number; pageSize: number}) {
    //logger.debug(this.currentUserProfile.name)

    metaTagFilter.pageIndex = metaTagFilter.pageIndex ? metaTagFilter.pageIndex : 0;
    metaTagFilter.pageSize = metaTagFilter.pageSize ? metaTagFilter.pageSize : 20;

    let metaTagList = await this.systemMetaService.getMetaTagList(metaTagFilter);

    return metaTagList;
  }

  /**
   * Use to get Meta tag list
   * @param metaTagFilter {object} tag filter
   * @returns {success: boolean}
   */
  @post('/api/systemMeta/tags/count')
  async getMetaTagCount(@requestBody() metaTagFilter: {searchKey: string}) {
    return await this.systemMetaService.getMetaTagCount(metaTagFilter);
  }

  /**
   * Use to create Meta tag
   * @param metaTagCreate {object} new tag name
   * @returns {success: boolean}
   */
  @post('/api/systemMeta/tags/create')
  async createMetaTag(@requestBody() metaTagCreate: {name: string}) {
    return await this.systemMetaService.createMetaTag(metaTagCreate.name, this.currentUserProfile);
  }

  /**
   * Use to edit Meta tag
   * @param tagId {string} id of the tag
   * @param metaTagCreate {object} new tag name
   * @returns {success: boolean}
   */
  @post('/api/systemMeta/tags/{tagId}/edit')
  async editMetaTag(
    @param.path.string('tagId') tagId: string,
    @requestBody()
    metaTagCreate: {
      name: string;
    },
  ) {
    return await this.systemMetaService.editMetaTag(tagId, metaTagCreate.name, this.currentUserProfile);
  }

  /**
   * Use to delete Meta tag
   * @param tagId {string} id of the tag
   * @param metaTagCreate {object} new tag name
   * @returns {success: boolean}
   */
  @post('/api/systemMeta/tags/{tagId}/delete')
  async deleteMetaTag(@param.path.string('tagId') tagId: string) {
    return await this.systemMetaService.deleteMetaTag(tagId, this.currentUserProfile);
  }
}
