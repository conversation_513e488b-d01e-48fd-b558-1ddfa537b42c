/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta-data model
 */

/**
 * @class MetaDataController
 * Handle the request related to the MetaDataC Controller
 * @description This controller use for Handle the request related to the MetaData controller eg: upload metadata
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, get, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings, securityId} from '@loopback/security';
import {EJSON, ObjectId} from 'bson';
import dotenv from 'dotenv';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  ContentType,
  EditMetaDataInputFormat,
  EditMetaFieldInputFormat,
  GetMetaFieldsOfSystemRequest,
  MERGE_COLLECTION_BODY,
  MergeCollectionsBody,
  MetaDataCollectionInputObjectFormat,
  MetaDataInputFeedObject,
  MetaDataUpdateObject,
  OBJECT_STATUS,
  OperationMode,
  OperationType,
  UpdateJobConfig,
} from '../models';
import {
  ApiKeyRepository,
  InputMetaDataFeedRepository,
  MetaDataRepository,
  MetaDataUpdateRepository,
  QueryOptionRepository,
} from '../repositories';
import {
  DATASET_MANAGER_INTERFACE_SERVICE,
  DatasetManagerInterfaceService,
  InputMetadataFeedService,
  META_DATA_SERVICE,
  META_FIELD_PROPAGATOR_SERVICE,
  MetaDataService,
  MetaFieldPropagatorService,
  OBJECT_META_UPDATER_SERVICE,
  ObjectMetaUpdaterService,
  SEARCH_QUERY_BUILDER_SERVICE,
  STORAGE_CRAWLER_SERVICE,
  SYSTEM_VALIDATION_SERVICE,
  SearchQueryBuilderService,
  StorageCrawlerService,
  SystemValidationService,
} from '../services';
import {SYSTEM_META_SERVICE, SystemMetaService} from '../services/system-meta.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
dotenv.config();

const baseUrl = '/api/metadata';
const internalBaseUrl = '/internal/metadata';

export class MetaDataController {
  constructor(
    @repository(InputMetaDataFeedRepository) public inputMetaDataFeedRepository: InputMetaDataFeedRepository,
    @repository(ApiKeyRepository) public apiKeyRepository: ApiKeyRepository,
    @inject(STORAGE_CRAWLER_SERVICE) private storageCrawlerService: StorageCrawlerService,
    @inject(OBJECT_META_UPDATER_SERVICE) private objectMetaUpdaterService: ObjectMetaUpdaterService,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @repository(QueryOptionRepository) public queryOptionRepository: QueryOptionRepository,
    @repository(MetaDataUpdateRepository) public metaDataUpdateRepository: MetaDataUpdateRepository,
    @inject(SYSTEM_VALIDATION_SERVICE) private systemValidationService: SystemValidationService,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @inject(SecurityBindings.USER, {optional: true}) public currentUserProfile: UserProfileDetailed,
    @service(InputMetadataFeedService) private inputMetadataFeedService: InputMetadataFeedService,
    @inject(DATASET_MANAGER_INTERFACE_SERVICE) private datasetManagerInterfaceService: DatasetManagerInterfaceService,
    @inject(META_FIELD_PROPAGATOR_SERVICE)
    private metaFieldPropagatorService: MetaFieldPropagatorService,
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(SYSTEM_META_SERVICE) private systemMetaService: SystemMetaService,
  ) {}

  /**
   * use to save input metadata feed
   * @param taskId {string} id of the task
   * @returns collectionId
   */
  @post(baseUrl + '/uploadMetadata')
  async saveMetadata(
    @param.header.string('Authorization') authorization: string,
    @requestBody() metaDataInputObject: MetaDataInputFeedObject,
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basci key: ', authorizationKeySecret);

    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    metaDataInputObject.apiKey = authorizeDetails.key;

    if (!metaDataInputObject) {
      return;
    }
    return this.inputMetaDataFeedRepository.onInputMetaData(metaDataInputObject, this.currentUserProfile);
  }

  /**
   * use to save input metadata feed in bulk
   * Gets object key list and a common metadataUpdateSet
   * Create fileUpload document (to track progress)
   * called from frontend
   */
  @authenticate('jwt') //---------------------------------
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/uploadMetadataInCollection')
  async startMetadataUploadInCollection(@requestBody() metaDataInputCollection: MetaDataCollectionInputObjectFormat) {
    // const apiKey = metaDataInputBulk.apiKey;
    // await this.systemValidationService.validateApiKey(apiKey);

    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : undefined;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : undefined;
    let userName = this.currentUserProfile ? this.currentUserProfile.name : undefined;
    logger.debug(`teamId: ${teamId}`);

    if (!metaDataInputCollection) {
      return;
    }
    return await this.inputMetadataFeedService.handleMetaDataCollectionInputStart(
      metaDataInputCollection,
      this.currentUserProfile,
      teamId,
      userId,
      userName,
    );
    // return this.inputMetaDataFeedRepository.onInputMetaData(metaDataInputBulk)
  }

  /**
   * use to get fields list in metaData upload call
   * @params contentType
   * @returns Recently used metaData fields , all metaData fields, recently used tags, all tags, existing collection list (filtered by type and team)
   */
  @authenticate('jwt') //---------------------------------
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @get(baseUrl + '/getMetaDataFields')
  async getMetaDataFields(@param.query.number('collectionContentType') contentType: ContentType) {
    // const apiKey = metaDataInputBulk.apiKey;
    // await this.systemValidationService.validateApiKey(apiKey);
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : undefined;
    logger.debug(`teamId: ${teamId}`);
    //  if (!metaDataInputCollection) {
    //    return
    //  }
    // return await this.inputMetadataFeedService.generateMetaDataFields(contentType, teamId)
    // return this.inputMetaDataFeedRepository.onInputMetaData(metaDataInputBulk)
  }

  /**
   * use to get object type by object id
   * @params id : string
   * @returns {objectType: number}
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @get(baseUrl + '/getObjectTypeById/{id}')
  async getObjectTypeById(@param.path.string('id') id: string) {
    return this.metaDataService.getObjectTypeById(id);
  }

  /**
   * Use to insert or update a list of MetaDataUpdate documents
   * @param operationType {OperationType} type of operation
   * @param operationMode {OperationMode} mode of operation type
   * @param operationId {string} Unique id for perticular operation (ex: if annotation operation then we can use annotation projectId as operationId)
   * @param data {UpdateOperationDataRequestBody}
   * @returns returnArray {operationId: string, objectKey: string, success: boolean}[] status of insert or update of each document
   */
  @post(baseUrl + '/operationdata/{operationId}/update')
  async updateOperationData(
    @param.header.string('Authorization') authorization: string,
    @param.path.string('operationId') operationId: string,
    @param.query.boolean('isNormalized') isNormalized: boolean,
    @param.query.string('operationName') operationName: string,
    @requestBody()
    data: {
      metaUpdatesArray: string;
      sessionId: string;
      totalImageCount: number;
      uploadedImageCount: number;
      operationMode: OperationMode;
      operationType: OperationType;
    },
  ) {
    const authorizationKeySecret = authorization;
    //logger.debug('Basci key: ', authorizationKeySecret)

    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let sessionData = {
      sessionId: data.sessionId,
      totalImageCount: data.totalImageCount,
      uploadedImageCount: data.uploadedImageCount,
    };

    let teamId = authorizeDetails.teamId;
    let operationType = data.operationType;
    let operationMode = data.operationMode;

    let parseData: MetaDataUpdateObject[] = EJSON.parse(data.metaUpdatesArray) as MetaDataUpdateObject[];

    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    return await this.objectMetaUpdaterService.updateOperationData(
      operationType,
      operationMode,
      operationId,
      parseData,
      teamId,
      isNormalized,
      sessionData,
      operationName,
      currentUserProfile,
    );
  }

  /**
   * use to create a virtual collection using a selectionId
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/vCollection/create')
  async createVirtualCollection(
    @requestBody() data: {selectionId: string; vCollectionId?: string; vCollectionName?: string},
  ) {
    let currentUserProfile = this.currentUserProfile;
    let userId = currentUserProfile.id;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.VIRTUAL_COLLECTION} | MetaDataController.createVirtualCollection | ${userId} | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    const config: UpdateJobConfig = {isAwait: false};

    return await this.metaDataService.updateVirtualCollection(
      currentUserProfile,
      data.selectionId,
      config,
      data.vCollectionId,
      data.vCollectionName,
      undefined,
    );
  }

  /**
   * Merge selected collections(Image,Video or Other collection)
   * @param data {MergeCollectionsBody} selectionId, collectionName
   * @returns job id
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/collection/merge')
  async mergeCollections(@requestBody(MERGE_COLLECTION_BODY) data: MergeCollectionsBody) {
    let teamId = this.currentUserProfile.teamId;
    let userId = this.currentUserProfile.id;

    if (!teamId) {
      logger.error(
        `${FLOWS.VIRTUAL_COLLECTION} | MetaDataController.mergeCollections | ${userId} | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    /**
     * Merge selected collections
     */
    return await this.metaDataService.mergeCollection(
      teamId,
      data.selectionId,
      data.collectionName,
      this.currentUserProfile,
    );
  }

  /**
   * use for testing purposes
   * @returns string
   */
  @get(baseUrl + '/testAPI')
  async testAPI() {
    logger.debug(`Testing | MetaDataController.testAPI | N/A | N/A `);

    this.objectMetaUpdaterService.fileUrlRenew();

    return 'Done';
  }

  /**
   * use to trigger processes manually
   * @returns
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/manual/trigger')
  async manualTrigger(@requestBody() body: {processId: string; data: {[k: string]: any}}) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : undefined;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : undefined;

    logger.debug(
      `Trigger process manually | MetaDataController.manualTrigger | ${userId} | teamId: ${teamId}, requested process: `,
      body,
    );

    switch (body.processId) {
      // renew s3 pre-signed urls
      case 'lmwtuy9h7nyov7v6txb2':
        this.objectMetaUpdaterService.fileUrlRenew();
        break;

      // renew s3 pre-signed urls
      case '6k74dfbd48391axc8xp5':
        this.datasetManagerInterfaceService.rebuildSearchQueryOfDataset(body.data.datasetIdList);
        break;

      case '9k74dfbd48391axc8xp6':
        for (let datasetId of body.data.datasetIdList) {
          this.metaDataService.calcDatasetAnalyticsWhileChangeFiles(datasetId);
        }

        break;

      default:
        break;
    }

    return 'started';
  }

  /**
   * use to retrive file information of a list
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  @post(baseUrl + '/getFileDataList')
  async getFileDataList(
    @param.header.string('Authorization') authorization: string,
    @requestBody() objectIdList: string[],
  ) {
    const authorizationKeySecret = authorization;
    logger.debug('Basci key: ', authorizationKeySecret);

    await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    return await this.metaDataRepository.getFileDataArray(objectIdList);
  }

  /**
   * use to retrive file information of a id list and include child items
   * @param objectIdList[] ObjectId[] id list of files
   * @returns data file info list
   */
  /*
   @post(baseUrl + '/getDataListWithChildFiles')
   async getDataListWithChildFiles(
     @param.header.string('Authorization') authorization: string,
     @param.query.string('projectId') projectId: string,
     @param.query.string('projectName') projectName: string,
     @requestBody() objectIdList: string[],
   ) {

     const authorizationKeySecret = authorization;
     logger.debug('Basic key: ', authorizationKeySecret)
     let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret)

     let teamId = authorizeDetails.teamId

     return await this.metaDataService.handleGetDataArraytWithChildFiles(objectIdList, projectId, projectName, teamId)
   }
   */

  /**
   * ---------------------------Internal Apis--------------------------------------------
   */

  /**
   * use to get metaData filtered by taskId
   * called from studio
   * @param taskId - filter metadata by taskId
   * @param projectId - id of the studio project which task belongs
   * @param body - operation list
   */
  @post(internalBaseUrl + '/getTaskMetaData/{taskId}/project/{projectId}')
  async getTaskMetaData(
    @param.path.string('taskId') taskId: string,
    @param.path.string('projectId') projectId: string,
    @requestBody()
    body: {
      operationList: string[];
    },
  ) {
    logger.debug(
      `Get metadata list of a task | MetaDataController.getTaskMetaData | taskId:${taskId} | task metadata request, projectId: ${projectId} `,
    );
    // return this.awsS3StorageService.fetchFilesFromStorageAndPopulateMetaData()
    // return this.metaDataRepository.handleCreateCollection("test")
    return this.metaDataRepository.getTaskMetaData(taskId, projectId, body.operationList);
  }

  /**
   * Get response by aggregating metaDataUploads
   * Use to calculate annotation stats from annotation studio
   * @param query {aggregate query}
   * @returns response from aggregation
   */
  @post(internalBaseUrl + '/metaDataUpdates/aggregate')
  async aggregateMetaDataUpdates(@requestBody() body: {query: string}) {
    logger.debug(`Aggregate metadata update | MetaDataController.aggregateMetaDataUpdates | N/A | N/A `);
    //logger.debug(JSON.stringify(body.query, null, 2))
    let data = await this.metaDataUpdateRepository.aggregateMetaDataUpdates(body.query);
    return data;
  }

  /**
   * use to save metadata by internal api call
   * @param taskId {string} id of the task
   * @returns collectionId
   */
  @post('internal/metadata/uploadMetadata')
  async saveMetadataInternal(@requestBody() metaDataInputObject: MetaDataInputFeedObject) {
    const projectId = metaDataInputObject.projectId;

    try {
      if (!metaDataInputObject) {
        return;
      }
      // return this.inputMetaDataFeedRepository.onInputMetaData(metaDataInputObject)
      this.metaDataService.updateAnnotationProjectInfoList(metaDataInputObject);
    } catch (err) {
      logger.error(
        `Internal API | SearchQueryBuilderService.getAnnotationProjectInfoList | N/A | Failed to update metadata of : ${projectId}, error: ${err}`,
      );
    }
  }

  /**
   * Use to update annotation project list and task id list of metadata
   * @param metaDataInputObject {MetaDataInputFeedObject}
   * @returns {isSuccess: boolean}
   */
  @post('internal/metadata/tagImageToProject')
  async tagImageToProject(@requestBody() metaDataInputObject: MetaDataInputFeedObject) {
    let projectId = metaDataInputObject.projectId;

    try {
      if (!metaDataInputObject) {
        return {isSuccess: false};
      }
      await this.metaDataService.tagImageToProject(metaDataInputObject);
      return {isSuccess: true};
    } catch (err) {
      logger.error(
        `Internal API | SearchQueryBuilderService.getAnnotationProjectInfoList | N/A | Failed to update metadata of : ${projectId}, error: ${err}`,
      );

      return {isSuccess: false};
    }
  }

  // ------------------------------------- tags ---------------------------------------------------

  /**
   * get metaData Tags of system as string array, use for suggestion in frontend
   * @returns metaTag list of the system
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @get(baseUrl + '/getMetaDataTagSuggestions')
  async getMetaDataTagSuggestions() {
    logger.debug(
      `Get meta tags | MetaDataController.getMetaDataTagSuggestions | N/A | get getMetaDataTagSuggestions request`,
    );

    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | MetaDataController.getMetaDataTagSuggestions | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return await this.objectMetaUpdaterService.getMetaDataTagSuggestions(teamId);
  }

  /**
   * update metadata of a file or a collection
   * @param body : EditMetaDataInputFormat (of collection or file)
   */
  @authenticate('jwt') //---------------------------------
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/editMetaData/updateFields')
  async updateMetadataFields(@requestBody() body: EditMetaDataInputFormat) {
    logger.debug(`Edit meta data | MetaDataController.updateMetadataFields | N/A | Update metadata request`);

    await this.objectMetaUpdaterService.validateAndUpdateMetadataFields(body, this.currentUserProfile);

    // this.objectMetaUpdaterService.updateMetadataFields(body, teamId, this.currentUserProfile.id)

    return;
  }

  /**
   * Get meta data labels
   * @param {any} body search query
   * @returns labels and count
   */
  @post(internalBaseUrl + '/datasetLabel/aggregate')
  async aggregateSystemLabel(
    @requestBody()
    body: {
      selectionId: string;
      removedCollectionIdList: string[];
      addedCollectionIdList: string[];
      datasetVersionId: string;
      selectedOperationIdList: string[];
    },
    @param.query.string('userInfo') userInfo: string,
  ) {
    logger.debug(`Aggregate dataset system label count  | MetaDataController.aggregateSystemLabel | N/A | N/A `);

    let currentUserProfile = userInfo ? (JSON.parse(userInfo) as UserProfileDetailed) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Aggregate dataset system label count  | MetaDataController.aggregateSystemLabel | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_USER_NOT_EXIST);
    }

    let matchFilter = await this.searchQueryBuilderService.createMatchQuery(
      currentUserProfile,
      body.selectionId,
      body.removedCollectionIdList,
      body.addedCollectionIdList,
      body.datasetVersionId,
    );

    if (!matchFilter) return [];

    return await this.metaDataService.getLabelList(matchFilter, body.selectedOperationIdList);
  }

  /**
   * Get meta data labels
   * @param {any} body search query
   * @returns labels and count
   */
  @post('internal/dataset/labelStat/aggregate')
  async aggregateLabelStat(@requestBody() body: {versionId: string; operationList: string[]}) {
    logger.debug(`Aggregate dataset system label count  | MetaDataController.aggregateSystemLabel | N/A | N/A `);

    let matchFilter = {
      'datasetVersionList.datasetVersionId': new ObjectId(body.versionId),
      objectType: ContentType.IMAGE,
    };

    return await this.metaDataService.getLabelListFromMetaDatUpdates(matchFilter, body.operationList);
  }

  /**
   * Get meta data labels
   * @param {any} body search query
   * @returns labels and count
   */
  @post('internal/dataset/frameStat/aggregate')
  async aggregateFrameStat(@requestBody() body: {versionId: string}) {
    logger.debug(`Aggregate dataset system label count  | MetaDataController.aggregateSystemLabel | N/A | N/A `);

    let matchFilter = {
      'datasetVersionList.datasetVersionId': new ObjectId(body.versionId),
      objectType: ContentType.IMAGE,
      objectStatus: OBJECT_STATUS.ACTIVE,
    };

    return await this.metaDataService.getFrameStat(matchFilter);
  }

  /**
   * Aggregate MetaData Collection from internam applications
   */
  @post(internalBaseUrl + '/aggregate')
  async getAggregateResult(@requestBody() body: {query: string}) {
    logger.debug(
      `Aggregate MetaData | MetaDataController.getAggregateResult | N/A | received query for aggregate on metadata ${body.query}`,
    );
    //logger.debug(JSON.stringify(body.query, null, 2));

    // let matchFilter = await this.searchQueryBuilderService.createMatchQuery();
    /**
     * aggregate meta data
     */
    let data = await this.metaDataRepository.aggregateMetaData(body.query);

    return data;
  }

  /**
   * UpdateMany with $set for MetaData Collection from internam applications
   */
  @post(internalBaseUrl + '/directUpdate')
  async directUpdate(@requestBody() body: {query: string; data: string; arrayFilter: string; type: string}) {
    logger.debug(`Aggregate MetaData | MetaDataController.directUpdate | N/A | update details: ${body} `);
    //logger.debug(JSON.stringify(body.query, null, 2));

    let query = EJSON.parse(body.query);
    let data = EJSON.parse(body.data);
    let arrayFilter = EJSON.parse(body.arrayFilter);

    /**
     * aggregate meta data
     */
    try {
      if (body.type == 'set') {
        await this.metaDataRepository.updateManySet(query, data, arrayFilter);
      } else if (body.type == 'unset') {
        await this.metaDataRepository.updateManyUnSet(query, data, arrayFilter);
      } else if (body.type == 'pull') {
        await this.metaDataRepository.updateManyRemoveFromList(query, data, arrayFilter);
      } else if (body.type == 'push') {
        await this.metaDataRepository.updateManyPushToList(query, data, arrayFilter);
      } else if (body.type == 'addToSet') {
        await this.metaDataRepository.updateManyAddToSetToList(query, data, arrayFilter);
      }

      return {success: true};
    } catch (err) {
      logger.error(`Set metadata | MetaDataRepository.aggregateMetaData | N/A | error: ${err}`, err);
      throw new HttpErrors.NotAcceptable('set meta data failed');
    }
  }

  /**
   * Get data to dataset manager datagrid
   */
  @post(internalBaseUrl + '/dataGrid/aggregate')
  async getDataGridImages(@requestBody() body: {query: string}) {
    logger.debug(`Aggregate data grid images | MetaDataController.getDataGridImages | N/A | N/A `);
    // logger.debug(`dataGrid images`, JSON.stringify(body.query, null, 2));
    // logger.debug(`dataGrid image count`, JSON.stringify(body.countQuery, null, 2));

    /**
     * aggregate meta data
     */
    let frameArray = await this.metaDataRepository.aggregateMetaData(body.query);

    return {
      frameArray: frameArray,
    };
  }

  /**
   * Get data to dataset stats
   */
  @post(internalBaseUrl + '/datasetStats/aggregate')
  async getDataSetStats(@requestBody() body: {datasetGroupId: string}) {
    logger.debug(
      `Aggregate dataset stats | MetaDataController.getDataSetStats | N/A | datasetGroupId: ${body.datasetGroupId}`,
    );

    /**
     *get dataset stats
     */
    let response = await this.datasetManagerInterfaceService.aggregateDatasetStats(body.datasetGroupId);

    /**
     * update frames count and label stats in metadata dataset object
     */
    await this.metaDataService.updateLabelAndFramesOfDataset(response, body.datasetGroupId);

    this.metaDataService.calcDatasetAnalyticsWhileChangeFiles(body.datasetGroupId);

    this.queryOptionRepository.rebuildDatasetQueryOptionForTags(body.datasetGroupId);

    return {
      labelStats: response.labelStats,
      frameCount: response.frameCount,
    };
  }

  /**
   * get meta fields list for collecction
   * @param collectionId collection id
   * @returns metaFields list
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @get(baseUrl + '/getMetaFieldsOfCollection')
  async getMetaFieldsOfCollection(@param.query.string('collectionId') collectionId: string) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `Get meta fields of collection | MetaDataController.getMetaFieldsOfCollection | N/A | teamId not found`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    return await this.metaFieldPropagatorService.getMetaFieldsOfCollection(collectionId, teamId);
  }

  /**
   * get meta fields list for collection or selection
   * @param collectionId collection id
   * @returns metaFields list
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/getSystemMetaFields')
  async getMetaFieldsOfSystem(
    @param.header.number('timeZoneOffset', {required: true}) timeOffset: number,
    @requestBody() payload: GetMetaFieldsOfSystemRequest,
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile) {
      logger.error(
        `Get meta fields of collection | MetaDataController.getMetaFieldsOfCollection | N/A | teamId not found`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    if (payload.isFileUpload) {
      return await this.metaFieldPropagatorService.getMetaFieldsOfCollection(payload.collectionId, teamId);
    } else {
      return await this.metaFieldPropagatorService.getMetaFieldsOfSelection(payload, timeOffset, currentUserProfile);
    }
  }

  /**
   * to delete metaFields
   * @param metaField meta field id : string
   * @returns {isSuccessfullyDeleted: boolean}
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/deleteMetaFields')
  async deleteMetaFields(@requestBody() metaField: {metaFieldId: string}) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    if (!teamId) {
      throw new HttpErrors.NotAcceptable('Team not found');
    }

    return await this.metaFieldPropagatorService.deleteMetaFields(metaField.metaFieldId, teamId);
  }

  /**
   * get meta data (collectionName, Tags)
   * @param collectionId collectionId : string
   * @param contentType contentType : ContentType
   * @returns MetaDataKeyInputList
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @get(baseUrl + '/getMetaFieldsSuggestions')
  async getMetaFieldsSuggestions(
    @param.query.string('collectionId') collectionId: string,
    @param.query.number('contentType') contentType: ContentType,
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Get meta fields suggestions | MetaDataController.getMetaFieldsSuggestions | N/A | teamId not found`,
      );
      throw new HttpErrors.NotAcceptable('Team not found');
    }
    let teamId = currentUserProfile ? currentUserProfile.teamId : null;

    if (!contentType) {
      contentType = ContentType.IMAGE_COLLECTION;
    }

    return await this.metaFieldPropagatorService.getMetaFieldsSuggestions(
      contentType,
      currentUserProfile,
      collectionId,
    );
  }

  /**
   * to edit metaFields
   * @param data EditMetaFieldInputFormat
   * @returns
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(baseUrl + '/editMetaFields')
  async editMetaFields(@requestBody() data: EditMetaFieldInputFormat) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let username = this.currentUserProfile ? this.currentUserProfile.name : null;

    if (!teamId || !username) {
      logger.error(`Edit meta fields | MetaDataController.editMetaFields | N/A | teamId or username not found`);
      throw new HttpErrors.NotAcceptable('Team not found');
    }

    return await this.metaFieldPropagatorService.editMetaFields(data, teamId, username);
  }

  /**
   * use to rename collection name
   * @param collectionId collection id : string
   * @param data {newName: string; collectionType: ContentType}
   * @returns
   */
  @authenticate('jwt')
  @post(baseUrl + `/rename/collection/{collectionId}`)
  async renameCollectionName(
    @param.path.string('collectionId') collectionId: string,
    @requestBody() data: {newName: string; collectionType: ContentType},
  ) {
    let teamId = this.currentUserProfile ? this.currentUserProfile.teamId : null;
    let userId = this.currentUserProfile ? this.currentUserProfile.id : null;

    if (!teamId || !userId) {
      logger.error(`Edit meta fields | MetaDataController.editMetaFields | N/A | teamId or username not found`);
      throw new HttpErrors.NotAcceptable('Team not found');
    }

    return await this.metaDataService.renameCollection(collectionId, data, teamId, userId);
  }
}
