/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller use for Handle the request related to the graph insert and graph coordinate retrieval
 */

/**
 * @class EmbeddingsController
 * @description This controller use for Handle the request related to the graph insert and graph coordinate retrieval
 * <AUTHOR>
 */

import {inject} from '@loopback/core';
import {HttpErrors, param, post, requestBody} from '@loopback/rest';
import {securityId} from '@loopback/security';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {EMBEDDINGS_SERVICE, EmbeddingsService} from '../services/embeddings.service';
import {EMBEDDING_COLLECTION} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
const VECTOR_DB_COLLECTION = EMBEDDING_COLLECTION;

export class EmbeddingsController {
  constructor(
    @inject(EMBEDDINGS_SERVICE)
    private embeddingsService: EmbeddingsService,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
  ) {}

  @post('api/client/embeddings/getVector')
  async getEmbeddingVector(
    @param.header.string('Authorization') authorization: string,
    @requestBody() body: {embeddingUniqueNameArray: string[]; embeddingModelName: string},
  ) {
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    let teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `create embedding collection | embeddingsService.createCollection | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );

      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let currentUserProfile: UserProfileDetailed = {
      id: authorizeDetails.userId,
      userType: authorizeDetails.userType,
      email: authorizeDetails.email || undefined,
      name: authorizeDetails.userName || authorizeDetails.name || 'Unnamed',
      teamId: authorizeDetails.teamId,
      [securityId]: '',
    };

    let vector = await this.embeddingsService.getEmbeddingVectors(
      body.embeddingUniqueNameArray,
      body.embeddingModelName,
    );

    return vector;
  }
}
