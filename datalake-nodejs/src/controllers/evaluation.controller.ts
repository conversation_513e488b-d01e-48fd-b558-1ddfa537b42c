/*
 * Copyright (c) 2024 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for APIs related to data dictionary evaluation process
 */

/**
 * @class EvaluationController
 * @description This controller use for Handle the request-response lifecycle for APIs related to data dictionary evaluation process
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {del, get, HttpErrors, param, patch, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {DifficultyLevel, QuestionEvalStatus} from '../models';
import {EVALUATION_SERVICE, EvaluationService} from '../services/evaluation.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN,UserTypeDetailed.SUPER_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR]})
export class EvaluationController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(EVALUATION_SERVICE) private evaluationService: EvaluationService,
  ) {}

  @post('api/evaluation/create')
  /**
   * Creates a new evaluation set
   *
   * @returns The newly created evaluation set data if successful.
   * @throws HttpErrors.NotAcceptable if the current user's team is not found
   * or if the creation of the evaluation set fails.
   */
  async createEvalSet() {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.createEvalSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.createEvalSet | <<<< | request by user ${currentUserProfile.id}`,
    );

    let res = await this.evaluationService.createEvalSet();
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @patch('api/evaluation/{evalSetId}/update')
  /**
   * Updates the name of an existing evaluation set.
   *
   * @param evalSetId - The ID of the evaluation set to be updated.
   * @param data - An object containing the new name for the evaluation set.
   * @returns The updated evaluation set data if the operation is successful.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found
   * or if the update operation fails.
   */
  async updateEvalSet(@param.path.string('evalSetId') evalSetId: string, @requestBody() data: {name: string}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.createEvalSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.updateEvalSet | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}, name: ${data.name}`,
    );

    let res = await this.evaluationService.updateEvalSet(evalSetId, data.name);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @get('api/evaluation/list')
  /**
   * Retrieves a list of evaluation sets and data dictionary tune-up status.
   *
   * @returns The evaluation sets and data dictionary tune-up status if the operation is successful.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the retrieval operation fails.
   */
  async getEvalSets() {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSets | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSets | <<<< | request by user ${currentUserProfile.id}`,
    );

    let res = await this.evaluationService.getEvalSets();
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }
  @get('api/evaluation/{evalSetId}/eval-list')
  async getEvalSetSnapList(
    @param.path.string('evalSetId') evalSetId: string,
    @param.query.number('pageIndex') pageIndex: number = 0,
    @param.query.number('pageSize') pageSize: number = 20,
    @param.query.string('searchKey') searchKey?: string,
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetSnapList | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetSnapList | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}, pageIndex: ${pageIndex}, pageSize: ${pageSize}`,
    );

    let res = await this.evaluationService.getEvalSetSnapList(evalSetId, pageIndex, pageSize, searchKey);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @get('api/evaluation/{evalSetId}/eval-info')
  /**
   * Gets the info of an evaluation set snapshot, including its current status (e.g. is running, is runnable, score, etc.)
   *
   * @param evalSetId - The ID of the evaluation set snapshot
   * @returns The evaluation set snapshot info if the operation is successful
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the retrieval operation fails
   */
  async getEvalSetSnapInfo(@param.path.string('evalSetId') evalSetId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetSnapInfo | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetSnapInfo | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}`,
    );

    let res = await this.evaluationService.getEvalSetSnapInfo(evalSetId);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  //deletes an evaluation set
  @del('api/evaluation/{evalSetId}/delete')
  /**
   * Deletes an evaluation set.
   *
   * @param evalSetId - The ID of the evaluation set to be deleted
   * @returns The result of the deletion if successful
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the deletion operation fails
   */
  async deleteEvalSet(@param.path.string('evalSetId') evalSetId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.deleteEvalSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `Delete an evaluation set | EvaluationController.deleteEvalSet | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}`,
    );

    let res = await this.evaluationService.deleteEvalSet(evalSetId);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/evaluation/{evalSetId}/add-truth')
  /**
   * Adds a truth entry to a specified evaluation set.
   *
   * @param evalSetId - The ID of the evaluation set where the truth is to be added.
   * @param data - The data object containing the question, truth, and difficulty level to be added.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the addition fails.
   * @returns The result of the addition if successful.
   */
  async addTruthToEvalSet(
    @param.path.string('evalSetId') evalSetId: string,
    @requestBody() data: {question: string; truth: string; difficulty: DifficultyLevel},
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.addTruthToEvalSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.addTruthToEvalSet | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}`,
    );

    let res = await this.evaluationService.addTruthToEvalSet(evalSetId, data.question, data.truth, data.difficulty);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @patch('api/evaluation/{evalSnapId}/edit-truth')
  /**
   * Edits a truth entry in an evaluation set.
   *
   * @param evalSnapId - The ID of the evaluation snap to be edited.
   * @param data - The data object containing the question, truth, and difficulty level to be updated.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the editing fails.
   * @returns The result of the editing if successful.
   */
  async editTruth(
    @param.path.string('evalSnapId') evalSnapId: string,
    @requestBody() data: {question: string; truth: string; difficulty: DifficultyLevel},
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.editTruth | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.editTruth | <<<< | request by user ${currentUserProfile.id}, evalSnapId: ${evalSnapId}`,
    );

    let res = await this.evaluationService.editTruth(evalSnapId, data.question, data.truth, data.difficulty);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @del('api/evaluation/{evalSnapId}/delete-truth')
  /**
   * Deletes a truth entry from an evaluation set.
   *
   * @param evalSnapId - The ID of the evaluation snap to be deleted.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the deletion fails.
   * @returns The result of the deletion if successful.
   */
  async deleteTruth(@param.path.string('evalSnapId') evalSnapId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.deleteTruthOfEvalSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.deleteTruthOfEvalSet | <<<< | request by user ${currentUserProfile.id}, evalSnapId: ${evalSnapId}`,
    );

    let res = await this.evaluationService.deleteTruth(evalSnapId);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @patch('api/evaluation/{evalSnapId}/change-status')
  /**
   * Changes the evaluation status of a given evaluation snap.
   *
   * @param evalSnapId - The ID of the evaluation snap whose status is to be changed.
   * @param data - An object containing the new status of the evaluation snap.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the status change fails.
   * @returns The result of the status change if successful.
   */
  async changeEvaluationStatus(
    @param.path.string('evalSnapId') evalSnapId: string,
    @requestBody() data: {status: QuestionEvalStatus},
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.changeEvaluationStatus | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.changeEvaluationStatus | <<<< | request by user ${currentUserProfile.id}, evalSnapId: ${evalSnapId}`,
    );

    let res = await this.evaluationService.changeEvaluationStatus(evalSnapId, data.status);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/evaluation/{evalSetId}/run-set')
  /**
   * Runs an evaluation set.
   *
   * @param evalSetId - The ID of the evaluation set to be run.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the run fails.
   * @returns The result of the evaluation set run if successful.
   */
  async runEvaluationSet(@param.path.string('evalSetId') evalSetId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.runEvaluationSet | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.runEvaluationSet | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}`,
    );

    let res = await this.evaluationService.runEvaluationSet(evalSetId, currentUserProfile);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/evaluation/{evalSnapId}/run')
  /**
   * Runs an evaluation snap.
   *
   * @param evalSnapId - The ID of the evaluation snap to be run.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the run fails.
   * @returns The result of the evaluation snap run if successful.
   */
  async runEvalSnap(@param.path.string('evalSnapId') evalSnapId: string) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.runEvalSnap | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.runEvalSnap | <<<< | request by user ${currentUserProfile.id}, evalSnapId: ${evalSnapId}`,
    );

    let res = await this.evaluationService.runEvalSnap(evalSnapId, currentUserProfile);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @get('api/evaluation/{evalSetId}/run-history')
  /**
   * Retrieves the run history of an evaluation set.
   *
   * @param evalSetId - The ID of the evaluation set for which the run history is to be retrieved.
   * @param pageIndex - The page index of the run history to be retrieved. Defaults to 0.
   * @param pageSize - The page size of the run history to be retrieved. Defaults to 20.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the run fails.
   * @returns The run history of the evaluation set if successful.
   */
  async getEvalSetRunHistory(
    @param.path.string('evalSetId') evalSetId: string,
    @param.query.number('pageIndex') pageIndex: number = 0,
    @param.query.number('pageSize') pageSize: number = 20,
  ) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetRunHistory | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.getEvalSetRunHistory | <<<< | request by user ${currentUserProfile.id}, evalSetId: ${evalSetId}`,
    );

    let res = await this.evaluationService.getEvalSetRunHistory(evalSetId, pageIndex, pageSize);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/evaluation/tune-up')
  /**
   * Triggers a data dictionary tuning up process.
   * @throws HttpErrors.NotAcceptable - If the current user's team is not found or if the tuning up fails.
   * @returns The result of the tuning up if successful.
   */
  async dataDictTuneUp() {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.dataDictTuneUp | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.debug(
      `${FLOWS.DATA_DICT_EVAL} | EvaluationController.dataDictTuneUp | <<<< | request by user ${currentUserProfile.id}`,
    );

    let res = await this.evaluationService.dataDictTuneUp(currentUserProfile);
    if (res.success) {
      return res.data;
    } else {
      throw new HttpErrors.NotAcceptable(res.message);
    }
  }

  @post('api/evaluation/csv-upload')
  /**
   * Uploads a CSV file containing truths for an evaluation set
   * @param data - The body of the request
   * @returns - The result of the upload
   * @throws HttpErrors.NotAcceptable - If the team for the current user is not found
   * @throws HttpErrors.NotAcceptable - If the upload fails
   */
  async uploadTruthsByCSV(@requestBody() data: {evalSetId: string; filePath: string}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATA_DICT_EVAL} | EvaluationController.uploadTruthsByCSV | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return this.evaluationService.uploadTruthsByCSV(data.evalSetId, data.filePath);
  }
}
