/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the data crawl model
 */

/**
 * @class DatalakeTrashController
 * Handle the request related to the DatalakeTrash Controller
 * @description This controller use for Handle the request related to the DatalakeTrash controller eg: trash selected object list
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ContentType, ExplorerFilterV2, TrashDefaultViewRequest, TrashRestoreRequest} from '../models';
import {DatalakeSelectionRepository} from '../repositories/datalake-selection.repository';
import {DATALAKE_TRASH_SERVICE, DatalakeTrashService} from '../services/datalake-trash.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
export class DatalakeTrashController {
  constructor(
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(DATALAKE_TRASH_SERVICE)
    private datalakeTrashService: DatalakeTrashService,
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
  ) {}

  /**
   * use to trash object list of relavant to selection id
   * @param data {SelectionId : string}
   * @returns message how many items successfully trashed
   */
  @post('api/trash/selections')
  async trashSelectionObject(@requestBody() data: {selectionId: string}) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.trashSelectionObject | N/A | Couldn't find the user profile for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable('User profile not found');
    }
    //logged user team id
    let teamId = currentUserProfile ? currentUserProfile.teamId : undefined;
    //logged user id
    let userId = currentUserProfile ? currentUserProfile.id : undefined;

    let userName = currentUserProfile ? currentUserProfile.name : undefined;

    try {
      // validate selectionId
      let selectionObj = await this.datalakeSelectionRepository.findById(data.selectionId);

      this.datalakeTrashService.trashSelectionObject(selectionObj, currentUserProfile, userId, userName);

      return {
        message: DatalakeUserMessages.JOB_STARTED_IN_BACKGROUND,
        isSuccess: true,
      };
    } catch (e) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.trashSelectionObject | N/A | ${DatalakeUserMessages.BACKGROUND_JOB_FAILED}, selectionId: ${data.selectionId}, userId: ${userId} | Error: `,
        e,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BACKGROUND_JOB_FAILED);
    }
  }

  /**
   * Use to get trashed object list for Trash tab view
   * @param filterObj {pageIndex: number, pageSize: number, searchKey: string}
   * @returns TrashItemListViewResponse
   */
  @post('api/trash/objects/list')
  async getTrashObjectList(@requestBody() filterObj: TrashDefaultViewRequest) {
    //logged user team id
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.getTrashObjectList | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let startTime = new Date().getTime();
    let pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    let pageSize = filterObj.pageSize ? filterObj.pageSize : 12;
    let searchKey = filterObj.searchKey ? filterObj.searchKey : '';

    let filter: ExplorerFilterV2 = {};
    if (filterObj.filterData) filter = filterObj.filterData;

    let response = await this.datalakeTrashService.getTrashObjectList(
      filter,
      filterObj.contentType ? filterObj.contentType : ContentType.ALL,
      teamId,
      searchKey,
      pageIndex,
      pageSize,
      this.currentUserProfile,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.getTrashObjectList | N/A | Response Time = ${responseTime} ms`,
    );

    return response;
  }

  /**
   * get trash object count
   * @param filterObj {searchKey: string}
   * @returns TrashItemCountViewResponse
   */
  @post('api/trash/objects/count')
  async getTrashObjectCount(
    @requestBody() filterObj: {searchKey: string; filterData: ExplorerFilterV2; contentType: ContentType},
  ) {
    //logged user team id
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.getTrashObjectCount | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let searchKey = filterObj.searchKey ? filterObj.searchKey : '';

    let filter: ExplorerFilterV2 = {};
    if (filterObj.filterData) filter = filterObj.filterData;

    let response = await this.datalakeTrashService.getTrashObjectCount(
      currentUserProfile,
      searchKey,
      filter,
      filterObj.contentType ? filterObj.contentType : ContentType.ALL,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.getTrashObjectCount | N/A | Response Time = ${responseTime} ms`,
    );

    return response;
  }

  /**
   * to restore trash object
   * @param data {searchKey: string, isAllSelected: boolean, objectIdList: string[]}
   * @returns message what is the status of the restore process
   */
  @post('api/trash/objects/restore')
  async restoreTrashObject(@requestBody() data: TrashRestoreRequest) {
    //logged user team id
    let teamId = this.currentUserProfile.teamId;
    //logged user id
    let userId = this.currentUserProfile ? this.currentUserProfile.id : undefined;
    let userName = this.currentUserProfile ? this.currentUserProfile.name : undefined;

    let searchKey = data.searchKey ? data.searchKey : '';
    let isAllSelected = data.isAllSelected ? data.isAllSelected : false;
    let objectIdList = data.objectIdList ? data.objectIdList : [];

    let filter: ExplorerFilterV2 = {};
    if (data.filterData) filter = data.filterData;

    try {
      this.datalakeTrashService.restoreTrashObject(
        objectIdList,
        searchKey,
        isAllSelected,
        this.currentUserProfile,
        userId,
        filter,
        data.contentType ? data.contentType : ContentType.ALL,
        userName,
      );

      return {
        message: DatalakeUserMessages.JOB_STARTED_IN_BACKGROUND,
        isSuccess: true,
      };
    } catch (e) {
      logger.error(
        `${FLOWS.DATALAKE_TRASH} | DatalakeTrashController.restoreTrashObject | N/A | ${
          DatalakeUserMessages.BACKGROUND_JOB_FAILED
        }, data: ${JSON.stringify(data)}, userId: ${userId} | Error: `,
        e,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.BACKGROUND_JOB_FAILED);
    }
  }
}
