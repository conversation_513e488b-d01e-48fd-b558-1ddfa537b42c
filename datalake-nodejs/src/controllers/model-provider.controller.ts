/**
 * Copyright (c) 2025 LayerNext, Inc.
 * 
 * all rights reserved.
 * 
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 * 
 * Handle the request related to the model provider
 */


/**
 * @description This controller is used to get and set the API key for the model provider
 * <AUTHOR>
 * 
 */

import {authenticate} from "@loopback/authentication";
import {authorize} from "@loopback/authorization";
import {UserTypeDetailed} from "../settings/constants";
import {inject} from "@loopback/core";
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from "../authServices/custom-token.service";
import {get, HttpErrors, param, post, requestBody} from "@loopback/rest";
import {logger} from "../config";
import {ModelProviderService} from "../services/model-provider.service";

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.SUPER_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR]})
export class ModelProviderController {
  constructor(
    @inject(SecurityBindings.USER)
    public currentUserProfile: UserProfileDetailed,
    @inject('services.ModelProviderService')
    private modelProviderService: ModelProviderService,
  ) { }



  @get('api/model-provider/config')
  async getApiKeys(
    @param.query.string('provider') provider?: string,
  ) {
    try {
      const supportedProviders = ['openai'];
      if (provider && !supportedProviders.includes(provider)) {
        logger.error('Get Api Keys From Datalake | ModelProviderController.getApiKeys | N/A | Provider not supported: ', provider);
        throw new HttpErrors.NotAcceptable('Provider not supported');
      }

      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error('Get Api Keys From Datalake | ModelProviderController.getApiKeys | N/A | Couldn\'t find teamId for user: ', this.currentUserProfile);
        throw new HttpErrors.NotAcceptable('Team does not exist');
      }
      const userId = this.currentUserProfile?.id;
      if (!userId) {
        logger.error('Get Api Keys From Datalake | ModelProviderController.getApiKeys | N/A | Couldn\'t find userId for user: ', this.currentUserProfile);
        throw new HttpErrors.NotAcceptable('User does not exist');
      }
      const apiKeys = await this.modelProviderService.getApiKeys(provider, teamId);
      return apiKeys;
    } catch (error) {
      throw new HttpErrors.InternalServerError(error.message);
    }
  }

  @post('api/model-provider/config')
  async createApiKey(@requestBody() data: {apiUrl?: string, apiKey: string, provider: string}) {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error('Create Api Key From Datalake | ModelProviderController.createApiKey | N/A | Couldn\'t find teamId for user: ', this.currentUserProfile);
        throw new HttpErrors.NotAcceptable('Team does not exist');
      }
      const userId = this.currentUserProfile?.id;
      const userName = this.currentUserProfile?.name;
      if (!userId || !userName) {
        logger.error('Create Api Key From Datalake | ModelProviderController.createApiKey | N/A | Couldn\'t find userId for user: ', this.currentUserProfile);
        throw new HttpErrors.NotAcceptable('User does not exist');
      }
      if (!data.apiUrl) {
        data.apiUrl = '';
      }
      const validateApiKey = await this.modelProviderService.validateApiKey(data);
      if (!validateApiKey.isSuccess) {
        return {
          isSuccess: false,
          message: validateApiKey.message,
        }
      }
      const apiKey = await this.modelProviderService.createApiKey(data, userId, userName, teamId);
      return apiKey;
    } catch (error) {
      throw new HttpErrors.InternalServerError(error.message);
    }
  }



}
