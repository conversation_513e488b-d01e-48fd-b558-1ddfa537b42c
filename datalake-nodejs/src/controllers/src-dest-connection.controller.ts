/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta-data model
 */

/**
 * @class SrcDestConnectionController
 * Handle the request related to the SrcDestConnectionController
 * @description This controller use for Handle the request related to the SrcDestConnections
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {HttpErrors, get, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {ConnectionSourceCredentials, ConnectionSourceType, SelectionConfiguration} from '../models/source.model';
import {SRC_DEST_CONNECTION_SERVICE, SrcDestConnectionService} from '../services/src-dest-connection.service';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

/**
 * A simple controller to bounce back http requests
 */
@authenticate('jwt')
export class SrcDestConnectionController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(SRC_DEST_CONNECTION_SERVICE)
    private srcDestConnectionService: SrcDestConnectionService,
  ) {}

  /**
   * create connection using user input
   * @param body - name, type, credentials, selectionConfiguration
   * @returns create connection response
   */
  @post('/api/connection/source/create')
  async createConnection(
    @requestBody()
    body: {
      name: string;
      type: ConnectionSourceType;
      credentials: ConnectionSourceCredentials;
      selectionConfiguration: SelectionConfiguration;
    },
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Add new data source connection | DataSourceController.addDataSource | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    logger.info('create connection | SrcDestConnectionController.createConnection | N/A | body: ', body);

    return this.srcDestConnectionService.createConnection(
      body.name,
      body.type,
      body.credentials,
      body.selectionConfiguration,
      currentUserProfile,
    );
  }

  /**
   * get source connection list
   * @param body - searchKey, type, pageIndex, pageSize
   * @returns source connection list
   */
  @get('/api/connection/source/list')
  async getsourceConnectionList(
    @param.query.string('searchKey') searchKey?: string,
    @param.query.string('type') type?: ConnectionSourceType,
    @param.query.string('pageIndex') pageIndex?: number,
    @param.query.string('pageSize') pageSize?: number,
  ) {
    logger.debug(
      `get source connection list | SrcDestConnectionController.getsourceConnectionList | N/A | searchKey: ${searchKey}, type: ${type}, pageIndex: ${pageIndex}, pageSize: ${pageSize}`,
    );

    return this.srcDestConnectionService.getsourceConnectionList(searchKey, type, pageIndex, pageSize);
  }

  /**
   * get source connection details
   * @param body - connectionId
   * @returns source connection list
   */
  @get('/api/connection/source/{connectionId}/details')
  async getsourceConnectionDetails(@param.path.string('connectionId') connectionId?: string) {
    logger.debug(
      `get source connection details | SrcDestConnectionController.getsourceConnectionDetails | N/A | connectionId: ${connectionId}, get connection details failed`,
    );

    return this.srcDestConnectionService.getsourceConnectionDetails(connectionId);
  }

  @get('/api/connection/history/test')
  async historyTest() {
    return this.srcDestConnectionService.syncConnectionJobList();
  }

  /**
   * get source connection list
   * @param body - searchKey, type, pageIndex, pageSize
   * @returns source connection list
   */
  @get('/api/connection/source/{connectionId}/history')
  async getsourceConnectionHistory(
    @param.path.string('connectionId') connectionId?: string,
    @param.query.number('pageIndex') pageIndex?: number,
    @param.query.number('pageSize') pageSize?: number,
  ) {
    logger.debug(
      `get source connection history | SrcDestConnectionController.getsourceConnectionHistory | N/A | pageIndex: ${pageIndex}, pageSize: ${pageSize}`,
    );

    return this.srcDestConnectionService.getsourceConnectionHistory(connectionId, pageIndex, pageSize);
  }

  @get('api/generate/dictionary/data')
  async generateDataDictionary() {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Get data from connected db | DataSourceController.getData | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    return this.srcDestConnectionService.generateDataDictionary();
  }
}
