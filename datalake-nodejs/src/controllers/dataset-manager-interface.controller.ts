/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for APIs between the dataset manager and the datalake
 */

/**
 * @class DatasetManagerInterfaceController
 * Handle the request related to the internal Dataset Manager Application
 * @description APIs between the dataset manager and the datalake
 * <AUTHOR> isuru, chathushka
 */

import {inject} from '@loopback/core';
import {HttpErrors, get, param, post, requestBody} from '@loopback/rest';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {AugmentedInfoWithMetaList, ContentType, DatasetSplitCount, DatasetUpdateFormat} from '../models';
import {META_DATA_SERVICE, MetaDataService} from '../services';
import {
  DATASET_MANAGER_INTERFACE_SERVICE,
  DatasetManagerInterfaceService,
} from '../services/dataset-manager-interface.service';
import {JOB_SERVICE, JobService} from '../services/job.service';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

const internalBaseUrl = '/internal/datasetInterface';

export class DatasetManagerInterfaceController {
  constructor(
    @inject(DATASET_MANAGER_INTERFACE_SERVICE) private datasetManagerInterfaceService: DatasetManagerInterfaceService,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
    @inject(JOB_SERVICE) private jobService: JobService,
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
  ) {}

  /**
   * Create new dataset
   * update relavent frames with the dataset group and version data
   * @param selectionId string
   * @returns
   */
  @post(internalBaseUrl + '/create/newDataset')
  async createNewDataset(
    //@param.path.string('selectionId') selectionId: string,
    @param.query.string('userInfo') userInfo: string,
    @requestBody() reqBody: DatasetUpdateFormat,
  ) {
    let currentUserProfile: UserProfileDetailed = JSON.parse(userInfo)
    return await this.datasetManagerInterfaceService.createNewDataset(reqBody, currentUserProfile);
  }

  /**
   * delete dataset version details from metadata
   * @param versionId {string} id of the version
   * @returns {isSuccess: boolean}
   */
  @post('/internal/datasetInterface/version/{versionId}/delete')
  async deleteDatasetVersion(
    @param.path.string('versionId') versionId: string,
    @param.query.boolean('deleteDatasetGroup') deleteDatasetGroup: boolean,
    @param.query.string('datasetGroupId') datasetGroupId: string,
  ) {
    return await this.datasetManagerInterfaceService.deleteDatasetVersion(
      versionId,
      deleteDatasetGroup,
      datasetGroupId,
    );
  }

  /**
   * Get image and text file data from metadata
   * @param selectionId string
   * @returns
   */
  @get(internalBaseUrl + '/getFiles/{versionId}/datasetVersion')
  async getDatasetVersionFiles(
    @param.path.string('versionId') versionId: string,
    @param.query.string('exportFormat') exportFormat: string,
    @param.query.boolean('isTextFileRquired') isTextFileRquired: boolean,
    @param.query.number('pageSize') pageSize: number,
    @param.query.number('skip') skip: number,
    //  @requestBody() reqBody: DatasetUpdateFormat
  ) {
    logger.debug(
      `Fetch dataset version data | DatasetManagerInterfaceController.getDatasetVersionFiles | N/A | Fetch version data request `,
    );

    return await this.datasetManagerInterfaceService.getDatasetVersionFiles(
      versionId,
      isTextFileRquired,
      exportFormat,
      pageSize,
      skip,
    );
  }

  /**
   * Use to get total frame count of a datasetVersion
   * @param datasetVersionId string
   * @returns
   */
  @get(internalBaseUrl + '/datasetVersion/{datasetVersionId}/frameCount')
  async frameCountDatasetVersion(@param.path.string('datasetVersionId') datasetVersionId: string) {
    return await this.metaDataService.frameCountDatasetVersion(datasetVersionId);
  }

  /**
   * use to check if new frame is added to the dataset version
   * @param datasetVersionId string
   * @returns
   */
  @get(internalBaseUrl + '/datasetVersion/{datasetVersionId}/isNewFrameAdded')
  async isNewFrameAdded(@param.path.string('datasetVersionId') datasetVersionId: string) {
    return await this.datasetManagerInterfaceService.isNewFrameAddedToDatasetVersion(datasetVersionId);
  }

  /**
   * Use to auto split frames of given datasetVersion into training, validation & testing sets
   * @param datasetVersionId string
   * @returns
   */
  @post(internalBaseUrl + '/splitDatasetVersion/{datasetVersionId}/random')
  async randomSplitDatasetVersion(
    @param.path.string('datasetVersionId') datasetVersionId: string,
    @requestBody() splitData: DatasetSplitCount,
  ) {
    return await this.datasetManagerInterfaceService.randomSplitDatasetVersion(datasetVersionId, splitData);
  }

  /**
   * Use to add or remove the dataset, datasetversion tags from meta frames
   * @param reqBody
   * datasetGroupId: string, Dataset Manager side id of the dataset group
   * datasetName: string, Dataset Manager side name of the dataset group
   * datasetVersionId: string, Dataset Manager side id of the dataset version
   * selectionId?: string, datalake side id for a particular selection to add frames to dataset version
   * @returns
   */
  @post(internalBaseUrl + '/datasetVersion/updateFrames')
  async addOrRemoveFramesToDatasetVersion(
    @requestBody()
    reqBody: {
      datasetGroupId: string;
      datasetName: string;
      datasetVersionId: string;
      selectionId?: string;
      removedCollectionIdList?: string[];
      addedCollectionIdList?: string[];
      createNewVersion: boolean;
      baseVersionId: string | undefined;
    },
    @param.query.string('userInfo') userInfo: string,
  ) {
    let currentUserProfile = userInfo ? JSON.parse(userInfo) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `add or remove frames | DatasetManagerInterfaceController.addOrRemoveFramesToDatasetVersion | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return this.datasetManagerInterfaceService.addOrRemoveFramesToDatasetVersion(
      reqBody.datasetGroupId,
      reqBody.datasetName,
      reqBody.datasetVersionId,
      reqBody.createNewVersion,
      reqBody.baseVersionId,
      currentUserProfile,
      reqBody.selectionId,
      reqBody.removedCollectionIdList,
      reqBody.addedCollectionIdList,
    );
  }

  /**
   * Use to create new dataset version based on existing dataset version
   * @param reqBody
   * datasetGroupId: string, Dataset Manager side id of the dataset group
   * baseDatasetVersionId: string, Dataset Manager side id of the base dataset version
   * newDatasetVersionId: string, Dataset Manager side id of the new dataset version
   * @returns
   */
  @post(internalBaseUrl + '/datasetVersion/newVersion')
  async createNewDatasetVersion(
    @requestBody()
    reqBody: {
      datasetGroupId: string;
      baseDatasetVersionId: string;
      newDatasetVersionId: string;
      datasetGroupName: string;
    },
  ) {
    return this.datasetManagerInterfaceService.createNewDatasetVersion(
      reqBody.datasetGroupId,
      reqBody.baseDatasetVersionId,
      reqBody.newDatasetVersionId,
      reqBody.datasetGroupName,
    );
  }

  /**
   * Use to get list of all collections with selected counts for a given selectionId or datasetVersion id
   * @param selectionId string datalake side selection id
   * @param datasetVersionId string dataset manager side version id
   * @returns
   */
  @get(internalBaseUrl + '/datasetVersion/frameCollections/selection')
  async getFrameCollectionsWithSelectionDetails(
    @param.query.boolean('isSelectedOnly') isSelectedOnly: boolean,
    @param.query.string('selectionId') selectionId: string,
    @param.query.string('datasetVersionId') datasetVersionId: string,
    @param.header.string('Authorization') authorization: string,
    @param.header.string('UserInfo') userInfo: string,
  ) {
    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    let teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `get frame collection | DatasetManagerInterfaceController.getFrameCollectionsWithSelectionDetails | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let currentUserProfile: UserProfileDetailed = userInfo ? JSON.parse(userInfo) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `get frame collection | DatasetManagerInterfaceController.getFrameCollectionsWithSelectionDetails | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return await this.metaDataService.getFrameCollectionsWithSelectionDetails(
      isSelectedOnly,
      currentUserProfile,
      selectionId,
      datasetVersionId,
    );
  }

  /**
   * use to find metaupdates operation list of the selected frames for a particular dataset version
   * @param reqBody
   * datasetVersionId?: string, id of the dataset version
   * selectionId?: string, id of the selection object
   * removedCollectionIdList?: string[], ids of collections which are removed from the dataset version
   * addedCollectionIdList?: string[], ids of collection which are added to the dataset version
   * @returns
   */
  @post(internalBaseUrl + '/operationList')
  async findOperationList(
    @requestBody()
    reqBody: {
      datasetVersionId?: string;
      selectionId?: string;
      removedCollectionIdList?: string[];
      addedCollectionIdList?: string[];
    },
    @param.query.string('userInfo') userInfo: string,
  ) {
    let currentUserProfile = userInfo ? JSON.parse(userInfo) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `find operation list | DatasetManagerInterfaceController.findOperationList | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    console.log('currentUserProfile', currentUserProfile);
    return this.datasetManagerInterfaceService.findOperationList(
      currentUserProfile,
      reqBody.datasetVersionId,
      reqBody.selectionId,
      reqBody.removedCollectionIdList,
      reqBody.addedCollectionIdList,
    );
  }

  /**
   * Use for get the job status
   * @param authorization basic auth key
   * @param jobId {string} id of the job
   * @returns status of the job
   */
  @get(internalBaseUrl + '/jobs/{jobId}/getStatus')
  async getJobStatus(@param.path.string('jobId') jobId: string) {
    let job = await this.jobService.getJobStatus(jobId);

    return job;
  }

  /**
   * call when augmentation is removed from dataset version
   * Use to remove augmentation from dataset version
   */
  @post(internalBaseUrl + '/removeAugmentationFromDatasetVersion')
  async removeAugmentationFromDatasetVersion(
    @requestBody()
    reqBody: {
      augmentationId: string;
      augmentationPropertyId: string;
      datasetVersionId: string;
      datasetGroupId: string;
    },
  ) {
    return this.datasetManagerInterfaceService.removeAugmentationFromDatasetVersion(
      reqBody.augmentationId,
      reqBody.augmentationPropertyId,
      reqBody.datasetVersionId,
      reqBody.datasetGroupId,
    );
  }

  /**
   * Call when add/edit augmentation type to a dataset version
   * Use to set metadata for augmented images
   * add augmentation info to metadata
   * tag augmented images for dataset version
   */
  @post(internalBaseUrl + '/addAugmentationToDatasetVersion')
  async addAugmentationToDatasetVersion(
    @requestBody() reqBody: {augmentedInfoWithMetaList: AugmentedInfoWithMetaList},
  ) {
    return this.datasetManagerInterfaceService.addAugmentationToDatasetVersion(reqBody.augmentedInfoWithMetaList);
  }

  /**
   * Get the download url (pre-signed url) of given object key
   * @param authorization basic auth key
   * @param objectKey object key
   */
  @get('internal/downloadUrl')
  async getDownloadUrl(@param.query.string('file_key') objectKey: string) {
    let downloadUrl = await this.metaDataService.getDownloadableUrl(objectKey);

    return {
      url: downloadUrl,
      isSuccess: true,
    };
  }

  /**
   * Use for create collection head
   * @param authorization {string} basic authentication
   * @param body {object} collection details
   * @returns {success: boolean}
   */
  @post('/api/metadata/collection/create')
  async createCollectionHead(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('userInfo') userInfo: string,
    @requestBody()
    body: {
      collectionName: string;
      objectType: ContentType;
      customMetaObject: {[k: string]: any};
      teamId: string;
    },
  ) {
    const authorizationKeySecret = authorization;

    let authorizeDetails = await this.basicAuthService.basicAuthentication(authorizationKeySecret);

    let teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Create collection head | DatalakeClientInterfaceController.createCollectionHead | N/A | Couldn't find the team for: `,
        authorizeDetails,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    //validate objectType
    if (
      ![ContentType.IMAGE_COLLECTION, ContentType.VIDEO_COLLECTION, ContentType.OTHER_COLLECTION].includes(
        body.objectType,
      )
    ) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    let currentUserProfile: UserProfileDetailed = userInfo ? JSON.parse(userInfo) : undefined;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `Create collection head | DatalakeClientInterfaceController.createCollectionHead | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    return await this.metaDataService.createCollectionHead(
      body.collectionName,
      body.objectType,
      currentUserProfile,
      body.customMetaObject,
    );
  }
}
