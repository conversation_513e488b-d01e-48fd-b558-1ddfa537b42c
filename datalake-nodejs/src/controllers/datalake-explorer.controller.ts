/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the meta-data model
 */

/**
 * @class DatalakeExplorerController
 * Handle the request related to the DatalakeExplorer Controller
 * @description This controller use for Handle the request related to the DatalakeExplorer controller eg: get object list to explorer view
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, HttpErrors, param, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {
  ACCESS_CONTROL_BODY,
  ContentType,
  DATALAKE_SELECTION_BODY,
  DatalakeSelectionRequest,
  DatalakeSelectionRequestForAnalytics,
  Explore_API_TYPE,
  ExploreObjectListRequest,
  ExplorerCollectionViewRequest,
  ExplorerDefaultViewRequest,
  ExplorerFilterV2,
  ExploreSortBy,
  ExploreSortOrder,
  GraphDataAvailability,
  InputField,
  MetaData,
  MetaWithMetaUpdatesFilter,
  OBJECT_STATUS,
  SortObject,
  SortOrder,
  TagsAndMetaDataConfig,
  UserSelectionRequest,
} from '../models';
import {DatalakeSelectionRepository, MetaDataRepository} from '../repositories';
import {
  DATALAKE_EXPLORER_SERVICE,
  DATALAKE_SELECTION_SERVICE,
  DATALAKE_TRASH_SERVICE,
  DatalakeExplorerService,
  DatalakeSelectionService,
  DatalakeTrashService,
  META_DATA_SERVICE,
  META_FIELD_PROPAGATOR_SERVICE,
  MetaDataService,
  MetaFieldPropagatorService,
  SEARCH_QUERY_BUILDER_SERVICE,
  SearchQueryBuilderService,
  SYSTEM_STATS_SERVICE,
  SystemStatsService,
} from '../services';
import {EMBEDDINGS_SERVICE, EmbeddingsService} from '../services/embeddings.service';
import {STATS_CALCULATION_SERVICE, StatsCalculationService} from '../services/stats-calculation.service';
import {EMBEDDING_COLLECTION, FLOWS, SIMILARITY_SCORE_THRESHOLD, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
export class DatalakeExplorerController {
  constructor(
    @inject(SEARCH_QUERY_BUILDER_SERVICE) private searchQueryBuilderService: SearchQueryBuilderService,
    @inject(DATALAKE_EXPLORER_SERVICE) private datalakeExplorerService: DatalakeExplorerService,
    @inject(DATALAKE_TRASH_SERVICE) private datalakeTrashService: DatalakeTrashService,
    @inject(SYSTEM_STATS_SERVICE) private systemStatsService: SystemStatsService,
    @repository(MetaDataRepository) public metaDataRepository: MetaDataRepository,
    @repository(DatalakeSelectionRepository)
    private datalakeSelectionRepository: DatalakeSelectionRepository,
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(META_FIELD_PROPAGATOR_SERVICE)
    public metaFieldPropagatorService: MetaFieldPropagatorService,
    @inject(META_DATA_SERVICE) private metaDataService: MetaDataService,
    @inject(STATS_CALCULATION_SERVICE)
    private statsCalculationService: StatsCalculationService,
    @inject(EMBEDDINGS_SERVICE)
    private embeddingsService: EmbeddingsService,
    @inject(DATALAKE_SELECTION_SERVICE) private datalakeSelectionService: DatalakeSelectionService,
  ) {}

  /**
   * Use to get object list for datalake explorer tab initial view
   * @param filterObj {ExplorerDefaultViewRequest} object list filtering options
   * contentType {ContentType} type of which objects should include in response
   * pageIndex {number} page number [start from 0]
   * pageSize {number} page size [default 4]
   * @returns ExplorerDefaultViewResponse
   */
  @post('/api/explorer/objects/list')
  async getObjectListToExplorer(@requestBody() filterObj: ExploreObjectListRequest) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getObjectListToExplorer | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    let pageSize = filterObj.pageSize ? filterObj.pageSize : 4;
    let referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    let sortObj: SortObject = {
      sortByField: filterObj?.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj?.sortBy?.sortOrder,
    };
    let query: object = {};
    let embeddingSelection = filterObj.embeddingSelection ? filterObj.embeddingSelection : undefined;

    let rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    if (referenceImage) {
      sortObj.sortByField = ExploreSortBy.SIMILARITY_SCORE;
      sortObj.sortOrder = SortOrder.DESC;
    }

    let response = await this.searchQueryBuilderService.getObjectListToExplorer(
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      pageIndex,
      pageSize,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.LIST,
      undefined,
      sortObj.sortOrder,
      embeddingSelection,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getObjectListToExplorer | N/A | Response Time = ${responseTime}`,
    );

    return {itemList: response.itemList};
  }

  /**
   * Use to get object count for datalake explorer tab initial view
   * @param filterObj {ExplorerDefaultViewRequest} object count filtering options
   * contentType {ContentType} type of which objects should include in response
   * pageIndex {number} page number [strart from 0]
   * pageSize {number} page size [default 4]
   * @returns ExplorerDefaultViewResponse
   */
  @post('/api/explorer/objects/count')
  async getObjectCountToExplorer(@requestBody() filterObj: ExploreObjectListRequest) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getObjectCountToExplorer | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    let pageSize = filterObj.pageSize ? filterObj.pageSize : 4;
    let referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    let sortObj: SortObject = {
      sortByField: filterObj?.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj?.sortBy?.sortOrder,
    };

    let query: object = {};
    let embeddingSelection = filterObj.embeddingSelection ? filterObj.embeddingSelection : undefined;

    let rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    if (rawQuery) {
      query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    let response = await this.searchQueryBuilderService.getObjectListToExplorer(
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      pageIndex,
      pageSize,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.COUNT,
      undefined,
      sortObj.sortOrder,
      embeddingSelection,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getObjectListToExplorer | N/A | Response Time = ${responseTime}`,
    );

    let count = response.count;
    if (referenceImage) {
      count += 1;
    }

    return {count: count};
  }

  /**
   * Get explorer detail tab data
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer detail tab data
   */
  @post('/api/explorer/objects/details')
  async getDetailTab(
    @param.header.number('timeZoneOffset', {required: true}) timeOffset: number,
    @requestBody(DATALAKE_SELECTION_BODY) filterObj: DatalakeSelectionRequest,
  ) {
    let currentUserProfile = this.currentUserProfile;
    logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getDetailTab`);

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getDetailTab | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId: string = currentUserProfile.teamId;

    if (filterObj.referenceImage) {
      filterObj.modelName = EMBEDDING_COLLECTION;
      filterObj.scoreThreshold = SIMILARITY_SCORE_THRESHOLD;
    }

    if (filterObj.objectStatus && filterObj.objectStatus == OBJECT_STATUS.TRASHED) {
      return this.datalakeTrashService.getTrashItemCounts(filterObj, currentUserProfile);
    } else {
      /**
       * Use to get explorer detail tab data
       */
      return this.datalakeExplorerService.getExplorerDetailTab(filterObj, currentUserProfile, timeOffset);
    }
  }

  /**
   * Get explorer tag list
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer tag list
   */
  @post('/api/explorer/objects/tags')
  async getTags(
    @param.header.number('timeZoneOffset', {required: true}) timeOffset: number,
    @requestBody(DATALAKE_SELECTION_BODY) filterObj: DatalakeSelectionRequest,
  ) {
    let currentUserProfile = this.currentUserProfile;
    logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getTags`);

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getTags | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId: string | undefined = currentUserProfile.teamId;

    let config: TagsAndMetaDataConfig = {
      allMetaData: false,
      isTagList: true,
    };

    /**
     * Use to getTags
     */
    return this.datalakeExplorerService.getTagsOrMetaData(filterObj, currentUserProfile, timeOffset, config);
  }

  /**
   * Get explorer meta data
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer meta data
   */
  @post('/api/explorer/objects/metaData')
  async getMetaData(
    @param.header.number('timeZoneOffset', {required: true}) timeOffset: number,
    @requestBody(DATALAKE_SELECTION_BODY) filterObj: DatalakeSelectionRequest,
  ) {
    let currentUserProfile = this.currentUserProfile;
    logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaData`);

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaData | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId: string | undefined = currentUserProfile.teamId;

    let config: TagsAndMetaDataConfig = {
      allMetaData: !!filterObj.allMetaData,
      isTagList: false,
    };

    /**
     * Use to get meta data
     */
    return this.datalakeExplorerService.getTagsOrMetaData(filterObj, currentUserProfile, timeOffset, config);
  }

  /**
   * Get explorer analytics data
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer analytics data
   */
  @post('/api/explorer/objects/analytics/models')
  async getAnalyticsData(@requestBody(DATALAKE_SELECTION_BODY) filterObj: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;
    logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsData`);

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsData | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    let teamId: string | undefined = currentUserProfile.teamId;
    /**
     * Use to get analytics detail tab data
     */
    return this.datalakeExplorerService.getAnalyticsData(filterObj, currentUserProfile);
  }

  /**
   * Object remove from collection
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns job id
   */
  @post('/api/explorer/objects/remove')
  async removeObjects(@requestBody(DATALAKE_SELECTION_BODY) filterObj: DatalakeSelectionRequest) {
    let teamId: string | undefined = this.currentUserProfile.teamId;
    let userId: string | undefined = this.currentUserProfile?.id;
    let userName: string | undefined = this.currentUserProfile?.name;

    logger.debug(`${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeObjects`);

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeObjects | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    const {selectionTag} = await this.datalakeSelectionService.createProjectSelectionTag(
      filterObj,
      this.currentUserProfile,
    );
    /**
     * Remove object from a collection
     */
    return this.metaDataService.removeObjects(filterObj, teamId, selectionTag ?? '', this.currentUserProfile);
  }

  // /**
  //  * Get explorer analytics data
  //  * @param filterObj {DatalakeSelectionRequest} filter for query
  //  * @returns explorer analytics data
  //  */
  // @post('/api/explorer/objects/analytics/metadataLabelwise')
  // async getAnalyticsMetadataLabelwise(@requestBody() filterObj: DatalakeSelectionRequest) {
  //   let teamId: string | undefined = this.currentUserProfile.teamId;
  //   logger.debug(
  //     `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsMetadataLabelwise | N/A | request`,
  //   );

  //   if (!teamId) {
  //     logger.error(
  //       `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsMetadataLabelwise | N/A | Couldn't find the team for: ${this.currentUserProfile}`,
  //     );
  //     throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
  //   }

  //   /**
  //    * Use to get analytics detail tab data
  //    */
  //   return this.statsCalculationService.getMetaFieldAnalyticsLabelwiseForFrontendChart(filterObj, teamId);
  // }

  /**
   * Get explorer analytics data chart headers
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer analytics data
   */
  @post('/api/explorer/objects/analytics/metadataLabelwise/head')
  async getAnalyticsMetadataLabelwiseLabelHeaders(@requestBody() filterObj: DatalakeSelectionRequestForAnalytics) {
    logger.debug(
      `Datalake analytics labelwise metadata | DatalakeExplorerController.getAnalyticsMetadataLabelwiseLabelHeaders | N/A | request`,
    );
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsMetadataLabelwiseLabelHeaders | N/A | Couldn't find the team for: ${this.currentUserProfile}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    //Use to get analytics detail tab data headers
    return await this.statsCalculationService.getLabelWiseAnalyticsLabelHeadersForFrontend(
      filterObj,
      currentUserProfile,
    );
  }

  /**
   * Get explorer analytics data chart headers
   * @param filterObj {DatalakeSelectionRequest} filter for query
   * @returns explorer analytics data
   */
  @post('/api/explorer/objects/analytics/metadataLabelwise/label')
  async getAnalyticsMetadataOfLabel(@requestBody() filterObj: DatalakeSelectionRequestForAnalytics) {
    let currentUserProfile = this.currentUserProfile;
    logger.debug(
      `Datalake analytics labelwise metadata | DatalakeExplorerController.getAnalyticsMetadataLabelwiseLabelHeaders | N/A | request`,
    );

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getAnalyticsMetadataLabelwiseLabelHeaders | N/A | Couldn't find the team for: ${this.currentUserProfile}`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId: string = currentUserProfile.teamId;

    // get label's metadata analytics
    return await this.statsCalculationService.getOneLabelAnalyticsMetadataForFrontend(filterObj, currentUserProfile);
  }

  /**
   * Use to get stat tab details
   * @param filter {DatalakeSelectionRequest} filter for query
   * @returns stat tab details
   */
  @post('api/explorer/objects/stats')
  async frame(@requestBody() filter: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    if (filter.referenceImage) {
      filter.modelName = EMBEDDING_COLLECTION;
      filter.scoreThreshold = SIMILARITY_SCORE_THRESHOLD;
    }

    let frameLevelStatDistribution = await this.statsCalculationService.getFrameLevelStats(filter, currentUserProfile);

    let metaDataDistribution = await this.statsCalculationService.getMetaFieldStatsForFrontendChart(
      filter,
      currentUserProfile,
    );

    return {
      frameChartData: {
        chartTitle: 'frameDistribution',
        frameCount: frameLevelStatDistribution.frameStats.frameCountSum,
        maxDataValue: frameLevelStatDistribution.frameStats.maxDataValue,
        chartData: [
          {
            label: 'Machine Annotated',
            value: frameLevelStatDistribution.frameStats.machineAnnotatedSum,
            color: '#78B7DE',
          },
          {
            label: 'Human Annotated',
            value: frameLevelStatDistribution.frameStats.verifiedSum,
            color: '#8FD790',
          },
          {
            label: 'Raw Data',
            value: frameLevelStatDistribution.frameStats.rawSum,
            color: '#B283DA',
          },
        ],
      },
      labelChartData: {
        chartTitle: 'labelDistribution',
        maxDataValue: frameLevelStatDistribution.labelStats.maxDataValue,
        labelCount: frameLevelStatDistribution.labelStats.labelCount,
        annotationCount: frameLevelStatDistribution.labelStats.annotationCount,
        chartData: frameLevelStatDistribution.labelStats.labelList.sort((a, b) => b.value - a.value),
      },
      metaDataChartData: metaDataDistribution,
    };
  }

  /**
   * Use to get object list for datalake explorer collection view
   * @param filterObj {ExplorerCollectionViewRequest} object list filtering options
   * collectionId {string} id of the collection which is going to view
   * pageIndex {number} page number [start from 0]
   * pageSize {number} page size [default 16]
   * @returns ExplorerCollectionViewResponse
   */
  @post('/api/explorer/{collectionId}/objects/list')
  async getObjectListOfCollection(
    @param.path.string('collectionId') collectionId: string,
    @requestBody() filterObj: ExploreObjectListRequest,
  ) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    let pageSize = filterObj.pageSize ? filterObj.pageSize : 16;
    let referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    let sortObj: SortObject = {
      sortByField: filterObj?.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj?.sortBy?.sortOrder,
    };

    let rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    let embeddingSelection = filterObj.embeddingSelection ? filterObj.embeddingSelection : undefined;
    let query: object = {};
    if (rawQuery) {
      if (referenceImage) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId, 'metaData');
      else query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);
    }

    if (referenceImage) {
      sortObj.sortByField = ExploreSortBy.SIMILARITY_SCORE;
      sortObj.sortOrder = SortOrder.DESC;
    }

    let response = await this.searchQueryBuilderService.getObjectListOfCollection(
      collectionId,
      pageIndex,
      pageSize,
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.LIST,
      undefined,
      sortObj.sortOrder,
      embeddingSelection,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Response Time = ${responseTime}`,
    );

    return {itemList: response.itemList};
  }

  /**
   * Use to get object count for datalake explorer collection view
   * @param filterObj {ExplorerCollectionViewRequest} object count filtering options
   * collectionId {string} id of the collection which is going to view
   * pageIndex {number} page number [start from 0]
   * pageSize {number} page size [default 16]
   * @returns ExplorerCollectionViewResponse
   */
  @post('/api/explorer/{collectionId}/objects/count')
  async getObjectCountOfCollection(
    @param.path.string('collectionId') collectionId: string,
    @requestBody() filterObj: ExploreObjectListRequest,
  ) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectCountOfCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let pageIndex = filterObj.pageIndex ? filterObj.pageIndex : 0;
    let pageSize = filterObj.pageSize ? filterObj.pageSize : 16;

    let rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj?.filterData,
      filterObj.query,
    );

    let embeddingSelection = filterObj.embeddingSelection ? filterObj.embeddingSelection : undefined;

    let query: object = {};
    if (rawQuery) query = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);

    let referenceImage = filterObj.referenceImage ? filterObj.referenceImage : undefined;
    let sortObj: SortObject = {
      sortByField: filterObj?.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterObj?.sortBy?.sortOrder,
    };
    let response = await this.searchQueryBuilderService.getObjectListOfCollection(
      collectionId,
      pageIndex,
      pageSize,
      filterObj?.filterData ?? {},
      filterObj.contentType ?? ContentType.ALL,
      currentUserProfile,
      query,
      referenceImage,
      EMBEDDING_COLLECTION,
      SIMILARITY_SCORE_THRESHOLD,
      sortObj.sortByField,
      Explore_API_TYPE.COUNT,
      undefined,
      sortObj.sortOrder,
      embeddingSelection,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Response Time = ${responseTime}`,
    );

    let count = response.count;
    if (referenceImage) {
      count += 1;
    }

    return {count: count};
  }

  /**
   * Use to get initial info of metaData object for datalake explorer detail view
   * @param metaDataId mongodb id of meta data object
   * @returns object with details of meta object (response is vary according to meta object type)
   */
  @post('/api/explorer/detailview/info/{metaDataId}')
  async getInfoToDetailView(
    @param.path.string('metaDataId') metaDataId: string,
    @requestBody() filterObj: DatalakeSelectionRequest,
    // @param.query.string('navigatedCollectionId') navigatedCollectionId: string,
    // @param.query.string('filterData') filterData?: string,
    // @param.query.string('query') query?: string,
    @param.header.number('timeZoneOffset') timeZoneOffset?: number,
    // @param.query.number('contentType') contentType?: ContentType,
    // @param.query.number('sortByField') sortByField?: ExploreSortBy,
    // @param.query.string('sortOrder') sortOrder?: SortOrder,
    // @param.query.string('referenceImage') referenceImage?: string
  ) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getInfoToDetailView | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = this.currentUserProfile.teamId;

    let startTime = new Date().getTime();

    //handle 'undefined' stringified query param cases
    // if (filterObj.query === 'undefined') {
    //   query = undefined;
    // }
    // if (filterData === 'undefined') {
    //   filterData = undefined;
    // }

    let formattedQuery: {[k: string]: any} = {};
    let filter: ExplorerFilterV2 = filterObj.filterData || {};
    // if (filterObj.filterData) filter = JSON.parse(decodeURIComponent(filterObj.filterData));

    // let decodeQuery: string = filterObj.query ? JSON.parse(decodeURIComponent(filterObj.query)) : '';

    let rawQuery: string = await this.searchQueryBuilderService.combineFilterAndQuery(
      filterObj.filterData,
      filterObj.query,
    );

    if (rawQuery) formattedQuery = await this.searchQueryBuilderService.generateQuery(rawQuery, teamId);

    let res = await this.searchQueryBuilderService.getDetailViewInfo(
      metaDataId,
      currentUserProfile,
      filterObj.contentType ?? ContentType.ALL,
      timeZoneOffset,
      filterObj.collectionId,
      formattedQuery,
      filter,
      filterObj.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      filterObj.sortBy?.sortOrder,
      filterObj.referenceImage,
      filterObj.embeddingSelection,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getInfoToDetailView | N/A | Response Time = ${responseTime}`,
    );

    return res;
  }

  /**
   * Use to get annotation data in a frame range of a frame collection by user by clicking metaObject (opening player view)
   * @param filterOptions {MetaWithMetaUpdatesFilter}
   * frameCollectionId: id of collation user going to vie oin player
   * frameWindow start point and end point
   * contentType {ContentType} object type of user clicked metaObject
   * @returns  MetaWithMetaUpdates[]
   */
  @post('/api/explorer/fetch/metaupdates')
  async fetchMetaWithMetaUpdates(
    @requestBody() filterOptions: MetaWithMetaUpdatesFilter,
    @param.header.number('timeZoneOffset') timeZoneOffset?: number,
  ) {
    //logged user teamId
    let currentUserProfile = this.currentUserProfile;
    let userId = currentUserProfile.id ? currentUserProfile.id : '';
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.fetchMetaWithMetaUpdates | N/A |request from userId: ${userId} : Could not find the Team of the user`,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let sortObj: SortObject = {
      sortByField: filterOptions?.sortBy?.sortByField ?? ExploreSortBy.DATE_MODIFIED,
      sortOrder: filterOptions?.sortBy?.sortOrder,
    };

    if (filterOptions.referenceImage) {
      filterOptions.modelName = EMBEDDING_COLLECTION;
      filterOptions.scoreThreshold = SIMILARITY_SCORE_THRESHOLD;
      sortObj.sortByField = ExploreSortBy.SIMILARITY_SCORE;
      sortObj.sortOrder = SortOrder.DESC;
    }

    let res = await this.searchQueryBuilderService.getMetaWithMetaUpdates(
      filterOptions,
      currentUserProfile,
      sortObj,
      timeZoneOffset,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.fetchMetaWithMetaUpdates | N/A | Response Time = ${responseTime}`,
    );
    return res;
  }

  /**
   * Use to get Project List of a given metaData object
   * @param metaDataId mongodbId of metaData object
   * @returns MetaUpdateProjectInfo[]
   */
  @get('/api/explorer/{metaDataId}/project/list')
  async getMetaObjectProjectList(@param.path.string('metaDataId') metaDataId: string) {
    let startTime = new Date().getTime();

    let res = await this.searchQueryBuilderService.getProjectListOfMetaObject(metaDataId);

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaObjectProjectList | N/A | Response Time = ${responseTime}`,
    );

    return res;
  }

  /**
   * Use to total size of the files
   * @returns {object} totalSize
   */
  @get('/api/explorer/totalDataSize')
  async getTotalSize() {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    let systemData = await this.systemStatsService.getSystemData(teamId);

    let imageSize = systemData?.objectTypeWiseCounts?.images.size || 0;
    let videoSize = systemData?.objectTypeWiseCounts?.videos.size || 0;
    let otherSize = systemData?.objectTypeWiseCounts?.other.size || 0;
    let returnObj = {totalDataSize: imageSize + videoSize + otherSize};

    logger.info(`${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getTotalSize | N/A`);

    return returnObj;
  }

  /**
   * Use to get query options for suggestions
   * @returns
   */
  @get('/api/explorer/query/options')
  async getQueryOptions(@param.query.string('collectionId') collectionId: string) {
    //logged user teamId
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    logger.info(`${FLOWS.DATALAKE_EXPLORER_SERACH_QUERY} | DatalakeExplorerController.getQueryOptions | N/A`);

    let queryOptionList = await this.searchQueryBuilderService.getSearchQueryOptions(teamId, collectionId);

    // NOTE: if this formatting can be done in frontend, we can reduce response Byte size by 50%
    let queryOptionListFormatted = queryOptionList.map(elem => {
      return {
        isRoot: elem.isRoot,
        key: {
          parentKey: elem.key,
          value: elem.key,
        },
        operators: elem.operators.map(op => {
          return {
            parentKey: elem.key,
            value: op,
          };
        }),
        values: elem.values
          .filter(v => v != '' && v != null)
          .sort((a, b) => {
            if (typeof a === 'number' && typeof b === 'number') {
              return a - b;
            } else if (typeof a === 'number') {
              return -1;
            } else if (typeof b === 'number') {
              return 1;
            } else {
              return a > b ? 1 : -1;
            }
          })
          .map(val => {
            return {
              parentKey: elem.key,
              value: val.toString(),
            };
          }),
      };
    });

    //return returnObjFormatted
    return queryOptionListFormatted;
  }

  @post('/api/explorer/testAPI')
  async testAPI(@requestBody() data: any) {
    return; //await this.searchQueryBuilderService.getContentTypeFromQuery(data.query)
  }

  /**
   * Use to get the explorer analytics data
   * @param id {string} id of the object
   * @returns list of model run analytics for that object
   */
  @get('/api/explorer/{id}/analytics')
  async analytics(@param.path.string('id') id: string) {
    let returnObj: MetaData;
    try {
      returnObj = await this.metaDataRepository.findById(id);

      if (!returnObj.analytics || returnObj.analytics.length == 0) return [];

      let returnList = [];
      for (let analytic of returnObj.analytics) {
        returnList.push({
          modelName: analytic.operationId,
          ...analytic,
        });
      }
      return returnList;
    } catch (error) {
      logger.error(`get analytics failed for objectId: ${id} with error: ${error}`);

      throw new HttpErrors.NotFound(`get analytics failed for objectId: ${id} with error: ${error}`);
    }
  }

  /**
   * Use to get virtual collection list
   * @param contentType ContentType (1: Image, 2: Video)
   * @returns
   */
  @get('/api/explorer/{contentType}/vCollections')
  async getVirtualCollectionList(@param.path.number('contentType') contentType: ContentType) {
    //logged user teamId

    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    //let res = await this.searchQueryBuilderService.getProjectListOfMetaObject(metaDataId)
    let objectType = await this.metaDataRepository.getObjectType(true, contentType);

    if (
      objectType != ContentType.IMAGE_COLLECTION &&
      objectType != ContentType.VIDEO_COLLECTION &&
      objectType != ContentType.OTHER_COLLECTION
    ) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    return await this.datalakeExplorerService.getVirtualCollectionList(objectType, currentUserProfile);
  }

  /**
   * Use to get collectionId by using collection name and object type
   * @param name {string} collection name
   * @param objectType {ContentType}
   * @returns CollectionObject
   */
  @get('/api/explorer/{collectionName}/getCollectionId')
  async getCollectionIdByName(
    @param.path.string('collectionName') name: string,
    @param.query.number('objectType') objectType: ContentType,
  ) {
    //validate objectType
    if (
      ![
        ContentType.IMAGE_COLLECTION,
        ContentType.VIDEO_COLLECTION,
        ContentType.DATASET,
        ContentType.OTHER_COLLECTION,
      ].includes(objectType)
    ) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.INVALID_OBJECT_TYPE);
    }

    const result = await this.datalakeExplorerService.getCollectionIdByName(name, objectType, this.currentUserProfile);

    if (result == null) {
      throw new HttpErrors.NotFound(DatalakeUserMessages.INVALID_COLLECTION_NAME_OR_OBJECT_TYPE);
    }

    return result;
  }

  /**
   * Use to get collection name by using collection id
   * @param id {string} collection id
   * @returns collectionNameObject
   */
  @get('/api/explorer/{id}/getName')
  async getNameById(@param.path.string('id') id: string) {
    return await this.datalakeExplorerService.getNameById(id);
  }

  /**
   * Use to retrieve information of a object in datalake
   * @param id {string} id of the object
   * @returns
   */
  @get('/api/explorer/{id}/info')
  async getObjectInfo(@param.path.string('id') id: string) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaFields | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    return await this.datalakeExplorerService.getObjectInfo(id);
  }

  /**
   * to create meta fields
   * @param data InputField
   * @returns {isSuccess: boolean, message: string}
   */
  @post('/api/explorer/createMetaFields')
  async createMetaFields(@requestBody() data: InputField) {
    let teamId = this.currentUserProfile.teamId;
    let username = this.currentUserProfile.name;

    if (!username) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_USER_NOT_EXIST);
    }

    if (!teamId) {
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let response: {
      success: boolean;
      message: string;
      data: any;
    } = await this.metaFieldPropagatorService.createMetaFields(data, username, teamId);

    if (!response?.success) {
      throw new HttpErrors.NotAcceptable(response?.message);
    } else {
      return response.data;
    }
  }

  /**
   * to get meta fields list
   * @param searchKey search key : string
   * @returns metaFieldsObject list
   */
  @get('/api/explorer/getMetaFields/list')
  async getMetaFields(
    @param.query.number('pageIndex') pageIndex: number,
    @param.query.number('pageSize') pageSize: number,
    @param.query.string('searchKey') searchKey: string,
  ) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaFields | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let startTime = new Date().getTime();
    if (searchKey == null || searchKey == undefined) searchKey = '';
    let index = pageIndex ? pageIndex : 0;
    let size = pageSize ? pageSize : 20;

    let response = await this.metaFieldPropagatorService.getMetaFields(
      searchKey,
      teamId,
      Explore_API_TYPE.LIST,
      index,
      size,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getMetaFields | N/A | Response Time = ${responseTime}`,
    );

    return response;
  }

  /**
   * to get meta fields count
   * @param searchKey search key : string
   * @returns metaFieldsObject count
   */
  @get('/api/explorer/getMetaFields/count')
  async getMetaFieldsCount(@param.query.string('searchKey') searchKey: string) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getMetaFieldsCount | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let startTime = new Date().getTime();
    if (searchKey == null || searchKey == undefined) searchKey = '';

    let response = await this.metaFieldPropagatorService.getMetaFields(
      searchKey,
      teamId,
      Explore_API_TYPE.COUNT,
      undefined,
      undefined,
    );

    let endTime = new Date().getTime();
    let responseTime = endTime - startTime;

    logger.info(
      `${FLOWS.DATALAKE_EXPLORER_VIEW_DEFAULT} | DatalakeExplorerController.getMetaFieldsCount | N/A | Response Time = ${responseTime}`,
    );

    return response;
  }

  /**
   * Remove files from virtual collection
   * @param collectionId {string} id of virtual collection
   * @param data {selectionId: string} selection id
   * @returns {isSuccess: boolean, message: string}
   */
  @post('/api/remove/file/{collectionId}/vCollection')
  async removeFilesInVirtualCollection(
    @param.path.string('collectionId') collectionId: string,
    @requestBody()
    data: {
      selectionId: string;
    },
  ) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeFilesInVirtualCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    try {
      // validate selectionId
      let selectionObj = await this.datalakeSelectionRepository.findById(data.selectionId);
      if (selectionObj) {
        await this.metaDataService.removeFilesInVirtualCollection(collectionId, data.selectionId, currentUserProfile);
      }

      return {
        isSuccess: true,
      };
    } catch (error) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.removeFilesInVirtualCollection | N/A | Error: `,
        error,
      );
      return {
        isSuccess: false,
      };
    }
  }

  /**
   * Use to get filter options for datalake explorer filter view
   * @param filterObj {collectionId: string, contentType: ContentType}
   * @returns {labelOptions: LabelOption[], metadataOptions: MetadataOption[]}
   */
  @post('/api/explorer/filter/options')
  async getFilterOptions(@requestBody() filterObj: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;
    let startTime = new Date().getTime();

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.GET_OPTIONS} | DatalakeExplorerController.getFilterOptions | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let returnObject = await this.datalakeExplorerService.getFilterOptions(filterObj, currentUserProfile);

    let endTime = new Date().getTime();

    let responseTime = endTime - startTime;
    logger.info(
      `Getting filter options list | DatalakeExplorerController.getFilterOptions | N/A | Response Time = ${responseTime}`,
    );

    return returnObject;
  }

  /**
   * Use to get filter options for datalake metadata analytics filter in explorer sidebar
   * @param filterObj
   * @returns {labelOptions: LabelOption[], metadataOptions: MetadataOption[]}
   */
  @post('/api/explorer/metaAnalytics/filter/options')
  async getMetaAnalyticsFilterOptions(@requestBody() filterObj: DatalakeSelectionRequest) {
    // let startTime = new Date().getTime();
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.GET_OPTIONS} | DatalakeExplorerController.getMetaAnalyticsFilterOptions | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let returnObject = await this.datalakeExplorerService.getMetaAnalyticsChartFilterOptions(
      filterObj,
      currentUserProfile,
    );

    // let endTime = new Date().getTime();
    // let responseTime = endTime - startTime;
    // logger.info(
    //   `Getting filter options list | DatalakeExplorerController.getMetaAnalyticsFilterOptions | N/A | Response Time = ${responseTime}`,
    // );

    return returnObject;
  }

  /**
   * Use to get available sort options for content type or inside a collection
   * @param filterObj DatalakeSelectionRequest
   * @returns list of available filter options
   */
  @post('/api/explorer/sort/options')
  async getSortOptions(@requestBody() filterObj: DatalakeSelectionRequest) {
    let teamId = this.currentUserProfile.teamId;

    if (!teamId) {
      logger.error(
        `${FLOWS.GET_OPTIONS} | DatalakeExplorerController.getFilterOptions | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.DATALAKE_TEAM_NOT_EXIST);
    }

    let sortOptions = await this.datalakeExplorerService.getSortOptions(filterObj, teamId);

    let formattedSortOptions = sortOptions.map(elem => {
      if ([ExploreSortBy.DATE_MODIFIED, ExploreSortBy.DATE_CREATED].includes(elem)) {
        return {
          field: elem,
          order: ExploreSortOrder.DESCENDING,
        };
      } else {
        return {
          field: elem,
          order: ExploreSortOrder.ASCENDING,
        };
      }
    });

    return {
      sortOptions: formattedSortOptions,
    };
  }

  /**
   * Use to get graph data
   * @param filter {DatalakeSelectionRequest} filter for query
   * @returns stat tab details
   */
  @post('api/explorer/objects/graph/embeddings')
  async getGraph(@requestBody(DATALAKE_SELECTION_BODY) filter: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;
    let graphId = filter.embeddingGraph?.graphId ?? undefined;
    let coordinates = filter.embeddingGraph?.coordinates ?? undefined;

    if (filter.referenceImage) {
      filter.modelName = EMBEDDING_COLLECTION;
      filter.scoreThreshold = SIMILARITY_SCORE_THRESHOLD;
    }

    if (!graphId) {
      graphId = await this.datalakeExplorerService.getPreCalculatedGraphId(filter, currentUserProfile);
    }

    if (!graphId) {
      try {
        let returnObj = await this.searchQueryBuilderService.insertGraphObjectKeys(filter, currentUserProfile, teamId);
        graphId = returnObj.graphId;

        if (!graphId) {
          logger.error(
            `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getGraph | N/A | Couldn't find the graphId for: `,
            filter,
          );
          throw new HttpErrors.NotAcceptable('Error while creating graph object');
        }
      } catch (error) {
        logger.error(
          `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getGraph | N/A | Error: `,
          error,
        );
        throw new HttpErrors.NotAcceptable('Error while creating graph object');
      }
    } else {
      // generate pca after embedding generation
      await this.searchQueryBuilderService.generatePCAAfterEmbeddingGeneration(graphId, filter, currentUserProfile);
    }

    let graphData = await this.searchQueryBuilderService.getGraphSampleCoordinates(graphId, teamId, coordinates);

    return graphData;
  }

  /**
   * Use to get check graph data availability
   * @param filter {DatalakeSelectionRequest} filter for query
   * @returns {status: GraphDataAvailability}
   */
  @post('api/graph/check/graph/availability')
  async checkGraphEmbeddingAvailability(@requestBody(DATALAKE_SELECTION_BODY) filter: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let status: GraphDataAvailability = await this.searchQueryBuilderService.checkIfGraphEmbeddingAvailable(
      filter,
      currentUserProfile,
    );

    return {
      status: status,
    };
  }

  /**
   * Use to generate graph embeddings
   * @param filter DatalakeSelectionRequest details for generate graph
   * @returns {success: boolean}
   */
  @post('api/generate/graph/embeddings')
  async generateGraphEmbeddings(@requestBody() filter: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getObjectListOfCollection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    if (!filter.collectionId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getGraph | N/A | collectionId not found in body: `,
        filter,
      );
      throw new HttpErrors.NotAcceptable('collectionId not found in body');
    }

    let graphData = await this.datalakeExplorerService.generateGraphEmbeddings(filter, currentUserProfile);

    return graphData;
  }

  /**
   * Use to get collaborator user list
   * @param filter {DatalakeSelectionRequest} filter for query
   * @returns collaborator user list
   */
  @post('api/explorer/user/getListForSelection')
  async getUserListForSelection(@requestBody(DATALAKE_SELECTION_BODY) filter: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;

    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.getUserListForSelection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let userList = await this.datalakeExplorerService.getUserListForSelection(filter, currentUserProfile);

    return userList;
  }

  /**
   * Use to update allowed user list for selection
   * @param filter {UserSelectionRequest} selected and unselected user list
   * @returns success or failure
   */
  @post('api/explorer/user/updateAllowedUserListForSelection')
  async updateAllowedUserListForSelection(@requestBody(ACCESS_CONTROL_BODY) filter: UserSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.updateAllowedUserListForSelection | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    if (!filter.selectionId) {
      logger.error(
        `${FLOWS.DATALAKE_EXPLORER_VIEW_COLLECTION} | DatalakeExplorerController.updateAllowedUserListForSelection | N/A | selectionId not found in filter: `,
        filter,
      );
      throw new HttpErrors.NotAcceptable('selectionId not found in filter');
    }

    return await this.datalakeExplorerService.updateAllowedUserListForSelection(filter, currentUserProfile);
  }
}
