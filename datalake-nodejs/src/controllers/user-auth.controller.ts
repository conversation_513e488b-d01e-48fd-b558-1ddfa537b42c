/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the user authentication
 */

/**
 * @class AuthController
 * Handle the request related to the AuthController for expose internal enpoints to internal server api calls
 * @description This controller use for Handle the request related to the AuthController
 * <AUTHOR> chathushka
 */
import {authenticate, TokenService} from '@loopback/authentication';
import {
  Credentials,
  RefreshTokenService,
  RefreshTokenServiceBindings,
  TokenServiceBindings,
} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {HttpErrors, post, requestBody, SchemaObject} from '@loopback/rest';
import {SecurityBindings, securityId} from '@loopback/security';
import Axios from 'axios';
import dotenv from 'dotenv';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {OTP_AUTH_SERVICE, OTPAuthService} from '../authServices/otp-auth.service';
import {logger} from '../config';
import {AccessibleUsers} from '../settings/app-access-user';
import {UserType, UserTypeDetailed} from '../settings/constants';
// import {UserType} from '../settings/constants'
dotenv.config();

// Describes the schema of grant object
const RefreshGrantSchema: SchemaObject = {
  type: 'object',
  required: ['refreshToken'],
  properties: {
    refreshToken: {
      type: 'string',
    },
  },
};

// Describes the request body of grant object
const RefreshGrantRequestBody = {
  description: 'Reissuing Acess Token',
  required: true,
  content: {
    'application/json': {schema: RefreshGrantSchema},
  },
};

// Describes the type of grant object taken in by method "refresh"
type RefreshGrant = {
  refreshToken: string;
};

export interface RefreshResult {
  token: string;
  refreshToken: string;
}

export class AuthController {
  constructor(
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(RefreshTokenServiceBindings.REFRESH_TOKEN_SERVICE)
    public refreshService: RefreshTokenService,
    @inject(SecurityBindings.USER, {optional: true})
    public user: UserProfileDetailed,
    //@inject(UserServiceBindings.USER_SERVICE)
    //public userService: MyUserService
    @inject(OTP_AUTH_SERVICE) private otpAuthService: OTPAuthService,
  ) {}

  /**
   * Use to log with verifying data with sso server
   * @param credentials application details
   * @returns tokens and user details
   */
  @post('/api/login')
  async login(@requestBody() credentials: {applicationId: string; applicationCode: string}) {
    //logger.debug(credentials)

    let url = `${process.env.SSO_INTERNAL_SERVER}/internal/application/${credentials.applicationId}/validate?applicationCode=${credentials.applicationCode}`;
    logger.debug('sso login:', url);
    let response: any = {};
    try {
      response = await Axios({
        url: url,
        method: 'GET',
        // headers: {
        //   Authorization: `Bearer ${token}`,
        // },
      });
      logger.debug(response.data);
    } catch (err: any) {
      logger.debug('login faild');
      let errorMessage = '';
      if (err && err.response && err.response.data && err.response.data.error)
        errorMessage = err.response.data.error.message;
      throw new HttpErrors.Unauthorized(errorMessage ? String(errorMessage) : String(err));
    }

    let data: {
      success: true;
      user: {
        id: string;
        name: string;
        email: string;
        userType: number;
        teamId: string;
        teamName: string;
        profileImgUrl: string;
        imageUrl: string;
      };
    } = response.data;

    if (!data) throw new HttpErrors.Unauthorized('user data doesnt exists');

    let isAccessible = AccessibleUsers.includes(data.user.userType);

    if (!isAccessible) {
      throw new HttpErrors.Unauthorized('user cant access to this app');
    }

    //console.log(userProfile)
    let userProfileDetailed: UserProfileDetailed = {
      [securityId]: data.user.id,
      id: data.user.id,
      name: data.user.name,
      email: data.user.email,
      userType: data.user.userType,
      teamId: data.user.teamId,
    };

    // create a JSON Web Token based on the user profile
    const accessToken = await this.jwtService.generateToken(userProfileDetailed);
    //Create refresh token too
    const tokens = await this.refreshService.generateToken(userProfileDetailed, accessToken);
    return <UserLoginResult>{
      token: accessToken,
      refreshToken: tokens.refreshToken,
      expireTime: tokens.expiresIn,
      user: {
        email: data.user.email,
        userId: data.user.id,
        name: data.user.name,
        userType: data.user.userType || UserType.USER_TYPE_ANNOTATOR,
        isFirstTime: false,
        teamId: data.user.teamId || '',
        teamName: data.user.teamName || '',
        profileImgUrl: data.user.profileImgUrl || 'defaultProfileImage.png',
        imageUrl:
          data.user.imageUrl || `${process.env.BASE_URL}/api/user/profileImage/${data.user.id}/defaultProfileImage.png`,
      },
    };
  }

  @post('/api/refresh-token')
  async refresh(@requestBody(RefreshGrantRequestBody) refreshGrant: RefreshGrant): Promise<RefreshResult> {
    let tokenObj = await this.refreshService.refreshToken(refreshGrant.refreshToken);
    return {
      token: tokenObj.accessToken,
      refreshToken: tokenObj.refreshToken || refreshGrant.refreshToken,
    };
  }

  /**
   * Use to send otp email to user
   * @param details {object} details of the otp
   * @param currentUserProfile {UserProfileDetailed} user details
   * @returns {success: boolean}
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR,UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/user/sendOTP')
  async sendOTP(
    @requestBody()
    details: {
      type: number;
    },
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfileDetailed,
  ) {
    logger.info(`AuthController | AuthController.sendOTP | ${JSON.stringify(currentUserProfile)} | Start Send OTP`);

    return this.otpAuthService.sendOTP(currentUserProfile, details.type);
  }

  /**
   * Use to validate otp of user
   * @param details {object} details of the otp
   * @param currentUserProfile {UserProfileDetailed} user details
   * @returns {success: boolean}
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/user/validateOTP')
  async validateOTP(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfileDetailed,
    @requestBody()
    details: {
      otp: string;
      type: number;
    },
  ) {
    logger.info(`AuthController | AuthController.sendOTP | ${JSON.stringify(currentUserProfile)} | Start validate OTP`);

    return this.otpAuthService.validateOTP(details.otp.toString(), details.type, currentUserProfile);
  }
}

export interface CredentialsDetailed extends Credentials {
  email: string;
  password: string;
  applicationId: string;
}

export interface returnUser {
  userId: string;
  email: string;
  name?: string;
  userType: number;
  teamId: string;
}

export interface UserLoginResult {
  token: string;
  refreshToken?: string;
  expireTime?: string;

  user: {
    email: string;
    userId: string;
    name: string;
    userType: number;
    teamId: string;
    teamName: string;
    profileImgUrl: string;
    isFirstTime?: boolean;
    imageUrl?: string;
  };
}
