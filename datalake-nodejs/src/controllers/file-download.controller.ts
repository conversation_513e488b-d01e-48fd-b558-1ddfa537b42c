/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 *use to download file to the front end
 */

/**
 * @class file download controller
 * purpose of file download controller is to handle api and call the repository for response without jwt authentication
 * @description use to download file to the front end eg: profile images, label images, documents, layerX logo
 * <AUTHOR> vinura
 */
import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, HttpErrors, oas, param, Response, RestBindings} from '@loopback/rest';
import * as path from 'path';
import * as fs from 'fs';
import {logger} from '../config';
import {promisify} from 'util';
import {MetaDataRepository} from '../repositories/meta-data.repository';
import jwt from 'jsonwebtoken';
import {DiskStorageConfiguration} from '../settings/disk.configuration';
const IMAGE_URL = path.resolve(__dirname, '../../imagePreview');
const readFile = promisify(fs.readFile);


const DISK_BUCKET_NAME = DiskStorageConfiguration.DISK_BUCKET_NAME;
const DISK_BASE_URL = DiskStorageConfiguration.DISK_BASE_URL;
const DISK_SECRET_KEY = DiskStorageConfiguration.DISK_SECRET_KEY;
const DISK_BASE_PATH = DiskStorageConfiguration.DISK_BASE_PATH;

export class FileDownloadController {
  constructor(
    @repository(MetaDataRepository)
    public metaDataRepository: MetaDataRepository,
  ) {}

  /**
   * Use for send user profile Images
   * @param userId {string} userId of the user
   * @param response {Response} interface
   * @returns profile image or default profile image
   */
  @get('/api/user/profileImage/{userId}/{imageName}')
  @oas.response.file()
  async getProfileImage(
    @param.path.string('userId') userId: string,
    @param.path.string('imageName') imageName: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    let pathToImage = path.join(__dirname, `../../storage/profileImages/${userId}/defaultProfileImage.png`);
    if (!fs.existsSync(pathToImage)) {
      pathToImage = path.join(__dirname, '../../profileImages/defaultProfile.png');
    }

    logger.debug(pathToImage);
    response.download(pathToImage);
    return response;
  }

  /**
   * Use for send layerx logo
   * @returns layerx logo
   */
  @get('/api/layerx/logo')
  @oas.response.file()
  async getLogoImage(@inject(RestBindings.Http.RESPONSE) response: Response) {
    let pathToImage = path.join(__dirname, `../../src/logo/logo.png`);

    logger.debug(pathToImage);
    response.download(pathToImage);
    return response;
  }

  /**
   * Use for send label image
   * @param projectId {string} projectId of the project ---> projectId has been changed to teamId at the system label migration development
   * @param label {string} label of the project
   * @param attribute {string} attribute of the label
   * @param image {string} image of the attribute
   * @param response {Response} interface
   * @returns label image or default profile image
   */
  @get('/api/storage/labels/{projectId}/{label}/label/{image}')
  @oas.response.file()
  async getLabelImage(
    @param.path.string('projectId') projectId: string,
    @param.path.string('label') label: string,
    @param.path.string('image') image: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    let pathToImage = path.join(__dirname, `../../storage/labels/${projectId}/${label}/label/${image}`);
    if (!fs.existsSync(pathToImage)) {
      pathToImage = path.join(__dirname, '../../storage/labels/defaultLabel/defaultLabel.png');
    }

    logger.debug(pathToImage);
    response.download(pathToImage);
    return response;
  }

  /**
   * Use for send label image
   * @param projectId {string} projectId of the project ---> projectId has been changed to teamId at the system label migration development
   * @param label {string} label of the project
   * @param attribute {string} attribute of the label
   * @param image {string} image of the attribute
   * @param response {Response} interface
   * @returns label image or default profile image
   */
  @get('/api/storage/labels/{projectId}/{label}/attribute/{attributeKey}/{valueName}/{image}')
  @oas.response.file()
  async getAttributeImage(
    @param.path.string('projectId') projectId: string,
    @param.path.string('label') label: string,
    @param.path.string('attributeKey') attributeKey: string,
    @param.path.string('valueName') valueName: string,
    @param.path.string('image') image: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    let pathToImage = path.join(
      __dirname,
      `../../storage/labels/${projectId}/${label}/attribute/${attributeKey}/${valueName}/${image}`,
    );
    if (!fs.existsSync(pathToImage)) {
      pathToImage = path.join(__dirname, '../../storage/labels/defaultLabel/defaultLabel.png');
    }

    logger.debug(pathToImage);
    response.download(pathToImage);
    return response;
  }

  /**
   * Use for see preview of the image
   * @param imageName
   * @param response
   * @returns
   */
  @get('api/download/imagePreview/{imageName}')
  @oas.response.file()
  downloadFile(
    @param.path.string('imageName') imageName: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    let fileName = `/${imageName}`;
    const imagePath = path.join(IMAGE_URL, fileName);
    response.download(imagePath, fileName);
    return response;
  }

  @authenticate('jwt')
  @get('/api/file/download')
  @oas.response.file()
  async fileDownload(
    @param.query.string('objectKey') objectKey: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ) {
    let objectDetails = await this.metaDataRepository.findOne({where: {objectKey: objectKey}});
    const redirectUrl = objectDetails?.url;

    if (redirectUrl) {
      // Redirect the request
      response.redirect(redirectUrl);
    } else {
      throw HttpErrors.NotAcceptable('File not found');
    }
  }



/**
   * Used to download when a presigned URL is cliacked
   * @param token {string} JWT token of the the URL 
   * @param response {Response} interface
   * @returns Downloads selected file
*/
@get('/api/download/file/localDisk')
@oas.response.file()
async downloadFileLocalDisk(
  @param.query.string('token') token: string,
  @inject(RestBindings.Http.RESPONSE) response: Response
) {
    try {
      // Verify and decode the JWT token
      const decodedToken = jwt.verify(token, DISK_SECRET_KEY) as { bucket: string, keyValue: string, exp: number };

      // Extract necessary parameters from the decoded token
      const { bucket, keyValue, exp } = decodedToken;

      // Check expiration time
      const currentTime = Math.floor(Date.now() / 1000);
      if (currentTime > exp) {
        response.status(403).send('URL has expired');
        return;
      }

      // Construct the file path and send the file for download
      const filePath = path.join(DISK_BASE_PATH + bucket, keyValue);
      const file = await readFile(filePath);
      response.attachment(keyValue);
      response.send(file);

    } catch (error) {
      console.error('Error in downloadFileLocalDisk:', error);

      if (error.name === 'TokenExpiredError') {
        response.status(403).send('URL has expired');
      } else if (error.name === 'JsonWebTokenError') {
        response.status(401).send('Invalid token');
      } else {
        response.status(500).send('Internal server error');
      }
    }
  }
}