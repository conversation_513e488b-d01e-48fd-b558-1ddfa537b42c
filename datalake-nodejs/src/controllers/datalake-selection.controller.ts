/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for API for the DatalakeSelection model
 */

/**
 * @class DatalakeSelectionController
 * Handle the request related to the DatalakeSelectionController Controller
 * Use to select(tag) datalake objects to initiate project creations
 * @description This controller use for Handle the request related to the DatalakeExplorer controller eg: get a unique tag for a selection
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {HttpErrors, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {DatalakeSelectionRequest} from '../models';
import {DATALAKE_SELECTION_SERVICE, DatalakeSelectionService} from '../services';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

const internalBaseUrl = '/internal/datalakeSelection';

export class DatalakeSelectionController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject(DATALAKE_SELECTION_SERVICE) private datalakeSelectionService: DatalakeSelectionService,
  ) {}

  /**
   * Return a tag for a specific selection
   * Use to select files for project creations
   * @param reqBody DatalakeSelectionRequest
   * @returns selectionId
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post('/api/datalake/selection/tag')
  async getDatalakeSelectionTag(@requestBody() reqBody: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getDatalakeSelectionTag | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;
    return await this.datalakeSelectionService.createProjectSelectionTag(reqBody, currentUserProfile);
  }

  /**
   * to get the option list of selections
   * @param reqBody DatalakeSelectionRequest
   * @returns option list of selections
   */
  @authenticate('jwt')
  @authorize({allowedRoles: [UserTypeDetailed.TEAM_ADMIN, UserTypeDetailed.MEMBER, UserTypeDetailed.COLLABORATOR, UserTypeDetailed.SUPER_ADMIN]})
  @post(`/api/datalake/selection/options`)
  async getOptionListOfSelections(@requestBody() reqBody: DatalakeSelectionRequest) {
    let currentUserProfile = this.currentUserProfile;
    if (!currentUserProfile || !currentUserProfile.teamId) {
      logger.error(
        `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getOptionListOfSelections | N/A | Couldn't find the team for: `,
        this.currentUserProfile,
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    let teamId = currentUserProfile.teamId;

    let startTime = new Date().getTime();
    let response = await this.datalakeSelectionService.getOptionListOfSelections(reqBody, currentUserProfile);
    let endTime = new Date().getTime();

    logger.info(
      `${FLOWS.DATALAKE_SELECTION} | DatalakeSelectionController.getOptionListOfSelections | ${
        endTime - startTime
      } ms | response: `,
      response,
    );

    return response;
  }
}
