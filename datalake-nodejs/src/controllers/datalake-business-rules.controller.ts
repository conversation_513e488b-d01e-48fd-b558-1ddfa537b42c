/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * controller class that use handle the request-response lifecycle for APIs related to data dictionary business rules
 */

/**
 * @class DatalakeBusinessRuleController
 * @description This controller use for Handle the request-response lifecycle for APIs related to data dictionary business rules
 * <AUTHOR>
 */

import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {del, get, HttpErrors, param, patch, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {UserProfileDetailed} from '../authServices/custom-token.service';
import {logger} from '../config';
import {BusinessRuleService} from '../services/business-rule.service';
import {FLOWS, UserTypeDetailed} from '../settings/constants';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';

@authenticate('jwt')
@authorize({
  allowedRoles: [
    UserTypeDetailed.TEAM_ADMIN,
    UserTypeDetailed.SUPER_ADMIN,
    UserTypeDetailed.MEMBER,
    UserTypeDetailed.COLLABORATOR,
  ],
})
export class DatalakeBusinessRuleController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    public currentUserProfile: UserProfileDetailed,
    @inject('services.BusinessRuleService')
    private businessRuleService: BusinessRuleService,
  ) {}

  /**
   * Get all business rules with optional search
   * @param search Optional search key to filter rules
   * @returns List of business rules
   */
  @get('/api/business-rules/rules')
  async getBusinessRules(
    @param.query.string('search') search?: string,
    @param.query.number('pageSize') pageSize: number = 20,
    @param.query.number('pageIndex') pageIndex: number = 0,
  ): Promise<{businessRules: any[]}> {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.getBusinessRules | N/A | Couldn't find teamId for user: `,
          this.currentUserProfile,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST}`);
      }
      const result = await this.businessRuleService.getBusinessRules(search, pageSize, pageIndex);
      if (result.isSuccess) {
        return result.data ?? {businessRules: []};
      } else {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.getBusinessRules | N/A | Failed to fetch business rules: ${result.message}`,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULES_FETCH_FAILED}`);
      }
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.getBusinessRules | N/A | Error: ${error.message}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULES_FETCH_FAILED}`);
    }
  }

  /**
   * Create a new business rule
   * @param data Business rule data
   * @returns Success message
   */
  @post('/api/business-rules/create')
  async createBusinessRule(@requestBody() data: {rule: string}): Promise<{isSuccess: boolean; message: string}> {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.createBusinessRule | N/A | Couldn't find teamId for user: `,
          this.currentUserProfile,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST}`);
      }
      if (!data.rule || data.rule.trim() === '') {
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_CANNOT_BE_EMPTY}`);
      }
      const result = await this.businessRuleService.createBusinessRule(data.rule, this.currentUserProfile);
      if (result.isSuccess) {
        return result;
      } else {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.createBusinessRule | N/A | Failed to create business rule: ${result.message}`,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_CREATION_FAILED}`);
      }
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.createBusinessRule | N/A | Error: ${error.message}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_CREATION_FAILED}`);
    }
  }

  /**
   * Update an existing business rule
   * @param id Business rule ID
   * @param data Updated business rule data
   * @returns Success message
   */
  @patch('/api/business-rules/update/{id}')
  async updateBusinessRule(
    @param.path.string('id') id: string,
    @requestBody() data: {rule: string},
  ): Promise<{isSuccess: boolean; message: string}> {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.updateBusinessRule | N/A | Couldn't find teamId for user: `,
          this.currentUserProfile,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST}`);
      }
      if (!data.rule || data.rule.trim() === '') {
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_CANNOT_BE_EMPTY}`);
      }
      const result = await this.businessRuleService.updateBusinessRule(id, data.rule, this.currentUserProfile);
      if (result.isSuccess) {
        return result;
      } else {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.updateBusinessRule | N/A | Failed to update business rule: ${result.message}`,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_UPDATE_FAILED}`);
      }
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.updateBusinessRule | N/A | Error: ${error.message}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_UPDATE_FAILED}`);
    }
  }

  /**
   * Soft delete a business rule
   * @param id Business rule ID
   * @returns Success message
   */
  @del('/api/business-rules/delete/{id}')
  async deleteBusinessRule(@param.path.string('id') id: string): Promise<{isSuccess: boolean; message: string}> {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.deleteBusinessRule | N/A | Couldn't find teamId for user: `,
          this.currentUserProfile,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST}`);
      }
      const result = await this.businessRuleService.deleteBusinessRule(id, this.currentUserProfile);
      if (result.isSuccess) {
        return result;
      } else {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.deleteBusinessRule | N/A | Failed to delete business rule: ${result.message}`,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_DELETION_FAILED}`);
      }
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.deleteBusinessRule | N/A | Error: ${error.message}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_DELETION_FAILED}`);
    }
  }

  @post('/api/business-rules/toggle_rule_status/{id}')
  async toggleBusinessRuleStatus(
    @param.path.string('id') id: string,
    @requestBody() data: {is_enabled: boolean},
  ): Promise<{isSuccess: boolean; message: string}> {
    try {
      const teamId = this.currentUserProfile?.teamId;
      if (!teamId) {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.toggleBusinessRuleStatus | N/A | Couldn't find teamId for user: `,
          this.currentUserProfile,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST}`);
      }
      const result = await this.businessRuleService.toggleBusinessRuleStatus(id, data.is_enabled);
      if (result.isSuccess) {
        return result;
      } else {
        logger.error(
          `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.toggleBusinessRuleStatus | N/A | Failed to toggle business rule status: ${result.message}`,
        );
        throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_TOGGLE_STATUS_FAILED}`);
      }
    } catch (error) {
      logger.error(
        `${FLOWS.BUSINESS_RULES} | DatalakeBusinessRuleController.toggleBusinessRuleStatus | N/A | Error: ${error.message}`,
      );
      throw new HttpErrors.NotAcceptable(`${DatalakeUserMessages.BUSINESS_RULE_TOGGLE_STATUS_FAILED}`);
    }
  }
}
