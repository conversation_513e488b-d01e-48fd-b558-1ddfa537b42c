import {AuthenticationComponent} from '@loopback/authentication';
import {
  JWTAuthenticationComponent,
  RefreshTokenServiceBindings,
  TokenServiceBindings,
} from '@loopback/authentication-jwt';
import {
  AuthorizationComponent,
  AuthorizationDecision,
  AuthorizationOptions,
  AuthorizationTags,
} from '@loopback/authorization';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig, BindingScope, asLifeCycleObserver} from '@loopback/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication, RestBindings} from '@loopback/rest';
import {RestExplorerBindings, RestExplorerComponent} from '@loopback/rest-explorer';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import {AdapterFactory} from './adapters/adapter.factory';
import {MyAuthorizationProvider} from './authServices/authorization.service';
import {BasicAuthService} from './authServices/basic-auth.service';
import {RefreshService} from './authServices/custom-refresh.service';
import {JWTService} from './authServices/custom-token.service';
import {OTPAuthService} from './authServices/otp-auth.service';
import {RefreshTokenServiceConstants, TokenServiceConstants} from './keys';
import {MySequence} from './sequence';
import {
  AnnotationDownloadHandlerService,
  AwsS3StorageService,
  ConnectionService,
  CronTriggerService,
  DatalakeExplorerService,
  DatalakeSelectionService,
  DatalakeTrashService,
  DatasetManagerInterfaceService,
  DiskStorageService,
  KnowledgeBlockService,
  KnowledgeSourceService,
  MetaDataService,
  MetaFieldPropagatorService,
  ObjectMetaUpdaterService,
  SearchQueryBuilderService,
  StorageCrawlerService,
  StorageOperationHandlerService,
  SystemLabelService,
  SystemStatsService,
  SystemValidationService,
  TableDataService,
  QuickBooksApiService,
} from './services';
import {BusinessRuleService} from './services/business-rule.service';
import {EmbeddingsService} from './services/embeddings.service';
import {EvaluationService} from './services/evaluation.service';
import {FileUploadHandlerService} from './services/file-upload-handler.service';
import {JobService} from './services/job.service';
import {MediaProcessorService} from './services/media-processor.service';
import {MetaDataUpdateService} from './services/meta-data-update.service';
import {SrcDestConnectionService} from './services/src-dest-connection.service';
import {StatsCalculationService} from './services/stats-calculation.service';
import {StudioInterfaceService} from './services/studio-interface.service';
import {SystemMetaService} from './services/system-meta.service';
export {ApplicationConfig};

export class DatalakeApplication extends BootMixin(ServiceMixin(RepositoryMixin(RestApplication))) {
  constructor(options: ApplicationConfig = {}) {
    super(options);
    //For JWT Authentication
    // Mount authentication system
    this.component(AuthenticationComponent);
    // Mount jwt component
    this.component(JWTAuthenticationComponent);

    // Set up the custom sequence
    this.sequence(MySequence);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    // cron service for triggering - saving metadata from input queue
    this.bind('service.cronTrigger').toClass(CronTriggerService).apply(asLifeCycleObserver);

    // service to process system labels
    this.bind('service.systemLabelService').toClass(SystemLabelService);

    // service to process system labels
    this.bind('service.systemStatsService').toClass(SystemStatsService);

    // service to process updating meta objects
    this.bind('service.objectMetaUpdater').toClass(ObjectMetaUpdaterService);

    // service to update Query Option of a team
    this.bind('service.searchQueryBuilderService').toClass(SearchQueryBuilderService);

    // service to handle aws connection
    this.bind('service.awsS3Storage').toClass(AwsS3StorageService);

    // service to handle disk storage connection
    this.bind('service.diskStorage').toClass(DiskStorageService);

    // service to handle storage connection
    this.bind('service.storageOperationHandler').toClass(StorageOperationHandlerService);

    this.bind('service.storageCrawler').toClass(StorageCrawlerService);

    this.bind('service.systemValidationService').toClass(SystemValidationService);

    this.bind('service.metaDataService').toClass(MetaDataService);

    this.bind('service.datalakeExplorerService').toClass(DatalakeExplorerService);

    this.bind('service.fileUploadHandler').toClass(FileUploadHandlerService);

    this.bind('service.MediaProcessor').toClass(MediaProcessorService);

    this.bind('service.basicAuth').toClass(BasicAuthService);

    this.bind('service.otpAuth').toClass(OTPAuthService);

    this.bind('service.metaDataUpdateService').toClass(MetaDataUpdateService);
    this.bind('service.metaFieldPropagator').toClass(MetaFieldPropagatorService);

    this.bind('service.datalakeSelection').toClass(DatalakeSelectionService);

    this.bind('service.evaluation').toClass(EvaluationService);

    this.bind('service.businessRuleService').toClass(BusinessRuleService);

    this.bind('service.knowledgeSourceService').toClass(KnowledgeSourceService);

    this.bind('service.knowledgeBlockService').toClass(KnowledgeBlockService);

    this.bind('service.datasetManagerInterface').toClass(DatasetManagerInterfaceService);

    this.bind('service.annotationDownloadHandler').toClass(AnnotationDownloadHandlerService);

    this.bind('service.studioInterface').toClass(StudioInterfaceService);

    this.bind('service.Job').toClass(JobService);

    this.bind('service.datalakeTrash').toClass(DatalakeTrashService);

    this.bind('service.systemMetaService').toClass(SystemMetaService);

    this.bind('service.embeddings')
      .toClass(EmbeddingsService)
      .apply(asLifeCycleObserver)
      .inScope(BindingScope.SINGLETON);

    this.bind('service.statsCalculation').toClass(StatsCalculationService);

    this.bind('service.tableDataService').toClass(TableDataService);

    this.bind('service.SrcDestConnectionService').toClass(SrcDestConnectionService);

    this.bind(RestBindings.REQUEST_BODY_PARSER_OPTIONS).to({
      limit: '250MB',
    });

    this.bind(TokenServiceBindings.TOKEN_SECRET).to(TokenServiceConstants.TOKEN_SECRET_VALUE);

    this.bind(TokenServiceBindings.TOKEN_EXPIRES_IN).to(TokenServiceConstants.TOKEN_EXPIRES_IN_VALUE);

    this.bind(TokenServiceBindings.TOKEN_SERVICE).toClass(JWTService);

    this.bind(RefreshTokenServiceBindings.REFRESH_SECRET).to(RefreshTokenServiceConstants.REFRESH_TOKEN_SECRET_VALUE);

    this.bind(RefreshTokenServiceBindings.REFRESH_EXPIRES_IN).to(
      RefreshTokenServiceConstants.REFRESH_TOKEN_EXPIRES_IN_VALUE,
    );

    this.bind(RefreshTokenServiceBindings.REFRESH_TOKEN_SERVICE).toClass(RefreshService);

    this.bind('adapters.AdapterFactory').toClass(AdapterFactory);

    this.bind('service.connectionService').toClass(ConnectionService);

    // bind set authorization options
    const authoptions: AuthorizationOptions = {
      precedence: AuthorizationDecision.DENY,
      defaultDecision: AuthorizationDecision.DENY,
    };

    // mount authorization component
    const binding = this.component(AuthorizationComponent);
    // configure authorization component
    this.configure(binding.key).to(authoptions);

    // bind the authorizer provider
    this.bind('authorizationProviders.my-authorizer-provider')
      .toProvider(MyAuthorizationProvider)
      .tag(AuthorizationTags.AUTHORIZER);
  }
}
