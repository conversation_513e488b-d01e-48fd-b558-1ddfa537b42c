/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class QueryGraphData
 * purpose of QueryGraphData model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class QueryGraphData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  objectKey?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'boolean',
  })
  isCronJobDeletable: boolean;

  [prop: string]: any;

  constructor(data?: Partial<QueryGraphData>) {
    super(data);
  }
}

export interface QueryGraphDataRelations {
  // describe navigational properties here
}

export type QueryGraphDataWithRelations = QueryGraphData & QueryGraphDataRelations;
