/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, rule, created_date, modified_date, created_by, modified_by, is_edit, is_active
 */

/**
 * @class BusinessRule
 * purpose of BusinessRule model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, rule, created_date, modified_date, created_by, modified_by, is_edit, is_active
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {KnowledgeSourceType} from './knowledge-source.model';

@model({settings: {mongodb: {collection: 'BusinessRule'}}})
export class BusinessRule extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  rule: string;

  @property({
    type: 'date',
  })
  created_date?: Date;

  @property({
    type: 'date',
  })
  modified_date?: Date;

  @property({
    type: 'string',
  })
  created_by?: string;

  @property({
    type: 'string',
  })
  modified_by?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  is_edit?: boolean;

  @property({
    type: 'boolean',
    default: true,
  })
  is_active?: boolean;

  @property({
    type: 'boolean',
    default: true,
  })
  is_enabled?: boolean;

  @property({
    type: 'string',
  })
  rule_source_type?: KnowledgeSourceType;

  @property({
    type: 'string',
  })
  rule_source_id?: string;

  @property({
    type: 'string',
  })
  rule_source_name?: string;

  //tables list
  @property({
    type: 'array',
    itemType: 'object',
  })
  table_columns?: {
    connection_id?: string;
    connection_name?: string;
    table_name: string;
    columns?: string[];
  }[];

  @property({
    type: 'string',
  })
  knowledge_source_id?: string;

  @property({
    type: 'string',
  })
  knowledge_id?: string;

  constructor(data?: Partial<BusinessRule>) {
    super(data);
  }
}

export interface BusinessRuleRelations {
  // describe navigational properties here
}

export type BusinessRuleWithRelations = BusinessRule & BusinessRuleRelations;

export interface BusinessRuleDataSource {
  connection_id?: string;
  connection_name?: string;
  table_name: string;
  columns?: string[];
}
