/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, source name, type and configuration
 */

/**
 * @class Source
 * purpose of Source model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, source name, type and configuration
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class Source extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'any',
  })
  schema?: any;

  @property({
    type: 'string',
  })
  createdBy?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
  })
  type?: ConnectionSourceType;

  @property({
    type: 'string',
  })
  sourceDefinitionId?: string;

  @property({
    type: 'string',
  })
  sourceId?: string;

  @property({
    type: 'string',
  })
  destinationId?: string;

  @property({
    type: 'string',
  })
  connectionId?: string;

  @property({
    type: 'string',
  })
  workspaceId?: string;

  @property({
    type: 'string',
  })
  sourceName?: string;

  @property({
    type: 'object',
  })
  connectionConfiguration?: ConnectionSourceConfiguration;

  [prop: string]: any;

  constructor(data?: Partial<Source>) {
    super(data);
  }
}

export interface SourceRelations {
  // describe navigational properties here
}

export type SourceWithRelations = Source & SourceRelations;

export enum ConnectionSourceType {
  QUICK_BOOK = 'qb',
  MONGO_DB = 'mongodb',
  AWS_S3 = 'aws_s3',
  GCS = 'gcs',
  DISK = 'lds',
  AZURE_BLOB = 'azure_blob',
  MYSQL_DB = 'mysql',
  MSSQL_DB = 'mssql',
  POSTGRESQL = 'postgresql',
  BIGQUERY = 'bigquery',
  SNOWFLAKE = 'snowflake',
}

export interface ConnectionSourceConfiguration {
  sandbox?: boolean;
  credentials?: {
    // db
    host?: string;
    port?: number;
    username?: string;
    password?: string;
    database?: string;

    //quick book
    auth_type?: string;
    access_token?: string;
    refresh_token?: string;
    realm_id?: string;
    client_id?: string;
    client_secret?: string;
    token_expiry_date?: Date;
  };
  start_date?: Date;
}

export interface ConnectionSourceCredentials {
  //mongo db
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database: string;
  authSource?: string;

  //quick book
  sandbox?: boolean;
  access_token?: string;
  refresh_token?: string;
  realm_id?: string;
  client_id?: string;
  client_secret?: string;
  token_expiry_date?: string;

  // for big query
  credentials_path?: string;
  project_id?: string;

  //for snowflake connection
  account_id?: string;
  warehouse?: string;
  schema?: string;
  role?: string;

  //common
  start_date?: string;

  //sql server windows authentication
  is_windows_authentication?: boolean;
  domain?: string;
}

export interface AirbyteConnectionSourceCredentials {
  name?: string;
  sourceDefinitionId?: string;
  workspaceId?: string;
  connectionConfiguration?: {
    sandbox?: boolean;
    credentials?: {
      auth_type?: string;
      access_token?: string;
      refresh_token?: string;
      realm_id?: string;
      client_id?: string;
      client_secret?: string;
      token_expiry_date?: string;
    };
    start_date?: string;
  };
}

export interface AirbyteConnectionSourceUpdateCredentials {
  name?: string;
  sourceId?: string;
  workspaceId?: string;
  connectionConfiguration?: {
    sandbox?: boolean;
    credentials?: {
      auth_type?: string;
      access_token?: string;
      refresh_token?: string;
      realm_id?: string;
      client_id?: string;
      client_secret?: string;
      token_expiry_date?: string;
    };
    start_date?: string;
  };
}

export const SourceDefinitionIds = {
  sourceDefinitionIdQuickBook: 'cf9c4355-b171-4477-8f2d-6c5cc5fc8b7e',
  sourceDefinitionIdMongoDB: 'b2e713cd-cc36-4c0a-b5bd-b47cb8a0561e',
  sourceDefinitionIdGoogleSheet: '71607ba1-c0ac-4799-8049-7f4b90dd50f7',
  sourceDefinitionIdMysql: '435bb9a5-7887-4809-aa58-28c27df0d7ad',
  sourceDefinitionIdMssql: 'b5ea17b1-f170-46dc-bc31-cc744ca984c1',
};

export interface SelectionConfiguration {
  //quickbook
  accounts?: boolean;
  bill_payments?: boolean;
  bills?: boolean;
  budgets?: boolean;
  classes?: boolean;
  credit_memos?: boolean;
  customers?: boolean;
  departments?: boolean;
  deposits?: boolean;
  employees?: boolean;
  invoices?: boolean;
  items?: boolean;
  journal_entries?: boolean;
  payment_methods?: boolean;
  purcahse_orders?: boolean;
  purchases?: boolean;
  refund_receipts?: boolean;
  sales_receipts?: boolean;
  tax_agencies?: boolean;
  tax_codes?: boolean;
  tax_rates?: boolean;
  terms?: boolean;
  time_activities?: boolean;
  transfers?: boolean;
  vendor_credits?: boolean;
  vendors?: boolean;
}
