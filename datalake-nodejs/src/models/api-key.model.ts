/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class ApiKey
 * purpose of ApiKey model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class ApiKey extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  key?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'object',
  })
  apiConfigs?: ApiConfigFormat;

  @property({
    type: 'date',
  })
  lastSyncTimestamp?: Date;

  @property({
    type: 'boolean',
  })
  isProcessingLocked?: boolean;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<ApiKey>) {
    super(data);
  }
}

export interface ApiKeyRelations {
  // describe navigational properties here
}

export type ApiKeyWithRelations = ApiKey & ApiKeyRelations;

export interface ApiConfigFormat {
  maxSyncInterval: number
}
