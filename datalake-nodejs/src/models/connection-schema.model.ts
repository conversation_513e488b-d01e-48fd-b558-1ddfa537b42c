import {Entity, model, property} from '@loopback/repository';

@model()
export class ConnectionSchema extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  connectionId: string;

  @property({
    type: 'string',
    required: true,
  })
  tableName: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  columns: string[];

  @property({
    type: 'string',
  })
  schema?: string;

  @property({
    type: 'string',
  })
  tableType?: string;

  @property({
    type: 'boolean',
    required: true,
  })
  isSelected?: boolean;

  @property({
    type: 'boolean',
    required: true,
  })
  isCrawled?: boolean;

  //createdAt
  @property({
    type: 'date',
    required: true,
  })
  createdAt?: Date;

  constructor(data?: Partial<ConnectionSchema>) {
    super(data);
  }
}

export interface ConnectionSchemaRelations {
  // describe navigational properties here
}

export type ConnectionSchemaWithRelations = ConnectionSchema & ConnectionSchemaRelations;
