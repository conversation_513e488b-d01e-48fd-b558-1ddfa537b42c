/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, tag, uniqueTagName, etc. and other constraints.
 */

/**
 * @class MetaTag
 * purpose of MetaTag model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, tag, uniqueTagName, etc. and other constraints.
 * <AUTHOR> channa, chathushka, manelka, chamath
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class MetaTag extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  tag: string;

  @property({
    type: 'string',
    required: true,
  })
  uniqueTagName: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'date',
  })
  lastModifiedAt?: Date;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'string',
  })
  modifiedBy?: string;

  constructor(data?: Partial<MetaTag>) {
    super(data);
  }
}

export interface MetaTagRelations {
  // describe navigational properties here
}

export type MetaTagWithRelations = MetaTag & MetaTagRelations;
