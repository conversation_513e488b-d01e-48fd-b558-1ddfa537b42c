/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class LabelGroup
 * It's required to keep labels as groups to easily add to projects via automated scripts
 * @description LabelGroup model keeps a collection of labels in Data Lake
 * <AUTHOR>
 */
import {Entity, model, property} from '@loopback/repository';

@model()
export class LabelGroup extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  groupName: string;

  @property({
    type: 'string',
    required: true,
  })
  uniqueName: string;

  @property({
    type: 'string',
    required: false,
  })
  createdBy?: string;

  @property({
    type: 'date',
    required: false,
  })
  updatedAt?: Date;

  @property({
    type: 'string',
    required: false,
  })
  modifiedById?: string;

  @property({
    type: 'string',
    required: false,
  })
  modifiedByName?: string;

  @property({
    type: 'date',
  })
  createdAt: Date;

  @property({
    type: 'string',
  })
  teamId?: string;

  constructor(data?: Partial<LabelGroup>) {
    super(data);
  }
}

export interface LabelGroupRelations {
  // describe navigational properties here
}

export type LabelGroupWithRelations = LabelGroup & LabelGroupRelations;
