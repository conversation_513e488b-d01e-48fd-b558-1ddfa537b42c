/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, objectKey, parentList, etc. and other constraints.
 */

/**
 * @class MetaData
 * purpose of MetaData model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, object<PERSON>ey, parentList, etc. and other constraints.
 * <AUTHOR> channa
 */

import {Entity, model, property} from '@loopback/repository';
import {RequestBodyObject} from '@loopback/rest';
import {ObjectId} from 'mongodb';
import {SuccessResponse} from '../settings/tools';
import {EmbeddingSelectionObject} from './datalake-selection.model';
import {Analytics, AnnotationObject, MetaUpdatesLabelGroup, OperationMode} from './meta-data-update.model';
import {InputFieldTypes} from './meta-field.model';
import {SystemLabelType} from './system-label.model';

@model({settings: {strict: false}})
export class MetaData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  _id?: string; // id field is retrived as _id ehrn aggregating

  @property({
    type: 'string',
    // required: true,
  })
  metaObjectId?: string;

  @property({
    type: 'string',
    // required: true,
  })
  objectKey?: string;

  @property({
    type: 'string',
    // required: true,
  })
  storagePath?: string;

  @property({
    type: 'string',
    // required: true,
  })
  storagePrefixPath?: string;

  @property({
    type: 'string',
    // required: true,
  })
  bucketName?: string;

  @property({
    type: 'string',
    // required: true,
  })
  sourceObjectKey?: string; // if an image is generated using another image, this will keep the source image object key. (ex: exists in augmented images)

  @property({
    type: 'string',
    // required: true,
  })
  collectionId?: ObjectId;

  @property({
    type: 'string',
  })
  teamId?: ObjectId;

  @property({
    type: 'boolean',
    // required: true,
  })
  isPendingThumbnail?: boolean;

  @property({
    type: 'array',
    itemType: 'string',
  })
  parentList?: ObjectId[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  Tags?: string[];

  @property({
    type: 'string',
  })
  directory?: string;

  @property({
    type: 'number',
  })
  frameCount: number;

  @property({
    type: 'number',
  })
  frameRate: number;

  @property({
    type: 'object',
  })
  resolution: {
    width: number;
    height: number;
  };

  @property({
    type: 'number',
  })
  videoCount: number;

  @property({
    type: 'number',
  })
  otherCount: number;

  @property({
    type: 'number',
  })
  imageCount: number;

  @property({
    type: 'number',
  })
  frameCollectionCount: number; // exist only in video collection, this will keep frame collction count belongs to it. Use to calculate system data counts

  @property({
    type: 'number',
  })
  fileSize: number;

  @property({
    type: 'number',
  })
  videoLength: number;

  @property({
    type: 'number',
  })
  objectType: ContentType;

  @property({
    type: 'boolean',
  })
  isAccessible: boolean; // url has generated, then it is accesible from frontend

  @property({
    type: 'string',
  })
  dataCrawlId?: ObjectId;

  @property({
    type: 'string',
  })
  datasetGroupId?: string; //dataset manager side dataset id

  @property({
    type: 'string',
  })
  name: string;

  @property({
    type: 'string',
  })
  nameInLowerCase: string; //This add to keep the lowercase of the name which will be used in the sorting

  @property({
    type: 'date',
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  updatedAt: Date;

  @property({
    type: 'string',
  })
  url: string;

  @property({
    type: 'string',
  })
  thumbnailUrl: string;

  @property({
    type: 'string',
  })
  thumbnailKey: string;

  @property({
    type: 'date',
  })
  urlExpiredAt: Date;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  annotationProjectList?: AnnotationProjectList[];

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  curationProjectList?: CurationProjectList[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  taskIdList?: string[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  vCollectionIdList?: ObjectId[];

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  datasetVersionList?: DatasetVersionList[];

  @property({
    type: 'number',
  })
  frameVerificationStatus?: FrameVerificationStatus;

  @property({
    type: 'object',
  })
  verificationStatusCount?: VerificationStatusCount;

  @property({
    type: 'boolean',
    default: true,
  })
  isLeaf?: boolean; //False if there are dependant objects (eg: when there is related image collection for a video)

  @property({
    type: 'boolean',
    default: true,
  })
  statPending?: boolean; // use to update system stats & propagate child count changes to it's parents

  @property({
    type: 'date',
  })
  statPendingAt?: Date; // time when statPending flag set to true

  @property({
    type: 'boolean',
    default: false,
  })
  annotationStatPending?: boolean; // use to update annotation related counts in frames (ex: label counts)

  @property({
    type: 'boolean',
    default: false,
  })
  datasetStatPending?: boolean; // use to update analytics, ... related to dataset calculate and update

  @property({
    type: 'date',
  })
  frameAnalyticsCalcAt?: Date;

  @property({
    type: 'number',
  })
  hierarchyLevel?: number; //No of parents in parentList - field used for processing, not saved in DB

  @property({
    type: 'boolean',
  })
  isMediaProcessingPending?: boolean;

  @property({
    type: 'string',
  })
  frameCollectionId?: string;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  labelList?: LabelList[];

  @property({
    type: 'object',
  })
  augmentationType?: AugmentationType;

  @property({
    type: 'object',
  })
  augmentationSettings?: AugmentationSettings;

  @property({
    type: 'boolean',
    default: false,
  })
  isAugmentedImage?: boolean;

  @property({
    type: 'number',
  })
  collectionType: CollectionType;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  operationList?: OperationList[];

  @property({
    type: 'boolean',
  })
  isFromVideo?: boolean; //if a frame collection belongs to a video, then isVideoFrame = true

  @property({
    type: 'number',
  })
  videoFrameIndex?: number; // This property exist in frames which belongs to a video

  @property({
    type: 'number',
  })
  frameId?: number; // This property does not exist in db. Generating OnTheFly as user case: (if viewing frame collection belongs to a video while syncing with raw video, then the frameId=videoFrameIndex, if viewing frame collection in image collection view, then frame id will document number after sorting from videoFrameIndex(if videoFrameIndex exist) or id((if videoFrameIndex not exist)) )

  @property({
    type: 'number',
  })
  score?: number; // text search score, actually not saved in metadata, but added from $addFields for text search

  @property({
    type: 'string',
  })
  fileTitle?: string;

  @property({
    type: 'string',
  })
  namedEntities?: string;

  @property({
    type: 'string',
  })
  searchString?: string;

  @property({
    type: 'string',
  })
  mediaProcessError?: string; // inserted from python media process service if it is failed

  @property({
    type: 'boolean',
  })
  isError?: boolean; //if this is true media not suitable for data lake proccesses

  @property({
    type: 'boolean',
  })
  uploadInProgress?: boolean; // to identify upload pending collections

  @property({
    type: 'string',
  })
  uploadingUserId?: string; // to identify upload pending collections - of this user

  @property({
    type: 'string',
  })
  fileUploadId?: string | ObjectId; // to identify upload progress where the file belongs

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  fileUploadIdList?: ObjectId[];

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  analytics?: Analytics[];

  @property({
    type: 'boolean',
    default: false,
  })
  isDerivedFile?: boolean; // check whether file is directly uploaded or derived

  @property({
    type: 'boolean',
    default: false,
  })
  isOriginalUploadFile?: boolean; // check whether file is directly uploaded or derived

  @property({
    type: 'boolean',
  })
  isMetaPropagationPending?: boolean; // exist only in collections. If true, it should be updated from its childs

  @property({
    type: 'string',
  })
  sourceVideoId?: string | ObjectId; // to identify the video where this image is derived froms

  @property({
    type: 'boolean',
  })
  isVerificationStatusPending?: boolean; // to mark verification status recalculation

  // @property({
  //   type: 'boolean',
  // })
  // isLogical?: boolean; // if this true, that collection meta object is virtual

  @property({
    type: 'object',
  })
  customMeta?: {[k: string]: any}; // use to keep custom metadata

  @property({
    type: 'array',
    itemType: 'object',
  })
  metaFieldList?: metaFieldInput[];

  @property({
    type: 'number',
  })
  objectStatus?: OBJECT_STATUS; // to mark object status. ex: deleted, archived, ...

  @property({
    type: 'date',
  })
  trashedAt?: Date; // to mark trashed date

  @property({
    type: 'string',
  })
  trashingUserId?: string; // to identify trash objects - of this user

  // @property({
  //   type: 'object',
  // })
  // collectionHeadOnlyMeta?: CollectionHeadOnlyMeta;

  @property({
    type: 'boolean',
  })
  showInTrash?: boolean; // if true, then show in trash tab (use this flag to prevent showing children in case of parent also exist in trash)

  @property({
    type: 'string',
  })
  augmentationCollectionId: ObjectId; // original collection keep the id of augmented collection that belongs to it

  @property({
    type: 'array',
    itemType: 'object',
  })
  embeddingModels?: EmbeddingModel[]; // embedding generated model details for relevant metadata

  @property({
    type: 'array',
    itemType: 'object',
  })
  similarArray?: SimilarArray[]; // temporary search results for similarity search

  @property({
    type: 'boolean',
  })
  isMetaFieldsPropagationRequired?: boolean; // to mark metadata updated and requires updating collection head metadata summary

  @property({
    type: 'array',
    itemType: 'object',
  })
  metaDataFieldSummary?: MetaFieldAndArraySummaryFormat[]; // summary in collection heads used for stats and details

  @property({
    type: 'boolean',
  })
  isFeatureGraphPending?: boolean; // to mark feature graph calculation pending collections

  @property({
    type: 'string',
  })
  graphId?: string; // to identify the feature graph id of the collection

  @property({
    type: 'array',
    itemType: 'object',
  })
  metaDataFieldAnalyticsLabelwise?: MetaFieldAndTagAnalyticsLabelwiseFormat[]; // summary in collection heads used for metadata field analytics labelwise

  @property({
    type: 'array',
    itemType: 'object',
  })
  queryGraphData?: QueryGraphData[]; // temporary graph results for graph plot

  @property({
    type: 'object',
  })
  metDataUpdateInfo?: MetDataUpdateInfo; //This will keep the information about collection last meta update, last calculation start date and whether ongoing calculation

  @property({
    type: 'array',
    itemType: 'string',
  })
  allowedUserIdList?: ObjectId[]; //This will keep the list of users who can access the metadata object

  @property({
    type: 'object',
  })
  graphDataGenerateInfo?: OngoingGraphDataInfo;

  @property({
    type: 'string', // type of the file "pdf", "jpg", "csv", etc...
  })
  fileType?: string;

  // @property({
  //   type: 'array',
  //   itemType: 'string',
  // })
  // availableEmbeddingModels?: string[];

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<MetaData>) {
    super(data);
  }
}

export interface MetaDataRelations {
  // describe navigational properties here
}

export type MetaDataWithRelations = MetaData & MetaDataRelations;

export interface OngoingGraphDataInfo {
  isGraphDataGenerationOngoing: boolean;
  graphDataCalculationStartedAt: Date;
  graphDataCalculationFinishedAt: Date;
}

export interface QueryGraphData {
  graphId: string;
  isCronJobDeletable: boolean;
  coordinates: number[];
  createdAt: Date;
}

export interface ExplorerDefaultViewRequest {
  pageIndex: number;
  pageSize: number;
  query: string;
  filterData: ExplorerFilterV2;
  referenceImage: string;
  sortBy: number;
}

export interface ExplorerFilter {
  contentType: ContentType;
  filterBy?: FrameVerificationStatus[];
  date?: {fromDate: Date; toDate: Date};
}

export interface ExploreObjectListRequest extends Omit<ExplorerDefaultViewRequest, 'filterData' | 'sortBy'> {
  contentType: ContentType;
  filterData: ExplorerFilterV2;
  sortBy?: SortObject;
  embeddingSelection?: EmbeddingSelectionObject;
}

export interface SortObject {
  sortByField: ExploreSortBy;
  sortOrder?: SortOrder;
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export interface ExplorerFilterV2 {
  annotationTypes?: FrameVerificationStatus[];
  tags?: string[];
  metadata?: {[field: string]: string[]};
  labels?: string[];
  date?: {fromDate: Date; toDate: Date};
  keywords?: string[];
  categories?: string[];
}

export interface ExplorerDefaultViewResponse {
  // videos: CountWithItemObject;
  // videoCollections: CountWithItemObject;
  // images: CountWithItemObject;
  // imageCollections: CountWithItemObject;
  // datasets: CountWithItemObject;
  // others: CountWithItemObject;
  // otherCollections: CountWithItemObject;
  count: number;
  itemList: ItemObject[];
}

export interface ExplorerCollectionViewRequest {
  pageIndex: number;
  pageSize: number;
  query: string;
  filterData: ExplorerFilterV2;
  referenceImage: string;
  sortBy: number;
}

export interface ExplorerCollectionViewResponse {
  count: number;
  itemList: ItemObject[];
}

export interface CountWithItemObject {
  count?: number;
  itemList?: ItemObject[];
}

export interface ItemObject extends Partial<MetaData> {
  verificationStatusList: string[];
  datasetProjectList?: {id: string; name: string}[];
}

export enum VerificationStatusFrontendConstants {
  RAW = 'raw',
  MACHINE_ANNOTATED = 'machineAnnotated',
  VERIFIED = 'verified',
}

export enum VerificationStatusFrontendConstantsInDetailView {
  RAW = 'Raw Data',
  MACHINE_ANNOTATED = 'Machine Annotated',
  VERIFIED = 'Human Annotated',
}

export enum FrameVerificationStatus {
  RAW = 0,
  MACHINE_ANNOTATED = 1,
  VERIFIED = 2,
}

export interface VerificationStatusCount {
  raw: number;
  machineAnnotated: number;
  verified: number;
}

export interface ExplorerDefaultViewDetailsRequest {
  objectIdList: string[];
  query: string;
  filterData: ExplorerFilterV2;
  objectStatus?: OBJECT_STATUS;
}

export interface ExplorerDefaultViewDetailsResponse {
  totalSize: number | 'N/A';
  objectStatus?: OBJECT_STATUS;
  contentType: ContentType;
  details: {
    items: {
      totalCount: number | 'N/A';
      details: {
        videos?: number;
        videoCollections?: number;
        images?: number;
        imageCollections?: number;
        datasets?: number;
        otherCollections?: number;
        others?: number;
      };
    };
    frames: {
      totalCount: number | 'N/A';
      details: {
        raw?: number;
        machineAnnotated?: number;
        verified?: number;
      };
    };
    labels: {
      totalCount: number | 'N/A';
      details: {
        labelList: LabelCountInfo[];
      };
    };
  };
}

export interface LabelCountInfo {
  label: string;
  labelText: string;
  count: number;
}

export interface DatasetSplitCount {
  split: {
    splitCount: number;
    splitType: DatasetSplitType;
  }[];
}

export enum DatasetSplitType {
  TRAINING = 1,
  VALIDATION = 2,
  TESTING = 3,
}

export enum ContentType {
  UNSUPPORTED = -1,
  ALL = 0,
  VIDEO = 1,
  IMAGE = 2,
  DATASET = 3,
  VIDEO_COLLECTION = 4,
  IMAGE_COLLECTION = 5,
  OTHER = 6,
  OTHER_COLLECTION = 7,    
}

// export enum ContentType {
//   UNSUPPORTED = -1,
//   VIDEO = 1,
//   IMAGE = 2,
//   DATASET = 3,
//   VIDEO_COLLECTION = 4,
//   IMAGE_COLLECTION = 5,
//   OTHER = 6,
//   OTHER_COLLECTION = 7,
// }

export enum ContentTypeFrontendConstants {
  VIDEO = 'videos',
  IMAGE = 'images',
  DATASET = 'datasets',
  VIDEO_COLLECTION = 'videoCollections',
  IMAGE_COLLECTION = 'imageCollections',
  OTHER = 'others',
  OTHER_COLLECTION = 'otherCollections',
}

export enum ContentTypeOverviewTabIndexFrontendConstants {
  OTHER = 0,
  VIDEO = 1,
  IMAGE = 2,
}

export interface AnnotationProjectList {
  name: string; //project name
  id: string | ObjectId; //project id
}

export interface CurationProjectList {
  name: string; //project name
  id: string | ObjectId; //project id
}

export interface DatasetVersionList {
  datasetVersionId?: string | ObjectId;
  datasetSplitType?: DatasetSplitType;
  datasetMetaId?: string | ObjectId;
  datasetGroupId: string | ObjectId;
  datasetGroupName: string;
  isNew?: boolean;
}

export interface MetaDataStudioResponseType extends Partial<MetaData> {
  existingFrameIndexes?: number[];
}

export interface MetaWithMetaUpdatesFilter {
  collectionId: string; //if user clicked on image, then this field will be the actual collection id of the image, if user clicked on video, then this field will be the frameCollectionId of the video
  navigatedCollectionId?: string; // if the user is inside a collection view, its id will be the navigatedCollectionId
  contentType: ContentType; //object type of user clicked metaObject
  frameWindow: {
    start: number;
    end: number;
  };
  query?: string;
  filterData?: ExplorerFilterV2;
  sortBy?: SortObject;
  referenceImage?: string;
  modelName?: string;
  scoreThreshold?: number;
  embeddingSelection?: EmbeddingSelectionObject;
}

export interface MetaWithMetaUpdates extends Partial<MetaData> {
  frameId?: number;
  annotationObjects?: Partial<AnnotationObject>[];
  groundTruth?: MetaUpdatesLabelGroup[];
  modelRuns?: MetaUpdatesLabelGroup[];
}

export enum MetaObjectDetailViewType {
  singleView = 0,
  multiView = 1,
}

export interface DatasetUpdateFormat {
  datasetSelectionTag: string;
  datasetGroupId: string;
  datasetVersionId: string;
  datasetName: string;
  teamId: string;
}

export enum Explore_API_TYPE {
  LIST = 1,
  COUNT = 2,
}

export interface ObjectCount {
  _id: null;
  count: number;
  size: number;
  rawSum: number;
  machineAnnotatedSum: number;
  verifiedSum: number;
}

export interface DatasetLabelAggregate {
  count: number;
  mainLabel: string;
  labels?: {
    labelText: string;
    type: SystemLabelType;
  }[];
}

export interface OperationList {
  operationId: ObjectId;
  operationMode: OperationMode;
  operationName: string;
  labelList: LabelList[];
}

export interface LabelList {
  label: string;
  count: number;
}

export const defaultExplorerFilter = {
  contentType: ContentType.ALL,
};

export enum OBJECT_STATUS {
  TRASHED = 1, // trashed
  ACTIVE = 2, // ready to use any operation (create dataset, studio project, etc)
  ACCESSED_FAILED = 6, //This apply if the presigned-url is not accessible
  MEDIA_PROCESSING_FAILED = 8, //This apply if the media processing is failed
  MEDIA_PROCESSING_PENDING = 10, //This apply for both ContentType video and image to process media
}

export enum QueryOptionType {
  TAGS = 1,
  CUSTOM_META = 2,
}

export interface TrashDefaultViewRequest {
  pageIndex: number;
  pageSize: number;
  searchKey: string;
  contentType: ContentType;
  filterData: ExplorerFilterV2;
}

export interface TrashItemListViewResponse {
  trashItemList: ItemObject[];
}

export interface TrashItemCountViewResponse {
  trashItemCount: number;
}

export interface TrashRestoreRequest {
  isAllSelected: boolean;
  objectIdList: string[];
  searchKey: string;
  contentType: ContentType;
  filterData: ExplorerFilterV2;
}

export interface TrashDeleteRequest extends TrashRestoreRequest {
  otp: string;
  type: number;
}

export interface AugmentationType {
  id: string;
  property: {
    id: string;
    values: any;
  };
}

export interface AugmentedInfoWithMetaList {
  augmentedFrameDataId: string;
  augmentationType: Augmentation;
  datasetVersionId: string;
  datasetGroupId: string;
  datasetGroupName: string;
  images: AugmentedImageMetaObject[];
}

export interface Augmentation {
  id: string;
  description?: string;
  properties?: AugmentationProperty[];
  thumbnailUrl?: string;
}

export interface AugmentationProperty {
  id: string;
  values: any[];
}

export interface AugmentedImageMetaObject {
  objectKey: string;
  sourceObjectKey: string;
  augmentationType: string;
  augmentationSettings: AugmentationSettings;
}

export interface metaFieldInput {
  metaFieldId: ObjectId;
  isMandatory: boolean;
}

export enum CollectionType {
  RAW_UPLOAD = 1,
  AUGMENTED_UPLOAD = 2,
  VIDEO_FRAME_COLLECTION = 3,
  VIRTUAL = 4,
}

export enum OptionListType {
  UPLOAD_DATA = 1,
  ADD_META = 2,
  ADD_TAGS = 3,
  COPY_LINK = 4,
  TRASH = 5,
  REMOVE = 6,
  RESTORE = 7,
  DELETE = 8,
  SIMILAR_SEARCH = 9,
  SHARE = 10,
}

// export interface CollectionHeadOnlyMeta {
//   Tags: string[];
//   customMeta: {
//     [k: string]: any;
//   };
// }

export interface SDKMetaUpdateInput {
  file: string;
  metadata: Record<string, string | string[]>;
}

export interface SDKMetaUpdateRequestBody {
  metadata: SDKMetaUpdateInput[];
  bucketName?: string;
  jobId?: string;
  collectionId?: string;
}

export interface metaUpdates {
  tags: Set<string>;
  customFields: Set<string>;
}

export interface AugmentationSettings {
  augmentationValue: number;
}

export interface EmbeddingModel {
  modelName: string;
  dimension: number;
}

export enum ExploreSortBy {
  DATE_MODIFIED = 1,
  DATE_CREATED = 2,
  NAME = 3,
  SIZE = 4,
  SIMILARITY_SCORE = 5,
  VIDEO_INDEX = 6,
  TEXT_SCORE = 7,
  NOT_APPLICABLE = -1,
}

export enum ExploreSortOrder {
  ASCENDING = 'ASC',
  DESCENDING = 'DESC',
}

export interface SimilarArray {
  referenceObjectKey: string;
  modelName: string;
  createdAt: Date;
  scoreThreshold: number;
}

export interface MetaFieldAndArraySummaryFormat {
  _id: string;
  values: {
    value: string;
    count: number;
  }[];
  count: number;
}

export interface MetaFieldAndTagAnalyticsLabelwiseFormat {
  _id: string;
  labelText?: string;
  keys: MetaFieldAndTagAnalyticsLabelwiseKeysFormat[];
  labelFrameCount: number;
  labelLabelCount: number;
}

export interface MetaFieldAndTagAnalyticsLabelwiseKeysFormat {
  key: string;
  values: MetaFieldAndTagAnalyticsLabelwiseValuesFormat[];
  keyFrameCount: number;
  keyLabelCount: number;
  _id: string;
}

export interface MetaFieldAndTagAnalyticsLabelwiseValuesFormat {
  value: string;
  valueFrameCount: number;
  valueLabelCount: number;
}

export interface LabelAnalyticsHeadCountFormat {
  _id: string;
  labelCount: number;
  frameCount?: number;
}

export interface LabelAnalyticsHeadCountChartFormat extends LabelAnalyticsHeadCountFormat {
  labelText: string;
  percentage: number;
}

export interface DetailTabGetRes {
  tags: string[];
  metaData: DetailTabKeyVal[];
  frames: DetailTabKeyVal[];
  labels: DetailTabKeyVal[];
  labelCount: number;
  metaDataCount: number;
  tagCount: number;
  frameCount: number;
}

// export interface DetailTabGetResV2 {
//   tags: {list: string[]; count: number};
//   metaData: {list: DetailTabKeyVal[]; count: number};
//   frames: {list: DetailTabKeyVal[]; count: number};
//   labels: {list: DetailTabKeyVal[]; count: number};
// }

export interface DetailTabKeyVal {
  key: string;
  value: string;
  fieldType?: InputFieldTypes;
  options?: {valueId: string; value: string}[];
  fieldId?: string;
}

export interface FormatMetaDataFieldAndTagSummaryRes
  extends Pick<DetailTabGetRes, 'tags' | 'metaData' | 'tagCount' | 'metaDataCount'> {}
export interface DetailTabKeyValAllDataFormat extends DetailTabKeyVal {
  fieldType?: InputFieldTypes;
  options?: {valueId: string; value: string}[];
  fieldId?: string;
}

export interface ExplorerDetailTabMetaInfo {
  collectionName?: string;
  collectionId?: string;
  size?: string;
  frames?: string;
  updatedAt?: string;
  createdAt?: string;
  labelCount?: string;
}

export interface RequiredInfoForDetailINDynamicQueryRes {
  verificationStatus: VerificationStatusCount;
  labelList: LabelList[];
  metaDataFieldSummary: MetaFieldAndArraySummaryFormat[];
  frameCount: number;
}

export interface RequiredTagsAndMetaDataInDynamicQueryRes
  extends Omit<RequiredInfoForDetailINDynamicQueryRes, 'verificationStatus' | 'frameCount'> {}

export interface TagsAndMetaDataConfig {
  allMetaData: boolean;
  isTagList: boolean;
}

export interface AnalyticDataRes {
  modelName: string;
  precision: string | number;
  recall: string | number;
  f1Score: string | number;
  labelStats: AnalyticLabelStats[];
}

export interface AnalyticLabelStats {
  label: string;
  precision: string | number;
  recall: string | number;
  f1Score: string | number;
}

export interface SampleGraphDataFormat {
  coordinates: number[]; // [x, y]
  objectKey: string;
}

export interface FrontendFeatureGraphDataFormat {
  graphProgress: number;
  graphInfo: GraphDetail;
  data: FrontendResponseCoordinateDataFormat[];
}

export interface FrontendResponseCoordinateDataFormat {
  x: number[];
  y: number[];
}
export interface GraphDetail {
  embeddingGenerationMethod: string;
  dimensionReductionMethod: string;
  createdDate: string;
  graphId: string;
  isEmbeddingAvailable: boolean;
}

export interface FrontendRequestCoordinateDataFormat {
  x0: number;
  x1: number;
  y0: number;
  y1: number;
}

export interface GetEmbeddingAggregationForSelectionRes {
  $lookup?: Record<string, any>;
  $unwind?: string;
}

export interface RemoveObjectsFromCollectionsAgg extends Pick<MetaData, 'collectionId' | 'name'> {
  _id: string;
}

export interface MetDataUpdateInfo {
  isOngoing: boolean;
  calculationStartAt: Date;
  metaUpdateAt: Date;
}

export interface RemoveCollectionIfPossibleRes extends SuccessResponse {
  removeUnavailable: boolean;
}

export interface GetJobPartFailedReasonListForObjectRemoveRes {
  jobPartFailedReasonList: string[];
  failedRemovedFileNameList: string[];
  totalObjectCount: number;
}

export interface MergeCollectionsBody {
  selectionId: string;
  collectionName: string;
}

export const MERGE_COLLECTION_BODY: RequestBodyObject = {
  description: 'Merge collection body',
  required: true,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        additionalProperties: false,
        required: ['selectionId', 'collectionName'],
        properties: {
          selectionId: {
            type: 'string',
            not: {enum: ['']},
          },
          collectionName: {type: 'string'},
        },
      },
    },
  },
};

export interface UpdateJobConfig {
  isAwait: boolean;
  sessionId?: string;
  jobName?: string;
  userId?: string;
  userName?: string;
  teamId?: string;
  virtualCollectionJobId?: string;
  objectRemoveJobId?: string;
}

export interface GetTagsAndCustomMetaDataOfSelectedCollectionsRes {
  tags: string[];
  customMeta: {[k: string]: any};
}

export interface RemoveObjectsJobData {
  sessionId: string;
  jobName: string;
}
