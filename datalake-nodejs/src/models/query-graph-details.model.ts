/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, graphId, isCronJobDeletable, progress, etc. and other constraints.
 */

/**
 * @class QueryGraphDetails
 * purpose of QueryGraphDetails model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, graphId, isCronJobDeletable, progress, etc. and other constraints.
 * <AUTHOR>
 */
import {Entity, model, property} from '@loopback/repository';

@model()
export class QueryGraphDetails extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  graphId?: string;

  @property({
    type: 'string',
  })
  jobId?: string;

  @property({
    type: 'number',
  })
  status?: GraphStatus;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'boolean',
  })
  isPending?: boolean;

  @property({
    type: 'boolean',
  })
  isCronJobDeletable: boolean;

  @property({
    type: 'number',
  })
  progress: number;

  @property({
    type: 'number',
  })
  totalCount?: number;

  @property({
    type: 'number',
  })
  generatedCount?: number;

  @property({
    type: 'boolean',
  })
  isEmbeddingAvailable?: boolean; // if graph has embedding data this will be true

  constructor(data?: Partial<QueryGraphDetails>) {
    super(data);
  }
}

export interface QueryGraphDetailsRelations {
  // describe navigational properties here
}

export type QueryGraphDetailsWithRelations = QueryGraphDetails & QueryGraphDetailsRelations;

export enum GraphStatus {
  EMBEDDING_GENERATION = 1,
  PCA_GENERATION = 2,
}

export enum GraphDataAvailability {
  PARTIAL_AVAILABLE = 1,
  NOT_AVAILABLE = 2,
  AVAILABLE = 3,
  IN_PROGRESS = 4,
}
