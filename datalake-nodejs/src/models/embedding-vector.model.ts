/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class EmbeddingVector
 * purpose of EmbeddingVector model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class EmbeddingVector extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  objectKey?: string;

  @property({
    type: 'string',
  })
  modelName?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  embeddings?: number[];

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'date',
  })
  updatedAt?: Date;

  @property({
    type: 'object',
  })
  embeddingInfo?: EmbeddingInfoObject;

  [prop: string]: any;

  constructor(data?: Partial<EmbeddingVector>) {
    super(data);
  }
}

export interface EmbeddingVectorRelations {
  // describe navigational properties here
}

export interface EmbeddingInfoObject {
  npyFileUniqueName: string;
}

export type EmbeddingVectorWithRelations = EmbeddingVector & EmbeddingVectorRelations;
