/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, type, description and other constraints.
 */

/**
 * @class SystemChange
 * purpose of SystemLabel model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, type, description and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class SystemChange extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'number',
    required: true,
  })
  type: SystemChangeType;

  @property({
    type: 'object',
  })
  change: any;

  @property({
    type: 'object',
  })
  beforeChange: any;

  @property({
    type: 'number',
  })
  changeType: ChangeType;

  @property({
    type: 'date',
  })
  changeAt: Date;

  @property({
    type: 'string',
  })
  changeBy: string;

  @property({
    type: 'string',
  })
  teamId: string;



  constructor(data?: Partial<SystemChange>) {
    super(data);
  }
}

export interface SystemChangeRelations {
  // describe navigational properties here
}

export type SystemChangeWithRelations = SystemChange & SystemChangeRelations;

export interface SystemChangeDetails {
  type: SystemChangeType,
  changeType: ChangeType,
  beforeChange: any;
  change: any;
  changeAt?: Date;
  teamId: string,
  changeBy: string
}

export enum SystemChangeType {
  LABEL = 1
}

export enum ChangeType {
  DELETE = 1,
  EDIT = 2
}
