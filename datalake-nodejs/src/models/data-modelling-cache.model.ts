/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, connectionId, tableName, parentTable, childTable, key_column_mapping, relationship_type, type, data, keyCombination, etc. and other constraints.
 */

/**
 * @class DataModellingCache
 * purpose of DataModellingCache model is to keep the data modelling cache which used to find relationships between tables
 * @description model class describes business domain objects and defines a list of properties with id, connectionId, tableName, parentTable, childTable, key_column_mapping, relationship_type, type, data, keyCombination, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class DataModellingCache extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  connectionId: string;

  @property({
    type: 'string',
  })
  tableName?: string;

  @property({
    type: 'string',
  })
  parentTable?: string;

  @property({
    type: 'string',
  })
  childTable?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  key_column_mapping?: object[];

  @property({
    type: 'string',
  })
  relationship_type?: string;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'any',
  })
  data?: any;

  @property({
    type: 'array',
    itemType: 'string',
  })
  keyCombination?: string[];

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<DataModellingCache>) {
    super(data);
  }
}

export interface DataModellingCacheRelations {
  // describe navigational properties here
}

export type DataModellingCacheWithRelations = DataModellingCache & DataModellingCacheRelations;
