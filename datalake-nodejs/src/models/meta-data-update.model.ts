/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, objectKey, operationType, operationMode, etc. and other constraints.
 */

/**
 * @class MetaDataUpdate
 * purpose of MetaDataUpdate model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, objectKey, operationType, operationMode, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';

@model({settings: {strict: false}})
export class MetaDataUpdate extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  objectKey: string;

  @property({
    type: 'number',
    required: true,
  })
  operationType: OperationType;

  @property({
    type: 'number',
    required: true,
  })
  operationMode: OperationMode;

  @property({
    type: 'string',
    required: true,
  })
  operationId: string;

  @property({
    type: 'date',
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  updatedAt: Date;

  @property({
    type: 'string',
  })
  projectId?: string;

  @property({
    type: 'string',
  })
  operationName?: string;

  @property({
    type: 'string',
  })
  teamId?: ObjectId;

  @property({
    type: 'string',
  })
  collectionId?: ObjectId;

  @property({
    type: 'object',
  })
  analytics?: Analytics;

  @property({
    type: 'boolean',
  })
  analyticsPending?: boolean;

  @property({
    type: 'string',
  })
  taskId?: string;

  @property({
    type: 'string',
  })
  modelId?: string;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  annotationObjects?: AnnotationObject[];

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<MetaDataUpdate>) {
    super(data);
  }
}

export interface MetaDataUpdateRelations {
  // describe navigational properties here
}

export type MetaDataUpdateWithRelations = MetaDataUpdate & MetaDataUpdateRelations;

export enum OperationType {
  ANNOTATION = 1,
  CURATION = 2,
}

export enum MetaUpdateProjectType {
  ANNOTATION = 1,
  CURATION = 2,
  DATASET = 3,
}

export enum OperationMode {
  HUMAN = 1,
  AUTO = 2,
}

export enum AnnotationTypeString {
  HUMAN = 'human',
  AUTO = 'machine',
  ALL = 'all',
}

export interface AnnotationObject {
  id?: string;
  points?: number[][];
  boxBoundariesAndDimensions?: AnnotationDataBoxBoundariesAndDimensions;
  pointCoordinates?: AnnotationDataPointCoordinates[];
  shapeId?: number;
  type?: string;
  color?: string;
  label?: {
    label?: string;
    labelText?: string;
    key?: string;
    // attributeValues?: {[k: string]: string;},
    attributeValues?: {[k: string]: AttributeValues[]};
    color?: string;
  };
  createdAt?: Date;
  annotatorId?: ObjectId;
  confidence?: number;
  metadata?: AdditionalProperties;
  objectInfo?: AnnotationObjectInfo;
  operationId?: string;
}

export interface AttributeValues {
  value: string;
  confidence?: number;
  metadata?: AdditionalProperties;
}

export interface ExtendedAnnotationObject extends Partial<AnnotationObject> {
  operationMode?: OperationMode;
}

export interface AnnotationDataBoxBoundariesAndDimensions {
  x: number;
  y: number;
  w: number;
  h: number;
}

export interface AnnotationDataPointCoordinates {
  x: number;
  y: number;
}

export interface AdditionalProperties {
  [k: string]: any;
}

export interface UpdateOperationDataRequestBody {
  metaUpdatesArray: MetaDataUpdateObject[];
}

export interface MetaDataUpdateObject {
  data: Partial<MetaDataUpdate>;
  objectKey?: string;
  storagePath?: string;
  bucketName?: string;
  jobId?: string;
  fileName?: string;
}

// Below type definition can be used to make a type partial except perticular fields
export type PartialRequired<T, TRequired extends keyof T> = Partial<T> & Pick<T, TRequired>;
export type PartialRequiredMetaDataUpdate = PartialRequired<MetaDataUpdate, 'objectKey'>;
//if you need to make multiple fields required, then define as, export type PartialRequiredMetaDataUpdate = PartialRequired<MetaDataUpdate, 'objectKey'|'id'>

export interface MetaUpdatesToKeyMap {
  [k: string]: MetaUpdatesLabelGroup[];
}

export interface MetaUpdatesToKeyMaps {
  groundTruthToKeyMap?: MetaUpdatesToKeyMap;
  modelRunsToKeyMap?: MetaUpdatesToKeyMap;
}

export interface MetaUpdatesLabelGroup {
  labelCount: number;
  labelList: {
    label: string;
    labelText: string;
    count: number;
    annotationObjects: Partial<AnnotationObject>[];
  }[];
  operationId?: string;
  operationName?: string;
}

export interface AnnotationObjectInfo {
  id?: string;
  confidence?: number;
  metaData?: AdditionalProperties;
  operationId?: string;
  attributes?: {[k: string]: AttributeValues[]};
}

export interface AnnotationProjectInfo {
  id: string;
  name: string;
  projectStatus: number;
  statsSummary: AnnotationProjectStats;
  updatedAt: Date;
}

export interface AnnotationProjectStats {
  lastUpdatedTime: Date;
  totalTaskCount: number;
  completedTaskCount: number;
  inprogressTaskCount: number;
  totalAnnotatedCount: number;
  collectedFramesCount: number;
  etaDaysCount: number;
  annotationStartDate?: Date;
}

export interface MetaUpdateProjectInfo {
  projectId: string;
  projectType: MetaUpdateProjectType;
  projectName: string;
  // taskCount: number,
  count: number;
  status?: number;
  updatedAt: Date;
}

export interface Analytics {
  operationId: string;
  truePositive: number;
  falsePositive: number;
  falseNegative: number;
  precision: number | string;
  recall: number | string;
  f1Score: number | string;
  labelWiseAnalytics: LabelAnalytics[];
}

export interface LabelAnalytics {
  label: string;
  truePositive: number;
  falsePositive: number;
  falseNegative: number;
  precision: number | string;
  recall: number | string;
  f1Score: number | string;
}

export interface AnalyticsUpdateAgg {
  truePositive: number;
  falsePositive: number;
  falseNegative: number;
  operationId: string;
  labelData: LabelDataInAgg[];
}

export interface LabelDataInAgg {
  label: string;
  truePositive: number;
  falsePositive: number;
  falseNegative: number;
}
