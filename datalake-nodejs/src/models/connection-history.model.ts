/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, ConnectionHistory name, type and configuration
 */

/**
 * @class ConnectionHistory
 * purpose of ConnectionHistory model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, ConnectionHistory name, type and configuration
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {SyncJobStatus} from './connection.model';

@model({settings: {strict: false}})
export class ConnectionHistory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'string',
  })
  connectionId?: string;

  @property({
    type: 'date',
  })
  updatedAt?: Date;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  status?: SyncJobStatus;

  @property({
    type: 'number',
  })
  itemSize?: number;

  @property({
    type: 'number',
  })
  itemCount?: object;

  [prop: string]: any;

  constructor(data?: Partial<ConnectionHistory>) {
    super(data);
  }
}

export interface ConnectionHistoryRelations {
  // describe navigational properties here
}

export type ConnectionHistoryWithRelations = ConnectionHistory & ConnectionHistoryRelations;
