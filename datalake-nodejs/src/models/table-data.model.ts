import {Entity, model, property} from '@loopback/repository';

export interface TableField {
  name: string;
  dataType: string;
  description?: string;
  isVerified?: boolean;
  lastModifiedAt?: Date;
  verifiedBy?: string;
  createdAt?: Date;
  isVisible?: boolean;
  isVisibleToAI?: boolean;
  mappedName?: string; // mapped name for the field to be used in AI interfaces
  fieldStats?: any;
  availableValues?: any;
  distinctValuesCount?: number;
  sampleData?: string[];
}

export interface TableInfo {
  tableName: string;
  mappedName?: string;
  fields: TableField[];
  subFields?: TableField[];
  createdAt: Date;
  modifiedBy: string;
  modifiedDate: Date;
  fieldsCount: number;
  isVisible: boolean;
  isVerified: boolean;
  description: string;
  table_overview?: string;
}

export interface TableDataResponse {
  isSuccess: boolean;
  message?: string;
  data?: {
    _id: string;
    tableName: string;
    mappedName?: string;
    createdAt: Date;
    modifiedBy: string;
    lastModified: Date;
    fieldsCount: number;
    visibility: boolean;
    verified: boolean;
  }[];
  total?: number;
}

interface KeyColumnMapping {
  parent_column: string;
  child_column: string;
}

export interface TableRelationship {
  child_table: string;
  parent_table: string;
  key_column_mapping: KeyColumnMapping[];
  relationship_type: 'one-to-many' | 'one-to-one' | 'many-to-one';
}

@model()
export class TableData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  connectionId: string;

  @property({
    type: 'string',
    required: true,
  })
  tableName: string;

  @property.array(Object, {
    type: 'object',
    required: true,
  })
  fields: TableField[];

  @property.array(Object, {
    type: 'object',
    required: false,
  })
  relationships?: TableRelationship[];

  @property.array(Object, {
    type: 'object',
    required: false,
  })
  unique_fields_combinations?: string[][];

  @property.array(Object, {
    type: 'object',
  })
  subFields?: TableField[];

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'string',
    required: true,
  })
  modifiedBy: string;

  @property({
    type: 'date',
    required: true,
  })
  modifiedDate: Date;

  @property({
    type: 'number',
    required: true,
  })
  fieldsCount: number;

  @property({
    type: 'boolean',
    required: true,
  })
  isVisible: boolean; // this flag is used in raw data dictionary to identify whether it should be used to generate layernext data dictionary

  @property({
    type: 'boolean',
    required: false,
  })
  isVisibleToAI: boolean; // this flag is used in raw data dictionary to identify whether it is used in generated layernext data dictionary

  @property({
    type: 'boolean',
    required: true,
  })
  isVerified: boolean;

  @property({
    type: 'string',
    required: false,
  })
  description: string;

  @property({
    type: 'string',
    required: false,
  })
  table_overview?: string;

  @property({
    type: 'string',
    required: false,
  })
  mappedName?: string; // mapped name for the table to be used in AI interfaces

  constructor(data?: Partial<TableData>) {
    super(data);
  }
}

export interface TableDataRelations {
  // describe navigational properties here
}

export type TableDataWithRelations = TableData & TableDataRelations;

export interface DataSourceMetaDataResponse {
  isSuccess: boolean;
  message?: string;
  data?: {
    tableName: string;
    tableDescription?: string;
    tableOverview?: string;
    fields: {
      name: string;
      dataType: string;
      description?: string;
      availableValues?: any;
      distinctValuesCount?: number;
    }[];
    relationships?: TableRelationship[];
    uniqueFieldsCombinations?: string[][];
  }[];
}
