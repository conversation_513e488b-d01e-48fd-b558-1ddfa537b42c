import {Entity, model, property} from '@loopback/repository';

@model()
export class KnowledgeTree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  parent: string;

  @property({
    type: 'array',
    itemType: 'object',
    required: true,
  })
  nodes: object[];

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  constructor(data?: Partial<KnowledgeTree>) {
    super(data);
  }
}

export interface KnowledgeTreeRelations {
  // describe navigational properties here
}

export type KnowledgeTreeWithRelations = KnowledgeTree & KnowledgeTreeRelations;
