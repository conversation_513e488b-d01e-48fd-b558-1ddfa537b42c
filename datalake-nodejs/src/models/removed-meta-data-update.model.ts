/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, objectKey, operationType, operationMode, etc. and other constraints.
 */

/**
 * @class RemovedMetaDataUpdate
 * purpose of RemovedMetaDataUpdate model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, objectKey, operationType, operationMode, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';
import {Analytics, AnnotationObject, OperationMode, OperationType} from './meta-data-update.model';

@model({settings: {strict: false}})
export class RemovedMetaDataUpdate extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  objectKey: string;

  @property({
    type: 'number',
    required: true,
  })
  operationType: OperationType;

  @property({
    type: 'number',
    required: true,
  })
  operationMode: OperationMode;

  @property({
    type: 'string',
    required: true,
  })
  operationId: string;

  @property({
    type: 'date',
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  updatedAt: Date;

  @property({
    type: 'string',
  })
  projectId?: string;

  @property({
    type: 'string',
  })
  teamId?: ObjectId;

  @property({
    type: 'object',
  })
  analytics?: Analytics;

  @property({
    type: 'boolean',
  })
  analyticsPending?: boolean;

  @property({
    type: 'string',
  })
  taskId?: string;

  @property({
    type: 'string',
  })
  modelId?: string;

  @property({
    type: 'array',
    itemType: 'object',
    default: []
  })
  annotationObjects?: AnnotationObject[];

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<RemovedMetaDataUpdate>) {
    super(data);
  }
}

export interface RemovedMetaDataUpdateRelations {
  // describe navigational properties here
}

export type RemovedMetaDataUpdateWithRelations = RemovedMetaDataUpdate & RemovedMetaDataUpdateRelations;

