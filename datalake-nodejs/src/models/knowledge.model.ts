/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Model class describes business domain objects and defines a list of properties with id, key, name, etc. and other constraints.
 */

/**
 * @class Knowledge
 * @description Knowledge model keeps a collection of knowledge in Data Lake
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class Knowledge extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'object',
    required: true,
  })
  content: object;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: string;

  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  tables: string[];

  @property({
    type: 'string',
    required: true,
  })
  knowledgeSourceId: string;

  constructor(data?: Partial<Knowledge>) {
    super(data);
  }
}

export interface KnowledgeRelations {
  // describe navigational properties here
}

export type KnowledgeWithRelations = Knowledge & KnowledgeRelations;
