/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, teamId, queryKey, etc. and other constraints.
 */

/**
 * @class QueryOption
 * purpose of QueryOption model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, teamId, queryKey, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class QueryOption extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  // Define well-known properties here
  @property({
    type: 'string',
  })
  teamId: string;

  @property({
    type: 'string',
  })
  collectionId: string;

  @property({
    type: 'string',
    required: true,
  })
  keyGroup: SearchQueryRootGroup | string;

  @property({
    type: 'boolean',
    required: true,
  })
  isRootGroup: boolean;

  @property({
    type: 'any',
  })
  key?: string | number;

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  operators: string[];

  @property({
    type: 'number',
  })
  ignoredSearchType?: IgnoredSearchType; // use to filter search option according to collection type (EX: no need of frameRate suggetion for image collections)

  constructor(data?: Partial<QueryOption>) {
    super(data);
  }
}

export interface QueryOptionRelations {
  // describe navigational properties here
}

export type QueryOptionWithRelations = QueryOption & QueryOptionRelations;

export interface BulkUpdateOneQueryOptionFormat {
  updateOne: {
    filter: Partial<QueryOption>;
    update: {
      $set: Partial<QueryOption>;
    };
    upsert: boolean;
  };
}

export enum QueryOperators {
  eq = '=',
  neq = '!=',
  gt = '>',
  gte = '>=',
  lt = '<',
  lte = '<=',
  reg = '~',
}

export enum MongodbQueryOperators {
  eq = '$eq',
  neq = '$ne',
  gt = '$gt',
  gte = '$gte',
  lt = '$lt',
  lte = '$lte',
  reg = '$regex',
}

export enum QueryCombineOperators {
  and = 'AND',
  or = 'OR',
}

export enum QueryGroupOperators {
  dot = '.',
}

export enum SearchQueryRootGroup {
  METADATA = 'metadata',
  ANNOTATION = 'annotation',
  PREDICTION = 'prediction',
  DATASET = 'dataset',
  ANALYTICS = 'analytics',
}

export enum SearchQuerySubGroup {
  ANNOTATION_PROJECT = 'project',
  ANNOTATION_LABEL = 'label',
  DATASET_PROJECT = 'project',
  EMBEDDING_MODEL = 'embeddingModel',
}

export enum IgnoredSearchType {
  GLOBAL_SEARCH = 0,
  ANY_COLLECTION_SEARCH = 1,
  VIDEO_COLLECTION_SEARCH = 2,
  IMAGE_COLLECTION_SEARCH = 3,
}
