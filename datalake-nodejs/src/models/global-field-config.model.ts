/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, and other constraints.
 */

/**
 * @class GlobalFieldConfig
 * purpose of project model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {InputFieldTypes} from './meta-field.model';

@model({settings: {strict: false}})
export class GlobalFieldConfig extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GlobalFieldConfig>) {
    super(data);
  }
}

export interface GlobalFieldConfigRelations {
  // describe navigational properties here
}

export type GlobalFieldConfigWithRelations = GlobalFieldConfig & GlobalFieldConfigRelations;

export enum MetaDataKeyInputType {
  TEXT_LINE = 1,
  TAG = 2,
  TEXT_AREA = 3,
  SELECT_OR_WRITE = 4,
}

export interface MetaDataKeyInputListFormat {
  fieldName: string,
  fieldKey?: string,
  type: MetaDataKeyInputType,
  isEditable?: boolean,
  isDeletable?: boolean,
  isTagDeletable?: boolean,
  isSelected?: boolean,
  isMandatory?: boolean,
  values?: string[] | any,
  fieldId?: string,
  fieldType?: InputFieldTypes,
  options?: {valueId: string, value: string}[],
  suggestions?: string[] | InputFormCollectionListData[] | any,
}

export interface InputFormCollectionListData {name: string, id?: string}
