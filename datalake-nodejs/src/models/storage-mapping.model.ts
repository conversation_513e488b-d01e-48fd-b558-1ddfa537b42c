/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, collectionStoragePath, isDefaultBucketCrawledCollection, collectionId, etc. and other constraints.
 */

/**
 * @class StorageMapping
 * purpose of StorageMapping model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, collectionStoragePath, isDefaultBucketCrawledCollection, collectionId, etc. and other constraints.
 * <AUTHOR>
 */
import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';

@model()
export class StorageMapping extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: ObjectId;

  @property({
    type: 'string',
    required: true,
  })
  collectionStoragePath: string;

  @property({
    type: 'string',
  })
  collectionId?: ObjectId | undefined;

  @property({
    type: 'boolean',
  })
  isDefaultBucketCrawledCollection?: boolean;

  @property({
    type: 'string',
  })
  teamId?: ObjectId;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  constructor(data?: Partial<StorageMapping>) {
    super(data);
  }
}

export interface StorageMappingRelations {
  // describe navigational properties here
}

export type StorageMappingWithRelations = StorageMapping & StorageMappingRelations;
