/**
 * Copyright (c) 2025 LayerNext, Inc.
 * 
 * all rights reserved.
 * 
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 * 
 * Handle the request related to the model provider
 * 
 */

/**
 * @description This model is used to handle the request related to the model provider
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class ModelProvider extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  provider: string;

  @property({
    type: 'string',
    required: true,
  })
  apiKey: string;

  @property({
    type: 'string',
  })
  url?: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'string',
  })
  createdBy: string;

  @property({
    type: 'date',
    required: true,
  })
  updatedAt: Date;

  @property({
    type: 'string',
  })
  updatedBy: string;

  @property({
    type: 'string',
    required: true,
  })
  userId: string;

  @property({
    type: 'string',
    required: true,
  })
  teamId: string;


  constructor(data?: Partial<ModelProvider>) {
    super(data);
  }
}

export interface ModelProviderRelations {
  // describe navigational properties here
}

export type ModelProviderWithRelations = ModelProvider & ModelProviderRelations;
