import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';

@model()
export class DictionaryData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: ObjectId;

  @property({
    type: 'object',
    required: true,
  })
  data: object;

  constructor(data?: Partial<DictionaryData>) {
    super(data);
  }
}

export interface DictionaryDataRelations {
  // describe navigational properties here
}

export type DictionaryDataWithRelations = DictionaryData & DictionaryDataRelations;
