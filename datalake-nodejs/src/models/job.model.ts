/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class Job
 * purpose of Job model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {RequestBodyObject} from '@loopback/rest';
import {ObjectId} from 'bson';
import {getEnumNumberValues} from '../settings/tools';
import {ContentType} from './meta-data.model';

@model({settings: {strict: false}})
export class Job extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  jobName?: string;

  @property({
    type: 'string',
  })
  jobOwnerId?: string | null;

  @property({
    type: 'string',
  })
  sessionId?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
  })
  jobOwnerName?: string;

  @property({
    type: 'number',
  })
  jobType?: JobType;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'date',
  })
  updatedAt?: Date;

  @property({
    type: 'number',
  })
  progress?: number;

  @property({
    type: 'number',
  })
  status?: JobStatus;

  @property({
    type: 'object',
  })
  subJobs?: SubJobs;

  @property({
    type: 'object',
  })
  jobSpecificDetails?: JobSpecificDetails;

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Job>) {
    super(data);
  }
}

export interface JobRelations {
  // describe navigational properties here
}

export type JobWithRelations = Job & JobRelations;

export interface SubJobs {
  augmentation?: SubJobInfo;
  generation?: SubJobInfo;
  fileUploading?: SubJobInfo;
  inputMetaDataFeedDequeue?: SubJobInfo;
  mediaProcessing?: SubJobInfo;
}

export interface SubJobInfo {
  progress: number;
  totalCount?: number;
  completedCount?: number;
  uploadRemovedCount?: number;
}

export enum JobType {
  fileUpload = 1,
  annotationUpload = 2,
  annotationRemove = 3,
  datasetGeneration = 4,
  annotationProject = 5,
  Trash = 6,
  Restore = 7,
  PermanentDelete = 8,
  DownloadDataset = 9,
  DownloadCollection = 10,
  DownloadProject = 11,
  VirtualCollection = 12,
  ObjectRemove = 13,
  UploadEmbedding = 14,
  GenerateEmbedding = 15,
  GenerateAutoTagging = 16,
  GenerateAutoAnnotation = 17,
  CollectionMerge = 18,
  UnknownJob = -1,
}

export const JobTypeValues = getEnumNumberValues(JobType);

export enum JobStatus {
  inProgress = 1,
  completed = 2,
  queued = 3,
  failed = 4,
}

export const JobStatusValues = getEnumNumberValues(JobStatus);

export interface JobSpecificDetails {
  sessionId?: string;
  startedAt?: Date;
  finishedAt?: Date;
  contentType?: ContentType;
  warning?: string;
  error?: any;

  //file upload
  uploadId?: ObjectId;
  collectionId?: ObjectId;
  allFileList?: string[];
  allFileListCount?: number;
  uploadedFileList?: string[];
  skippedFileList?: string[];
  uploadFailedFileList?: string[];
  uploadedFileListCount?: number;
  uploadFailedFileListCount?: number;
  collectionList?: ObjectId[];
  uploadRemovedFileList?: string[];

  //annotation upload
  annotationUploadTotalCount?: number;
  annotationUploadOperationType?: number;
  annotationUploadOperationMode?: number;
  annotationUploadTryObjectKeys?: string[];
  annotationUploadTryObjectKeysCount?: number;
  annotationUploadFailedObjectKeys?: string[];
  annotationUploadFailedObjectKeysCount?: number;
  annotationUploadAnnotationCount?: number;
  annotationUploadLabelList?: string[];
  annotationUploadCollectionList?: ObjectId[];

  //annotation remove
  annotationRemoveOperationType?: number;
  annotationRemoveOperationMode?: number;
  operationId?: string;

  //dataset creation
  versionId?: string;
  datasetGroupId?: string;
  versionNo?: string;
  datasetMetaId?: string;
  datasetName?: string;

  //project creation
  projectName?: string;
  requiredFps?: string;
  selectionId?: string;
  projectId?: string;
  contentTypeName?: string;
  uploadIdList?: string[];

  //trash and restore
  successCount?: number;
  successFileCount?: number;
  failedFileCount?: number;
  failCount?: number;
  totalCount?: number;
  successFileNameList?: string[];
  failedFileNameList?: string[];

  //
  jobPartFailedReasonList?: string[];
  jobFailedReasonList?: string[];

  exportFormatType?: string;
  errorList?: any[];
  operationIdList?: string[];
  annotationTypeString?: string;
  projectIdList?: string[];
  statusList?: number[];

  //object remove
  objectRemoveSuccessCount?: number; //object count of successfully removed
  objectRemovedFailedCount?: number; //object count of failed to remove
  totalObjectToBeRemovedCount?: number; //total object count to be removed
  removeFailedIdList?: string[]; //object id list of failed to remove
  removeFailedObjectIdList?: string[]; //collection id list of failed to remove
  collectionRemoveWarningCount?: number; //collection id count with warning
  successRemovedFileNameList?: string[]; //file name list of successfully removed
  failedRemovedFileNameList?: string[]; //file name list of failed to remove

  //merge collection
  virtualCollectionJobId?: ObjectId;
  objectRemoveJobId?: ObjectId;
  // embedding upload
  embeddingUploadTotalCount?: number;
  modelName?: string;
  embeddingUploadFailedObjectKeys?: string[];
  embeddingUploadTryObjectKeys?: string[];
  embeddingUploadSuccessCount?: number;

  // embedding generate
  embeddingGenerateTotalCount?: number;
  completedCount?: number;
  affectedCollectionIdList?: ObjectId[];

  // auto tagging generate
  autoTaggingGenerateTotalCount?: number;

  // auto annotation generate
  autoAnnotationGenerateTotalCount?: number;
}

export interface JobFilter {
  fromDate?: string;
  toDate?: string;
  filterBy?: number[];
}

export interface JobListResponse {
  jobId?: string;
  jobName?: string;
  jobType?: string | JobType;
  createdDate?: Date;
  jobStatus?: {
    status?: number;
    value?: any;
  };
  contentType?: ContentType;
  drawerData?: {title: string; value: any; type: any}[];
}

export const GET_JOB_LIST_BODY: RequestBodyObject = {
  description: 'get object list',
  required: true,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: ['searchKey', 'status', 'filterObj'],
        properties: {
          searchKey: {type: 'string'},
          status: {
            type: 'array',
            items: {type: 'number', enum: JobStatusValues},
          },
          filterObj: {
            type: 'object',
            properties: {
              fromDate: {type: 'string', format: 'date-time'},
              toDate: {type: 'string', format: 'date-time'},
              filterBy: {type: 'array', items: {type: 'number', enum: JobTypeValues}},
            },
            additionalProperties: false,
          },
          pageSize: {type: 'number'},
          pageIndex: {type: 'number'},
        },
        additionalProperties: false,
      },
    },
  },
};

export const GET_JOB_COUNT_BODY: RequestBodyObject = {
  description: 'get object list',
  required: true,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: ['searchKey', 'status', 'filterObj'],
        properties: {
          searchKey: {type: 'string'},
          status: {
            type: 'array',
            items: {type: 'number', enum: JobStatusValues},
          },
          filterObj: {
            type: 'object',
            properties: {
              fromDate: {type: 'string', format: 'date-time'},
              toDate: {type: 'string', format: 'date-time'},
              filterBy: {type: 'array', items: {type: 'number', enum: JobTypeValues}},
            },
            additionalProperties: false,
          },
        },
        additionalProperties: false,
      },
    },
  },
};
