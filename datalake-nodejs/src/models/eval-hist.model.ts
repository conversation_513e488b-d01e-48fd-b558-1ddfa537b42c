/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, evalSnapId, evalSetId, question, etc. and other constraints.
 */

/**
 * @class EvalHist
 * purpose of EvalHist model is to define properties and relations with other models
 * This table keep time series data from the EvalSnap table
 * Before update EvalSnap table, its required data is moved here
 * @description model class describes business domain objects and defines a list of properties with id, evalSnapId, evalSetId, question, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {EvalFeedbackType} from '../settings/constants';
import {DifficultyLevel, QuestionEvalStatus, TuneUpUsageStatus} from './eval-snap.model';

@model()
export class EvalHist extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  evalSnapId: string;

  @property({
    type: 'string',
    required: true,
  })
  evalSetRunId: string;

  @property({
    type: 'string',
    required: true,
  })
  question: string;

  @property({
    type: 'string',
    required: true,
  })
  truth: string;

  @property({
    type: 'string',
    required: true,
  })
  evalSetId: string;

  @property({
    type: 'number',
    required: true,
  })
  questionEvalStatus: QuestionEvalStatus;

  @property({
    type: 'number',
    required: true,
  })
  qNum: number;

  @property({
    type: 'string',
  })
  aiAction?: string;

  @property({
    type: 'object',
  })
  aiAnswer?: {
    text?: string;
    csv?: string;
  };

  @property({
    type: 'string',
  })
  conversationId?: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
    required: true,
  })
  updatedAt: Date;

  @property({
    type: 'number',
    required: true,
  })
  difficulty: DifficultyLevel;

  @property({
    type: 'number',
  })
  tuneUpUsageStatus?: TuneUpUsageStatus;

  @property({
    type: 'string',
    default: EvalFeedbackType.GROUND_TRUTH,
  })
  feedbackType?: string;

  @property({
    type: 'date',
  })
  assessedAt?: Date;

  @property({
    type: 'object',
  })
  assessmentResult?: {
    is_data_retrieval_correct: boolean;
    is_business_logic_correct: boolean;
    explanation: string;
    is_correct: boolean;
  };

  @property({
    type: 'number',
  })
  dataDictRevisionNum?: number;

  @property({
    type: 'date',
  })
  tunedUpAt?: Date;

  constructor(data?: Partial<EvalHist>) {
    super(data);
  }
}

export interface EvalHistRelations {
  // describe navigational properties here
}

export type EvalHistWithRelations = EvalHist & EvalHistRelations;
