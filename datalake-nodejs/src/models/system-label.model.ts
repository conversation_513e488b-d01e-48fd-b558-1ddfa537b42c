/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, type, description and other constraints.
 */

/**
 * @class SystemLabel
 * purpose of SystemLabel model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, type, description and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class SystemLabel extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'number',
    required: true,
  })
  type: SystemLabelType;

  @property({
    type: 'number',
  })
  annotationType: AnnotationType;

  @property({
    type: 'string',
    required: true,
  })
  label: string;

  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'string',
    required: true,
  })
  uniqueLabelText: string;

  @property({
    type: 'string',
  })
  labelText?: string;

  @property({
    type: 'string',
    default: '',
  })
  description?: string;

  @property({
    type: 'string',
  })
  color?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
  })
  groupIdList?: string[];

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  distractorLabelList?: distractorLabels[];

  @property({
    type: 'date',
  })
  lastModifiedAt?: Date;

  @property({
    type: 'string',
  })
  lastModifiedBy?: string;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  imgFiles?: labelImageFilesInfo[];

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  attributes?: Attributes[];

  @property({
    type: 'boolean',
    default: true,
  })
  isAbleDelete?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  isDeleted?: boolean;

  constructor(data?: Partial<SystemLabel>) {
    super(data);
  }
}

export interface SystemLabelRelations {
  // describe navigational properties here
}

export type SystemLabelWithRelations = SystemLabel & SystemLabelRelations;

export interface LabelInfoCreateRequest {
  labelText: string;
  description: string;
  attributes: Attributes[];
  type: number;
  lableType?: number;
  imgFiles: labelImageFilesInfo[];
  distractorLabelList?: distractorLabels[];
  annotationType?: AnnotationType;
}

export interface Attributes {
  key: string;
  label: string;
  labelText?: string;
  values: attributesValue[];
}

export interface attributesValue {
  valueName: string;
  valueText: string;
  description: string;
  imgFiles?: labelImageFilesInfo[];
}

export interface labelImageFilesInfo {
  srcUrl: string;
  imageName: string;
  key: string;
  urlExpiredAt: Date;
}

export interface LabelInfoEditRequest {
  id: string;
  label: string;
  key: string;
  labelText: string;
  description: string;
  attributes: Attributes[];
  type: number;
  imgFiles?: labelImageFilesInfo[];
  distractorLabelList?: distractorLabels[];
  annotationType?: AnnotationType;
}

// export interface ProjectLabelListUpdateRequest {
//   type: number;
//   label: string;
//   labelText: string;
//   isSelected: boolean;
//   isUnableUnselected: boolean;
// }

export interface distractorLabels {
  distractorLabel: string;
}

export enum SystemLabelType {
  CLASS_ONLY = 1,
  CLASS_WITH_ATTRIBUTES = 2,
}

export interface systemLabelAttribute {
  key?: string;
  label?: string;
  labelText?: string;
  isNew?: boolean;
  errorMsg?: boolean;
  values?: systemLabelValues[];
}

export interface systemLabelValues {
  valueName?: string;
  valueText?: string;
  description?: string;
  isNew?: boolean;
  errorMsg?: boolean;
  imgFiles?: labelImageFilesInfo[];
}

export interface SystemLabelListResponse {
  type?: number;
  label?: string;
  key?: any;
  labelText?: string;
  description?: string;
  color?: string;
  teamId?: string;
  distractorLabelList?: distractorLabels[];
  lastModifiedAt?: Date | undefined;
  lastModifiedBy?: string | undefined;
  imgFiles?: any[];
  attributes?: Attributes[];
  isAbleDelete?: boolean | undefined;
  isEditable?: boolean | undefined;
  distractorObjectList?: Object[];
}

export enum AnnotationUserType {
  ANNOTATION_USER_TYPE_ANNOTATOR = 0,
  ANNOTATION_USER_TYPE_AUDITOR = 1,
  ANNOTATION_USER_TYPE_SUPER_ADMIN = 2,
  ANNOTATION_USER_TYPE_TEAM_ADMIN = 3,
  ANNOTATION_USER_TYPE_QA = 4,
}

export interface FileInterface {
  path: string;
  fieldname: string;
  originalname: string;
  buffer: any;
  mimetype: string;
}

export enum AnnotationType {
  BOX = 1,
  POLYGON = 2,
  LINE = 3,
  POINT = 4,
}

export interface APIClientLabelRefRequest {
  [k: string]: {
    [k: string]: string[];
  };
}
