/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, Connection name, type and configuration
 */

/**
 * @class Connection
 * purpose of Connection model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, Connection name, type and configuration
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ConnectionSourceType} from './source.model';

@model({settings: {strict: false}})
export class Connection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  sourceName?: string;

  @property({
    type: 'string',
  })
  type?: ConnectionSourceType;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'string',
  })
  createdBy?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
  })
  connectionId?: string;

  @property({
    type: 'string',
  })
  sourceId?: string;

  @property({
    type: 'string',
  })
  destinationId?: string;

  @property({
    type: 'boolean',
  })
  isEditable?: boolean;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  catalogId?: string;

  @property({
    type: 'number',
    default: 0,
  })
  itemCount?: number;

  @property({
    type: 'number',
    default: 0,
  })
  tableCount?: number;

  @property({
    type: 'date',
  })
  lastSyncTime?: Date;

  @property({
    type: 'number',
    default: 0,
  })
  collectionCount?: number;

  @property({
    type: 'number',
    default: 0,
  })
  itemSize?: number;

  @property({
    type: 'object',
  })
  configuration?: object;

  @property({
    type: 'object',
  })
  syncCatalog: {streams: Stream[]};

  @property({
    type: 'number',
  })
  connectionStatus?: DataSourceConnectionStatus;

  @property({
    type: 'object',
  })
  connectionCredentials?: ConnectionCredentials;

  @property({
    type: 'number',
  })
  connectionType: DataSourceConnectionType;

  @property({
    type: 'object',
  })
  unstructuredCollections?: UnstructuredCollection[];

  @property({
    type: 'string',
  })
  updateStatus?: DataSourceUpdateStatus;

  @property({
    type: 'number',
  })
  isDataStructureCrawled?: DataStructureCrawlStatus;
  [prop: string]: any;

  constructor(data?: Partial<Connection>) {
    super(data);
  }
}

export interface ConnectionRelations {
  // describe navigational properties here
}

export type ConnectionWithRelations = Connection & ConnectionRelations;

export enum SyncMode {
  full_refresh = 'full_refresh',
  incremental = 'incremental',
}

export enum DestinationSyncMode {
  APPEND = 'append',
  OVERWRITE = 'overwrite',
}

export interface SourceSchema {
  catalog?: {streams?: Stream[]};
  jobInfo?: any;
  catalogId?: string;
}

export interface Stream {
  stream?: {
    name: string;
    jsonSchema: any;
    supportedSyncModes: SyncMode[];
    defaultCursorField: any[];
    sourceDefinedPrimaryKey: any[];
    namespace: string;
  };
  config?: {
    syncMode?: string;
    cursorField?: string[];
    destinationSyncMode?: string;
    primaryKey?: string[][];
    aliasName?: string;
    selected?: boolean;
    suggested?: boolean;
  };
}

export interface AirbyteConnectionCredentials {
  sourceId: string;
  destinationId: string;
  name: string;
  scheduleType: string;
  scheduleData: {
    basicSchedule: {
      units: number;
      timeUnit: string;
    };
  };
  namespaceDefinition: string;
  nonBreakingChangesPreference: string;
  geography: string;
  syncCatalog: {streams: Stream[]};
  status: string;
  sourceCatalogId: string;
}

export interface ConnectionJob {
  job: {
    id: number;
    createdAt: number;
    updatedAt: number;
    status: string;
  };
  attempts: {
    totalStats: {
      recordsEmitted: number;
      bytesEmitted: number;
      recordsCommitted: number;
    };
  }[];
}

export enum SyncJobStatus {
  RUNNING = 1,
  COMPLETED = 3,
  FAILED = 2,
}

export interface ConnectionCredentials {
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database?: string;

  //for google big query
  credentials_path?: string;
  project_id?: string;

  //for snowflake connection
  warehouse?: string;
  role?: string;
  schema?: string;
  account_id?: string;

  //for sql server windows authentication
  is_windows_authentication?: boolean;
  domain?: string;
}

export interface ConnectionListAndTypeRes {
  sourceName: string;
  type: ConnectionSourceType;
}

export interface UnstructuredCollection {
  sourceCollection: string;
  metalakeCollection: string;
  dataAggregation: string;
}

export enum DataSourceConnectionType {
  RAW_DATABASE = 1,
  STORAGE = 2,
  LAYERNEXT_DATABASE = 3,
}

export enum DataSourceConnectionStatus {
  CONNECTING = 1,
  CONNECTED = 2,
  DISCONNECTED = 3,
  FAILED = 4,
}

export enum DataSourceUpdateStatus {
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

export enum DataStructureCrawlStatus {
  IN_PROGRESS = 1,
  COMPLETED = 2,
  FAILED = 3,
  NOT_INITIATED = 4,
}
