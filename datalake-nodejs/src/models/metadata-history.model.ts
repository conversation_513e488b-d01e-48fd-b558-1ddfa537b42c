/*
 * Copyright (c) 2023 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, metadataId, userId, etc. and other constraints.
 */

/**
 * @class MetaDataHistory
 * purpose of MetaDataHistory model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, metadataId, userId, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';

@model()
export class MetadataHistory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  metadataId?: ObjectId;

  @property({
    type: 'string',
  })
  userId?: ObjectId;

  @property({
    type: 'date',
  })
  date?: Date;

  @property({
    type: 'number',
  })
  activityType?: ACTIVITY_TYPE;

  @property({
    type: 'object',
  })
  info?: any;

  constructor(data?: Partial<MetadataHistory>) {
    super(data);
  }
}

export interface MetadataHistoryRelations {
  // describe navigational properties here
}

export type MetadataHistoryWithRelations = MetadataHistory & MetadataHistoryRelations;

export enum ACTIVITY_TYPE {
  MetaObjectRename = 1,
}
