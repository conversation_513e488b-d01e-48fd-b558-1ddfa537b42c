/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class SimilarImage
 * purpose of SimilarImage model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class SimilarImage extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  referenceObjectKey?: string;

  @property({
    type: 'string',
  })
  objectKey?: string;

  @property({
    type: 'number',
  })
  score?: string;

  @property({
    type: 'string',
  })
  modelName?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'number',
  })
  scoreThreshold?: string;

  [prop: string]: any;

  constructor(data?: Partial<SimilarImage>) {
    super(data);
  }
}

export interface SimilarImageRelations {
  // describe navigational properties here
}

export type SimilarImageWithRelations = SimilarImage & SimilarImageRelations;
