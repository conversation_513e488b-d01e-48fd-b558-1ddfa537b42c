/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, evalSetId, etc. and other constraints.
 */

/**
 * @class EvalSetRun
 * purpose of EvalSetRun model is to define properties and relations with other models
 * This table keep time series data from the EvalSet table
 * Before EvalSet snapshot is updated, the old data will be moved here
 * This table keep history of snapshots
 * @description model class describes business domain objects and defines a list of properties with id, evalSetId, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {EvalSetRunStats, EvalSetStatus} from './eval-set.model';

@model()
export class EvalSetRun extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  evalSetId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  movedAt: Date;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
    required: true,
  })
  updatedAt: Date;

  @property({
    type: 'number',
    required: true,
  })
  evalSetNumber: number;

  @property({
    type: 'number',
    required: true,
  })
  status: EvalSetStatus;

  @property({
    type: 'number',
    required: true,
  })
  totalQuestions: number;

  @property({
    type: 'object',
  })
  lastRunStats?: EvalSetRunStats;

  @property({
    type: 'date',
  })
  lastRunStartedAt?: Date;

  @property({
    type: 'date',
  })
  lastRunCompletedAt?: Date;

  @property({
    type: 'string',
  })
  lastRunUserName?: string;

  @property({
    type: 'string',
  })
  lastRunUserId?: string;

  @property({
    type: 'number',
  })
  dataDictRevisionNum?: number;

  @property({
    type: 'boolean',
    required: false,
    default: false,
  })
  isUserFeedback: boolean;

  constructor(data?: Partial<EvalSetRun>) {
    super(data);
  }
}

export interface EvalSetRunRelations {
  // describe navigational properties here
}

export type EvalSetRunWithRelations = EvalSetRun & EvalSetRunRelations;
