import {Entity, model, property} from '@loopback/repository';

@model()
export class KnowledgeBlock extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'boolean',
    required: true,
  })
  isEnabled: boolean;

  @property({
    type: 'object',
    required: true,
  })
  data: object;

  @property({
    type: 'string',
    required: true,
  })
  searchString: string;

  constructor(data?: Partial<KnowledgeBlock>) {
    super(data);
  }
}

export interface KnowledgeBlockRelations {
  // describe navigational properties here
}

export type KnowledgeBlockWithRelations = KnowledgeBlock & KnowledgeBlockRelations;
