/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 */

/**
 * @class EmbeddingModel
 * purpose of EmbeddingModel model is to define properties and relations with other models
 * @description EmbeddingModel class describes business domain objects and defines a list of properties with id, key, name, lastSyncTimestamp, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class EmbeddingModel extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  embeddingModelName?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  embeddingDimension?: number[];

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'string',
  })
  createdBy?: string;

  @property({
    type: 'date',
  })
  updatedAt?: Date;

  [prop: string]: any;

  constructor(data?: Partial<EmbeddingModel>) {
    super(data);
  }
}

export interface EmbeddingModelRelations {
  // describe navigational properties here
}

export type EmbeddingModelWithRelations = EmbeddingModel & EmbeddingModelRelations;
