/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, question, truth, etc. and other constraints.
 */

/**
 * @class EvalSnap
 * purpose of EvalSnap model is to define properties and relations with other models
 * this table keep latest up-to-date question, truth and aiAction
 * this is a latest snapshot of questions and answers
 * @description model class describes business domain objects and defines a list of properties with id, question, truth, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {EvalFeedbackType} from '../settings/constants';

@model()
export class EvalSnap extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  question: string;

  @property({
    type: 'string',
    required: true,
  })
  truth: string;

  @property({
    type: 'string',
    required: true,
  })
  evalSetId: string;

  @property({
    type: 'number',
    required: true,
  })
  questionEvalStatus: QuestionEvalStatus;

  @property({
    type: 'number',
    required: true,
  })
  qNum: number;

  @property({
    type: 'string',
  })
  aiAction?: string;

  @property({
    type: 'object',
  })
  aiAnswer?: {
    text?: string;
    csv?: string;
  };

  @property({
    type: 'string',
  })
  conversationId?: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
    required: true,
  })
  updatedAt: Date;

  @property({
    type: 'boolean',
    required: true,
  })
  isRunning: boolean;

  @property({
    type: 'number',
    required: true,
  })
  difficulty: DifficultyLevel;

  @property({
    type: 'number',
  })
  tuneUpUsageStatus?: TuneUpUsageStatus;

  @property({
    type: 'string',
    default: EvalFeedbackType.GROUND_TRUTH,
  })
  feedbackType?: string;

  @property({
    type: 'date',
  })
  assessedAt?: Date;

  @property({
    type: 'object',
  })
  assessmentResult?: {
    is_data_retrieval_correct: boolean;
    is_business_logic_correct: boolean;
    explanation: string;
    is_correct: boolean;
  };

  @property({
    type: 'number',
  })
  dataDictRevisionNum?: number;

  @property({
    type: 'date',
  })
  tunedUpAt?: Date;

  constructor(data?: Partial<EvalSnap>) {
    super(data);
  }
}

export interface EvalSnapRelations {
  // describe navigational properties here
}

export type EvalSnapWithRelations = EvalSnap & EvalSnapRelations;

export interface EvalSnapObject {
  evalSnapId?: string;
  createdAt: Date;
  question: string;
  truth: string;
  qNum: number;
  aiAction?: string;
  isRunnable: boolean;
  isRunning: boolean;
  questionEvalStatus: QuestionEvalStatus;
  questionType: 'easy' | 'normal' | 'hard';
}

export enum QuestionEvalStatus {
  NOT_RUN = 1,
  PASSED = 2,
  FAILED = 3,
}

export enum TuneUpUsageStatus {
  AI_ACTION_PENDING = 0,
  ASSESSMENT_PENDING = 1,
  TUNE_UP_PENDING = 2,
  TUNE_UP_DONE = 3,
  AI_ACTION_FAILED = 11,
  ASSESSMENT_FAILED = 12,
  TUNE_UP_NOT_REQUIRED = 20,
}

export enum DifficultyLevel {
  EASY = 1,
  NORMAL = 2,
  HARD = 3,
}
