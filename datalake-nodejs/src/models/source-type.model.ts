/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, source name, type and configuration
 */

/**
 * @class SourceType
 * purpose of SourceType model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, source name, type and configuration
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class SourceType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'object',
  })
  configuration?: object;

  [prop: string]: any;

  constructor(data?: Partial<SourceType>) {
    super(data);
  }
}

export interface SourceTypeRelations {
  // describe navigational properties here
}

export type SourceTypeWithRelations = SourceType & SourceTypeRelations;
