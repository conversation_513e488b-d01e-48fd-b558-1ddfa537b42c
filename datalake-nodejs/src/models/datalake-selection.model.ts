/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, isAllSelected, DatalakeSelectionRequest, etc. and other constraints.
 */

/**
 * @class DatalakeSelection
 * purpose of DatalakeSelection model is to track the selection
 * @description model class describes business domain objects and defines a list of properties with id, isAllSelected, DatalakeSelectionRequest, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {RequestBodyObject, SchemaObject} from '@loopback/rest';
import {CONTENT_TYPE_VALUES, getEnumNumberValues, getEnumStringValue} from '../settings/tools';
import {
  ContentType,
  ExploreSortBy,
  ExploreSortOrder,
  ExplorerFilterV2,
  FrameVerificationStatus,
  FrontendRequestCoordinateDataFormat,
  OBJECT_STATUS,
  SortObject,
} from './meta-data.model';

@model()
export class DatalakeSelection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  teamId: string;

  @property({
    type: 'object',
    required: true,
  })
  selectionRequest: DatalakeSelectionRequest;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'number',
    required: true,
  })
  objectType: ContentType;

  constructor(data?: Partial<DatalakeSelection>) {
    super(data);
  }
}

export interface DatalakeSelectionRelations {
  // describe navigational properties here
}

export type DatalakeSelectionWithRelations = DatalakeSelection & DatalakeSelectionRelations;

export interface DatalakeSelectionRequest {
  isAllSelected?: boolean;
  collectionId?: string;
  objectIdList?: string[];
  contentType?: ContentType;
  sortBy?: SortObject;
  query?: string;
  filterData?: ExplorerFilterV2;
  allMetaData?: boolean; //Use to identify whether to get custom meta data and other info(createdAt, updatedAt, size, collection name etc.)
  objectStatus?: OBJECT_STATUS;
  referenceImage?: string;
  modelName?: string;
  scoreThreshold?: number;
  embeddingGraph?: EmbeddingGraphObject;
  embeddingSelection?: EmbeddingSelectionObject;
}

export interface DatalakeSelectionRequestForAnalytics extends DatalakeSelectionRequest {
  chartFilterData?: ExplorerFilterV2;
  labelId?: string;
}

export interface EmbeddingSelectionObject {
  graphId: string;
  selectionType: SELECTION_TYPE; // box selection or lasso selection
  x: number[]; // x coordinate of the selection
  y: number[]; // y coordinate of the selection
}

export interface EmbeddingGraphObject {
  graphId?: string;
  coordinates?: FrontendRequestCoordinateDataFormat; // coordinates of the graph for graph zoom and pan
}

// export interface DatalakeSelectionExtendForSimilarSearch extends DatalakeSelectionRequest{
//   modelName?: string,
//   scoreThreshold?: number
// }

export enum SELECTION_TYPE {
  NOT_SELECTED = 0,
  LASSO = 1,
  BOX = 2,
}

export const SELECTION_TYPE_VALUES = getEnumNumberValues(SELECTION_TYPE) as SELECTION_TYPE[];

export interface GetMetaFieldsOfSystemRequest extends DatalakeSelectionRequest {
  isFileUpload: boolean;
}

export interface SelectedCollectionListUpdateFormat {
  collectionId: string;
  isSelected: CollectionSelectedStatus;
}

export enum CollectionSelectedStatus {
  UN_SELECTED = 0,
  SELECTED = 1,
  PARTIAL_SELECT = 2,
}

export interface SelectionOptionsObject {
  name: string;
  selected: boolean;
  filterOptions?: SelectionOptionsObject[];
}

export interface LabelMetadataSelectionOptionsObject {
  labelOptions: SelectionOptionsObject[];
  metadataOptions: SelectionOptionsObject[];
}

export const FRAME_VERIFICATION_TYPE_VALUES = getEnumNumberValues(FrameVerificationStatus) as FrameVerificationStatus[];
export const OBJECT_STATUS_VALUES = getEnumNumberValues(OBJECT_STATUS) as OBJECT_STATUS[];

let embeddingSelectionProperty: {[key in keyof Required<EmbeddingSelectionObject>]: SchemaObject} = {
  graphId: {type: 'string'},
  selectionType: {type: 'number', enum: SELECTION_TYPE_VALUES},
  x: {
    type: 'array',
    items: {type: 'number'},
  },
  y: {
    type: 'array',
    items: {type: 'number'},
  },
};

let embeddingSelection: SchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: embeddingSelectionProperty,
};

let embeddingGraph: SchemaObject = {
  type: 'object',
  additionalProperties: true,
  properties: {
    graphId: {type: 'string'},
    coordinates: {
      type: 'object',
      additionalProperties: true,
      properties: {
        x0: {type: 'number'},
        x1: {type: 'number'},
        y0: {type: 'number'},
        y1: {type: 'number'},
      },
    },
  },
};

let filterProperty: {[key in keyof Required<ExplorerFilterV2>]: SchemaObject} = {
  annotationTypes: {
    type: 'array',
    items: {type: 'number', enum: FRAME_VERIFICATION_TYPE_VALUES},
  },
  tags: {
    type: 'array',
    items: {type: 'string'},
  },
  metadata: {
    type: 'object',
    additionalProperties: true,
  },
  labels: {
    type: 'array',
    items: {type: 'string'},
  },
  date: {
    type: 'object',
    additionalProperties: false,
    properties: {
      fromDate: {type: 'string', format: 'date-time'},
      toDate: {type: 'string', format: 'date-time'},
    },
  },
  keywords: {
    type: 'array',
    items: {type: 'string'},
  },
  categories: {
    type: 'array',
    items: {type: 'string'},
  },
};

let filterData: SchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: filterProperty,
};

export const EXPLORER_SORT_TYPE_VALUES = getEnumNumberValues(ExploreSortBy) as ExploreSortBy[];
export const EXPLORER_SORT_ORDER_TYPE_VALUES = getEnumStringValue(ExploreSortOrder) as ExploreSortOrder[];

export let selectionREquestProperty: {[key in keyof Required<DatalakeSelectionRequest>]: SchemaObject} = {
  isAllSelected: {type: 'boolean'},
  collectionId: {type: 'string'},
  objectIdList: {
    type: 'array',
    items: {type: 'string'},
  },
  contentType: {
    type: 'number',
    enum: CONTENT_TYPE_VALUES,
  },
  sortBy: {
    type: 'object',
    additionalProperties: false,
    properties: {
      sortByField: {type: 'number', enum: EXPLORER_SORT_TYPE_VALUES},
      sortOrder: {type: 'string', enum: EXPLORER_SORT_ORDER_TYPE_VALUES},
    },
  },
  query: {type: 'string'},
  filterData: filterData,
  allMetaData: {type: 'boolean'},
  objectStatus: {type: 'number', enum: OBJECT_STATUS_VALUES},
  referenceImage: {type: 'string'},
  modelName: {type: 'string'},
  scoreThreshold: {type: 'number'},
  embeddingGraph: embeddingGraph,
  embeddingSelection: embeddingSelection,
};

export const DATALAKE_SELECTION_BODY: RequestBodyObject = {
  description: 'datalake selection body',
  required: true,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        additionalProperties: false,
        properties: selectionREquestProperty,
      },
    },
  },
};

export const OBJECT_REMOVE_BODY: RequestBodyObject = {
  description: 'object remove body',
  required: true,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        additionalProperties: false,
        required: ['collectionId'],
        properties: selectionREquestProperty,
      },
    },
  },
};
const UserSelectionRequestSchema: SchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    selectionId: {
      type: 'string',
    },
    selectedUserIds: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
    unSelectedUserIds: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
  },
  required: ['selectionId', 'selectedUserIds', 'unSelectedUserIds'],
};

export const ACCESS_CONTROL_BODY: RequestBodyObject = {
  description: 'access control body',
  required: true,
  content: {
    'application/json': {
      schema: UserSelectionRequestSchema,
    },
  },
};

export interface UserSelectionRequest {
  selectionId: string;
  selectedUserIds: string[];
  unSelectedUserIds: string[];
}
