/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, metaDataObject, apiKey, etc. and other constraints.
 */

/**
 * @class InputMetaDataFeed
 * purpose of InputMetaDataFeed model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, metaDataObject, apiKey, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';
import {ContentType, MetaData} from './meta-data.model';
import {MetaDataSuggestionFormat} from './meta-field.model';

@model({settings: {strict: false}})
export class InputMetaDataFeed extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  _id?: string; // id field is retrived as _id ehrn aggregating

  // Define well-known properties here

  @property({
    type: 'string',
    required: true,
  })
  apiKey: string;

  @property({
    type: 'object',
    required: true,
  })
  metaDataObject: Partial<MetaData>;

  @property({
    type: 'object',
  })
  metaDataPushList?: Partial<MetaData>;

  @property({
    type: 'array',
    itemType: 'string',
  })
  parentList?: ObjectId[];

  @property({
    type: 'string',
    required: true,
  })
  collectionName: string;

  @property({
    type: 'string',
    required: true,
  })
  collectionId: string;

  @property({
    type: 'number',
    required: true,
  })
  collectionType: ContentType;

  @property({
    type: 'boolean',
    required: true,
  })
  isActive: boolean;

  @property({
    type: 'string',
    // required: true,
  })
  storagePath?: string;

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<InputMetaDataFeed>) {
    super(data);
  }
}

export interface InputMetaDataFeedRelations {
  // describe navigational properties here
}

export type InputMetaDataFeedWithRelations = InputMetaDataFeed & InputMetaDataFeedRelations;

export interface MetaDataInputFeedObject {
  apiKey?: string;
  objectId?: string;
  metaDataList: InputMetaDataItem[];
  collectionName?: string;
  collectionType?: ContentType;
  collectionId?: string;
  projectId?: string;
}

export interface MetaDataCollectionInputObjectFormat {
  objectKeyList: string[];
  metaDataObject: Partial<MetaData>;
  metaDataPushList: Partial<MetaData>;
  collectionName?: string;
  collectionType?: ContentType;
  collectionId?: string;
  fieldConfig?: MetaDataSuggestionFormat[];
  isOverrideMetaData?: boolean;
  storagePrefixPath?: string;
}

export interface InputMetaDataItem {
  objectKey: string;
  metaDataObject: Partial<MetaData>;
  metaDataPushList: Partial<MetaData>;
  parentList?: string[];
  isActive?: boolean;
  apiKey?: string;
}

export interface InputMetaDataFeedGroup {
  _id: string;
  metaDataArray: InputMetaDataFeed[];
  metaDataObjectForUpdate?: MetaData[];
}

export interface EditMetaDataInputFormat {
  id: string; // file or collection id
  updates?: MetadataUpdatesInputType; // ---> after v16 frontend changes, this field contains only updated fields (if no changes in a field, it will not be included in this object)
  deleteFields?: string[];
  tags?: string[];
  deleteTags?: string[];
  updateSelfOnly?: boolean;
  fieldConfig?: MetaDataSuggestionFormat[];
}

export interface EditMetadataAllFlowsInputFormat extends Partial<EditMetaDataInputFormat> {
  selectionId?: string;
}

export type MetadataUpdatesInputType = Record<string, string | string[]>;
