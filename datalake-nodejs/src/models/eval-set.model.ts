/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, name, description, etc. and other constraints.
 */

/**
 * @class EvalSet
 * purpose of EvalSet model is to define properties and relations with other models
 * This is a group of questions and ground truths
 * This always keep latest up-to-date snapshot of groups
 * @description model class describes business domain objects and defines a list of properties with id, name, description, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {DifficultyLevel} from './eval-snap.model';

@model()
export class EvalSet extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
    required: true,
  })
  updatedAt: Date;

  @property({
    type: 'number',
    required: true,
  })
  evalSetNumber: number;

  @property({
    type: 'number',
    required: true,
  })
  status: EvalSetStatus;

  @property({
    type: 'number',
    required: true,
  })
  totalQuestions: number;

  @property({
    type: 'object',
  })
  lastRunStats?: EvalSetRunStats;

  @property({
    type: 'boolean',
    required: true,
  })
  isRunning: boolean;

  @property({
    type: 'date',
  })
  lastRunStartedAt?: Date;

  @property({
    type: 'date',
  })
  lastRunCompletedAt?: Date;

  @property({
    type: 'string',
  })
  lastRunUserName?: string;

  @property({
    type: 'string',
  })
  lastRunUserId?: string;

  @property({
    type: 'number',
  })
  dataDictRevisionNum?: number;

  @property({
    type: 'boolean',
    required: false,
    default: false,
  })
  isUserFeedback: boolean;

  constructor(data?: Partial<EvalSet>) {
    super(data);
  }
}

export interface EvalSetRelations {
  // describe navigational properties here
}

export type EvalSetWithRelations = EvalSet & EvalSetRelations;

export interface BulkUpdateOneDataFormat {
  updateOne: {
    filter: {[key: string]: any};
    update: {
      $set?: {[key: string]: any};
      $setOnInsert?: {[key: string]: any};
    };
    upsert: boolean;
  };
}

export enum EvalSetStatus {
  DRAFT = 1,
  ACTIVE = 2,
}

export interface EvalSetRunStats {
  calculatedAt: Date;
  passedQuestions: number;
  failedQuestions: number;
  notRunQuestions: number;
  score: number;
  difficultyLevelBreakdown: DifficultyLevelStatsBreakdown[];
}

export enum DataDictTuneUpStatus {
  NOT_TUNED = 0,
  TUNED = 1,
  IN_PROGRESS = 2,
}

export interface DifficultyLevelStatsBreakdown {
  difficultyLevel: DifficultyLevel;
  totalQuestions: number;
  passedQuestions: number;
  failedQuestions: number;
  notRunQuestions: number;
  score: number;
}

export interface EvalSetInfo {
  evalSetId?: string;
  name: string;
  evalSetNumber: number;
  lastRunStartedAt?: Date;
  isRunnable: boolean;
  isRunning: boolean;
  totalQuestions: number;
  passedQuestions: number;
  failedQuestions: number;
  notRunQuestions: number;
  score: number;
  difficultyLevelBreakdown: DifficultyLevelStatsBreakdown[];
}
