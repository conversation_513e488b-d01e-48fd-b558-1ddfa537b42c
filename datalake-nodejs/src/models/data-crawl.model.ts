/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, storageType, storageName, crawlStartedAt, etc. and other constraints.
 */

/**
 * @class DataCrawl
 * Use to track data crawlings
 * purpose of DataCrawl model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, storageType, storageName, crawlStartedAt, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ObjectId} from 'mongodb';
import {ConnectionSourceType} from './source.model';
import {CloudStorageActivityStatus, StorageConnectionStatus} from './system-data.model';

@model()
export class DataCrawl extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  storageType: ConnectionSourceType;

  @property({
    type: 'string',
  })
  storageName?: string;

  @property({
    type: 'string',
  })
  imageCollectionId?: string;

  @property({
    type: 'string',
  })
  videoCollectionId?: string;

  @property({
    type: 'string',
  })
  otherCollectionId?: string;

  @property({
    type: 'string',
  })
  teamId?: ObjectId | string;

  @property({
    type: 'date',
  })
  startedAt: Date; // whole process stared time

  @property({
    type: 'date',
  })
  updatedAt?: Date; // whole process updated time

  @property({
    type: 'date',
  })
  completedAt?: Date; // whole process completed time

  @property({
    type: 'date',
  })
  crawlStartedAt?: Date; // crawling from cloud stated time

  @property({
    type: 'date',
  })
  crawlFinishedAt?: Date; // crawling from cloud finished time

  @property({
    type: 'date',
  })
  thumbGenStartedAt?: Date; // thumbs generating started time

  @property({
    type: 'date',
  })
  thumbGenFinishedAt?: Date; // thumbs generating finished time

  @property({
    type: 'date',
  })
  storageSummaryReceivedAt?: Date; // received storage summaru from s3cmd service

  @property({
    type: 'number',
  })
  totalFileSize?: number;

  @property({
    type: 'number',
  })
  totalFileCount?: number;

  @property({
    type: 'number',
  })
  crawledFileSize: number;

  @property({
    type: 'number',
  })
  crawledFileCount: number;

  @property({
    type: 'number',
  })
  thumbnailGeneratableCount?: number;

  @property({
    type: 'number',
  })
  thumbnailGeneratedCount?: number;

  @property({
    type: 'number',
  })
  unpopulatedCount: number; // this is used to count objects we did't insert to db as meta objects. Ex: AWS folder objects, thumbnails files

  @property({
    type: 'number',
  })
  unpopulatedSize: number; // this is used to count size of objects we did't insert to db as meta objects. Ex: AWS folder objects, thumbnails files

  @property({
    type: 'string',
  })
  continuationToken?: string;

  @property({
    type: 'string',
  })
  nextContinuationToken?: string;

  @property({
    type: 'number',
  })
  status?: CrawlingStatus;

  @property({
    type: 'boolean',
  })
  isInitialCrawl?: boolean;

  @property({
    type: 'boolean',
  })
  isDefaultStorageBucket?: boolean;

  @property({
    type: 'string',
  })
  error?: string;

  @property({
    type: 'number',
  })
  newlyAddedFileCount: number; // number of files actually inserted to the db after filtering out exisiting files

  @property({
    type: 'number',
  })
  newlyAddedFileSize: number; // total size of files actually inserted to the db after filtering out exisiting files

  constructor(data?: Partial<DataCrawl>) {
    super(data);
  }
}

export interface DataCrawlRelations {
  // describe navigational properties here
}

export type DataCrawlWithRelations = DataCrawl & DataCrawlRelations;

export enum CrawlingStatus {
  FILES_CRAWLING_AND_STORAGE_SUMMARY_CALCULATING = 1,
  FILES_CRAWLING = 2,
  THUMBNAILS_GENERATING = 3,
  COMPLETED = 4,
  FAILED = 5,
}

export interface CrawlingStatusResponse {
  overallStatus: {
    crawlingStatus?: CrawlingStatus;
    progressString?: string;
    progress?: number | '--';
    crawlingResult: {
      crawlingFrom?: string;
      crawledFiles?: string;
      filesToBeCrawl?: number | '--';
      thumbsGenerated?: number;
      thumbsPending?: number;
      crawledSize?: string;
      crawledTime?: string;
      eta?: string;
    };
  };
  storageStatus: CloudStorageStatus[];
}

export interface CloudStorageStatus {
  storageName?: string;
  timeUpdated?: string;
  storageConnectionStatus?: StorageConnectionStatus;
  storageActivityStatus?: CloudStorageActivityStatus;
}

export interface CrawlHistory {
  storageName: string | undefined;
  lastCrawledAt: string;
  crawledFiles: string;
  crawledSize: string;
  crawledStatus: CrawlingStatus | undefined;
}
