/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, fieldName, fieldType, etc. and other constraints.
 */

/**
 * @class MetaField
 * purpose of MetaField model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, fieldName, fieldType, etc. and other constraints.
 * <AUTHOR> channa, chathushka, manelka, chamath
 */
import {Entity, model, property} from '@loopback/repository';
import {MetaData} from './meta-data.model';

@model()
export class MetaField extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  fieldName: string;

  @property({
    type: 'number',
    required: true,
  })
  fieldType: InputFieldTypes;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  options?: {valueId: string; value: string}[];

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'string',
    required: true,
  })
  uniqueName?: string;

  @property({
    type: 'date',
  })
  lastModifiedAt?: Date;

  @property({
    type: 'string',
  })
  modifiedBy?: string;

  constructor(data?: Partial<MetaField>) {
    super(data);
  }
}

export interface MetaFieldRelations {
  // describe navigational properties here
}

export type MetaFieldWithRelations = MetaField & MetaFieldRelations;

export enum InputFieldTypes {
  TEXT_BOX = 1,
  DROP_DOWN = 2,
}

export interface InputField {
  fieldName: string;
  fieldType: InputFieldTypes;
  options?: {valueId: string; value: string}[];
}

export interface MetaDataSuggestionFormat {
  id: string;
  fieldName: string;
  fieldType: InputFieldTypes;
  isSelected: boolean;
  isMandatory: boolean;
}

export interface MetaDataKeyInputList {
  collectionList: {name: string; id?: string}[];
  tagList: {
    allTags: any[];
    selectedTags: any[];
  };
}

export interface EditMetaFieldInputFormat {
  metaFieldId: string;
  editFieldConfig: InputField;
}

export interface MetaFieldSuggestionFormat {
  id: string;
  fieldName: string;
  fieldType: InputFieldTypes;
  options?: {valueId: string; value: string}[];
  isSelected: boolean;
  isMandatory: boolean;
  isDeletable?: boolean;
  isEditable?: boolean;
}

export interface MetaFieldFormatFileUpload {
  metaFieldList: any;
  metaObject: Partial<MetaData>;
}
