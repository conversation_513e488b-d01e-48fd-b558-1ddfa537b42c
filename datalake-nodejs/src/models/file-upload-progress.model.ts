/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, startedAt, progress, etc. and other constraints.
 */

/**
 * @class FileUploadProgress
 * Use to track file upload progress & to keep custom metadata for a particular upload
 * to show file upload progress of files uploaded from the frontend
 * @description model class describes business domain objects and defines a list of properties with id, startedAt, progress, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {ContentType, MetaData} from './meta-data.model';

@model()
export class FileUploadProgress extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'date',
  })
  startedAt?: Date;

  @property({
    type: 'date',
  })
  updatedAt?: Date;

  @property({
    type: 'date',
  })
  uploadFinishedAt?: Date;

  @property({
    type: 'date',
  })
  thumbGenStartedAt?: Date;

  @property({
    type: 'date',
  })
  thumbGenFinishedAt?: Date;

  @property({
    type: 'string',
    required: true,
  })
  collectionId: string;

  @property({
    type: 'string',
  })
  videoCollectionId?: string; // this exist if upload is doing to a other collection but the upload contains video files also

  @property({
    type: 'string',
  })
  imageCollectionId?: string; // this exist if upload is doing to a other collection but the upload contains image files also

  @property({
    type: 'string',
  })
  collectionName?: string;

  @property({
    type: 'string',
  })
  teamId?: string;

  @property({
    type: 'number',
  })
  totalFileCount?: number;

  @property({
    type: 'number',
  })
  status?: FileUploadProgressStatus;

  @property({
    type: 'object',
  })
  metaDataUpdates?: Partial<MetaData>;

  @property({
    type: 'array',
    itemType: 'string',
  })
  allFileList?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploadedFailedFileList?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploadedFileList?: string[];

  @property({
    type: 'number',
  })
  progress?: number;

  @property({
    type: 'boolean',
  })
  isThumbnailProcessingOngoing?: boolean;

  @property({
    type: 'boolean',
  })
  isOverrideMetaData?: boolean;

  @property({
    type: 'number',
  })
  collectionObjectType?: ContentType;

  constructor(data?: Partial<FileUploadProgress>) {
    super(data);
  }
}

export interface FileUploadProgressRelations {
  // describe navigational properties here
}

export type FileUploadProgressWithRelations = FileUploadProgress & FileUploadProgressRelations;

export enum FileUploadProgressStatus {
  UPLOADING = 1,
  COMPLETED = 2,
  FAILED = 3
}

