import {Entity, model, property} from "@loopback/repository";

export enum ObjectiveUpdateStatus {
  QUEUED = 'queued',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}

export enum ObjectiveUpdateType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete'
}

@model({settings: {mongodb: {collection: 'BusinessObjective'}}})
export class AnalyticObjective extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  objective?: string;

  @property({
    type: 'date',
  })
  created_date?: Date;

  @property({
    type: 'date',
  })
  modified_date?: Date;

  @property({
    type: 'string',
  })
  created_by?: string;

  @property({
    type: 'string',
  })
  modified_by?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  is_edit?: boolean;

  @property({
    type: 'boolean',
    default: true,
  })
  is_active?: boolean;

  @property({
    type: 'string',
    default: ObjectiveUpdateStatus.QUEUED
  })
  updateStatus?: ObjectiveUpdateStatus;

  @property({
    type: 'string',
    default: ObjectiveUpdateType.CREATE
  })
  updateType?: ObjectiveUpdateType;

  constructor(data?: Partial<AnalyticObjective>) {
    super(data);
  }
}

export interface AnalyticObjectiveRelations {
  // Describe navigational properties here
}

export type AnalyticObjectiveWithRelations = AnalyticObjective &
  AnalyticObjectiveRelations;