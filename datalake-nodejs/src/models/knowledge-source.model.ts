/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Model class describes business domain objects and defines a list of properties with id, key, name, etc. and other constraints.
 */

/**
 * @class KnowledgeSource
 * @description KnowledgeSource model keeps a collection of knowledge sources in Data Lake
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model()
export class KnowledgeSource extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  type: KnowledgeSourceType;

  @property({
    type: 'object',
    required: true,
  })
  credentials: object;

  @property({
    type: 'number',
    required: true,
  })
  status: KnowledgeSourceStatus;

  @property({
    type: 'number',
    required: true,
  })
  refreshStatus: KnowledgeSourceRefreshStatus;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  updatedAt: Date;

  @property({
    type: 'string',
  })
  updatedBy: string;

  @property({
    type: 'string',
  })
  updatedById: string;

  @property({
    type: 'string',
    required: true,
  })
  addedBy: string;

  @property({
    type: 'string',
    required: true,
  })
  addedById: string;

  @property({
    type: 'number',
  })
  businessRuleCount: number;

  constructor(data?: Partial<KnowledgeSource>) {
    super(data);
  }
}

export interface KnowledgeSourceRelations {
  // describe navigational properties here
}

export type KnowledgeSourceWithRelations = KnowledgeSource & KnowledgeSourceRelations;

export enum KnowledgeSourceType {
  DOCUMENT = 'document',
  LOOKER = 'looker',
  POWER_BI = 'power_bi',
  MANUAL = 'manual',
}

export enum KnowledgeSourceStatus {
  INITIALIZING = 1,
  CONNECTED = 2,
  DISCONNECTED = 3,
  FAILED = 4,
}

export enum KnowledgeSourceRefreshStatus {
  UNABLE_TO_REFRESH = 1,
  REFRESHING = 2,
  REFRESHED = 3,
  FAILED = 4,
}
