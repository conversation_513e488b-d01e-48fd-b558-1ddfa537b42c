/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with id, teamId, objectCounts, etc. and other constraints.
 */

/**
 * @class SystemData
 * purpose of SystemData model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, teamId, objectCounts, etc. and other constraints.
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';
import {Analytics} from './meta-data-update.model';
import {
  ContentTypeOverviewTabIndexFrontendConstants,
  MetaFieldAndArraySummaryFormat,
  MetaFieldAndTagAnalyticsLabelwiseFormat,
  OngoingGraphDataInfo,
} from './meta-data.model';

@model()
export class SystemData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  _id?: string; // id field is retrived as _id when aggregating

  @property({
    type: 'string',
  })
  teamId: string;

  // @property({
  //   type: 'object',
  // })
  // objectCounts?: MetaObjectCounts;

  @property({
    type: 'object',
  })
  objectTypeWiseCounts?: ObjectTypeWiseCounts;

  // @property({
  //   type: 'object',
  // })
  // frameCounts?: MetaFrameCounts;

  // @property({
  //   type: 'object',
  // })
  // labelCounts?: {[k: string]: number};

  // @property({
  //   type: 'number',
  // })
  // totalDataSize: number;

  @property({
    type: 'object',
  })
  apiConfigs: {maxSyncInterval: number};

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  cloudStorages?: CloudStorageInfo[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  recentMetaDataKeys?: string[];

  // @property({
  //   type: 'array',
  //   itemType: 'string',
  //   default: [],
  // })
  // recentTags?: string[];

  // @property({
  //   type: 'array',
  //   itemType: 'string',
  //   default: [],
  // })
  // allTags?: string[];

  @property({
    type: 'boolean',
  })
  isDefaultQueryOptionsInserted?: boolean;

  @property({
    type: 'boolean',
  })
  isStatCalculationOngoing?: boolean;

  // @property({
  //   type: 'number',
  // })
  // trashedObjectCount?: number;

  @property({
    type: 'object',
  })
  // metaDataFieldSummary?: {[key in ContentType]: MetaFieldAndArraySummaryFormat[]};
  metaDataFieldSummary?: MetaFieldSummaryObjectTypeFormat;
  //This contain system analytics
  @property({
    type: 'object',
  })
  systemAnalytics?: AnalyticsObjectTypeFormat;

  @property({
    type: 'object',
  })
  systemEmbeddingGraphDetail?: ObjectTypeWiseEmbeddingGraphDetail;

  @property({
    type: 'object',
  })
  metaDataFieldAnalyticsLabelwise?: MetaFieldAnalyticsLabelwiseObjectTypeFormat; // used to store metadata field analytics labelwise in system

  @property({
    type: 'object',
  })
  graphDataGenerateInfo?: OngoingGraphDataInfo;

  @property({
    type: 'object',
  })
  sourceConnectionStats?: {
    totalRecords: number;
    totalTables: number;
    totalCollections: number;
    totalFiles: number;
    totalSize: number;
  };

  @property({
    type: 'object',
  })
  dataSourcesOverview?: {
    _id: string;
    dataSourcesOverview: string; // this contain overall picture regarding all data sources available within the system
    documentDataSourceOverview: string; // overview regarding document data source
    description: string; // this contain the generated full text which is going to be used as prompt to LLM
    modifiedDate: Date;
  };

  @property({
    type: 'object',
  })
  businessOverview?: {
    description: string; //(business overview description
    modifiedDate: Date;
  };

  @property({
    type: 'number',
  })
  lastEvalSetNumber?: number; // this contain the last issued eval set number. It is an incremental integer

  @property({
    type: 'object',
  })
  dataDictTuneUpInfo?: DataDictTuneUpInfo;

  @property({
    type: 'date',
  })
  metaDataLastUpdatedAt?: Date;

  constructor(data?: Partial<SystemData>) {
    super(data);
  }
}

export interface SystemDataRelations {
  // describe navigational properties here
}

export type SystemDataWithRelations = SystemData & SystemDataRelations;

export interface DataDictTuneUpInfo {
  revisionNum: number;
  isTuneOngoing: boolean;
  lastTuneUpStartedAt?: Date;
  lastTuneUpCompletedAt?: Date;
  triggeredUserId?: string;
  triggeredUserName?: string;
}

export interface MetaObjectCounts {
  total: number;
  rootImageCount: number;
  rootVideoCount: number;
  videos: {
    count: number;
    size: number;
    frames: number;
    length: number;
  };
  images: {
    count: number;
    size: number;
  };
  videoCollections: {
    count: number;
    size: number;
    frames: number;
    length: number;
  };
  imageCollections: {
    count: number;
    size: number;
    frames: number;
  };
  datasets: {
    count: number;
    size: number;
    frames: number;
  };
  otherCollections: {
    count: number;
    size: number;
  };
  other: {
    count: number;
    size: number;
  };
}

export interface MetaFrameCounts {
  total: number;
  raw: number;
  machineAnnotated: number;
  verified: number;
}

export enum StorageConnectionStatus {
  NOT_CONNECTED = 0,
  CONNECTED = 1,
}

export interface CloudStorageInfo {
  storageType: string;
  storageName: string;
  connectionStatus?: StorageConnectionStatus;
  updatedAt?: Date;
}

export enum CloudStorageActivityStatus {
  NOT_CRAWLED = 1,
  CRAWLING = 2,
  CRAWLED = 3,
}

export enum ObjectCountFrontendConstants {
  VIDEO_LENGTH = 'Video Length',
  VIDEO_FRAME_COUNT = 'Frames',
  VIDEO_FILES_COUNT = 'Files',
  VIDEO_SIZE = 'Size',
  IMAGE_COUNT = 'Files',
  IMAGE_SIZE = 'Size',
  OTHER_FILE_COUNT = 'Files',
  OTHER_FILE_SIZE = 'Size',
}

export interface DataOverview {
  counts: {
    contentType: ContentTypeOverviewTabIndexFrontendConstants;
    details: {
      name: ObjectCountFrontendConstants;
      value: number | string;
    }[];
  }[];
}

export interface ObjectTypeWiseCounts {
  videos: ObjectTypeWiseCountsVal;
  images: ObjectTypeWiseCountsVal;
  videoCollections: ObjectTypeWiseCountsVal;
  imageCollections: ObjectTypeWiseCountsVal;
  datasets: ObjectTypeWiseCountsVal;
  other: ObjectTypeWiseCountsVal;
  otherCollections: ObjectTypeWiseCountsVal;
}

export interface MetaFieldSummaryObjectTypeFormat {
  videos?: MetaFieldAndArraySummaryFormat[];
  images?: MetaFieldAndArraySummaryFormat[];
  videoCollections?: MetaFieldAndArraySummaryFormat[];
  imageCollections?: MetaFieldAndArraySummaryFormat[];
  datasets?: MetaFieldAndArraySummaryFormat[];
  datasetsFrameData?: MetaFieldAndArraySummaryFormat[];
  other?: MetaFieldAndArraySummaryFormat[];
  otherCollections?: MetaFieldAndArraySummaryFormat[];
}

export type AnalyticsObjectTypeFormat = {[field in keyof Partial<ObjectTypeWiseCounts>]: Analytics[]};

export interface MetaFieldAnalyticsLabelwiseObjectTypeFormat {
  videos?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  images?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  videoCollections?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  imageCollections?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  datasets?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  datasetsFrameData?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  other?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
  otherCollections?: MetaFieldAndTagAnalyticsLabelwiseFormat[];
}
export interface LabelStats {
  human: number;
  machine: number;
}
export interface ObjectTypeWiseCountsVal {
  count: number;
  size: number;
  frames: number;
  length: number;
  imageCount: number;
  videoCount: number;
  otherCount: number;
  raw: number;
  verified: number;
  machineAnnotated: number;
  labelList: {[k: string]: number};
  labelStats?: LabelStats;
}

export interface ObjectTypeWiseEmbeddingGraphDetail {
  images: EmbeddingGraphDetail;
  datasets: EmbeddingGraphDetail;
}

export interface EmbeddingGraphDetail {
  graphId: string;
}

export interface DataDictionaryMappingResponse {
  dataDictionaryMapping: Array<any>;
}

export enum OverviewType {
  DATA_SOURCES_OVERVIEW = 'dataSourcesOverview',
  BUSINESS_OVERVIEW = 'businessOverview',
}
