/*
 * Copyright (c) 2024 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * model class describes business domain objects and defines a list of properties with _id, destination name, type and configuration
 */

/**
 * @class Destination
 * purpose of Destination model is to define properties and relations with other models
 * @description model class describes business domain objects and defines a list of properties with id, destination name, type and configuration
 * <AUTHOR>
 */

import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class Destination extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  _id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'date',
  })
  createdAt?: Date;

  @property({
    type: 'number',
  })
  type?: ConnectionDestinationType;

  @property({
    type: 'object',
  })
  connectionConfiguration?: ConnectionSourceConfiguration;

  @property({
    type: 'string',
  })
  destinationDefinitionId?: string;

  @property({
    type: 'string',
  })
  destinationId?: string;

  @property({
    type: 'string',
  })
  workspaceId?: string;

  @property({
    type: 'string',
  })
  destinationName?: string;

  [prop: string]: any;

  constructor(data?: Partial<Destination>) {
    super(data);
  }
}

export interface DestinationRelations {
  // describe navigational properties here
}

export type DestinationWithRelations = Destination & DestinationRelations;

export enum ConnectionDestinationType {
  MONGO_DB = 1,
  MYSQL_DB = 2,
}

export interface ConnectionDestinationCredentials {
  //mongo db
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database?: string;
}

export interface ConnectionSourceConfiguration {
  tunnel_method: {
    tunnel_method: string;
  };
  instance_type: {
    instance: string;
    port: number;
    host: string;
    tls: boolean;
  };
  auth_type: {
    authorization: string;
    password: string;
    username: string;
  };
  database: string;
}

export interface AirbyteConnectionDestinationCredentials {
  name?: string;
  destinationDefinitionId?: string;
  workspaceId?: string;
  connectionConfiguration?: {
    tunnel_method: {
      tunnel_method: string;
    };
    instance_type: {
      instance: string;
      port: number;
      host: string;
      tls: boolean;
    };
    auth_type: {
      authorization: string;
      password: string;
      username: string;
    };
    database: string;
  };
  connectionCredentials?: any;
}

export const DestinationDefinitionIds = {
  destinationDefinitionIdMysql: 'ca81ee7c-3163-4246-af40-094cc31e5e42',
  destinationDefinitionIdMongoDB: '8b746512-8c2e-6ac1-4adc-b59faafd473c',
};
