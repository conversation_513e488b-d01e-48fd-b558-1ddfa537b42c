/*
 * Copyright (c) 2022 ZOOMi Technologies Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * custom refresh token generation services
 */

/**
 * keys for jwt and refresh token service and constant binding
 * @description  use to bind keys for jwt and refresh token service and constant
 * <AUTHOR>
 */

import {TokenService} from '@loopback/authentication';
import {BindingKey} from '@loopback/core';
import dotenv from 'dotenv';
dotenv.config();

export namespace TokenServiceConstants {
  export const TOKEN_SECRET_VALUE = process.env.JWT_SECRET || 'layerxprod';
  export const TOKEN_EXPIRES_IN_VALUE = '21600'; //21600
}

export namespace RefreshTokenServiceConstants {
  export const REFRESH_TOKEN_SECRET_VALUE = process.env.JWT_SECRET || 'layerxproddd';
  export const REFRESH_TOKEN_EXPIRES_IN_VALUE = '604800'; //604800
}

export namespace TokenServiceBindings {
  export const TOKEN_SECRET = BindingKey.create<string>('authentication.jwt.secret');
  export const TOKEN_EXPIRES_IN = BindingKey.create<string>('authentication.jwt.expires.in.seconds');
  export const TOKEN_SERVICE = BindingKey.create<TokenService>('services.authentication.jwt.tokenservice');
}

export namespace RefreshTokenServiceBindings {
  export const REFRESH_SECRET = BindingKey.create<string>('authentication.jwt.refresh.secret');
  export const REFRESH_EXPIRES_IN = BindingKey.create<string>('authentication.jwt.refresh.expires.in.seconds');
  export const REFRESH_TOKEN_SERVICE = BindingKey.create<TokenService>(
    'services.authentication.jwt.refresh.tokenservice',
  );
}
