declare module 'intuit-oauth' {
  interface OAuthConfig {
    clientId: string;
    clientSecret: string;
    environment: string;
    redirectUri: string;
    token?: any;
    logging?: boolean;
  }

  interface Token {
    access_token: string;
    refresh_token?: string;
    getAccessToken(): string;
    getRefreshToken(): string;
  }

  class OAuthClient {
    constructor(config: OAuthConfig);
    getToken(): Token;
    authorizeUri(params: { scope: string[]; state?: string }): string;
    createToken(url: string): Promise<any>;
    refresh(): Promise<any>;
    makeApiCall(params: {
      url: string;
      method?: string;
      headers?: Record<string, string>;
      body?: any;
      responseType?: string;
    }): Promise<any>;
    
    static scopes: {
      Accounting: string;
      Payment: string;
      Payroll: string;
      TimeTracking: string;
      Benefits: string;
      Profile: string;
      Email: string;
      Phone: string;
      Address: string;
      OpenId: string;
      Intuit_name: string;
    };
  }

  export = OAuthClient;
}