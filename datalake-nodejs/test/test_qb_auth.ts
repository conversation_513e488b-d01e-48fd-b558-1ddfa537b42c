import {QuickBooksApiService} from '../src/services/quickbooks-api-service.ts';

async function testQuickBooksIntegration() {
  console.log('=== QuickBooks Integration Test ===\n');

  // Test 1: Service Initialization
  console.log('1. Testing service initialization...');
  try {
    const qbService = new QuickBooksApiService();
    console.log('✓ QuickBooksApiService initialized successfully\n');

    // Test 2: Generate Authorization URL
    console.log('2. Testing authorization URL generation...');
    const authUri = await qbService.connectQuickBooks();
    console.log('✓ Authorization URL generated:');
    console.log(`   ${authUri}\n`);

    // Test 3: Manual OAuth Flow Test
    console.log('3. Manual OAuth Flow Test:');
    console.log('   To complete the test:');
    console.log('   a) Copy the authorization URL above');
    console.log('   b) Open it in your browser');
    console.log('   c) Complete QuickBooks authorization');
    console.log('   d) Copy the "code" parameter from callback URL');
    console.log('   e) Run: node test-oauth-callback.js <code>\n');

    return true;
  } catch (error) {
    console.error('✗ Test failed:', error.message);
    return false;
  }
}

// Test OAuth callback separately
async function testOAuthCallback(authCode) {
  console.log('=== Testing OAuth Callback ===\n');

  try {
    const qbService = new QuickBooksApiService();
    console.log('Processing authorization code:', authCode);

    await qbService.onOAuthRedirect(authCode);
    console.log('✓ OAuth callback processed successfully');
    console.log('✓ QuickBooks client created and tested');
  } catch (error) {
    console.error('✗ OAuth callback failed:', error.message);
  }
}

// Check if running with auth code parameter
const authCode = null; //process.argv[2];
if (authCode) {
  testOAuthCallback(authCode);
} else {
  testQuickBooksIntegration().then(success => {
    if (success) {
      console.log('Integration test completed. Follow manual steps above to complete OAuth flow.');
    }
  });
}
